# Ottoman 产品研发资料系统

这是一个用于展示 Ottoman 系列产品研发资料的 Web 应用系统。

## 功能特点

- 支持多个产品型号的资料展示
- 包含产品介绍、结构分析、优化方案等模块
- 响应式设计，支持移动端访问
- 支持 Markdown 格式文档渲染
- 支持 SVG 图片展示

## 项目结构

```
ottoman-demo/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # 交互脚本
├── README.md           # 项目说明文档
└── ottoman-1000-data/  # 产品资料目录
    ├── product-introduction.md
    ├── structure_analysis.md
    ├── ottoman-optimization-overall.md
    └── ...其他文档
```

## 运行要求

- 现代浏览器（Chrome、Firefox、Safari、Edge 等）
- Python 3.x（用于启动本地服务器）

## 启动步骤

1. 克隆或下载项目到本地

2. 打开命令行终端，进入项目目录：
   ```bash
   cd ottoman-demo
   ```

3. 启动本地服务器：
   ```bash
   # 使用 Python 3
   python -m http.server 8000
   ```

4. 在浏览器中访问：
   ```
   http://localhost:8000
   ```

## 使用说明

1. 在左侧导航栏选择产品型号
2. 点击不同的标签页查看对应内容
3. 在右侧可以快速切换不同产品型号
4. 支持响应式布局，可以在移动设备上访问

## 注意事项

- 必须使用本地服务器运行项目，直接打开 HTML 文件可能无法正常加载内容
- 确保所有文档文件都放在正确的目录中
- 文档使用 Markdown 格式编写，支持表格、图片等元素

## 技术支持

如有问题或建议，请联系技术支持团队。 