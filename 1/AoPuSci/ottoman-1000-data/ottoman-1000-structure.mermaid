flowchart TD
    %% 主要组件定义
    MainFrame["主体框架结构<br>(基础支撑系统)"]
    ReactionPlate["反应盘+光电检测<br>(核心工作区域)"]
    ReagentStorage["试剂仓<br>(化学试剂储存)"]
    ReagentArm["试剂臂<br>(试剂取放)"]
    ReagentMixArm["试剂搅拌臂<br>(试剂混合)"]
    SampleArm["样品臂<br>(样品取放)"]
    SampleMixArm["样品搅拌臂<br>(样品混合)"]
    SampleRack["进样架<br>(样品入口)"]
    TransportSystem["传输系统<br>(物料传递)"]
    ControlSystem["控制系统<br>(流程协调)"]
    
    %% 样式设置
    classDef mainStructure fill:#f9f,stroke:#333,stroke-width:2px
    classDef coreModule fill:#bbf,stroke:#333,stroke-width:2px
    classDef reagentModule fill:#bfb,stroke:#333,stroke-width:2px
    classDef sampleModule fill:#fbb,stroke:#333,stroke-width:2px
    classDef supportModule fill:#ffd,stroke:#333,stroke-width:2px
    
    %% 应用样式
    class MainFrame mainStructure
    class ReactionPlate coreModule
    class ReagentStorage,ReagentArm,ReagentMixArm reagentModule
    class SampleRack,SampleArm,SampleMixArm sampleModule
    class TransportSystem,ControlSystem supportModule
    
    %% 连接关系
    MainFrame --> ReactionPlate
    MainFrame --> ReagentStorage
    MainFrame --> SampleRack
    MainFrame --> TransportSystem
    MainFrame --> ControlSystem
    
    %% 试剂流程
    ReagentStorage --> ReagentArm
    ReagentArm --> ReactionPlate
    ReagentArm --> TransportSystem
    ReagentMixArm --> ReactionPlate
    
    %% 样品流程
    SampleRack --> SampleArm
    SampleArm --> ReactionPlate
    SampleArm --> TransportSystem
    SampleMixArm --> ReactionPlate
    
    %% 控制系统连接
    ControlSystem --> ReagentArm
    ControlSystem --> ReagentMixArm
    ControlSystem --> SampleArm
    ControlSystem --> SampleMixArm
    ControlSystem --> ReactionPlate
    ControlSystem --> TransportSystem
    
    %% 工作流程标注
    SampleRack -->|1. 样品进入| SampleArm
    SampleArm -->|2. 样品转移| ReactionPlate
    ReagentStorage -->|3. 试剂准备| ReagentArm
    ReagentArm -->|4. 试剂添加| ReactionPlate
    ReagentMixArm -->|5a. 试剂混合| ReactionPlate
    SampleMixArm -->|5b. 样品混合| ReactionPlate
    ReactionPlate -->|6. 光电检测| ControlSystem
    ControlSystem -->|7. 数据处理| ReactionPlate
