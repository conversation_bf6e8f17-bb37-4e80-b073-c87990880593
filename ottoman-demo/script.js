document.addEventListener('DOMContentLoaded', () => {
    const productSelect = document.getElementById('productSelect');
    const tabButtons = document.querySelectorAll('.tab-btn, .submenu-btn');
    const contentDiv = document.getElementById('content');
    const productItems = document.querySelectorAll('.product-item');
    
    // 当前选中的产品和标签页
    let currentProduct = 'ottoman-1000';
    let currentTab = 'product-intro';

    // 文件路径映射
    const tabMapping = {
        'product-intro': './ottoman-1000-data/product-introduction.md',
        'structure': './ottoman-1000-data/structure_analysis.md',
        'optimization-overall': './ottoman-1000-data/ottoman-optimization-overall.md',
        'optimization-frame': './ottoman-1000-data/frame.md',
        'optimization-reaction': './ottoman-1000-data/reaction.md',
        'optimization-reagent': './ottoman-1000-data/reagent.md',
        'optimization-sample': './ottoman-1000-data/sample.md',
        'optimization-input': './ottoman-1000-data/input.md',
        'optimization-control': './ottoman-1000-data/control.md',
        'optimization-data': './ottoman-1000-data/data.md',
        'bom': './ottoman-1000-data/ottoman-bom-optimization.md'
    };

    // SVG图片映射
    const svgMapping = {
        'optimization-overall': './ottoman-1000-data/ottoman-optimization-overall.svg',
        'optimization-frame': './ottoman-1000-data/framework-optimization.svg',
        'optimization-reaction': './ottoman-1000-data/reaction-plate-optimization.svg',
        'optimization-reagent': './ottoman-1000-data/reagent-system-optimization.svg',
        'optimization-sample': './ottoman-1000-data/sample-system-optimization.svg',
        'optimization-input': './ottoman-1000-data/loading-rack-optimization.svg',
        'optimization-control': './ottoman-1000-data/control-system-optimization.svg',
        'optimization-data': './ottoman-1000-data/ui-system-optimization.svg'
    };

    // 加载Markdown内容
    async function loadContent(tabName) {
        const filePath = tabMapping[tabName];
        const svgPath = svgMapping[tabName];
        console.log('当前页面URL:', window.location.href);
        console.log('尝试加载文件:', filePath);
        console.log('尝试加载SVG:', svgPath);
        console.log('当前标签页:', tabName);
        
        if (!filePath) {
            console.error('未找到对应的文件路径');
            contentDiv.innerHTML = '<p>未找到对应的内容文件</p>';
            return;
        }

        try {
            console.log('开始加载文件...');
            const response = await fetch(filePath);
            console.log('响应状态:', response.status);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const content = await response.text();
            console.log('文件内容加载成功');
            
            const html = marked.parse(content);
            
            // 如果有对应的SVG文件，添加到内容中
            let finalHtml = `<div class="markdown-content">`;
            
            // 将HTML内容分割成标题和正文
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            const firstHeading = tempDiv.querySelector('h1, h2');
            
            if (firstHeading) {
                // 如果有标题，先添加标题
                finalHtml += firstHeading.outerHTML;
                
                // 如果有SVG，在标题后添加
                if (svgPath) {
                    console.log('添加SVG图片:', svgPath);
                    // 先尝试加载SVG文件
                    try {
                        const svgResponse = await fetch(svgPath);
                        if (!svgResponse.ok) {
                            throw new Error(`SVG加载失败: ${svgResponse.status}`);
                        }
                        const svgContent = await svgResponse.text();
                        console.log('SVG文件加载成功');
                        
                        finalHtml += `
                            <div class="optimization-image">
                                <h3>优化方案示意图</h3>
                                ${svgContent}
                            </div>
                        `;
                    } catch (svgError) {
                        console.error('SVG加载失败:', svgError);
                        finalHtml += `
                            <div class="optimization-image">
                                <h3>优化方案示意图</h3>
                                <p>图片加载失败: ${svgError.message}</p>
                            </div>
                        `;
                    }
                }
                
                // 添加剩余内容
                const remainingContent = tempDiv.innerHTML.replace(firstHeading.outerHTML, '');
                finalHtml += remainingContent;
            } else {
                // 如果没有标题，直接添加所有内容
                finalHtml += html;
                if (svgPath) {
                    console.log('添加SVG图片:', svgPath);
                    // 先尝试加载SVG文件
                    try {
                        const svgResponse = await fetch(svgPath);
                        if (!svgResponse.ok) {
                            throw new Error(`SVG加载失败: ${svgResponse.status}`);
                        }
                        const svgContent = await svgResponse.text();
                        console.log('SVG文件加载成功');
                        
                        finalHtml += `
                            <div class="optimization-image">
                                <h3>优化方案示意图</h3>
                                ${svgContent}
                            </div>
                        `;
                    } catch (svgError) {
                        console.error('SVG加载失败:', svgError);
                        finalHtml += `
                            <div class="optimization-image">
                                <h3>优化方案示意图</h3>
                                <p>图片加载失败: ${svgError.message}</p>
                            </div>
                        `;
                    }
                }
            }
            
            finalHtml += `</div>`;
            contentDiv.innerHTML = finalHtml;
        } catch (error) {
            console.error('加载失败:', error);
            contentDiv.innerHTML = `
                <div class="error-message">
                    <h2>内容加载失败</h2>
                    <p>文件路径：${filePath}</p>
                    <p>SVG路径：${svgPath}</p>
                    <p>错误信息：${error.message}</p>
                    <p>当前页面URL：${window.location.href}</p>
                    <p>请确保：</p>
                    <ol>
                        <li>使用本地服务器运行页面（例如：http://localhost:8000）</li>
                        <li>文件 ${filePath} 存在于正确的位置</li>
                        <li>SVG文件 ${svgPath} 存在于正确的位置</li>
                        <li>文件名大小写正确</li>
                    </ol>
                    <p>可以使用以下命令启动本地服务器：</p>
                    <pre>python -m http.server 8000</pre>
                </div>
            `;
        }
    }

    // 处理子菜单点击
    const submenuTrigger = document.querySelector('.submenu-trigger');
    if (submenuTrigger) {
        submenuTrigger.addEventListener('click', function(e) {
            e.preventDefault();
            const container = this.parentElement;
            container.classList.toggle('active');
        });
    }

    // 处理标签页切换
    tabButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            const tabName = button.dataset.tab;
            console.log('点击的按钮:', button.textContent);
            console.log('标签页名称:', tabName);
            
            if (!tabName) {
                console.error('按钮没有设置data-tab属性');
                return;
            }

            // 移除所有按钮的active类
            tabButtons.forEach(btn => btn.classList.remove('active'));
            
            // 添加active类到当前按钮
            button.classList.add('active');
            
            // 加载内容
            loadContent(tabName);
        });
    });

    // 处理产品型号切换
    function switchProduct(productId) {
        currentProduct = productId;
        
        // 更新产品选择器
        productSelect.value = productId;
        
        // 更新产品型号列表的激活状态
        productItems.forEach(item => {
            item.classList.toggle('active', item.dataset.product === productId);
        });
        
        // 重新加载当前标签页的内容
        loadContent(currentTab);
    }

    // 产品型号列表点击事件
    productItems.forEach(item => {
        item.addEventListener('click', () => {
            switchProduct(item.dataset.product);
        });
    });

    // 产品选择器变化事件
    productSelect.addEventListener('change', () => {
        switchProduct(productSelect.value);
    });

    // 提取特定章节内容的函数
    function extractSection(content, section) {
        const sectionMap = {
            'frame': '主体框架结构',
            'reaction': '反应盘与光电检测系统',
            'reagent': '试剂管理系统',
            'sample': '样本处理系统',
            'input': '进样架系统',
            'control': '控制与驱动系统',
            'data': '数据处理与用户界面系统'
        };

        const sectionTitle = sectionMap[section];
        if (!sectionTitle) return content;

        const regex = new RegExp(`##\\s*${sectionTitle}[\\s\\S]*?(?=##|$)`);
        const match = content.match(regex);
        return match ? match[0] : `<h2>${sectionTitle}</h2><p>该章节内容未找到</p>`;
    }

    // 初始加载
    loadContent('product-intro');
}); 