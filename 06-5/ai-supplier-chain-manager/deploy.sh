#!/bin/bash

# 供应商链管理系统 Docker 部署脚本
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    ports=(9002 9003 9004 9443)
    for port in "${ports[@]}"; do
        if ss -tlnp | grep -q ":$port "; then
            log_warning "端口 $port 已被占用"
        else
            log_info "端口 $port 可用"
        fi
    done
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p app/logs
    mkdir -p nginx/logs
    mkdir -p nginx/ssl
    
    log_success "目录创建完成"
}

# 停止现有服务
stop_services() {
    log_info "停止现有服务..."
    
    if docker-compose ps | grep -q "Up"; then
        docker-compose down
        log_success "现有服务已停止"
    else
        log_info "没有运行中的服务"
    fi
}

# 构建和启动服务
build_and_start() {
    log_info "构建和启动服务..."
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待后端服务
    log_info "等待后端服务启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:9002/api/ &>/dev/null; then
            log_success "后端服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "后端服务启动超时"
        return 1
    fi
    
    # 等待前端服务
    log_info "等待前端服务启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:9003/ &>/dev/null; then
            log_success "前端服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "前端服务启动超时"
        return 1
    fi
    
    # 等待Nginx服务
    log_info "等待Nginx服务启动..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:9004/health &>/dev/null; then
            log_success "Nginx服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Nginx服务启动超时"
        return 1
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    echo ""
    log_info "服务访问地址:"
    echo "  主入口: http://localhost:9004"
    echo "  前端直接访问: http://localhost:9003"
    echo "  后端API: http://localhost:9002/api/"
    echo "  健康检查: http://localhost:9004/health"
}

# 显示日志
show_logs() {
    if [ "$1" = "--logs" ]; then
        log_info "显示服务日志..."
        docker-compose logs -f
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "  供应商链管理系统 Docker 部署"
    echo "========================================"
    echo ""
    
    check_requirements
    check_ports
    create_directories
    stop_services
    build_and_start
    
    if wait_for_services; then
        log_success "部署成功！"
        show_status
        
        echo ""
        log_info "使用以下命令管理服务:"
        echo "  查看状态: docker-compose ps"
        echo "  查看日志: docker-compose logs -f"
        echo "  停止服务: docker-compose down"
        echo "  重启服务: docker-compose restart"
        
        show_logs "$1"
    else
        log_error "部署失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 执行主函数
main "$@" 