import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  
  // 根据环境确定API地址
  const getApiTarget = () => {
    if (mode === 'development') {
      return 'http://localhost:5000'
    } else if (mode === 'production') {
      // 生产环境下，如果是Docker部署，使用容器内网络
      return process.env.DOCKER_BUILD ? 'http://backend:5000' : 'http://localhost:9002'
    }
    return 'http://localhost:5000'
  }

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      port: 3000,
      host: true,
      proxy: {
        '/api': {
          target: getApiTarget(),
          changeOrigin: true,
          secure: false
        }
      }
    },
    build: {
      outDir: 'dist',
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            elementPlus: ['element-plus']
          }
        }
      }
    },
    define: {
      // 将环境变量注入到构建中
      __API_BASE_URL__: JSON.stringify(env.VITE_API_BASE_URL || getApiTarget())
    }
  }
}) 