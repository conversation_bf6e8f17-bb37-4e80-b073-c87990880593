# 构建阶段
FROM node:18-alpine as build-stage

WORKDIR /app

# 设置npm国内镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY package*.json ./

# 安装所有依赖（包括devDependencies，构建需要）
RUN npm ci

# 复制源码
COPY . .

# 设置构建环境变量
ENV DOCKER_BUILD=true
ENV NODE_ENV=production

# 构建生产版本
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 复制构建结果
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"] 