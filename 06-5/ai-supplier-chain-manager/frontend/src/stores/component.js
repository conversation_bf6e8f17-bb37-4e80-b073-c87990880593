import { defineStore } from 'pinia'
import axios from 'axios'

export const useComponentStore = defineStore('component', {
  state: () => ({
    components: [],
    currentComponent: null,
    loading: false,
    error: null,
    pagination: {
      total: 0,
      pages: 0,
      currentPage: 1,
      perPage: 20
    }
  }),

  actions: {
    // 获取物料列表
    async fetchComponents(params = {}) {
      this.loading = true
      this.error = null
      
      try {
        const response = await axios.get('/api/components', { params })
        
        if (response.data.success) {
          this.components = response.data.data.components
          this.pagination = {
            total: response.data.data.total,
            pages: response.data.data.pages,
            currentPage: response.data.data.current_page,
            perPage: response.data.data.per_page
          }
        } else {
          throw new Error(response.data.error || '获取物料列表失败')
        }
        
        return response.data
      } catch (error) {
        this.error = error.response?.data?.error || error.message || '获取物料列表失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取物料详情
    async fetchComponentDetail(componentId) {
      this.loading = true
      this.error = null
      this.currentComponent = null // 清空当前组件数据
      
      try {
        const response = await axios.get(`/api/components/${componentId}`)
        
        if (response.data.success) {
          this.currentComponent = response.data.data
        } else {
          throw new Error(response.data.error || '获取物料详情失败')
        }
        
        return response.data
      } catch (error) {
        this.error = error.response?.data?.error || error.message || '获取物料详情失败'
        this.currentComponent = null // 确保出错时清空数据
        throw error
      } finally {
        this.loading = false
      }
    },

    // 清空当前物料数据
    clearCurrentComponent() {
      this.currentComponent = null
    },

    // 清空错误
    clearError() {
      this.error = null
    },

    // 重置所有状态
    resetState() {
      this.currentComponent = null
      this.error = null
      this.loading = false
    }
  },

  getters: {
    // 是否有当前物料
    hasCurrentComponent: (state) => {
      return !!state.currentComponent
    }
  }
}) 