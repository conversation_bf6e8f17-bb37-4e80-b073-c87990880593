import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/axios'

export const useSupplierStore = defineStore('supplier', () => {
  // 状态
  const suppliers = ref([])
  const currentSupplier = ref(null)
  const supplierRatings = ref(new Map())
  const fetchingStatus = ref(new Map())
  const isLoading = ref(false)
  const pagination = ref({
    page: 1,
    per_page: 20,
    total: 0,
    pages: 0
  })

  // 计算属性
  const totalSuppliers = computed(() => pagination.value.total)
  const hasSuppliers = computed(() => suppliers.value.length > 0)

  // 方法
  const fetchSuppliers = async (params = {}) => {
    isLoading.value = true
    try {
      const response = await api.get('/api/suppliers', { params })
      
      if (response.data.success) {
        suppliers.value = response.data.data.suppliers
        pagination.value = response.data.data.pagination
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error)
      return { success: false, message: error.response?.data?.message || '获取供应商列表失败' }
    } finally {
      isLoading.value = false
    }
  }

  const fetchSupplierDetail = async (supplierId) => {
    try {
      const response = await api.get(`/api/suppliers/${supplierId}`)
      
      if (response.data.success) {
        currentSupplier.value = response.data.data
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取供应商详情失败:', error)
      return { success: false, message: error.response?.data?.message || '获取供应商详情失败' }
    }
  }

  const fetchSupplierInfo = async (supplierId) => {
    // 设置获取状态
    fetchingStatus.value.set(supplierId, {
      status: 'fetching',
      message: '正在获取企业信息...',
      progress: 0
    })

    try {
      const response = await api.post(`/api/suppliers/${supplierId}/fetch-info`)
      
      if (response.data.success) {
        fetchingStatus.value.set(supplierId, {
          status: 'success',
          message: '信息获取完成',
          progress: 100,
          data: response.data.data
        })
        
        // 更新供应商列表中的评级信息
        await refreshSupplierRating(supplierId)
        
        return { success: true, data: response.data.data }
      } else {
        fetchingStatus.value.set(supplierId, {
          status: 'error',
          message: response.data.message,
          progress: 0
        })
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取供应商信息失败:', error)
      const message = error.response?.data?.message || '获取信息失败'
      fetchingStatus.value.set(supplierId, {
        status: 'error',
        message: message,
        progress: 0
      })
      return { success: false, message }
    }
  }

  const getSupplierRating = async (supplierId) => {
    try {
      const response = await api.get(`/api/suppliers/${supplierId}/rating`)
      
      if (response.data.success) {
        if (response.data.data.has_rating) {
          supplierRatings.value.set(supplierId, response.data.data.rating)
        }
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取评级信息失败:', error)
      return { success: false, message: error.response?.data?.message || '获取评级信息失败' }
    }
  }

  const batchFetchSupplierInfo = async (supplierIds) => {
    // 设置批量获取状态
    supplierIds.forEach(id => {
      fetchingStatus.value.set(id, {
        status: 'fetching',
        message: '等待批量处理...',
        progress: 0
      })
    })

    try {
      const response = await api.post('/api/suppliers/batch-fetch-info', {
        supplier_ids: supplierIds
      })
      
      if (response.data.success) {
        // 更新每个供应商的状态
        response.data.data.results.forEach(result => {
          if (result.success) {
            fetchingStatus.value.set(result.supplier_id, {
              status: 'success',
              message: '信息获取完成',
              progress: 100,
              data: result
            })
          } else {
            fetchingStatus.value.set(result.supplier_id, {
              status: 'error',
              message: result.message,
              progress: 0
            })
          }
        })
        
        // 刷新供应商列表
        await fetchSuppliers()
        
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('批量获取失败:', error)
      // 设置所有供应商为错误状态
      supplierIds.forEach(id => {
        fetchingStatus.value.set(id, {
          status: 'error',
          message: '批量获取失败',
          progress: 0
        })
      })
      return { success: false, message: error.response?.data?.message || '批量获取失败' }
    }
  }

  const updateSupplierStatus = async (supplierId, status) => {
    try {
      const response = await api.put(`/api/suppliers/${supplierId}/status`, { status })
      
      if (response.data.success) {
        // 更新本地状态
        const supplier = suppliers.value.find(s => s.id === supplierId)
        if (supplier) {
          supplier.status = status
          supplier.status_text = response.data.data.status_text
          supplier.status_category = response.data.data.status_category
        }
        
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      return { success: false, message: error.response?.data?.message || '更新状态失败' }
    }
  }

  const refreshSupplierRating = async (supplierId) => {
    // 刷新单个供应商的评级信息
    const supplier = suppliers.value.find(s => s.id === supplierId)
    if (supplier) {
      const result = await getSupplierRating(supplierId)
      if (result.success && result.data.has_rating) {
        // 更新供应商的rating_info
        supplier.rating_info = {
          has_rating: true,
          business_info_status: result.data.rating.business_info ? '正常' : '未获取',
          certifications_status: result.data.rating.certifications ? '正常' : '未获取',
          valid_certs_count: result.data.rating.certifications?.valid_count || 0,
          total_certs_count: result.data.rating.certifications?.total_count || 0,
          last_updated: result.data.rating.evaluated_at
        }
      }
    }
  }

  const getFetchingStatus = (supplierId) => {
    return fetchingStatus.value.get(supplierId) || {
      status: 'idle',
      message: '',
      progress: 0
    }
  }

  const clearFetchingStatus = (supplierId) => {
    fetchingStatus.value.delete(supplierId)
  }

  const getSupplierRatingData = (supplierId) => {
    return supplierRatings.value.get(supplierId)
  }

  // 批量获取供应商信息
  const batchFetchInfo = async (supplierIds) => {
    try {
      const response = await api.post('/api/suppliers/batch-fetch-info', {
        supplier_ids: supplierIds
      })
      return response.data
    } catch (error) {
      console.error('批量获取供应商信息失败:', error)
      throw error
    }
  }

  // 分析供应商网站
  const analyzeWebsite = async (supplierId, params = {}) => {
    try {
      const response = await api.post(`/api/suppliers/${supplierId}/analyze-website`, params)
      return response.data
    } catch (error) {
      console.error('分析供应商网站失败:', error)
      throw error
    }
  }

  // 获取供应商评级信息（新方法，避免与现有方法冲突）
  const getSupplierRatingInfo = async (supplierId) => {
    try {
      const response = await api.get(`/api/suppliers/${supplierId}/rating`)
      return response.data
    } catch (error) {
      console.error('获取供应商评级失败:', error)
      throw error
    }
  }

  // ===== 分类相关功能 =====
  
  // 分类相关状态
  const categories = ref([])
  const currentProduct = ref(null)
  const productCategories = ref(null)
  const categoriesLoading = ref(false)
  const categoriesOverview = ref(null)

  // 获取分类概览
  const fetchCategoriesOverview = async () => {
    categoriesLoading.value = true
    try {
      const response = await api.get('/api/categories/overview')
      if (response.data.success) {
        categoriesOverview.value = response.data.data
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取分类概览失败:', error)
      return { success: false, message: error.response?.data?.message || '获取分类概览失败' }
    } finally {
      categoriesLoading.value = false
    }
  }

  // 获取特定产品的分类详情
  const fetchProductCategories = async (productId) => {
    categoriesLoading.value = true
    try {
      const response = await api.get(`/api/categories/product/${productId}`)
      if (response.data.success) {
        currentProduct.value = response.data.data.product
        productCategories.value = response.data.data.categories
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取产品分类失败:', error)
      return { success: false, message: error.response?.data?.message || '获取产品分类失败' }
    } finally {
      categoriesLoading.value = false
    }
  }

  // 获取分类详情
  const fetchCategoryDetail = async (categoryId) => {
    try {
      const response = await api.get(`/api/categories/${categoryId}`)
      if (response.data.success) {
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取分类详情失败:', error)
      return { success: false, message: error.response?.data?.message || '获取分类详情失败' }
    }
  }

  // 清空分类数据
  const clearCategoriesData = () => {
    categories.value = []
    currentProduct.value = null
    productCategories.value = null
    categoriesOverview.value = null
  }

  
  // 简化的评分方法
  const scoreSupplier = async (supplierId, forceRefresh = false) => {
    try {
      const response = await api.post(`/api/suppliers/${supplierId}/score`, {
        force_refresh: forceRefresh
      })
      
      if (response.data.success) {
        return { 
          success: true, 
          data: response.data.data,
          message: response.data.message
        }
      } else {
        return { 
          success: false, 
          message: response.data.message 
        }
      }
    } catch (error) {
      console.error('供应商评分失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '评分失败' 
      }
    }
  }

  // 获取已有的评分结果（仅查询数据库，不进行计算）
  const getSupplierScore = async (supplierId) => {
    try {
      const response = await api.get(`/api/suppliers/${supplierId}/score`)
      
      if (response.data.success) {
        return { 
          success: true, 
          data: response.data.data,
          has_scores: response.data.has_scores
        }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取评分失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '获取评分失败' 
      }
    }
  }

  // 计算供应商评分（触发实际的评分计算）
  const calculateSupplierScore = async (supplierId) => {
    try {
      const response = await api.post(`/api/suppliers/${supplierId}/score`)
      
      if (response.data.success) {
        return { 
          success: true, 
          data: response.data.data,
          message: response.data.message
        }
      } else {
        return { 
          success: false, 
          message: response.data.message 
        }
      }
    } catch (error) {
      console.error('供应商评分计算失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '评分计算失败' 
      }
    }
  }

  const batchScoreSuppliers = async (supplierIds, includeDetails = false) => {
    try {
      const response = await api.post('/api/suppliers/batch-score', {
        supplier_ids: supplierIds,
        include_details: includeDetails
      })
      
      if (response.data.success) {
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('批量评分失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '批量评分失败' 
      }
    }
  }

  const getScoringWeights = async () => {
    try {
      const response = await api.get('/api/suppliers/scoring/weights')
      
      if (response.data.success) {
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('获取评分权重失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '获取评分权重失败' 
      }
    }
  }

  const testGptConnection = async () => {
    try {
      const response = await api.get('/api/suppliers/scoring/test-gpt')
      
      if (response.data.success) {
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('测试GPT连接失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '测试GPT连接失败' 
      }
    }
  }

  return {
    // 状态
    suppliers,
    currentSupplier,
    supplierRatings,
    fetchingStatus,
    isLoading,
    pagination,
    
    // 计算属性
    totalSuppliers,
    hasSuppliers,
    
    // 方法
    fetchSuppliers,
    fetchSupplierDetail,
    fetchSupplierInfo,
    getSupplierRating,
    batchFetchSupplierInfo,
    updateSupplierStatus,
    refreshSupplierRating,
    getFetchingStatus,
    clearFetchingStatus,
    getSupplierRatingData,
    batchFetchInfo,
    analyzeWebsite,
    getSupplierRatingInfo,
    
    // 分类相关功能
    categories,
    currentProduct,
    productCategories,
    categoriesLoading,
    categoriesOverview,
    fetchCategoriesOverview,
    fetchProductCategories,
    fetchCategoryDetail,
    clearCategoriesData,

    // 新增的评分方法
    scoreSupplier,
    getSupplierScore,
    calculateSupplierScore,
    batchScoreSuppliers,
    getScoringWeights,
    testGptConnection
  }
}) 