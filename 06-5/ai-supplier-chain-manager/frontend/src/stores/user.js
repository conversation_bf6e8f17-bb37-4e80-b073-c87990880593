import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/axios'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const company = ref(null)
  const products = ref([])
  const currentProduct = ref(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!user.value)
  const userName = computed(() => user.value?.full_name || '')
  const companyName = computed(() => company.value?.name || '')
  const currentProductName = computed(() => currentProduct.value?.name || '')

  // 方法
  const login = async (credentials) => {
    isLoading.value = true
    try {
      const response = await api.post('/api/auth/login', credentials)
      
      if (response.data.success) {
        const { user: userData, products: productsData, current_product } = response.data.data
        
        user.value = userData
        company.value = userData.company
        products.value = productsData
        currentProduct.value = current_product
        
        // 保存到localStorage以支持页面刷新
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('company', JSON.stringify(userData.company))
        localStorage.setItem('products', JSON.stringify(productsData))
        localStorage.setItem('currentProduct', JSON.stringify(current_product))
        
        return { success: true, message: response.data.message }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('登录错误:', error)
      const message = error.response?.data?.message || '登录失败，请稍后重试'
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      await api.post('/api/auth/logout')
    } catch (error) {
      console.error('登出错误:', error)
    } finally {
      // 清除状态
      clearUserState()
    }
  }

  const clearUserState = () => {
    // 清除store状态
    user.value = null
    company.value = null
    products.value = []
    currentProduct.value = null
    
    // 清除localStorage
    localStorage.removeItem('user')
    localStorage.removeItem('company')
    localStorage.removeItem('products')
    localStorage.removeItem('currentProduct')
  }

  const handleAuthExpired = () => {
    // 处理登录状态失效
    clearUserState()
    
    // 如果当前不在登录页面，则跳转到登录页面
    if (router.currentRoute.value.path !== '/login') {
      router.push('/login')
    }
  }

  const checkAuthStatus = async () => {
    // 先从localStorage恢复状态
    const savedUser = localStorage.getItem('user')
    const savedCompany = localStorage.getItem('company')
    const savedProducts = localStorage.getItem('products')
    const savedCurrentProduct = localStorage.getItem('currentProduct')
    
    if (savedUser) {
      user.value = JSON.parse(savedUser)
      company.value = JSON.parse(savedCompany)
      products.value = JSON.parse(savedProducts)
      currentProduct.value = JSON.parse(savedCurrentProduct)
    }

    // 验证服务器端状态
    try {
      const response = await api.get('/api/auth/current')
      
      if (response.data.success) {
        const { user: userData, products: productsData, current_product } = response.data.data
        
        user.value = userData
        company.value = userData.company
        products.value = productsData
        currentProduct.value = current_product
        
        // 更新localStorage
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('company', JSON.stringify(userData.company))
        localStorage.setItem('products', JSON.stringify(productsData))
        localStorage.setItem('currentProduct', JSON.stringify(current_product))
      } else {
        // 服务器端未登录，清除本地状态
        handleAuthExpired()
      }
    } catch (error) {
      console.error('检查认证状态错误:', error)
      // 如果是401错误，说明登录已失效
      if (error.response?.status === 401) {
        handleAuthExpired()
      }
      // 其他网络错误时保持本地状态，但在实际API调用时会处理
    }
  }

  const switchProduct = async (productId) => {
    try {
      const response = await api.post('/api/products/switch', { product_id: productId })
      
      if (response.data.success) {
        currentProduct.value = response.data.data
        localStorage.setItem('currentProduct', JSON.stringify(response.data.data))
        return { success: true, message: response.data.message }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('切换产品错误:', error)
      const message = error.response?.data?.message || '切换产品失败'
      return { success: false, message }
    }
  }

  return {
    // 状态
    user,
    company,
    products,
    currentProduct,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userName,
    companyName,
    currentProductName,
    
    // 方法
    login,
    logout,
    checkAuthStatus,
    switchProduct,
    clearUserState,
    handleAuthExpired
  }
}) 