<template>
  <div class="dashboard">
    <!-- 欢迎信息 -->
    <div class="welcome-section mb-6">
      <el-card class="welcome-card">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">
              欢迎回来，{{ userStore.userName }}！
            </h2>
            <p class="text-gray-600">
              当前工作环境：{{ userStore.companyName }} - {{ userStore.currentProductName }}
            </p>
          </div>
          <div class="text-right">
            <p class="text-sm text-gray-500">今天是</p>
            <p class="text-lg font-semibold text-gray-700">{{ currentDate }}</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section mb-6">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon bg-blue-100">
                <el-icon class="text-blue-600" size="24"><Shop /></el-icon>
              </div>
              <div class="stat-info">
                <h3 class="stat-number">156</h3>
                <p class="stat-label">供应商总数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon bg-green-100">
                <el-icon class="text-green-600" size="24"><Grid /></el-icon>
              </div>
              <div class="stat-info">
                <h3 class="stat-number">2,847</h3>
                <p class="stat-label">组件总数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon bg-orange-100">
                <el-icon class="text-orange-600" size="24"><List /></el-icon>
              </div>
              <div class="stat-info">
                <h3 class="stat-number">23</h3>
                <p class="stat-label">进行中任务</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon bg-purple-100">
                <el-icon class="text-purple-600" size="24"><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <h3 class="stat-number">89%</h3>
                <p class="stat-label">匹配成功率</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要功能区域 -->
    <el-row :gutter="20">
      <!-- 快速操作 -->
      <el-col :span="12">
        <el-card class="quick-actions-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">快速操作</span>
            </div>
          </template>
          
          <div class="quick-actions">
            <el-button 
              type="primary" 
              size="large" 
              class="action-btn"
              @click="navigateTo('/main/suppliers/search')"
            >
              <el-icon><Search /></el-icon>
              搜索供应商
            </el-button>
            
            <el-button 
              type="success" 
              size="large" 
              class="action-btn"
              @click="navigateTo('/main/tasks/create')"
            >
              <el-icon><Plus /></el-icon>
              创建新任务
            </el-button>
            
            <el-button 
              type="info" 
              size="large" 
              class="action-btn"
              @click="navigateTo('/main/components')"
            >
              <el-icon><Grid /></el-icon>
              组件管理
            </el-button>
            
            <el-button 
              type="warning" 
              size="large" 
              class="action-btn"
              @click="navigateTo('/main/reports')"
            >
              <el-icon><DataAnalysis /></el-icon>
              查看报表
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 最近活动 -->
      <el-col :span="12">
        <el-card class="recent-activities-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">最近活动</span>
              <el-link type="primary" :underline="false">查看全部</el-link>
            </div>
          </template>
          
          <div class="activities-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-icon">
                <el-icon :class="activity.iconClass"><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <p class="activity-text">{{ activity.text }}</p>
                <p class="activity-time">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <div class="system-status mt-6">
      <el-card>
        <template #header>
          <div class="card-header">
            <span class="card-title">系统状态</span>
            <el-tag type="success" size="small">运行正常</el-tag>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="status-item">
              <span class="status-label">数据库连接</span>
              <el-tag type="success" size="small">正常</el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <span class="status-label">API服务</span>
              <el-tag type="success" size="small">正常</el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <span class="status-label">最后更新</span>
              <span class="status-value">{{ lastUpdateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  Shop,
  Grid,
  List,
  TrendCharts,
  Search,
  Plus,
  DataAnalysis
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 最后更新时间
const lastUpdateTime = ref('')

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    icon: 'Search',
    iconClass: 'text-blue-600',
    text: '完成了供应商搜索任务 #2024001',
    time: '2小时前'
  },
  {
    id: 2,
    icon: 'Plus',
    iconClass: 'text-green-600',
    text: '创建了新的组件分析任务',
    time: '4小时前'
  },
  {
    id: 3,
    icon: 'Grid',
    iconClass: 'text-orange-600',
    text: '更新了组件库信息',
    time: '6小时前'
  },
  {
    id: 4,
    icon: 'DataAnalysis',
    iconClass: 'text-purple-600',
    text: '生成了月度供应商报表',
    time: '1天前'
  }
])

// 导航到指定页面
const navigateTo = (path) => {
  router.push(path)
}

// 初始化
onMounted(() => {
  lastUpdateTime.value = new Date().toLocaleString('zh-CN')
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card :deep(.el-card__body) {
  padding: 2rem;
}

.welcome-card h2,
.welcome-card p {
  color: white;
}

.stat-card {
  height: 120px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
  line-height: 1;
}

.stat-label {
  color: #6b7280;
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
}

.card-header {
  display: flex;
  justify-content: between;
  align-items: center;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.action-btn {
  height: 60px;
  font-size: 1rem;
  border-radius: 8px;
}

.activities-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-size: 0.875rem;
}

.activity-time {
  margin: 0;
  color: #6b7280;
  font-size: 0.75rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.status-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.status-value {
  color: #1f2937;
  font-size: 0.875rem;
}
</style> 