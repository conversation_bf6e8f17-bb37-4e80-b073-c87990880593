<template>
  <div class="placeholder-page">
    <el-card class="placeholder-card">
      <div class="placeholder-content">
        <el-icon class="placeholder-icon" size="80">
          <Tools />
        </el-icon>
        <h2 class="placeholder-title">{{ pageTitle }}</h2>
        <p class="placeholder-description">
          此功能正在开发中，敬请期待...
        </p>
        <div class="placeholder-actions">
          <el-button type="primary" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上一页
          </el-button>
          <el-button @click="goHome">
            <el-icon><House /></el-icon>
            回到首页
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Tools, ArrowLeft, House } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 页面标题
const pageTitle = computed(() => {
  return route.meta?.title || '功能开发中'
})

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 回到首页
const goHome = () => {
  router.push('/main')
}
</script>

<style scoped>
.placeholder-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.placeholder-card {
  max-width: 500px;
  width: 100%;
}

.placeholder-content {
  text-align: center;
  padding: 2rem;
}

.placeholder-icon {
  color: #909399;
  margin-bottom: 1.5rem;
}

.placeholder-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 1rem;
}

.placeholder-description {
  color: #606266;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.placeholder-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}
</style> 