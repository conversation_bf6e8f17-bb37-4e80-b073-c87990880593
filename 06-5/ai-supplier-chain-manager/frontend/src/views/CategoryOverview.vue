<template>
  <div class="category-overview">
    <div class="page-header">
      <h1>产品分类管理</h1>
      <p class="page-description">查看和管理产品的分类体系，包括一级分类、二级分类及其特征描述</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="categoriesOverview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ categoriesOverview.summary.total_products }}</div>
              <div class="stat-label">产品总数</div>
            </div>
            <el-icon class="stat-icon"><Box /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ categoriesOverview.summary.total_categories }}</div>
              <div class="stat-label">分类总数</div>
            </div>
            <el-icon class="stat-icon"><Menu /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ categoriesOverview.summary.total_primary_categories }}</div>
              <div class="stat-label">一级分类</div>
            </div>
            <el-icon class="stat-icon"><Folder /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ categoriesOverview.summary.total_secondary_categories }}</div>
              <div class="stat-label">二级分类</div>
            </div>
            <el-icon class="stat-icon"><FolderOpened /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧：产品列表 -->
        <el-col :span="8">
          <el-card class="product-list-card">
            <template #header>
              <div class="card-header">
                <span>产品列表</span>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索产品..."
                  size="small"
                  style="width: 200px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </template>

            <div v-loading="categoriesLoading" class="product-list">
              <div 
                v-for="product in filteredProducts" 
                :key="product.product_id"
                class="product-item"
                :class="{ active: selectedProductId === product.product_id }"
                @click="selectProduct(product.product_id)"
              >
                <div class="product-info">
                  <h4>{{ product.product_name }}</h4>
                  <p v-if="product.product_model" class="product-model">型号：{{ product.product_model }}</p>
                  <p v-if="product.product_category" class="product-category">类别：{{ product.product_category }}</p>
                </div>
                <div class="product-stats">
                  <el-tag size="small" type="primary">{{ product.primary_categories_count }}个一级分类</el-tag>
                  <el-tag size="small" type="success">{{ product.secondary_categories_count }}个二级分类</el-tag>
                </div>
              </div>

              <el-empty v-if="filteredProducts.length === 0" description="暂无产品数据" />
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：分类详情 -->
        <el-col :span="16">
          <el-card class="category-detail-card">
            <template #header>
              <div class="card-header">
                <span v-if="currentProduct">{{ currentProduct.name }} - 分类详情</span>
                <span v-else>请选择产品查看分类</span>
                <el-button 
                  v-if="selectedProductId" 
                  type="primary" 
                  size="small" 
                  @click="refreshProductCategories"
                  :loading="categoriesLoading"
                >
                  刷新
                </el-button>
              </div>
            </template>

            <div v-if="!selectedProductId" class="no-selection">
              <el-empty description="请从左侧选择一个产品查看其分类详情" />
            </div>

            <div v-else-if="categoriesLoading" class="loading">
              <el-skeleton :rows="5" animated />
            </div>

            <div v-else-if="productCategories" class="categories-content">
              <CategoryTree 
                :categories="productCategories" 
                :product="currentProduct"
                @category-click="handleCategoryClick"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分类详情对话框 -->
    <CategoryDetailDialog 
      v-model="showCategoryDetail"
      :category-id="selectedCategoryId"
      @refresh="refreshProductCategories"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Box, Menu, Folder, FolderOpened, Search } from '@element-plus/icons-vue'
import { useSupplierStore } from '@/stores/supplier'
import CategoryTree from './components/CategoryTree.vue'
import CategoryDetailDialog from './components/CategoryDetailDialog.vue'

const supplierStore = useSupplierStore()

// 响应式数据
const searchKeyword = ref('')
const selectedProductId = ref(null)
const selectedCategoryId = ref(null)
const showCategoryDetail = ref(false)

// 计算属性
const categoriesOverview = computed(() => supplierStore.categoriesOverview)
const categoriesLoading = computed(() => supplierStore.categoriesLoading)
const currentProduct = computed(() => supplierStore.currentProduct)
const productCategories = computed(() => supplierStore.productCategories)

const filteredProducts = computed(() => {
  if (!categoriesOverview.value?.products) return []
  
  const products = categoriesOverview.value.products
  if (!searchKeyword.value) return products
  
  const keyword = searchKeyword.value.toLowerCase()
  return products.filter(product => 
    product.product_name.toLowerCase().includes(keyword) ||
    (product.product_model && product.product_model.toLowerCase().includes(keyword)) ||
    (product.product_category && product.product_category.toLowerCase().includes(keyword))
  )
})

// 方法
const selectProduct = async (productId) => {
  selectedProductId.value = productId
  
  try {
    const result = await supplierStore.fetchProductCategories(productId)
    if (!result.success) {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('获取产品分类失败')
  }
}

const refreshProductCategories = async () => {
  if (selectedProductId.value) {
    await selectProduct(selectedProductId.value)
  }
}

const handleCategoryClick = (categoryId) => {
  selectedCategoryId.value = categoryId
  showCategoryDetail.value = true
}

// 生命周期
onMounted(async () => {
  try {
    const result = await supplierStore.fetchCategoriesOverview()
    if (!result.success) {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('获取分类概览失败')
  }
})
</script>

<style scoped>
.category-overview {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  font-size: 40px;
  color: #E4E7ED;
}

.main-content {
  min-height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-list {
  max-height: 600px;
  overflow-y: auto;
}

.product-item {
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.product-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.product-item.active {
  border-color: #409EFF;
  background-color: #F0F9FF;
}

.product-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.product-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
}

.product-stats {
  margin-top: 10px;
  display: flex;
  gap: 8px;
}

.no-selection,
.loading {
  padding: 40px;
  text-align: center;
}

.categories-content {
  padding: 10px 0;
}
</style> 