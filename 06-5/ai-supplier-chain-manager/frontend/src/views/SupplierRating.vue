<template>
  <div class="supplier-rating-page">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="header-left">
          <el-button 
            type="text" 
            @click="$router.go(-1)"
            class="mb-2"
          >
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <h1 class="text-2xl font-bold text-gray-800">
            {{ supplierName || '供应商评级' }}
          </h1>
          <p class="text-gray-500 mt-1">企业信息获取与认证管理</p>
        </div>
        
        <div class="header-actions">
          <SupplierInfoFetcher 
            mode="single"
            :supplier-id="supplierId"
            @fetch-complete="handleFetchComplete"
          />
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <el-skeleton :rows="8" animated />
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-alert 
        type="error"
        :title="error"
        :closable="false"
        show-icon
      />
    </div>
    
    <!-- 主要内容 -->
    <div v-else class="main-content">
      <!-- 供应商基本信息 -->
      <div class="supplier-basic-info mb-6">
        <el-card>
          <template #header>
            <span class="text-lg font-semibold">供应商基本信息</span>
          </template>
          
          <el-descriptions :column="2" border size="small" v-if="supplierDetail">
            <el-descriptions-item label="供应商名称" :span="2">
              <span class="font-medium text-lg">{{ supplierDetail.supplier_name }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="所在地区">
              {{ supplierDetail.region || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(supplierDetail.status)" size="small">
                {{ supplierDetail.status_text }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="产品分类">
              {{ supplierDetail.category_full_path || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="网站">
              <el-link 
                v-if="supplierDetail.website"
                :href="supplierDetail.website"
                target="_blank"
                type="primary"
              >
                {{ supplierDetail.website }}
              </el-link>
              <span v-else class="text-gray-400">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ supplierDetail.phone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ supplierDetail.email || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="认证信息" :span="2">
              <div v-if="certificationsInfo && certificationsInfo.length > 0" class="certifications-summary">
                <div class="flex flex-wrap gap-2">
                  <el-tag 
                    v-for="cert in certificationsInfo.slice(0, 5)" 
                    :key="cert.id || cert.name"
                    :type="getCertificationTagType(cert.status)"
                    size="small"
                    class="mb-1"
                  >
                    {{ cert.name }}
                    <span v-if="cert.valid_until" class="ml-1 text-xs">
                      ({{ formatCertDate(cert.valid_until) }})
                    </span>
                  </el-tag>
                  <el-tag v-if="certificationsInfo.length > 5" type="info" size="small">
                    +{{ certificationsInfo.length - 5 }}个
                  </el-tag>
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  共{{ certificationsInfo.length }}项认证，
                  有效{{ validCertificationsCount }}项
                </div>
              </div>
              <span v-else class="text-gray-400">暂无认证信息</span>
            </el-descriptions-item>
            <el-descriptions-item label="地址" :span="2">
              {{ supplierDetail.address || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="匹配原因" :span="2">
              {{ supplierDetail.matching_reason || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ supplierDetail.notes || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
      
      <!-- 企业基本信息卡片 -->
      <div class="business-info-section mb-6">
        <BusinessInfoCard 
          :business-info="businessInfo"
          :is-refreshing="isRefreshingBusinessInfo"
          @refresh="handleRefreshBusinessInfo"
        />
      </div>

      <!-- 网站分析卡片 -->
      <div class="website-analysis-section mb-6">
        <WebsiteAnalysisCard 
          :supplier="supplierDetail"
          :rating="ratingData"
          @analysis-completed="handleAnalysisCompleted"
        />
      </div>

      <!-- 智能评分卡片 -->
      <div class="intelligent-scoring">
        <SupplierScoringCard 
          :supplier-id="supplierId"
          :supplier-name="supplierName"
          :category-feature="supplierDetail?.category_feature"
          :auto-score="false"
          @scoring-complete="handleScoringComplete"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useSupplierStore } from '@/stores/supplier'
import SupplierInfoFetcher from './components/SupplierInfoFetcher.vue'
import BusinessInfoCard from './components/BusinessInfoCard.vue'
import WebsiteAnalysisCard from './components/WebsiteAnalysisCard.vue'

// 导入评分组件
import SupplierScoringCard from './components/SupplierScoringCard.vue'

// 添加评分完成处理方法
const handleScoringComplete = (scoringData) => {
  console.log('评分完成:', scoringData)
  ElMessage.success('供应商评分已完成')
  // 可以在这里更新其他相关数据
}

const route = useRoute()
const router = useRouter()
const supplierStore = useSupplierStore()

// 响应式数据
const isLoading = ref(false)
const isRefreshingBusinessInfo = ref(false)
const error = ref('')
const supplierDetail = ref(null)
const ratingData = ref(null)

// 计算属性
const supplierId = computed(() => parseInt(route.params.id))
const supplierName = computed(() => supplierDetail.value?.supplier_name || '')

const businessInfo = computed(() => {
  if (!ratingData.value?.business_info) return null
  
  try {
    // 如果business_info是字符串，解析JSON
    const businessInfoData = typeof ratingData.value.business_info === 'string' 
      ? JSON.parse(ratingData.value.business_info) 
      : ratingData.value.business_info
    
    // 返回企业基本信息部分（不包括website_analysis）
    const { website_analysis, ...businessInfo } = businessInfoData
    return Object.keys(businessInfo).length > 0 ? businessInfo : null
  } catch (e) {
    console.error('解析企业信息失败:', e)
    return null
  }
})

const certificationsInfo = computed(() => {
  if (!ratingData.value?.certifications) return null
  
  try {
    // 如果certifications是字符串，解析JSON
    return typeof ratingData.value.certifications === 'string' 
      ? JSON.parse(ratingData.value.certifications) 
      : ratingData.value.certifications
  } catch (e) {
    console.error('解析认证信息失败:', e)
    return null
  }
})

// 有效认证数量
const validCertificationsCount = computed(() => {
  if (!certificationsInfo.value) return 0
  return certificationsInfo.value.filter(cert => 
    cert.status === 'valid' || cert.status === '有效'
  ).length
})

// 方法
const getStatusTagType = (status) => {
  switch (status) {
    case 0:
    case 1:
      return 'warning'
    case 99:
      return 'success'
    case 2:
    case 3:
    case 4:
    case 5:
      return 'danger'
    default:
      return 'info'
  }
}

// 获取认证标签类型
const getCertificationTagType = (status) => {
  if (!status) return 'info'
  
  const statusLower = status.toLowerCase()
  if (statusLower === 'valid' || statusLower === '有效') {
    return 'success'
  } else if (statusLower === 'expired' || statusLower === '过期') {
    return 'danger'
  } else if (statusLower === 'pending' || statusLower === '待审核') {
    return 'warning'
  }
  return 'info'
}

// 格式化认证日期
const formatCertDate = (dateStr) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit' 
    })
  } catch (e) {
    return dateStr
  }
}

const loadSupplierDetail = async () => {
  if (!supplierId.value) {
    error.value = '无效的供应商ID'
    return
  }

  isLoading.value = true
  error.value = ''

  try {
    // 获取供应商详情
    const detailResult = await supplierStore.fetchSupplierDetail(supplierId.value)
    if (!detailResult.success) {
      error.value = detailResult.message
      return
    }
    
    supplierDetail.value = detailResult.data

    // 获取评级信息
    const ratingResult = await supplierStore.getSupplierRating(supplierId.value)
    if (ratingResult.success && ratingResult.data.has_rating) {
      ratingData.value = ratingResult.data.rating
      
      // 检查是否需要自动获取缺失的数据
      await autoFetchMissingData()
    } else {
      // 如果没有评级数据，尝试自动获取企业信息
      await autoFetchInitialData()
    }

  } catch (err) {
    console.error('加载供应商信息失败:', err)
    error.value = '加载供应商信息失败'
  } finally {
    isLoading.value = false
  }
}

// 自动获取缺失的数据
const autoFetchMissingData = async () => {
  const promises = []
  
  // 检查企业基本信息
  if (!businessInfo.value) {
    console.log('企业基本信息缺失，自动获取...')
    promises.push(fetchBusinessInfoIfMissing())
  }
  
  // 检查认证信息
  if (!certificationsInfo.value) {
    console.log('认证信息缺失，自动获取...')
    promises.push(fetchCertificationsIfMissing())
  }
  
  // 检查网站分析结果
  if (!ratingData.value?.main_products && supplierDetail.value?.website) {
    console.log('网站分析结果缺失，自动获取...')
    promises.push(fetchWebsiteAnalysisIfMissing())
  }
  
  // 并行执行所有缺失数据的获取
  if (promises.length > 0) {
    await Promise.allSettled(promises)
    // 重新加载评级数据以获取最新信息
    await loadRatingData()
  }
}

// 自动获取初始数据（当没有评级记录时）
const autoFetchInitialData = async () => {
  console.log('没有评级数据，开始获取企业基本信息...')
  try {
    const result = await supplierStore.fetchSupplierInfo(supplierId.value)
    if (result.success) {
      console.log('企业信息获取成功')
      await loadRatingData()
      
      // 如果有网站，自动获取网站分析
      if (supplierDetail.value?.website) {
        await fetchWebsiteAnalysisIfMissing()
      }
    }
  } catch (error) {
    console.error('获取初始数据失败:', error)
  }
}

// 获取企业基本信息（如果缺失）
const fetchBusinessInfoIfMissing = async () => {
  try {
    const result = await supplierStore.fetchSupplierInfo(supplierId.value)
    if (result.success) {
      console.log('企业基本信息获取成功')
    }
  } catch (error) {
    console.error('获取企业基本信息失败:', error)
  }
}

// 获取认证信息（如果缺失）
const fetchCertificationsIfMissing = async () => {
  // 认证信息通常与企业基本信息一起获取
  // 这里可以添加单独的认证信息获取逻辑
  console.log('认证信息通常与企业基本信息一起获取')
}

// 获取网站分析结果（如果缺失）
const fetchWebsiteAnalysisIfMissing = async () => {
  if (!supplierDetail.value?.website) {
    console.log('供应商没有网站信息，跳过网站分析')
    return
  }
  
  try {
    const result = await supplierStore.analyzeWebsite(supplierId.value, {
      website_url: supplierDetail.value.website
    })
    if (result.success) {
      console.log('网站分析完成')
    }
  } catch (error) {
    console.error('网站分析失败:', error)
  }
}

const handleFetchComplete = async (data) => {
  // 信息获取完成后，重新加载评级数据
  await loadRatingData()
}

const handleRefreshBusinessInfo = async () => {
  // 刷新企业基本信息
  isRefreshingBusinessInfo.value = true
  
  try {
    const result = await supplierStore.fetchSupplierInfo(supplierId.value)
    
    if (result.success) {
      ElMessage.success('企业信息刷新成功')
      await loadRatingData()
    } else {
      ElMessage.error(result.message || '刷新失败')
    }
  } catch (error) {
    console.error('刷新企业信息失败:', error)
    ElMessage.error('刷新企业信息失败')
  } finally {
    isRefreshingBusinessInfo.value = false
  }
}

const handleAnalysisCompleted = async (analysisResult) => {
  // 网站分析完成后，重新加载评级数据
  await loadRatingData()
  ElMessage.success('网站分析结果已更新')
}

const loadRatingData = async () => {
  try {
    const ratingResult = await supplierStore.getSupplierRating(supplierId.value)
    if (ratingResult.success && ratingResult.data.has_rating) {
      ratingData.value = ratingResult.data.rating
    }
  } catch (error) {
    console.error('加载评级数据失败:', error)
  }
}

// 监听路由参数变化
watch(() => route.params.id, () => {
  loadSupplierDetail()
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (supplierId.value) {
    loadSupplierDetail()
  }
  loadRatingData()
})
</script>

<style scoped>
.supplier-rating-page {
  @apply p-6 bg-gray-50 min-h-screen;
}

.page-header {
  @apply bg-white p-6 rounded-lg shadow-sm;
}

.header-left h1 {
  @apply mb-0;
}

.header-actions {
  @apply flex items-center space-x-4;
}

.loading-state {
  @apply bg-white p-6 rounded-lg shadow-sm;
}

.error-state {
  @apply bg-white p-6 rounded-lg shadow-sm;
}

.main-content {
  @apply space-y-6;
}

.supplier-basic-info {
  @apply bg-white rounded-lg shadow-sm;
}

.business-info-section {
  @apply h-fit;
}

.website-analysis-section {
  @apply bg-white rounded-lg shadow-sm;
}

.intelligent-scoring {
  @apply bg-white rounded-lg shadow-sm;
}

.certifications-summary {
  @apply w-full;
}

.certifications-summary .flex {
  @apply items-center;
}
</style> 