<template>
  <div class="component-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/main/categories' }">产品分类</el-breadcrumb-item>
        <el-breadcrumb-item>物料详情</el-breadcrumb-item>
      </el-breadcrumb>
      
      <div class="header-actions">
        <el-button @click="$router.go(-1)" icon="ArrowLeft">返回</el-button>
        <el-button type="primary" @click="refreshData" :loading="loading">刷新数据</el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && !currentComponent" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 错误状态 -->
    <el-alert
      v-if="shouldShowError"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      style="margin-bottom: 20px"
    />

    <!-- 主要内容 -->
    <div v-if="currentComponent" class="content-container">
      <!-- 基本信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
            <el-tag :type="getComponentTypeTag(currentComponent.category)">
              {{ currentComponent.category || '未分类' }}
            </el-tag>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label>物料名称：</label>
              <span class="value">{{ currentComponent.name }}</span>
            </div>
            <div class="info-item">
              <label>物料编号：</label>
              <span class="value">{{ currentComponent.component_code || '暂无' }}</span>
            </div>
            <div class="info-item">
              <label>规格型号：</label>
              <span class="value">{{ currentComponent.spec || '暂无' }}</span>
            </div>
            <div class="info-item">
              <label>材料类型：</label>
              <span class="value">{{ currentComponent.material || '暂无' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>原始供应商：</label>
              <span class="value">{{ currentComponent.original_supplier || '暂无' }}</span>
            </div>
            <div class="info-item">
              <label>价格：</label>
              <span class="value price">
                {{ currentComponent.price ? `¥${currentComponent.price}` : '暂无' }}
              </span>
            </div>
            <div class="info-item">
              <label>数量：</label>
              <span class="value">{{ currentComponent.quantity || '暂无' }}</span>
            </div>
            <div class="info-item">
              <label>标签：</label>
              <el-tag v-if="currentComponent.tag" size="small">{{ currentComponent.tag }}</el-tag>
              <span v-else class="value">暂无</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 图纸链接卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>图纸文档</h3>
            <el-tag size="small" type="info">{{ currentComponent.documents?.length || 0 }} 个文档</el-tag>
          </div>
        </template>
        
        <div v-if="currentComponent.documents?.length" class="documents-list">
          <div 
            v-for="document in currentComponent.documents" 
            :key="document.id"
            class="document-item"
          >
            <div class="document-info">
              <el-icon class="document-icon" :class="getDocumentIconClass(document.file_type)">
                <component :is="getDocumentIcon(document.file_type)" />
              </el-icon>
              <div class="document-details">
                <div class="document-name">{{ document.document_name }}</div>
                <div class="document-description" v-if="document.description">
                  {{ document.description }}
                </div>
                <div class="document-meta">
                  <el-tag size="small" :type="getFileTypeTag(document.file_type)">
                    {{ getFileTypeLabel(document.file_type) }}
                  </el-tag>
                  <span class="document-date">{{ formatDate(document.created_at) }}</span>
                </div>
              </div>
            </div>
            <div class="document-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="openDocument(document.oss_path)"
                icon="View"
              >
                查看
              </el-button>
              <el-button 
                size="small" 
                @click="downloadDocument(document.oss_path, document.document_name)"
                icon="Download"
              >
                下载
              </el-button>
            </div>
          </div>
        </div>
        
        <div v-else class="no-documents">
          <el-empty description="暂无图纸文档" :image-size="80">
            <template #description>
              <p>该物料暂无相关的技术图纸或文档</p>
            </template>
          </el-empty>
        </div>
      </el-card>

      <!-- 其他信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <h3>其他信息</h3>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label>所属公司ID：</label>
              <span class="value">{{ currentComponent.client_company_id || '暂无' }}</span>
            </div>
            <div class="info-item">
              <label>所属产品ID：</label>
              <span class="value">{{ currentComponent.client_product_id || '暂无' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>创建时间：</label>
              <span class="value">{{ formatDate(currentComponent.created_at) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useComponentStore } from '@/stores/component'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Picture, 
  Files, 
  Folder, 
  VideoPlay,
  Setting
} from '@element-plus/icons-vue'

const route = useRoute()
const componentStore = useComponentStore()

// 计算属性
const currentComponent = computed(() => componentStore.currentComponent)
const loading = computed(() => componentStore.loading)
const error = computed(() => componentStore.error)

// 只有在不是加载状态且有错误时才显示错误
const shouldShowError = computed(() => {
  return !loading.value && error.value && !currentComponent.value
})

// 监听路由参数变化，重新加载数据
watch(() => route.params.id, (newId, oldId) => {
  if (newId && newId !== oldId) {
    loadComponentData()
  }
}, { immediate: false })

// 方法
const getComponentTypeTag = (category) => {
  const typeMap = {
    'A': 'primary',
    'B': 'success', 
    'C': 'warning',
    'D': 'danger'
  }
  return typeMap[category] || 'info'
}

const formatDate = (dateString) => {
  if (!dateString) return '暂无'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch (error) {
    return '暂无'
  }
}

// 文档相关方法
const getDocumentIcon = (fileType) => {
  const iconMap = {
    'pdf': Document,
    'cad': Setting,
    'solidworks': Setting,
    '3d': Setting,
    'image': Picture,
    'word': Document,
    'excel': Files,
    'other': Folder
  }
  return iconMap[fileType] || Folder
}

const getDocumentIconClass = (fileType) => {
  const classMap = {
    'pdf': 'pdf-icon',
    'cad': 'cad-icon',
    'solidworks': 'sw-icon',
    '3d': 'model-icon',
    'image': 'image-icon',
    'word': 'word-icon',
    'excel': 'excel-icon',
    'other': 'other-icon'
  }
  return classMap[fileType] || 'other-icon'
}

const getFileTypeTag = (fileType) => {
  const tagMap = {
    'pdf': 'danger',
    'cad': 'warning',
    'solidworks': 'primary',
    '3d': 'success',
    'image': 'info',
    'word': 'primary',
    'excel': 'success',
    'other': ''
  }
  return tagMap[fileType] || ''
}

const getFileTypeLabel = (fileType) => {
  const labelMap = {
    'pdf': 'PDF文档',
    'cad': 'CAD图纸',
    'solidworks': 'SolidWorks',
    '3d': '3D模型',
    'image': '图片',
    'word': 'Word文档',
    'excel': 'Excel表格',
    'other': '其他'
  }
  return labelMap[fileType] || '未知'
}

const openDocument = (ossPath) => {
  if (!ossPath) {
    ElMessage.warning('文档链接无效')
    return
  }
  
  // 在新窗口中打开文档
  window.open(ossPath, '_blank')
}

const downloadDocument = (ossPath, fileName) => {
  if (!ossPath) {
    ElMessage.warning('文档链接无效')
    return
  }
  
  try {
    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a')
    link.href = ossPath
    link.download = fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('开始下载文档')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败，请直接点击查看')
  }
}

const refreshData = async () => {
  await loadComponentData()
  ElMessage.success('数据刷新成功')
}

const loadComponentData = async () => {
  const componentId = route.params.id
  
  if (!componentId) {
    console.error('物料ID不存在')
    return
  }
  
  try {
    await componentStore.fetchComponentDetail(componentId)
  } catch (error) {
    console.error('加载物料数据失败:', error)
    // 错误信息已经在store中设置，会在页面上显示
  }
}

// 生命周期
onMounted(() => {
  // 重置状态，避免显示之前的错误信息
  componentStore.resetState()
  loadComponentData()
})

// 组件卸载时清理状态
onBeforeUnmount(() => {
  componentStore.clearError()
})
</script>

<style scoped>
.component-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 10px;
}

.info-item .value {
  color: #303133;
}

.info-item .price {
  font-weight: 600;
  color: #e6a23c;
  font-size: 16px;
}

.drawing-section {
  padding: 40px 20px;
  text-align: center;
}

.drawing-placeholder {
  color: #909399;
}

.drawing-icon {
  font-size: 48px;
  color: #C0C4CC;
  margin-bottom: 16px;
}

.drawing-placeholder p {
  margin: 8px 0;
}

.drawing-placeholder .hint {
  font-size: 12px;
  color: #C0C4CC;
}

.documents-list {
  margin-top: 20px;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.document-info {
  display: flex;
  align-items: center;
}

.document-icon {
  font-size: 24px;
  margin-right: 10px;
}

.document-details {
  flex: 1;
}

.document-name {
  font-weight: 600;
  color: #303133;
}

.document-description {
  color: #606266;
  margin-top: 5px;
}

.document-meta {
  margin-top: 5px;
  color: #909399;
}

.document-actions {
  display: flex;
  gap: 10px;
}

.no-documents {
  padding: 40px;
  text-align: center;
}

/* 文档图标样式 */
.pdf-icon {
  color: #e74c3c;
}

.cad-icon {
  color: #f39c12;
}

.sw-icon {
  color: #3498db;
}

.model-icon {
  color: #27ae60;
}

.image-icon {
  color: #9b59b6;
}

.word-icon {
  color: #2980b9;
}

.excel-icon {
  color: #27ae60;
}

.other-icon {
  color: #95a5a6;
}

.document-item:last-child {
  border-bottom: none;
}

.document-item:hover {
  background-color: #f8f9fa;
}

.document-date {
  font-size: 12px;
  margin-left: 10px;
}
</style> 