<template>
  <div class="main-layout">
    <el-container class="h-full">
      <!-- 顶部工具栏 -->
      <el-header class="main-header">
        <div class="flex items-center justify-between h-full px-6">
          <!-- 左侧：系统标题和公司产品信息 -->
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold text-gray-800">供应商查找系统</h1>
            <el-divider direction="vertical" />
            <div class="flex items-center space-x-2">
              <el-tag type="info" size="large">{{ userStore.companyName }}</el-tag>
              <span class="text-gray-400">-</span>
              <!-- 产品切换下拉框 -->
              <el-select
                v-model="currentProductId"
                placeholder="选择产品"
                size="large"
                style="width: 200px"
                @change="handleProductChange"
              >
                <el-option
                  v-for="product in userStore.products"
                  :key="product.id"
                  :label="product.name"
                  :value="product.id"
                />
              </el-select>
            </div>
          </div>

          <!-- 右侧：用户信息和操作 -->
          <div class="flex items-center space-x-4">
            <el-dropdown @command="handleUserCommand">
              <span class="flex items-center space-x-2 cursor-pointer hover:text-primary-600">
                <el-avatar :size="32" :src="userAvatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="text-gray-700">{{ userStore.userName }}</span>
                <el-icon class="text-gray-400"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边导航栏 -->
        <el-aside class="main-sidebar" width="250px">
          <el-menu
            :default-active="activeMenu"
            class="h-full border-none"
            @select="handleMenuSelect"
          >
            <el-menu-item index="/main">
              <el-icon><House /></el-icon>
              <span>控制台</span>
            </el-menu-item>
            
            <el-sub-menu index="supplier">
              <template #title>
                <el-icon><Shop /></el-icon>
                <span>供应商管理</span>
              </template>
              <el-menu-item index="/main/suppliers">供应商列表</el-menu-item>
              <el-menu-item index="/main/suppliers/search">供应商搜索</el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/main/categories">
              <el-icon><Menu /></el-icon>
              <span>产品分类管理</span>
            </el-menu-item>

            <el-sub-menu index="component">
              <template #title>
                <el-icon><Grid /></el-icon>
                <span>组件管理</span>
              </template>
              <el-menu-item index="/main/components">组件列表</el-menu-item>
              <el-menu-item index="/main/components/analysis">组件分析</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="task">
              <template #title>
                <el-icon><List /></el-icon>
                <span>任务管理</span>
              </template>
              <el-menu-item index="/main/tasks">任务列表</el-menu-item>
              <el-menu-item index="/main/tasks/create">创建任务</el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/main/reports">
              <el-icon><DataAnalysis /></el-icon>
              <span>报表分析</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区域 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import {
  User,
  ArrowDown,
  House,
  Shop,
  Grid,
  List,
  DataAnalysis,
  Menu
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 当前选中的产品ID
const currentProductId = ref(null)

// 用户头像（暂时使用默认）
const userAvatar = ref('')

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 初始化
onMounted(() => {
  if (userStore.currentProduct) {
    currentProductId.value = userStore.currentProduct.id
  }
})

// 处理产品切换
const handleProductChange = async (productId) => {
  if (productId === userStore.currentProduct?.id) return
  
  try {
    const result = await userStore.switchProduct(productId)
    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
      // 恢复原来的选择
      currentProductId.value = userStore.currentProduct?.id
    }
  } catch (error) {
    console.error('切换产品失败:', error)
    // 恢复原来的选择
    currentProductId.value = userStore.currentProduct?.id
  }
}

// 处理菜单选择
const handleMenuSelect = (index) => {
  if (index !== route.path) {
    router.push(index)
  }
}

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '退出确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await userStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch (error) {
        // 用户取消退出
      }
      break
  }
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
}

.main-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.main-sidebar {
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
}

.main-content {
  background: #f5f5f5;
  padding: 24px;
}

:deep(.el-menu) {
  background: transparent;
}

:deep(.el-menu-item:hover) {
  background-color: #e5e7eb;
}

:deep(.el-menu-item.is-active) {
  background-color: #dbeafe;
  color: #2563eb;
}

:deep(.el-sub-menu__title:hover) {
  background-color: #e5e7eb;
}
</style> 