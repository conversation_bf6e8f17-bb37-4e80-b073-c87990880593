<template>
  <div class="radar-chart">
    <h3 class="text-lg font-semibold mb-4">能力雷达图</h3>
    <div ref="radarChart" class="radar-container h-80"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const radarChart = ref(null)
let chartInstance = null

const initChart = async () => {
  if (!radarChart.value || !props.data) return
  
  await nextTick()
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(radarChart.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    radar: {
      indicator: [
        { name: '特征匹配度', max: 100 },
        { name: '企业基础实力', max: 100 },
        { name: '资质认证', max: 100 },
        { name: '技术创新', max: 100 }
      ],
      radius: '70%',
      axisName: {
        color: '#666',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
        }
      }
    },
    series: [{
      type: 'radar',
      data: [{
        value: [
          props.data.feature_matching || 0,
          props.data.basic_strength || 0,
          props.data.certification || 0,
          props.data.innovation || 0
        ],
        name: '供应商能力',
        areaStyle: {
          opacity: 0.3,
          color: '#409EFF'
        },
        lineStyle: {
          color: '#409EFF',
          width: 2
        },
        itemStyle: {
          color: '#409EFF'
        }
      }]
    }]
  }
  
  chartInstance.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
}

onMounted(() => {
  initChart()
})

watch(() => props.data, () => {
  initChart()
}, { deep: true })
</script>

<style scoped>
.radar-container {
  width: 100%;
  height: 320px;
}
</style>