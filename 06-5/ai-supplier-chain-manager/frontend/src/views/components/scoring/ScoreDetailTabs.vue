<template>
  <div class="score-detail-tabs">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="特征匹配" name="feature_matching">
        <div class="dimension-detail">
          <div class="score-header">
            <span class="score-value">{{ scores.feature_matching?.total_score || 0 }}分</span>
            <span class="score-label">特征匹配度</span>
          </div>
          <p class="analysis-text">{{ scores.feature_matching?.analysis || '暂无分析' }}</p>
          
          <div class="sub-scores mt-4" v-if="scores.feature_matching?.sub_scores">
            <div v-for="(score, key) in scores.feature_matching.sub_scores" :key="key" class="sub-score-item">
              <span class="sub-score-name">{{ getSubScoreName(key) }}</span>
              <div class="sub-score-bar">
                <div class="sub-score-fill" :style="{ width: score + '%' }"></div>
              </div>
              <span class="sub-score-value">{{ score }}分</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="企业实力" name="basic_strength">
        <div class="dimension-detail">
          <div class="score-header">
            <span class="score-value">{{ scores.basic_strength?.total_score || 0 }}分</span>
            <span class="score-label">企业基础实力</span>
          </div>
          <p class="analysis-text">{{ scores.basic_strength?.analysis || '暂无分析' }}</p>
          
          <div class="sub-scores mt-4" v-if="scores.basic_strength?.sub_scores">
            <div v-for="(score, key) in scores.basic_strength.sub_scores" :key="key" class="sub-score-item">
              <span class="sub-score-name">{{ getSubScoreName(key) }}</span>
              <div class="sub-score-bar">
                <div class="sub-score-fill" :style="{ width: score + '%' }"></div>
              </div>
              <span class="sub-score-value">{{ score }}分</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="资质认证" name="certification">
        <div class="dimension-detail">
          <div class="score-header">
            <span class="score-value">{{ scores.certification?.total_score || 0 }}分</span>
            <span class="score-label">资质认证</span>
          </div>
          <p class="analysis-text">{{ scores.certification?.analysis || '暂无分析' }}</p>
          
          <div class="sub-scores mt-4" v-if="scores.certification?.sub_scores">
            <div v-for="(score, key) in scores.certification.sub_scores" :key="key" class="sub-score-item">
              <span class="sub-score-name">{{ getSubScoreName(key) }}</span>
              <div class="sub-score-bar">
                <div class="sub-score-fill" :style="{ width: score + '%' }"></div>
              </div>
              <span class="sub-score-value">{{ score }}分</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="技术创新" name="innovation">
        <div class="dimension-detail">
          <div class="score-header">
            <span class="score-value">{{ scores.innovation?.total_score || 0 }}分</span>
            <span class="score-label">技术创新能力</span>
          </div>
          <p class="analysis-text">{{ scores.innovation?.analysis || '暂无分析' }}</p>
          
          <div class="sub-scores mt-4" v-if="scores.innovation?.sub_scores">
            <div v-for="(score, key) in scores.innovation.sub_scores" :key="key" class="sub-score-item">
              <span class="sub-score-name">{{ getSubScoreName(key) }}</span>
              <div class="sub-score-bar">
                <div class="sub-score-fill" :style="{ width: score + '%' }"></div>
              </div>
              <span class="sub-score-value">{{ score }}分</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  scores: {
    type: Object,
    required: true
  }
})

const activeTab = ref('feature_matching')

const getSubScoreName = (key) => {
  const nameMap = {
    'feature_business_match': '业务范围匹配',
    'feature_products_match': '产品匹配',
    'business_products_consistency': '业务一致性',
    'keyword_match_score': '关键词匹配',
    'capital_score': '注册资本',
    'staff_score': '员工规模',
    'years_score': '经营年限',
    'status_score': '企业状态',
    'location_score': '地理位置',
    'valid_count_score': '有效认证数量',
    'quality_score': '认证质量',
    'patent_count_score': '专利数量',
    'patent_quality_score': '专利质量'
  }
  return nameMap[key] || key
}
</script>

<style scoped>
.dimension-detail {
  @apply p-4;
}

.score-header {
  @apply flex items-baseline space-x-4 mb-4;
  min-height: 3rem;
}

.score-value {
  @apply text-3xl font-bold text-blue-600;
  line-height: 1.2;
  flex-shrink: 0;
}

.score-label {
  @apply text-lg text-gray-600;
  line-height: 1.4;
  margin-top: 0.25rem;
}

.analysis-text {
  @apply text-gray-700 leading-relaxed;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  border-left: 4px solid #3b82f6;
}

.sub-scores {
  @apply space-y-4;
}

.sub-score-item {
  @apply flex items-center space-x-4;
  padding: 0.5rem 0;
}

.sub-score-name {
  @apply w-36 text-sm text-gray-600 flex-shrink-0;
  font-weight: 500;
}

.sub-score-bar {
  @apply flex-1 h-3 bg-gray-200 rounded-full overflow-hidden;
  min-width: 100px;
}

.sub-score-fill {
  @apply h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all duration-500;
  border-radius: inherit;
}

.sub-score-value {
  @apply w-14 text-sm text-gray-600 text-right flex-shrink-0;
  font-weight: 600;
}
</style>