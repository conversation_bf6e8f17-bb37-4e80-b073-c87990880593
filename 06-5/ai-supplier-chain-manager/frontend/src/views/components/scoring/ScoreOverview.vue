<template>
  <div class="score-overview bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
    <div class="flex items-center justify-center">
      <!-- 圆形进度条显示总分 -->
      <div class="score-circle relative">
        <el-progress 
          type="circle" 
          :percentage="scorePercentage"
          :width="120"
          :stroke-width="8"
          :color="scoreColor"
          :show-text="false"
        />
        <div class="score-text-overlay">
          <span class="score-number">{{ finalScore }}</span>
          <span class="score-label">综合得分</span>
        </div>
      </div>
      
      <!-- 评级等级 -->
      <div class="ml-8">
        <div class="text-3xl font-bold" :style="{ color: gradeColor }">
          {{ grade }}
        </div>
        <div class="text-lg text-gray-600">{{ gradeLevel }}</div>
        <div class="text-sm text-gray-500 mt-2">
          评估时间：{{ evaluationDate }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

// 计算属性
const finalScore = computed(() => {
  return Math.round(props.result.evaluation_summary?.final_score || 0)
})

const scorePercentage = computed(() => finalScore.value)

const grade = computed(() => props.result.evaluation_summary?.grade || 'N/A')

const gradeLevel = computed(() => props.result.evaluation_summary?.level || '未知')

const evaluationDate = computed(() => {
  const date = props.result.evaluation_summary?.evaluation_date
  if (!date) return '未知'
  
  try {
    return new Date(date).toLocaleDateString('zh-CN')
  } catch {
    return '未知'
  }
})

const scoreColor = computed(() => {
  const score = finalScore.value
  if (score >= 90) return '#00C851'
  if (score >= 80) return '#33B679'
  if (score >= 70) return '#FF8A00'
  if (score >= 60) return '#FF8A00'
  if (score >= 50) return '#FF4444'
  return '#CC0000'
})

const gradeColor = computed(() => scoreColor.value)
</script>

<style scoped>
.score-circle {
  @apply relative;
  display: inline-block;
}

.score-text-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  pointer-events: none;
  width: 80px;
  height: 80px;
}

.score-number {
  font-size: 1.75rem;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.score-label {
  font-size: 0.75rem;
  color: #909399;
  line-height: 1;
  white-space: nowrap;
  text-align: center;
}
</style>