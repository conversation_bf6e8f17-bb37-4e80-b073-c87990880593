<template>
  <div class="score-recommendations">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 改进建议 -->
      <div v-if="recommendations?.length" class="recommendations-card">
        <div class="card-header">
          <el-icon class="text-green-500 text-xl"><Check /></el-icon>
          <h4 class="font-semibold text-green-600">改进建议</h4>
        </div>
        <ul class="recommendations-list">
          <li v-for="(rec, index) in recommendations" :key="index" class="recommendation-item">
            <el-icon class="text-green-500 mr-2"><ArrowRight /></el-icon>
            <span>{{ rec }}</span>
          </li>
        </ul>
      </div>
      
      <!-- 风险提示 -->
      <div v-if="warnings?.length" class="warnings-card">
        <div class="card-header">
          <el-icon class="text-orange-500 text-xl"><Warning /></el-icon>
          <h4 class="font-semibold text-orange-600">风险提示</h4>
        </div>
        <ul class="warnings-list">
          <li v-for="(warning, index) in warnings" :key="index" class="warning-item">
            <el-icon class="text-orange-500 mr-2"><InfoFilled /></el-icon>
            <span>{{ warning }}</span>
          </li>
        </ul>
      </div>
    </div>
    
    <!-- 如果没有建议和警告 -->
    <div v-if="!recommendations?.length && !warnings?.length" class="no-recommendations">
      <el-empty description="暂无改进建议和风险提示" :image-size="80" />
    </div>
  </div>
</template>

<script setup>
import { Check, Warning, ArrowRight, InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  recommendations: {
    type: Array,
    default: () => []
  },
  warnings: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
.score-recommendations {
  @apply bg-gray-50 p-6 rounded-lg;
}

.recommendations-card,
.warnings-card {
  @apply bg-white p-4 rounded-lg shadow-sm;
}

.card-header {
  @apply flex items-center space-x-2 mb-4;
}

.recommendations-list,
.warnings-list {
  @apply space-y-3;
}

.recommendation-item,
.warning-item {
  @apply flex items-start text-sm text-gray-700 leading-relaxed;
}

.no-recommendations {
  @apply py-8;
}
</style>