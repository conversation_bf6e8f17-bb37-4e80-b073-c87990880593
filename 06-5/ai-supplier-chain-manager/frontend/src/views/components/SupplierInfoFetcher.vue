<template>
  <div class="supplier-info-fetcher">
    <!-- 单个获取按钮 -->
    <div v-if="mode === 'single'" class="single-fetch">
      <el-button 
        :type="getButtonType()"
        :loading="fetchStatus.status === 'fetching'"
        :disabled="fetchStatus.status === 'fetching'"
        @click="handleFetchInfo"
        size="small"
      >
        <el-icon v-if="!fetchStatus.status || fetchStatus.status === 'idle'">
          <Download />
        </el-icon>
        <el-icon v-else-if="fetchStatus.status === 'success'">
          <Check />
        </el-icon>
        <el-icon v-else-if="fetchStatus.status === 'error'">
          <Close />
        </el-icon>
        {{ getButtonText() }}
      </el-button>
      
      <!-- 进度显示 -->
      <div v-if="fetchStatus.status === 'fetching'" class="mt-2">
        <el-progress 
          :percentage="fetchStatus.progress || 0" 
          :status="fetchStatus.status === 'error' ? 'exception' : ''"
          size="small"
        />
        <p class="text-xs text-gray-500 mt-1">{{ fetchStatus.message }}</p>
      </div>
      
      <!-- 结果显示 -->
      <el-alert 
        v-if="showResult && (fetchStatus.status === 'success' || fetchStatus.status === 'error')"
        :type="fetchStatus.status === 'success' ? 'success' : 'error'"
        :title="fetchStatus.message"
        :closable="true"
        @close="clearStatus"
        class="mt-2"
        size="small"
      />
    </div>
    
    <!-- 批量获取 -->
    <div v-else-if="mode === 'batch'" class="batch-fetch">
      <el-button 
        type="primary"
        :loading="isBatchFetching"
        :disabled="!selectedSuppliers.length || isBatchFetching"
        @click="handleBatchFetch"
      >
        <el-icon><Download /></el-icon>
        批量获取信息 ({{ selectedSuppliers.length }})
      </el-button>
      
      <!-- 批量进度 -->
      <div v-if="isBatchFetching" class="mt-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium">批量获取进度</span>
          <span class="text-sm text-gray-500">{{ batchProgress.completed }}/{{ batchProgress.total }}</span>
        </div>
        <el-progress 
          :percentage="batchProgressPercentage" 
          :status="batchProgress.hasError ? 'exception' : ''"
        />
        <p class="text-xs text-gray-500 mt-1">{{ batchProgress.message }}</p>
      </div>
      
      <!-- 批量结果 -->
      <div v-if="batchResult" class="mt-4">
        <el-alert 
          :type="batchResult.success_count === batchResult.total_count ? 'success' : 'warning'"
          :title="`批量获取完成: 成功 ${batchResult.success_count}/${batchResult.total_count}`"
          :closable="true"
          @close="clearBatchResult"
        />
        
        <!-- 详细结果 -->
        <el-collapse v-if="batchResult.results.length" class="mt-2">
          <el-collapse-item title="查看详细结果" name="details">
            <div class="space-y-2">
              <div 
                v-for="result in batchResult.results" 
                :key="result.supplier_id"
                class="flex items-center justify-between p-2 bg-gray-50 rounded"
              >
                <span class="text-sm">{{ result.supplier_name || `供应商 ${result.supplier_id}` }}</span>
                <el-tag 
                  :type="result.success ? 'success' : 'danger'"
                  size="small"
                >
                  {{ result.success ? '成功' : '失败' }}
                </el-tag>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Check, Close } from '@element-plus/icons-vue'
import { useSupplierStore } from '@/stores/supplier'

const props = defineProps({
  mode: {
    type: String,
    default: 'single', // 'single' | 'batch'
    validator: (value) => ['single', 'batch'].includes(value)
  },
  supplierId: {
    type: Number,
    default: null
  },
  selectedSuppliers: {
    type: Array,
    default: () => []
  },
  showResult: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['fetch-complete', 'batch-complete'])

const supplierStore = useSupplierStore()

// 单个获取状态
const fetchStatus = ref({
  status: 'idle', // 'idle' | 'fetching' | 'success' | 'error'
  message: '',
  progress: 0
})

// 批量获取状态
const isBatchFetching = ref(false)
const batchProgress = ref({
  completed: 0,
  total: 0,
  hasError: false,
  message: ''
})
const batchResult = ref(null)

// 计算属性
const batchProgressPercentage = computed(() => {
  if (batchProgress.value.total === 0) return 0
  return Math.round((batchProgress.value.completed / batchProgress.value.total) * 100)
})

// 监听供应商ID变化，更新获取状态
watch(() => props.supplierId, (newId) => {
  if (newId && props.mode === 'single') {
    const status = supplierStore.getFetchingStatus(newId)
    fetchStatus.value = { ...status }
  }
}, { immediate: true })

// 监听store中的获取状态变化
watch(() => supplierStore.fetchingStatus, (newStatus) => {
  if (props.supplierId && props.mode === 'single') {
    const status = supplierStore.getFetchingStatus(props.supplierId)
    fetchStatus.value = { ...status }
  }
}, { deep: true })

// 方法
const getButtonType = () => {
  switch (fetchStatus.value.status) {
    case 'success':
      return 'success'
    case 'error':
      return 'danger'
    case 'fetching':
      return 'primary'
    default:
      return 'default'
  }
}

const getButtonText = () => {
  switch (fetchStatus.value.status) {
    case 'fetching':
      return '获取中...'
    case 'success':
      return '获取成功'
    case 'error':
      return '获取失败'
    default:
      return '获取企业信息'
  }
}

const handleFetchInfo = async () => {
  if (!props.supplierId) {
    ElMessage.error('供应商ID不能为空')
    return
  }

  try {
    const result = await supplierStore.fetchSupplierInfo(props.supplierId)
    
    if (result.success) {
      ElMessage.success('企业信息获取成功')
      emit('fetch-complete', result.data)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('获取企业信息失败:', error)
    ElMessage.error('获取企业信息失败')
  }
}

const handleBatchFetch = async () => {
  if (!props.selectedSuppliers.length) {
    ElMessage.error('请选择要获取信息的供应商')
    return
  }

  isBatchFetching.value = true
  batchProgress.value = {
    completed: 0,
    total: props.selectedSuppliers.length,
    hasError: false,
    message: '开始批量获取...'
  }

  try {
    const result = await supplierStore.batchFetchSupplierInfo(props.selectedSuppliers)
    
    batchProgress.value.completed = batchProgress.value.total
    batchProgress.value.message = '批量获取完成'
    
    if (result.success) {
      batchResult.value = result.data
      ElMessage.success(`批量获取完成: 成功 ${result.data.success_count}/${result.data.total_count}`)
      emit('batch-complete', result.data)
    } else {
      batchProgress.value.hasError = true
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('批量获取失败:', error)
    batchProgress.value.hasError = true
    batchProgress.value.message = '批量获取失败'
    ElMessage.error('批量获取失败')
  } finally {
    isBatchFetching.value = false
  }
}

const clearStatus = () => {
  if (props.supplierId) {
    supplierStore.clearFetchingStatus(props.supplierId)
  }
  fetchStatus.value = {
    status: 'idle',
    message: '',
    progress: 0
  }
}

const clearBatchResult = () => {
  batchResult.value = null
  batchProgress.value = {
    completed: 0,
    total: 0,
    hasError: false,
    message: ''
  }
}
</script>

<style scoped>
.supplier-info-fetcher {
  @apply w-full;
}

.single-fetch {
  @apply space-y-2;
}

.batch-fetch {
  @apply space-y-4;
}
</style> 