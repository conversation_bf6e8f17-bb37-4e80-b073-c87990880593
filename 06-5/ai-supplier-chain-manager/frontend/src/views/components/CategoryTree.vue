<template>
  <div class="category-tree">
    <div v-if="!categories?.primary_categories?.length" class="no-categories">
      <el-empty description="该产品暂无分类数据" />
    </div>

    <div v-else class="categories-container">
      <!-- 产品信息 -->
      <div v-if="product" class="product-header">
        <h3>{{ product.name }}</h3>
        <div class="product-meta">
          <el-tag v-if="product.model" size="small">型号：{{ product.model }}</el-tag>
          <el-tag v-if="product.category" size="small" type="success">类别：{{ product.category }}</el-tag>
          <el-tag v-if="product.lifecycle_stage" size="small" type="warning">阶段：{{ product.lifecycle_stage }}</el-tag>
        </div>
      </div>

      <!-- 分类树 -->
      <div class="tree-container">
        <div 
          v-for="primaryCategory in categories.primary_categories" 
          :key="primaryCategory.id"
          class="primary-category"
        >
          <!-- 一级分类 -->
          <CategoryCard
            :category="primaryCategory"
            :level="1"
            @click="handleCategoryClick"
          />

          <!-- 二级分类 -->
          <div 
            v-if="primaryCategory.secondary_categories?.length" 
            class="secondary-categories"
          >
            <div 
              v-for="secondaryCategory in primaryCategory.secondary_categories"
              :key="secondaryCategory.id"
              class="secondary-category"
            >
              <CategoryCard
                :category="secondaryCategory"
                :level="2"
                @click="handleCategoryClick"
              />
            </div>
          </div>

          <!-- 无二级分类提示 -->
          <div v-else class="no-secondary">
            <el-text type="info" size="small">该一级分类下暂无二级分类</el-text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import CategoryCard from './CategoryCard.vue'

const props = defineProps({
  categories: {
    type: Object,
    default: null
  },
  product: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['category-click'])

const handleCategoryClick = (categoryId) => {
  emit('category-click', categoryId)
}
</script>

<style scoped>
.category-tree {
  width: 100%;
}

.no-categories {
  padding: 40px;
  text-align: center;
}

.product-header {
  margin-bottom: 20px;
  padding: 15px;
  background: #F8F9FA;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.product-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.product-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tree-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.primary-category {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  overflow: hidden;
  background: #FFFFFF;
}

.secondary-categories {
  padding: 15px;
  background: #FAFAFA;
  border-top: 1px solid #E4E7ED;
}

.secondary-category {
  margin-bottom: 10px;
}

.secondary-category:last-child {
  margin-bottom: 0;
}

.no-secondary {
  padding: 15px;
  text-align: center;
  background: #FAFAFA;
  border-top: 1px solid #E4E7ED;
}
</style> 