<template>
  <el-card>
    <template #header>
      <div class="flex items-center justify-between">
        <span class="text-lg font-semibold">智能评分系统</span>
        <div class="flex items-center space-x-2">
          <el-button 
            type="primary" 
            :loading="isScoring"
            @click="startScoring"
            :disabled="!canScore"
          >
            {{ isScoring ? '评分中...' : (scoringResult ? '重新评分' : '开始评分') }}
          </el-button>
        </div>
      </div>
    </template>

    <!-- 评分进度 -->
    <div v-if="isScoring" class="scoring-progress mb-6">
      <el-progress 
        :percentage="scoringProgress" 
        :status="scoringStatus"
        :stroke-width="8"
      />
      <p class="text-sm text-gray-600 mt-2">{{ scoringMessage }}</p>
      
      <!-- 降级提示 -->
      <el-alert
        v-if="scoringState.usedFallback"
        type="warning"
        title="使用了降级算法"
        description="由于GPT服务异常，系统使用关键词匹配算法完成评分，结果可能不如智能分析精确"
        :closable="false"
        show-icon
        class="mt-3"
      />
    </div>

    <!-- 评分结果 -->
    <div v-if="scoringResult && !isScoring" class="scoring-result">
      <!-- 总分展示 -->
      <ScoreOverview :result="scoringResult" class="mb-6" />
      
      <!-- 雷达图 -->
      <ScoreRadarChart :data="radarData" class="mb-6" />
      
      <!-- 详细评分 -->
      <ScoreDetailTabs :scores="scoringResult.dimension_scores" class="mb-6" />
      
      <!-- 建议和警告 -->
      <ScoreRecommendations 
        :recommendations="scoringResult.recommendations || []"
        :warnings="scoringResult.risk_warnings || []"
      />
    </div>

    <!-- 无评分结果时的提示 -->
    <div v-else-if="!isScoring" class="no-score-state">
      <el-empty description="点击上方按钮开始智能评分">
        <template #image>
          <el-icon size="60" color="#909399">
            <TrendCharts />
          </el-icon>
        </template>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts } from '@element-plus/icons-vue'
import { useSupplierStore } from '@/stores/supplier'
import ScoreOverview from './scoring/ScoreOverview.vue'
import ScoreRadarChart from './scoring/ScoreRadarChart.vue'
import ScoreDetailTabs from './scoring/ScoreDetailTabs.vue'
import ScoreRecommendations from './scoring/ScoreRecommendations.vue'

const props = defineProps({
  supplierId: {
    type: Number,
    required: true
  },
  supplierName: {
    type: String,
    default: ''
  },
  categoryFeature: {
    type: String,
    default: ''
  },
  autoScore: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['scoring-complete'])

const supplierStore = useSupplierStore()

// 响应式数据
const isScoring = ref(false)
const scoringResult = ref(null)
const scoringState = ref({
  status: 'idle',
  progress: 0,
  message: '',
  usedFallback: false
})

// 计算属性
const canScore = computed(() => {
  return props.supplierId && !isScoring.value
})

const scoringProgress = computed(() => scoringState.value.progress)
const scoringStatus = computed(() => {
  switch (scoringState.value.status) {
    case 'success':
    case 'fallback':
      return 'success'
    case 'error':
      return 'exception'
    default:
      return ''
  }
})
const scoringMessage = computed(() => scoringState.value.message)

const usedFallback = computed(() => {
  if (!scoringResult.value) return false
  const analysis = scoringResult.value.dimension_scores?.feature_matching?.analysis || ''
  return analysis.includes('降级方案') || analysis.includes('关键词匹配')
})

const radarData = computed(() => {
  if (!scoringResult.value) return null
  
  const scores = scoringResult.value.dimension_scores
  return {
    feature_matching: scores.feature_matching?.total_score || 0,
    basic_strength: scores.basic_strength?.total_score || 0,
    certification: scores.certification?.total_score || 0,
    innovation: scores.innovation?.total_score || 0
  }
})

// 页面加载时只查询已有评分结果，不进行计算
onMounted(async () => {
  if (props.supplierId) {
    await loadExistingScore()
  }
})

// 加载已有的评分结果（仅查询数据库）
const loadExistingScore = async () => {
  try {
    // 调用GET接口，只查询数据库，不进行计算
    const result = await supplierStore.getSupplierScore(props.supplierId)
    
    if (result.success && result.data && result.has_scores) {
      scoringResult.value = result.data
      // 静默加载，不显示消息提示
    }
  } catch (error) {
    console.error('加载评分结果失败:', error)
    // 静默失败，不显示错误消息
  }
}

// 开始评分方法（触发实际计算）
const startScoring = async () => {
  if (!canScore.value) return
  
  isScoring.value = true
  scoringState.value = {
    status: 'scoring',
    progress: 10,
    message: '正在开始评分计算...',
    usedFallback: false
  }
  
  try {
    // 模拟进度
    const progressSteps = [
      { progress: 30, message: '正在获取供应商信息...' },
      { progress: 60, message: '正在进行智能分析...' },
      { progress: 90, message: '正在生成评分报告...' }
    ]
    
    for (const step of progressSteps) {
      scoringState.value.progress = step.progress
      scoringState.value.message = step.message
      await new Promise(resolve => setTimeout(resolve, 300))
    }
    
    // 调用POST接口进行实际的评分计算
    const result = await supplierStore.calculateSupplierScore(props.supplierId)
    
    if (result.success) {
      scoringResult.value = result.data
      
      // 检查是否使用了降级方案
      const fallbackUsed = usedFallback.value
      
      scoringState.value = {
        status: fallbackUsed ? 'fallback' : 'success',
        progress: 100,
        message: '评分计算完成',
        usedFallback: fallbackUsed
      }
      
      ElMessage.success(result.message || '评分完成')
      emit('scoring-complete', scoringResult.value)
    } else {
      scoringState.value = {
        status: 'error',
        progress: 0,
        message: result.message || '评分失败',
        usedFallback: false
      }
      ElMessage.error(result.message || '评分失败')
    }
  } catch (error) {
    console.error('评分过程出错:', error)
    scoringState.value = {
      status: 'error',
      progress: 0,
      message: '评分过程中发生错误',
      usedFallback: false
    }
    ElMessage.error('评分过程中发生错误')
  } finally {
    isScoring.value = false
  }
}

// 格式化日期方法
const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return dateString
  }
}
</script>

<style scoped>
.cache-notice {
  border-radius: 6px;
}
</style>