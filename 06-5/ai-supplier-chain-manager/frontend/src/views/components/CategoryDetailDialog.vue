<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="category-detail">
      <div v-if="categoryData" class="detail-content">
        <!-- 基本信息 -->
        <div class="basic-info">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="分类名称">
              <el-tag :type="categoryData.level === 1 ? 'primary' : 'success'">
                {{ categoryData.name }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="分类级别">
              {{ categoryData.level === 1 ? '一级分类' : '二级分类' }}
            </el-descriptions-item>
            <el-descriptions-item label="所属产品">
              {{ categoryData.product_name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="分类ID">
              {{ categoryData.id }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 特征描述 -->
        <div class="feature-section">
          <h3>特征描述</h3>
          <div v-if="categoryData.feature" class="feature-content">
            <p>{{ categoryData.feature }}</p>
          </div>
          <div v-else class="no-feature">
            <el-empty description="暂无特征描述" :image-size="80" />
          </div>
        </div>

        <!-- 父分类信息（二级分类） -->
        <div v-if="categoryData.parent_category" class="parent-section">
          <h3>父分类信息</h3>
          <el-card class="parent-card" shadow="hover">
            <div class="parent-info">
              <el-icon class="parent-icon"><Folder /></el-icon>
              <div class="parent-details">
                <div class="parent-name">{{ categoryData.parent_category.name }}</div>
                <div class="parent-label">一级分类</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 子分类信息（一级分类） -->
        <div v-if="categoryData.child_categories?.length" class="children-section">
          <h3>子分类列表 ({{ categoryData.child_categories.length }})</h3>
          <div class="children-grid">
            <el-card 
              v-for="child in categoryData.child_categories"
              :key="child.id"
              class="child-card"
              shadow="hover"
            >
              <div class="child-info">
                <el-icon class="child-icon"><Document /></el-icon>
                <div class="child-details">
                  <div class="child-name">{{ child.name }}</div>
                  <div class="child-feature" v-if="child.feature">
                    {{ child.feature.substring(0, 50) }}{{ child.feature.length > 50 ? '...' : '' }}
                  </div>
                  <div class="child-feature no-feature-text" v-else>暂无特征描述</div>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 无子分类提示（一级分类） -->
        <div v-else-if="categoryData.level === 1" class="no-children">
          <h3>子分类列表</h3>
          <el-empty description="该一级分类下暂无二级分类" :image-size="80" />
        </div>

        <!-- 物料列表 -->
        <div class="components-section">
          <h3>物料列表 ({{ categoryData.components?.length || 0 }})</h3>
          <div v-if="categoryData.components?.length" class="components-grid">
            <el-card 
              v-for="component in categoryData.components"
              :key="component.id"
              class="component-card"
              shadow="hover"
              @click="handleComponentClick(component.id)"
            >
              <div class="component-info">
                <el-icon class="component-icon"><Box /></el-icon>
                <div class="component-details">
                  <div class="component-name">{{ component.name }}</div>
                  <div class="component-spec" v-if="component.spec">
                    规格：{{ component.spec.substring(0, 50) }}{{ component.spec.length > 50 ? '...' : '' }}
                  </div>
                  <div class="component-meta">
                    <el-tag v-if="component.material" size="small" type="info">{{ component.material }}</el-tag>
                    <el-tag v-if="component.price" size="small" type="warning">¥{{ component.price }}</el-tag>
                    <el-tag v-if="component.component_code" size="small">{{ component.component_code }}</el-tag>
                  </div>
                </div>
                <el-icon class="arrow-icon"><ArrowRight /></el-icon>
              </div>
            </el-card>
          </div>
          <div v-else class="no-components">
            <el-empty description="该分类下暂无物料数据" :image-size="80" />
          </div>
        </div>
      </div>

      <div v-else-if="!loading" class="error-content">
        <el-result
          icon="error"
          title="获取分类详情失败"
          sub-title="请稍后重试或联系管理员"
        >
          <template #extra>
            <el-button type="primary" @click="fetchCategoryData">重新加载</el-button>
          </template>
        </el-result>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="fetchCategoryData" :loading="loading">
          刷新数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Folder, Document, Box, ArrowRight } from '@element-plus/icons-vue'
import { useSupplierStore } from '@/stores/supplier'
import { useRouter } from 'vue-router'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  categoryId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const supplierStore = useSupplierStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const categoryData = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  if (!categoryData.value) return '分类详情'
  return `${categoryData.value.name} - 分类详情`
})

// 监听分类ID变化
watch(() => props.categoryId, (newId) => {
  if (newId && props.modelValue) {
    fetchCategoryData()
  }
}, { immediate: true })

// 监听对话框显示状态
watch(() => props.modelValue, (visible) => {
  if (visible && props.categoryId) {
    fetchCategoryData()
  } else if (!visible) {
    categoryData.value = null
  }
})

// 方法
const fetchCategoryData = async () => {
  if (!props.categoryId) return

  loading.value = true
  try {
    const result = await supplierStore.fetchCategoryDetail(props.categoryId)
    if (result.success) {
      categoryData.value = result.data
    } else {
      ElMessage.error(result.message)
      categoryData.value = null
    }
  } catch (error) {
    ElMessage.error('获取分类详情失败')
    categoryData.value = null
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  emit('refresh')
}

const handleComponentClick = (componentId) => {
  // 关闭对话框
  dialogVisible.value = false
  // 跳转到物料详情页
  router.push(`/main/components/${componentId}`)
}
</script>

<style scoped>
.category-detail {
  min-height: 200px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.basic-info h3,
.feature-section h3,
.parent-section h3,
.children-section h3,
.no-children h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 2px solid #E4E7ED;
  padding-bottom: 8px;
}

.feature-content {
  background: #F8F9FA;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.feature-content p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.no-feature {
  text-align: center;
  padding: 20px;
}

.parent-card {
  border: 1px solid #E4E7ED;
}

.parent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.parent-icon {
  font-size: 24px;
  color: #409EFF;
}

.parent-details {
  flex: 1;
}

.parent-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.parent-label {
  font-size: 12px;
  color: #909399;
}

.children-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.child-card {
  border: 1px solid #E4E7ED;
  transition: all 0.3s;
}

.child-card:hover {
  border-color: #67C23A;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
}

.child-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.child-icon {
  font-size: 20px;
  color: #67C23A;
  margin-top: 2px;
}

.child-details {
  flex: 1;
}

.child-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
}

.child-feature {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.child-feature.no-feature-text {
  color: #C0C4CC;
  font-style: italic;
}

.no-children {
  text-align: center;
}

.error-content {
  text-align: center;
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.components-section {
  margin-top: 20px;
}

.components-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.component-card {
  border: 1px solid #E4E7ED;
  transition: all 0.3s;
}

.component-card:hover {
  border-color: #67C23A;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
}

.component-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.component-details {
  flex: 1;
}

.component-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
}

.component-spec {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.component-meta {
  margin-top: 6px;
}

.component-meta .el-tag {
  margin-right: 5px;
}

.arrow-icon {
  font-size: 16px;
  color: #909399;
  cursor: pointer;
}

.no-components {
  text-align: center;
}
</style> 