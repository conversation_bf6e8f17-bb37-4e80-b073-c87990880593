<template>
  <div 
    class="category-card"
    :class="{ 
      'primary-level': level === 1, 
      'secondary-level': level === 2,
      'clickable': true
    }"
    @click="handleClick"
  >
    <div class="category-header">
      <div class="category-title">
        <el-icon class="level-icon">
          <Folder v-if="level === 1" />
          <Document v-else />
        </el-icon>
        <span class="category-name">{{ category.name }}</span>
        <el-tag 
          :type="level === 1 ? 'primary' : 'success'" 
          size="small"
          class="level-tag"
        >
          {{ level === 1 ? '一级分类' : '二级分类' }}
        </el-tag>
      </div>
      
      <el-button 
        type="text" 
        size="small" 
        @click.stop="handleDetailClick"
        class="detail-btn"
      >
        查看详情
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>

    <div v-if="category.feature" class="category-feature">
      <div class="feature-label">特征描述：</div>
      <div class="feature-content">{{ category.feature }}</div>
    </div>

    <div v-else class="no-feature">
      <el-text type="info" size="small">暂无特征描述</el-text>
    </div>

    <!-- 二级分类数量（仅一级分类显示） -->
    <div v-if="level === 1 && category.secondary_categories" class="secondary-count">
      <el-icon><Files /></el-icon>
      <span>包含 {{ category.secondary_categories.length }} 个二级分类</span>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Folder, Document, ArrowRight, Files } from '@element-plus/icons-vue'

const props = defineProps({
  category: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    required: true,
    validator: (value) => [1, 2].includes(value)
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click', props.category.id)
}

const handleDetailClick = () => {
  emit('click', props.category.id)
}
</script>

<style scoped>
.category-card {
  padding: 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.category-card.clickable {
  cursor: pointer;
}

.category-card.primary-level {
  background: #FFFFFF;
  border: 1px solid #E4E7ED;
}

.category-card.secondary-level {
  background: #F8F9FA;
  border: 1px solid #EBEEF5;
}

.category-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.category-card.primary-level:hover {
  border-color: #409EFF;
}

.category-card.secondary-level:hover {
  border-color: #67C23A;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.level-icon {
  font-size: 18px;
  color: #409EFF;
}

.category-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.level-tag {
  margin-left: auto;
  margin-right: 8px;
}

.detail-btn {
  color: #606266;
  padding: 4px 8px;
}

.detail-btn:hover {
  color: #409EFF;
}

.category-feature {
  margin-bottom: 12px;
}

.feature-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.feature-content {
  color: #606266;
  line-height: 1.5;
  font-size: 14px;
  background: #F5F7FA;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
}

.no-feature {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #FAFAFA;
  border-radius: 4px;
  text-align: center;
}

.secondary-count {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #EBEEF5;
}

.secondary-count .el-icon {
  font-size: 14px;
}

/* 一级分类特殊样式 */
.primary-level .category-name {
  font-size: 18px;
  color: #409EFF;
}

.primary-level .level-icon {
  color: #409EFF;
  font-size: 20px;
}

/* 二级分类特殊样式 */
.secondary-level .category-name {
  font-size: 15px;
  color: #67C23A;
}

.secondary-level .level-icon {
  color: #67C23A;
  font-size: 16px;
}

.secondary-level .feature-content {
  border-left-color: #67C23A;
}
</style> 