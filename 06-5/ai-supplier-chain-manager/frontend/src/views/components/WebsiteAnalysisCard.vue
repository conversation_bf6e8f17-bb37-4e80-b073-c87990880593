<template>
  <el-card class="website-analysis-card">
    <template #header>
      <div class="card-header">
        <span>网站分析结果</span>
        <el-button 
          type="primary" 
          size="small" 
          @click="analyzeWebsite"
          :loading="analyzing"
          :disabled="!supplier.website"
        >
          {{ analyzing ? '分析中...' : '重新分析' }}
        </el-button>
      </div>
    </template>

    <div v-if="!analysisData && !analyzing" class="no-data">
      <el-empty description="暂无网站分析数据">
        <el-button 
          type="primary" 
          @click="analyzeWebsite"
          :disabled="!supplier.website"
        >
          开始分析
        </el-button>
      </el-empty>
    </div>

    <div v-if="analyzing" class="analyzing">
      <el-skeleton :rows="5" animated />
      <div class="analyzing-text">
        <el-icon class="is-loading"><Loading /></el-icon>
        正在分析网站内容，请稍候...
      </div>
    </div>

    <div v-if="analysisData && !analyzing" class="analysis-content">
      <!-- 网站分析结果 - Markdown渲染 -->
      <div class="section">
        <h4>网站分析结果</h4>
        <div 
          class="markdown-content"
          v-html="renderedMarkdown"
        ></div>
      </div>

      <!-- 分析时间 -->
      <div class="analysis-meta">
        <el-text type="info" size="small">
          分析时间：{{ formatDate(analysisData.analyzed_at) }}
        </el-text>
      </div>
    </div>

    <div v-if="error" class="error-message">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useSupplierStore } from '@/stores/supplier'
import { marked } from 'marked'
import hljs from 'highlight.js'

// 配置marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {}
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

const props = defineProps({
  supplier: {
    type: Object,
    required: true
  },
  rating: {
    type: Object,
    default: null
  }
})

const supplierStore = useSupplierStore()

const analyzing = ref(false)
const error = ref('')

// 从rating中提取网站分析数据
const analysisData = computed(() => {
  if (!props.rating) return null
  
  try {
    let analysisText = ''
    
    // 直接从main_products字段获取原始的GPT分析文本
    if (props.rating.main_products) {
      // main_products现在是longtext类型，直接包含GPT的原始分析文本
      analysisText = props.rating.main_products
    }
    
    // 如果有专利信息，附加到文本后面
    if (props.rating.patents) {
      try {
        const patents = typeof props.rating.patents === 'string' 
          ? JSON.parse(props.rating.patents) 
          : props.rating.patents
        
        if (patents && patents.length > 0) {
          const validPatents = patents.filter(patent => 
            patent && patent.name && patent.name !== 'NA'
          )
          
          if (validPatents.length > 0) {
            if (!analysisText) {
              analysisText = '## 专利信息\n\n'
            } else {
              analysisText += '\n\n## 补充专利信息\n\n'
            }
            validPatents.forEach((patent, index) => {
              analysisText += `### ${index + 1}. ${patent.name}\n`
              if (patent.type && patent.type !== 'NA') {
                analysisText += `- **专利类型**：${patent.type}\n`
              }
              if (patent.number && patent.number !== 'NA') {
                analysisText += `- **专利号**：${patent.number}\n`
              }
              if (patent.status && patent.status !== 'NA') {
                analysisText += `- **状态**：${patent.status}\n`
              }
              analysisText += '\n'
            })
          }
        }
      } catch (e) {
        console.error('解析patents失败:', e)
      }
    }
    
    // 如果没有任何分析文本，返回null
    if (!analysisText.trim()) {
      return null
    }
    
    return {
      analysis_text: analysisText,
      analyzed_at: props.rating.evaluated_at
    }
    
  } catch (e) {
    console.error('解析网站分析数据失败:', e)
    return null
  }
})

// 渲染Markdown内容
const renderedMarkdown = computed(() => {
  if (!analysisData.value || !analysisData.value.analysis_text) {
    return ''
  }
  
  try {
    return marked.parse(analysisData.value.analysis_text)
  } catch (e) {
    console.error('Markdown渲染失败:', e)
    // 如果markdown渲染失败，返回纯文本
    return `<pre>${analysisData.value.analysis_text}</pre>`
  }
})

const analyzeWebsite = async () => {
  if (!props.supplier.website) {
    ElMessage.warning('该供应商没有网站信息')
    return
  }

  analyzing.value = true
  error.value = ''

  try {
    const result = await supplierStore.analyzeWebsite(props.supplier.id, {
      website_url: props.supplier.website
    })

    if (result.success) {
      ElMessage.success('网站分析完成')
      // 触发父组件刷新评级数据
      emit('analysis-completed', result.data.analysis_result)
    } else {
      error.value = result.message || '分析失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    error.value = '网站分析失败：' + (err.message || '未知错误')
    ElMessage.error(error.value)
  } finally {
    analyzing.value = false
  }
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  try {
    return new Date(dateStr).toLocaleString('zh-CN')
  } catch (e) {
    return dateStr
  }
}

const emit = defineEmits(['analysis-completed'])

onMounted(() => {
  // 组件挂载时可以检查是否需要自动分析
})
</script>

<style scoped>
.website-analysis-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-data {
  text-align: center;
  padding: 40px 20px;
}

.analyzing {
  padding: 20px;
}

.analyzing-text {
  text-align: center;
  margin-top: 20px;
  color: #666;
}

.analysis-content {
  padding: 10px 0;
}

.section {
  margin-bottom: 30px;
}

.section h4 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}

.analysis-meta {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.error-message {
  margin-top: 20px;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.8;
  color: #606266;
  background: #f5f7fa;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: 14px;
  max-height: 600px;
  overflow-y: auto;
}

/* Markdown标题样式 */
.markdown-content :deep(h1) {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin: 20px 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.markdown-content :deep(h2) {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
  margin: 18px 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.markdown-content :deep(h3) {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  margin: 15px 0 10px 0;
}

.markdown-content :deep(h4) {
  font-size: 14px;
  font-weight: bold;
  color: #909399;
  margin: 12px 0 8px 0;
}

/* Markdown列表样式 */
.markdown-content :deep(ul) {
  margin: 10px 0;
  padding-left: 20px;
}

.markdown-content :deep(ol) {
  margin: 10px 0;
  padding-left: 20px;
}

.markdown-content :deep(li) {
  margin: 5px 0;
  line-height: 1.6;
}

/* Markdown段落样式 */
.markdown-content :deep(p) {
  margin: 10px 0;
  line-height: 1.8;
}

/* Markdown强调样式 */
.markdown-content :deep(strong) {
  font-weight: bold;
  color: #303133;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #606266;
}

/* Markdown代码样式 */
.markdown-content :deep(code) {
  background: #f1f2f3;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  color: #e6a23c;
}

.markdown-content :deep(pre) {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 15px 0;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  line-height: 1.5;
}

.markdown-content :deep(pre code) {
  background: transparent;
  padding: 0;
  color: inherit;
}

/* Markdown表格样式 */
.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #e4e7ed;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content :deep(th) {
  background: #f5f7fa;
  font-weight: bold;
  color: #303133;
}

/* Markdown引用样式 */
.markdown-content :deep(blockquote) {
  border-left: 4px solid #409eff;
  padding: 10px 15px;
  margin: 15px 0;
  background: #f0f9ff;
  color: #606266;
  font-style: italic;
}

/* Markdown分割线样式 */
.markdown-content :deep(hr) {
  border: none;
  border-top: 1px solid #e4e7ed;
  margin: 20px 0;
}

/* Markdown链接样式 */
.markdown-content :deep(a) {
  color: #409eff;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}
</style>

<style>
/* 导入highlight.js样式 */
@import 'highlight.js/styles/github.css';
</style> 