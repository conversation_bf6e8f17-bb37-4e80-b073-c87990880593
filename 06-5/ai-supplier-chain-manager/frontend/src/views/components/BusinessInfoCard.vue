<template>
  <el-card class="business-info-card">
    <template #header>
      <div class="card-header flex items-center justify-between">
        <span class="text-lg font-semibold">企业基本信息</span>
        <div class="flex items-center space-x-2">
          <el-tag :type="getStatusType()" size="small">
            {{ getStatusText() }}
          </el-tag>
          <el-button 
            v-if="showRefreshButton"
            type="text" 
            size="small"
            @click="$emit('refresh')"
            :loading="isRefreshing"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    
    <!-- 无数据状态 -->
    <div v-if="!businessInfo" class="no-data">
      <el-empty 
        description="暂无企业信息"
        :image-size="80"
      >
        <template #description>
          <p class="text-gray-500">点击"获取企业信息"按钮从天眼查获取数据</p>
        </template>
      </el-empty>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="businessInfo.error" class="error-state">
      <el-alert 
        type="error"
        :title="businessInfo.error"
        :closable="false"
        show-icon
      />
    </div>
    
    <!-- 企业信息展示 -->
    <div v-else class="business-info-content">
      <el-descriptions :column="2" border size="small">
        <el-descriptions-item label="企业名称" :span="2">
          <span class="font-medium">{{ businessInfo.company_name || '-' }}</span>
        </el-descriptions-item>
        
        <el-descriptions-item label="统一社会信用代码">
          <span class="font-mono text-sm">{{ businessInfo.unified_credit_code || '-' }}</span>
        </el-descriptions-item>
        
        <el-descriptions-item label="法定代表人">
          {{ businessInfo.legal_representative || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="注册资本">
          {{ businessInfo.registered_capital || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="成立日期">
          {{ formatDate(businessInfo.registration_date) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="企业状态">
          <el-tag 
            :type="getCompanyStatusType(businessInfo.company_status)"
            size="small"
          >
            {{ businessInfo.company_status || '-' }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="企业类型">
          {{ businessInfo.company_type || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="所属行业">
          {{ businessInfo.industry || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="人员规模">
          {{ businessInfo.staff_size || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="注册地址" :span="2">
          <span class="text-sm">{{ businessInfo.address || '-' }}</span>
        </el-descriptions-item>
        
        <el-descriptions-item label="经营范围" :span="2">
          <div class="business-scope">
            <el-text 
              v-if="businessInfo.business_scope"
              :line-clamp="isExpanded ? 0 : 3"
              class="text-sm"
            >
              {{ businessInfo.business_scope }}
            </el-text>
            <span v-else class="text-gray-400">-</span>
            
            <el-button 
              v-if="businessInfo.business_scope && businessInfo.business_scope.length > 100"
              type="text" 
              size="small"
              @click="isExpanded = !isExpanded"
              class="mt-1"
            >
              {{ isExpanded ? '收起' : '展开' }}
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 数据来源和更新时间 -->
      <div class="data-source mt-4 pt-4 border-t border-gray-200">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <span>
            数据来源: {{ businessInfo.data_source === 'tianyancha' ? '天眼查' : businessInfo.data_source }}
          </span>
          <span>
            更新时间: {{ formatDateTime(businessInfo.last_updated) }}
          </span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  businessInfo: {
    type: Object,
    default: null
  },
  status: {
    type: String,
    default: '未获取' // '未获取' | '正常' | '已过期' | '获取失败'
  },
  showRefreshButton: {
    type: Boolean,
    default: true
  },
  isRefreshing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh'])

const isExpanded = ref(false)

// 计算属性
const getStatusType = () => {
  switch (props.status) {
    case '正常':
      return 'success'
    case '已过期':
      return 'warning'
    case '获取失败':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = () => {
  return props.status || '未获取'
}

const getCompanyStatusType = (status) => {
  if (!status) return 'info'
  
  const normalStatuses = ['存续', '在业', '正常', '开业']
  const warningStatuses = ['迁出', '迁入', '停业']
  const dangerStatuses = ['注销', '吊销', '清算']
  
  if (normalStatuses.some(s => status.includes(s))) return 'success'
  if (warningStatuses.some(s => status.includes(s))) return 'warning'
  if (dangerStatuses.some(s => status.includes(s))) return 'danger'
  
  return 'info'
}

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return dateString
  }
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateString
  }
}
</script>

<style scoped>
.business-info-card {
  @apply w-full;
}

.card-header {
  @apply w-full;
}

.no-data {
  @apply py-8;
}

.error-state {
  @apply py-4;
}

.business-info-content {
  @apply space-y-4;
}

.business-scope {
  @apply w-full;
}

.data-source {
  @apply text-xs;
}

:deep(.el-descriptions__label) {
  @apply font-medium;
}

:deep(.el-descriptions__content) {
  @apply break-words;
}
</style> 