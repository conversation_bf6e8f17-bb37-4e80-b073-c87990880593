<template>
  <el-card class="certifications-card">
    <template #header>
      <div class="card-header flex items-center justify-between">
        <span class="text-lg font-semibold">认证信息</span>
        <div class="flex items-center space-x-2">
          <el-tag 
            v-if="certifications && !certifications.error"
            :type="getValidCountType()"
            size="small"
          >
            有效: {{ certifications.valid_count || 0 }}/{{ certifications.total_count || 0 }}
          </el-tag>
          <el-tag :type="getStatusType()" size="small">
            {{ getStatusText() }}
          </el-tag>
          <el-button 
            v-if="showRefreshButton"
            type="text" 
            size="small"
            @click="$emit('refresh')"
            :loading="isRefreshing"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    
    <!-- 无数据状态 -->
    <div v-if="!certifications" class="no-data">
      <el-empty 
        description="暂无认证信息"
        :image-size="80"
      >
        <template #description>
          <p class="text-gray-500">点击"获取企业信息"按钮从天眼查获取认证数据</p>
        </template>
      </el-empty>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="certifications.error" class="error-state">
      <el-alert 
        type="error"
        :title="certifications.error"
        :closable="false"
        show-icon
      />
    </div>
    
    <!-- 认证信息展示 -->
    <div v-else class="certifications-content">
      <!-- 统计信息 -->
      <div class="stats-row mb-4">
        <div class="grid grid-cols-3 gap-4">
          <div class="stat-item text-center">
            <div class="text-2xl font-bold text-blue-600">{{ certifications.total_count || 0 }}</div>
            <div class="text-sm text-gray-500">总认证数</div>
          </div>
          <div class="stat-item text-center">
            <div class="text-2xl font-bold text-green-600">{{ certifications.valid_count || 0 }}</div>
            <div class="text-sm text-gray-500">有效认证</div>
          </div>
          <div class="stat-item text-center">
            <div class="text-2xl font-bold text-red-600">{{ (certifications.total_count || 0) - (certifications.valid_count || 0) }}</div>
            <div class="text-sm text-gray-500">失效认证</div>
          </div>
        </div>
      </div>
      
      <!-- 认证列表 -->
      <div v-if="certifications.certifications && certifications.certifications.length" class="certifications-list">
        <el-table 
          :data="certifications.certifications" 
          stripe
          size="small"
          :default-sort="{ prop: 'expire_date', order: 'descending' }"
        >
          <el-table-column prop="cert_name" label="认证名称" min-width="200">
            <template #default="{ row }">
              <div class="cert-name">
                <span class="font-medium">{{ row.cert_name }}</span>
                <div class="text-xs text-gray-500 mt-1">{{ row.cert_no }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="cert_status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="getCertStatusType(row)"
                size="small"
              >
                {{ row.is_valid ? '有效' : '失效' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="issue_date" label="颁发日期" width="120" sortable>
            <template #default="{ row }">
              {{ formatDate(row.issue_date) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="expire_date" label="到期日期" width="120" sortable>
            <template #default="{ row }">
              <span :class="getExpireDateClass(row.expire_date, row.is_valid)">
                {{ formatDate(row.expire_date) }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="issuing_authority" label="发证机构" min-width="150">
            <template #default="{ row }">
              <span class="text-sm">{{ row.issuing_authority || '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="standard" label="认证依据" min-width="180">
            <template #default="{ row }">
              <el-tooltip 
                v-if="row.standard"
                :content="row.standard"
                placement="top"
              >
                <span class="text-sm truncate">{{ row.standard }}</span>
              </el-tooltip>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="80" align="center">
            <template #default="{ row }">
              <el-button 
                type="text" 
                size="small"
                @click="showCertDetail(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 无认证数据 -->
      <div v-else class="no-certifications">
        <el-empty 
          description="暂无认证记录"
          :image-size="60"
        />
      </div>
      
      <!-- 数据来源和更新时间 -->
      <div class="data-source mt-4 pt-4 border-t border-gray-200">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <span>
            数据来源: {{ certifications.data_source === 'tianyancha' ? '天眼查' : certifications.data_source }}
          </span>
          <span>
            更新时间: {{ formatDateTime(certifications.last_updated) }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 认证详情对话框 -->
    <el-dialog 
      v-model="showDetailDialog"
      title="认证详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedCert" class="cert-detail">
        <el-descriptions :column="1" border size="small">
          <el-descriptions-item label="认证名称">
            {{ selectedCert.cert_name }}
          </el-descriptions-item>
          <el-descriptions-item label="证书编号">
            {{ selectedCert.cert_no }}
          </el-descriptions-item>
          <el-descriptions-item label="证书状态">
            <el-tag :type="getCertStatusType(selectedCert)" size="small">
              {{ selectedCert.cert_status || (selectedCert.is_valid ? '有效' : '失效') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="颁发日期">
            {{ formatDate(selectedCert.issue_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="到期日期">
            {{ formatDate(selectedCert.expire_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="发证机构">
            {{ selectedCert.issuing_authority || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="认证依据">
            {{ selectedCert.standard || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="覆盖人数">
            {{ selectedCert.coverage_staff || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="组织地址">
            {{ selectedCert.organization_address || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="业务范围" v-if="selectedCert.business_scope">
            <div class="business-scope-detail">
              {{ selectedCert.business_scope }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  certifications: {
    type: Object,
    default: null
  },
  status: {
    type: String,
    default: '未获取' // '未获取' | '正常' | '已过期' | '获取失败'
  },
  showRefreshButton: {
    type: Boolean,
    default: true
  },
  isRefreshing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh'])

const showDetailDialog = ref(false)
const selectedCert = ref(null)

// 计算属性
const getStatusType = () => {
  switch (props.status) {
    case '正常':
      return 'success'
    case '已过期':
      return 'warning'
    case '获取失败':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = () => {
  return props.status || '未获取'
}

const getValidCountType = () => {
  if (!props.certifications) return 'info'
  
  const { valid_count = 0, total_count = 0 } = props.certifications
  
  if (total_count === 0) return 'info'
  if (valid_count === total_count) return 'success'
  if (valid_count > 0) return 'warning'
  return 'danger'
}

const getCertStatusType = (cert) => {
  if (cert.is_valid) return 'success'
  
  // 根据具体状态判断
  if (cert.cert_status) {
    if (cert.cert_status.includes('撤销') || cert.cert_status.includes('注销')) return 'danger'
    if (cert.cert_status.includes('暂停')) return 'warning'
  }
  
  return 'info'
}

const getExpireDateClass = (expireDate, isValid) => {
  if (!expireDate || !isValid) return 'text-gray-500'
  
  const expire = new Date(expireDate)
  const now = new Date()
  const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'text-red-500' // 已过期
  if (diffDays < 30) return 'text-orange-500' // 即将过期
  if (diffDays < 90) return 'text-yellow-500' // 临近过期
  
  return 'text-green-500' // 正常
}

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return dateString
  }
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateString
  }
}

const showCertDetail = (cert) => {
  selectedCert.value = cert
  showDetailDialog.value = true
}
</script>

<style scoped>
.certifications-card {
  @apply w-full;
}

.card-header {
  @apply w-full;
}

.no-data {
  @apply py-8;
}

.error-state {
  @apply py-4;
}

.certifications-content {
  @apply space-y-4;
}

.stats-row {
  @apply bg-gray-50 p-4 rounded-lg;
}

.stat-item {
  @apply p-3;
  min-height: 4rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.stat-item > div:first-child {
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.stat-item > div:last-child {
  line-height: 1.4;
  text-align: center;
  word-break: keep-all;
}

.certifications-list {
  @apply w-full;
}

.cert-name {
  @apply space-y-1;
}

.no-certifications {
  @apply py-8;
}

.data-source {
  @apply text-xs;
}

.cert-detail {
  @apply space-y-4;
}

.business-scope-detail {
  @apply text-sm leading-relaxed;
  white-space: pre-wrap;
}

:deep(.el-table .cell) {
  @apply break-words;
}

:deep(.el-descriptions__content) {
  @apply break-words;
}
</style> 