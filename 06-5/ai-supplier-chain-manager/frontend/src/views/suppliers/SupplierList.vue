<template>
  <div class="supplier-list">
    <!-- 页面标题和统计 -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-gray-900">供应商管理</h1>
        
        <div class="flex items-center gap-3">
          <!-- 新增供应商按钮 -->
          <el-button type="primary" @click="handleAddSupplier">
            <el-icon><Plus /></el-icon>
            新增供应商
          </el-button>
          
          <!-- 开发环境测试按钮 -->
          <div v-if="isDev" class="flex gap-2">
            <el-button 
              type="warning" 
              size="small" 
              @click="testAuthExpired"
            >
              测试登录失效
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat-card bg-white p-4 rounded-lg shadow">
          <div class="text-sm text-gray-500">总供应商</div>
          <div class="text-2xl font-bold text-blue-600">{{ stats.total || 0 }}</div>
        </div>
        <div class="stat-card bg-white p-4 rounded-lg shadow">
          <div class="text-sm text-gray-500">待处理</div>
          <div class="text-2xl font-bold text-orange-600">{{ stats.status_categories?.['待处理'] || 0 }}</div>
        </div>
        <div class="stat-card bg-white p-4 rounded-lg shadow">
          <div class="text-sm text-gray-500">进行中</div>
          <div class="text-2xl font-bold text-green-600">{{ stats.status_categories?.['进行中'] || 0 }}</div>
        </div>
        <div class="stat-card bg-white p-4 rounded-lg shadow">
          <div class="text-sm text-gray-500">已拒绝</div>
          <div class="text-2xl font-bold text-red-600">{{ stats.status_categories?.['已拒绝'] || 0 }}</div>
        </div>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索框 -->
        <div>
          <el-input
            v-model="searchForm.search"
            placeholder="搜索供应商名称、地区、网站..."
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <!-- 状态分类过滤 -->
        <div>
          <el-select
            v-model="searchForm.status_category"
            placeholder="选择状态分类"
            clearable
            @change="handleSearch"
          >
            <el-option label="待处理" value="待处理" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已拒绝" value="已拒绝" />
          </el-select>
        </div>
        
        <!-- 产品分类过滤 -->
        <div>
          <el-cascader
            v-model="searchForm.category_id"
            :options="categoryOptions"
            :props="cascaderProps"
            placeholder="选择产品分类"
            clearable
            @change="handleSearch"
          />
        </div>
        
        <!-- 搜索按钮 -->
        <div>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 供应商列表 -->
    <div class="bg-white rounded-lg shadow">
      <el-table
        v-loading="loading"
        :data="suppliers"
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="supplier_name" label="供应商名称" min-width="150">
          <template #default="{ row }">
            <div class="font-medium text-blue-600 cursor-pointer hover:text-blue-800">
              {{ row.supplier_name }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="region" label="地区" width="120" />
        
        <el-table-column prop="category_full_path" label="产品分类" min-width="200">
          <template #default="{ row }">
            <div class="text-sm text-gray-600">
              {{ row.category_full_path || '未分类' }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="website" label="网站" min-width="180">
          <template #default="{ row }">
            <a
              v-if="row.website"
              :href="row.website"
              target="_blank"
              class="text-blue-600 hover:text-blue-800 text-sm"
            >
              {{ row.website }}
            </a>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ row.status_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="评级信息" width="150">
          <template #default="{ row }">
            <div class="text-xs space-y-1">
              <div v-if="row.rating_info && row.rating_info.has_rating">
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">企业信息:</span>
                  <el-tag 
                    :type="getRatingStatusType(row.rating_info.business_info_status)" 
                    size="small"
                  >
                    {{ row.rating_info.business_info_status }}
                  </el-tag>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">认证:</span>
                  <span class="text-blue-600 font-medium">
                    {{ row.rating_info.valid_certs_count || 0 }}/{{ row.rating_info.total_certs_count || 0 }}
                  </span>
                </div>
              </div>
              <div v-else class="text-gray-400 text-center">
                未获取
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="120">
          <template #default="{ row }">
            <div class="text-sm text-gray-600">
              {{ formatDate(row.created_at) }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleViewDetail(row)"
            >
              查看详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              :loading="isLoadingRating(row.id)"
              @click.stop="handleViewRating(row)"
            >
              <el-icon v-if="!isLoadingRating(row.id)"><TrendCharts /></el-icon>
              {{ getRatingButtonText(row) }}
            </el-button>
            <el-button
              type="success"
              size="small"
              @click.stop="handleEditSupplier(row)"
            >
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleStatusChange(row, command)">
              <el-button size="small" type="text">
                更改状态<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="0">验证正常</el-dropdown-item>
                  <el-dropdown-item command="1">验证pending</el-dropdown-item>
                  <el-dropdown-item command="99">验证通过</el-dropdown-item>
                  <el-dropdown-item command="2">网站无效</el-dropdown-item>
                  <el-dropdown-item command="3">网站被标为黑名单</el-dropdown-item>
                  <el-dropdown-item command="4">产品不能做</el-dropdown-item>
                  <el-dropdown-item command="5">报价过高</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
        <el-table-column label="评分状态" width="120">
          <template #default="{ row }">
            <el-tag 
              v-if="row.rating_info?.has_dimension_scores" 
              type="success" 
              size="small"
            >
              已评分
            </el-tag>
            <el-tag 
              v-else-if="row.rating_info?.has_rating" 
              type="warning" 
              size="small"
            >
              待评分
            </el-tag>
            <el-tag 
              v-else 
              type="info" 
              size="small"
            >
              未获取
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="p-4 flex justify-center">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.per_page"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 供应商详情对话框 -->
    <SupplierDetailDialog
      v-model="detailDialogVisible"
      :supplier="selectedSupplier"
      @updated="handleSupplierUpdated"
    />

    <!-- 供应商表单对话框 -->
    <SupplierFormDialog
      v-model="formDialogVisible"
      :supplier="editingSupplier"
      :categories="categories"
      @success="handleSupplierUpdated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, ArrowDown, Plus, TrendCharts } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { supplierApi } from '@/utils/api'
import { useUserStore } from '@/stores/user'
import { useSupplierStore } from '@/stores/supplier'
import SupplierDetailDialog from './components/SupplierDetailDialog.vue'
import SupplierFormDialog from './components/SupplierFormDialog.vue'

// 响应式数据
const loading = ref(false)
const suppliers = ref([])
const stats = ref({})
const categories = ref([])
const selectedSupplier = ref(null)
const detailDialogVisible = ref(false)
const formDialogVisible = ref(false)
const editingSupplier = ref(null)
const ratingLoadingMap = ref(new Map()) // 追踪每个供应商的评级加载状态

// 路由和store
const router = useRouter()
const userStore = useUserStore()
const supplierStore = useSupplierStore()

// 开发环境检测
const isDev = computed(() => import.meta.env.DEV)

// 搜索表单
const searchForm = reactive({
  search: '',
  status_category: '',
  category_id: []
})

// 分页信息
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0,
  pages: 0
})

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  emitPath: false
}

// 计算属性
const categoryOptions = computed(() => {
  return categories.value
})

// 方法
const loadSuppliers = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      ...searchForm
    }
    
    // 处理分类ID
    if (searchForm.category_id && searchForm.category_id.length > 0) {
      params.category_id = searchForm.category_id[searchForm.category_id.length - 1]
    }
    
    const response = await supplierApi.getSuppliers(params)
    if (response.success) {
      suppliers.value = response.data.suppliers
      Object.assign(pagination, response.data.pagination)
    } else {
      ElMessage.error(response.message || '获取供应商列表失败')
    }
  } catch (error) {
    console.error('获取供应商列表失败:', error)
    ElMessage.error('获取供应商列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await supplierApi.getSupplierStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

const loadCategories = async () => {
  try {
    const response = await supplierApi.getCategories()
    if (response.success) {
      categories.value = response.data
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadSuppliers()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    status_category: '',
    category_id: []
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.per_page = size
  pagination.page = 1
  loadSuppliers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadSuppliers()
}

const handleRowClick = (row) => {
  handleViewDetail(row)
}

const handleViewDetail = (supplier) => {
  selectedSupplier.value = supplier
  detailDialogVisible.value = true
}

const handleStatusChange = async (supplier, newStatus) => {
  try {
    await ElMessageBox.confirm(
      `确定要将供应商"${supplier.supplier_name}"的状态更改吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await supplierApi.updateSupplierStatus(supplier.id, {
      status: parseInt(newStatus)
    })
    
    if (response.success) {
      ElMessage.success('状态更新成功')
      // 更新本地数据
      const index = suppliers.value.findIndex(s => s.id === supplier.id)
      if (index !== -1) {
        suppliers.value[index].status = response.data.status
        suppliers.value[index].status_text = response.data.status_text
      }
      // 重新加载统计信息
      loadStats()
    } else {
      ElMessage.error(response.message || '状态更新失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新状态失败:', error)
      ElMessage.error('状态更新失败')
    }
  }
}

const handleSupplierUpdated = () => {
  loadSuppliers()
  loadStats()
}

const getStatusTagType = (status) => {
  const statusMap = {
    0: 'warning',   // 验证正常
    1: 'warning',   // 验证pending
    2: 'danger',    // 网站无效
    3: 'danger',    // 网站被标为黑名单
    4: 'danger',    // 产品不能做
    5: 'danger',    // 报价过高
    99: 'success'   // 验证通过
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const testAuthExpired = async () => {
  try {
    await ElMessageBox.confirm(
      '这将模拟登录状态失效，您将被重定向到登录页面。确定继续吗？',
      '测试登录失效',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用用户store的登录失效处理方法
    userStore.handleAuthExpired()
    ElMessage.warning('已模拟登录失效，正在跳转到登录页面...')
  } catch {
    // 用户取消操作
  }
}

const handleAddSupplier = () => {
  editingSupplier.value = null
  formDialogVisible.value = true
}

const handleEditSupplier = (supplier) => {
  editingSupplier.value = supplier
  formDialogVisible.value = true
}

const handleViewRating = async (supplier) => {
  try {
    // 检查是否已有评级数据
    const hasRatingData = supplier.rating_info && supplier.rating_info.has_rating
    
    if (hasRatingData) {
      // 如果已有评级数据，直接跳转到评级页面
      router.push(`/main/suppliers/${supplier.id}/rating`)
    } else {
      // 如果没有评级数据，先获取数据再跳转
      ratingLoadingMap.value.set(supplier.id, true)
      
      try {
        const result = await supplierStore.fetchSupplierInfo(supplier.id)
        
        if (result.success) {
          ElMessage.success('企业信息获取成功')
          // 更新本地供应商数据
          const index = suppliers.value.findIndex(s => s.id === supplier.id)
          if (index !== -1) {
            // 刷新供应商的评级信息
            await supplierStore.refreshSupplierRating(supplier.id)
            // 重新加载供应商列表以获取最新的rating_info
            await loadSuppliers()
          }
          // 跳转到评级页面
          router.push(`/main/suppliers/${supplier.id}/rating`)
        } else {
          ElMessage.error(result.message || '获取企业信息失败')
        }
      } catch (error) {
        console.error('获取企业信息失败:', error)
        ElMessage.error('获取企业信息失败')
      } finally {
        ratingLoadingMap.value.set(supplier.id, false)
      }
    }
  } catch (error) {
    console.error('查看评级失败:', error)
    ElMessage.error('操作失败')
    ratingLoadingMap.value.set(supplier.id, false)
  }
}

const isLoadingRating = (supplierId) => {
  return ratingLoadingMap.value.get(supplierId) || false
}

const getRatingButtonText = (supplier) => {
  if (isLoadingRating(supplier.id)) {
    return '获取中...'
  }
  
  const hasRatingData = supplier.rating_info && supplier.rating_info.has_rating
  
  if (hasRatingData) {
    // 如果有评级数据，显示认证统计
    const validCerts = supplier.rating_info.valid_certs_count || 0
    const totalCerts = supplier.rating_info.total_certs_count || 0
    return `查看评级 (${validCerts}/${totalCerts})`
  } else {
    return '获取评级'
  }
}

const getRatingStatusType = (status) => {
  switch (status) {
    case '正常':
      return 'success'
    case '已过期':
      return 'warning'
    case '获取失败':
      return 'danger'
    case '未获取':
    default:
      return 'info'
  }
}

// 生命周期
onMounted(() => {
  loadSuppliers()
  loadStats()
  loadCategories()
})
</script>

<style scoped>
.supplier-list {
  padding: 20px;
}

.stat-card {
  min-height: 5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.5rem;
}

.stat-card > div:first-child {
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.stat-card > div:last-child {
  line-height: 1.2;
}

.el-table .el-table__row {
  cursor: pointer;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}
</style> 