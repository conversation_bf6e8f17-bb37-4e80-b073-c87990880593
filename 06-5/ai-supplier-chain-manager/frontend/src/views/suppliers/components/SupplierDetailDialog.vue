<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEditing ? '编辑供应商信息' : '供应商详情'"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="supplier" class="supplier-detail">
      <!-- 基本信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">基本信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">供应商名称</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.supplier_name"
              placeholder="请输入供应商名称"
            />
            <div v-else class="text-gray-900">{{ supplier.supplier_name }}</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">地区</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.region"
              placeholder="请输入地区"
            />
            <div v-else class="text-gray-900">{{ supplier.region || '-' }}</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">产品分类</label>
            <div class="text-gray-900">{{ supplier.category_full_path || '未分类' }}</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <el-tag
              :type="getStatusTagType(supplier.status)"
              size="small"
            >
              {{ supplier.status_text }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">联系信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">网站</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.website"
              placeholder="请输入网站地址"
            />
            <div v-else class="text-gray-900">
              <a
                v-if="supplier.website"
                :href="supplier.website"
                target="_blank"
                class="text-blue-600 hover:text-blue-800"
              >
                {{ supplier.website }}
              </a>
              <span v-else>-</span>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">电话</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.phone"
              placeholder="请输入电话号码"
            />
            <div v-else class="text-gray-900">{{ supplier.phone || '-' }}</div>
          </div>
          
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.email"
              placeholder="请输入邮箱地址"
            />
            <div v-else class="text-gray-900">{{ supplier.email || '-' }}</div>
          </div>
          
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">地址</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.address"
              type="textarea"
              :rows="2"
              placeholder="请输入地址"
            />
            <div v-else class="text-gray-900">{{ supplier.address || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">详细信息</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">匹配原因</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.matching_reason"
              type="textarea"
              :rows="3"
              placeholder="请输入匹配原因"
            />
            <div v-else class="text-gray-900 whitespace-pre-wrap">{{ supplier.matching_reason || '-' }}</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">认证信息</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.certifications"
              type="textarea"
              :rows="3"
              placeholder="请输入认证信息"
            />
            <div v-else class="text-gray-900 whitespace-pre-wrap">{{ supplier.certifications || '-' }}</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
            <el-input
              v-if="isEditing"
              v-model="editForm.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
            <div v-else class="text-gray-900 whitespace-pre-wrap">{{ supplier.notes || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">时间信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
            <div class="text-gray-900">{{ formatDateTime(supplier.created_at) }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div>
          <el-button
            v-if="!isEditing"
            type="primary"
            @click="startEdit"
          >
            编辑信息
          </el-button>
        </div>
        <div>
          <el-button @click="handleClose">
            {{ isEditing ? '取消' : '关闭' }}
          </el-button>
          <el-button
            v-if="isEditing"
            type="primary"
            :loading="saving"
            @click="handleSave"
          >
            保存
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { supplierApi } from '@/utils/api'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  supplier: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'updated'])

// 响应式数据
const isEditing = ref(false)
const saving = ref(false)

// 编辑表单
const editForm = reactive({
  supplier_name: '',
  region: '',
  website: '',
  phone: '',
  email: '',
  address: '',
  matching_reason: '',
  certifications: '',
  notes: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.supplier, (newSupplier) => {
  if (newSupplier) {
    resetEditForm()
  }
}, { immediate: true })

// 方法
const resetEditForm = () => {
  if (props.supplier) {
    Object.assign(editForm, {
      supplier_name: props.supplier.supplier_name || '',
      region: props.supplier.region || '',
      website: props.supplier.website || '',
      phone: props.supplier.phone || '',
      email: props.supplier.email || '',
      address: props.supplier.address || '',
      matching_reason: props.supplier.matching_reason || '',
      certifications: props.supplier.certifications || '',
      notes: props.supplier.notes || ''
    })
  }
}

const startEdit = () => {
  isEditing.value = true
  resetEditForm()
}

const handleClose = async () => {
  if (isEditing.value) {
    try {
      await ElMessageBox.confirm(
        '确定要取消编辑吗？未保存的更改将丢失。',
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      isEditing.value = false
      resetEditForm()
    } catch {
      return
    }
  }
  
  dialogVisible.value = false
  isEditing.value = false
}

const handleSave = async () => {
  try {
    // 验证必填字段
    if (!editForm.supplier_name.trim()) {
      ElMessage.error('供应商名称不能为空')
      return
    }

    saving.value = true
    
    const response = await supplierApi.updateSupplier(props.supplier.id, editForm)
    
    if (response.success) {
      ElMessage.success('供应商信息更新成功')
      isEditing.value = false
      emit('updated')
      dialogVisible.value = false
    } else {
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新供应商信息失败:', error)
    ElMessage.error('更新失败')
  } finally {
    saving.value = false
  }
}

const getStatusTagType = (status) => {
  const statusMap = {
    0: 'warning',   // 验证正常
    1: 'warning',   // 验证pending
    2: 'danger',    // 网站无效
    3: 'danger',    // 网站被标为黑名单
    4: 'danger',    // 产品不能做
    5: 'danger',    // 报价过高
    99: 'success'   // 验证通过
  }
  return statusMap[status] || 'info'
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}
</script>

<style scoped>
.supplier-detail {
  max-height: 600px;
  overflow-y: auto;
}

.supplier-detail h3 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.supplier-detail label {
  font-weight: 500;
}

.supplier-detail .text-gray-900 {
  min-height: 20px;
  padding: 4px 0;
}
</style> 