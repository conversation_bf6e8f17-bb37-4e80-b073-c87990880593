<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑供应商' : '新增供应商'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="supplier-form"
    >
      <!-- 基本信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">基本信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <el-form-item label="供应商名称" prop="supplier_name" class="md:col-span-2">
            <el-input
              v-model="form.supplier_name"
              placeholder="请输入供应商名称"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="地区" prop="region">
            <el-input
              v-model="form.region"
              placeholder="请输入地区"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="产品分类" prop="category_id">
            <el-cascader
              v-model="form.category_id"
              :options="categoryOptions"
              :props="cascaderProps"
              placeholder="请选择产品分类"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">联系信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <el-form-item label="网站" prop="website">
            <el-input
              v-model="form.website"
              placeholder="请输入网站地址"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="电话" prop="phone">
            <el-input
              v-model="form.phone"
              placeholder="请输入电话号码"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email" class="md:col-span-2">
            <el-input
              v-model="form.email"
              placeholder="请输入邮箱地址"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="地址" prop="address" class="md:col-span-2">
            <el-input
              v-model="form.address"
              type="textarea"
              :rows="2"
              placeholder="请输入地址"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">详细信息</h3>
        <div class="space-y-4">
          <el-form-item label="匹配原因" prop="matching_reason">
            <el-input
              v-model="form.matching_reason"
              type="textarea"
              :rows="3"
              placeholder="请输入匹配原因"
            />
          </el-form-item>
          
          <el-form-item label="认证信息" prop="certifications">
            <el-input
              v-model="form.certifications"
              type="textarea"
              :rows="3"
              placeholder="请输入认证信息"
            />
          </el-form-item>
          
          <el-form-item label="备注" prop="notes">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 状态信息（仅编辑时显示） -->
      <div v-if="isEdit" class="mb-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">状态信息</h3>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="验证正常" :value="0" />
            <el-option label="验证pending" :value="1" />
            <el-option label="网站无效" :value="2" />
            <el-option label="网站被标为黑名单" :value="3" />
            <el-option label="产品不能做" :value="4" />
            <el-option label="报价过高" :value="5" />
            <el-option label="验证通过" :value="99" />
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          {{ isEdit ? '保存' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { supplierApi } from '@/utils/api'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  supplier: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const form = reactive({
  supplier_name: '',
  region: '',
  category_id: [],
  website: '',
  phone: '',
  email: '',
  address: '',
  matching_reason: '',
  certifications: '',
  notes: '',
  status: 1
})

// 表单验证规则
const rules = {
  supplier_name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { min: 2, max: 255, message: '供应商名称长度在 2 到 255 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  website: [
    { pattern: /^https?:\/\//, message: '网站地址应以 http:// 或 https:// 开头', trigger: 'blur' }
  ]
}

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  emitPath: false
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.supplier)

const categoryOptions = computed(() => props.categories)

// 方法
const resetForm = () => {
  Object.assign(form, {
    supplier_name: '',
    region: '',
    category_id: [],
    website: '',
    phone: '',
    email: '',
    address: '',
    matching_reason: '',
    certifications: '',
    notes: '',
    status: 1
  })
  
  // 清除验证错误
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听器
watch(() => props.supplier, (newSupplier) => {
  if (newSupplier) {
    // 编辑模式，填充表单数据
    Object.assign(form, {
      supplier_name: newSupplier.supplier_name || '',
      region: newSupplier.region || '',
      category_id: newSupplier.category_id ? [newSupplier.category_id] : [],
      website: newSupplier.website || '',
      phone: newSupplier.phone || '',
      email: newSupplier.email || '',
      address: newSupplier.address || '',
      matching_reason: newSupplier.matching_reason || '',
      certifications: newSupplier.certifications || '',
      notes: newSupplier.notes || '',
      status: newSupplier.status || 1
    })
  } else {
    // 新增模式，重置表单
    resetForm()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && !props.supplier) {
    // 新增模式时重置表单
    resetForm()
  }
})

const handleClose = async () => {
  // 检查表单是否有修改
  const hasChanges = Object.keys(form).some(key => {
    if (props.supplier) {
      // 编辑模式：检查是否与原始数据不同
      if (key === 'category_id') {
        const originalCategoryId = props.supplier.category_id ? [props.supplier.category_id] : []
        return JSON.stringify(form[key]) !== JSON.stringify(originalCategoryId)
      }
      return form[key] !== (props.supplier[key] || '')
    } else {
      // 新增模式：检查是否有任何输入
      if (key === 'category_id') {
        return form[key].length > 0
      }
      if (key === 'status') {
        return form[key] !== 1
      }
      return form[key] !== ''
    }
  })

  if (hasChanges) {
    try {
      await ElMessageBox.confirm(
        '确定要取消吗？未保存的更改将丢失。',
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return
    }
  }
  
  dialogVisible.value = false
}

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    saving.value = true
    
    // 准备提交数据
    const submitData = { ...form }
    
    // 处理分类ID
    if (submitData.category_id && submitData.category_id.length > 0) {
      submitData.category_id = submitData.category_id[submitData.category_id.length - 1]
    } else {
      submitData.category_id = null
    }

    let response
    if (isEdit.value) {
      // 编辑模式
      response = await supplierApi.updateSupplier(props.supplier.id, submitData)
    } else {
      // 新增模式
      response = await supplierApi.createSupplier(submitData)
    }
    
    if (response.success) {
      ElMessage.success(response.message)
      emit('success', response.data)
      dialogVisible.value = false
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('保存供应商失败:', error)
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.supplier-form {
  max-height: 600px;
  overflow-y: auto;
}

.supplier-form h3 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style> 