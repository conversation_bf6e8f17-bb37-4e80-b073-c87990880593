import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/main',
    name: 'MainLayout',
    component: () => import('@/views/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue')
      },
      // 供应商管理路由
      {
        path: 'suppliers',
        name: 'Suppliers',
        component: () => import('@/views/suppliers/SupplierList.vue'),
        meta: { title: '供应商列表' }
      },
      {
        path: 'suppliers/:id/rating',
        name: 'SupplierRating',
        component: () => import('@/views/SupplierRating.vue'),
        meta: { title: '供应商评级' }
      },
      {
        path: 'suppliers/search',
        name: 'SuppliersSearch',
        component: () => import('@/views/placeholder/Placeholder.vue'),
        meta: { title: '供应商搜索' }
      },
      // 分类管理路由
      {
        path: 'categories',
        name: 'Categories',
        component: () => import('@/views/CategoryOverview.vue'),
        meta: { title: '产品分类管理' }
      },
      // 物料详情路由
      {
        path: 'components/:id',
        name: 'ComponentDetail',
        component: () => import('@/views/ComponentDetail.vue'),
        meta: { title: '物料详情' }
      },
      // 组件管理路由（占位）
      {
        path: 'components',
        name: 'Components',
        component: () => import('@/views/placeholder/Placeholder.vue'),
        meta: { title: '组件列表' }
      },
      {
        path: 'components/analysis',
        name: 'ComponentsAnalysis',
        component: () => import('@/views/placeholder/Placeholder.vue'),
        meta: { title: '组件分析' }
      },
      // 任务管理路由（占位）
      {
        path: 'tasks',
        name: 'Tasks',
        component: () => import('@/views/placeholder/Placeholder.vue'),
        meta: { title: '任务列表' }
      },
      {
        path: 'tasks/create',
        name: 'TasksCreate',
        component: () => import('@/views/placeholder/Placeholder.vue'),
        meta: { title: '创建任务' }
      },
      // 报表分析路由（占位）
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('@/views/placeholder/Placeholder.vue'),
        meta: { title: '报表分析' }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/404.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果没有用户信息，尝试从服务器获取
    if (!userStore.isAuthenticated) {
      await userStore.checkAuthStatus()
    }
    
    // 仍然没有认证，跳转到登录页
    if (!userStore.isAuthenticated) {
      next('/login')
      return
    }
  }
  
  // 如果已经登录，访问登录页时跳转到主页
  if (to.path === '/login' && userStore.isAuthenticated) {
    next('/main')
    return
  }
  
  next()
})

export default router 