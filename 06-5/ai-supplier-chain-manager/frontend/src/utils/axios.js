import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 智能检测API基础URL
const getBaseURL = () => {
  // 1. 优先使用环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }
  
  // 2. 根据当前环境自动判断
  const { hostname, port, protocol } = window.location
  
  // 开发环境检测
  if (import.meta.env.DEV) {
    return 'http://localhost:5000'
  }
  
  // 生产环境检测
  if (port === '9003') {
    // Docker环境：前端在9003，后端在9002
    return `${protocol}//${hostname}:9002`
  } else if (port === '9004') {
    // Docker环境通过Nginx访问：统一入口在9004
    return `${protocol}//${hostname}:9004`
  } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // 本地生产构建
    return 'http://localhost:5000'
  }
  
  // 默认回退 - 如果通过Nginx代理访问，直接使用相对路径
  return ''
}

// 创建axios实例
const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 180000, // 3分钟超时
  withCredentials: false, // 临时禁用跨域cookie以解决CORS问题
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 开发环境下打印API请求信息
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`)
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('登录状态已失效，请重新登录')
          // 动态导入用户store以避免循环依赖
          import('@/stores/user').then(({ useUserStore }) => {
            const userStore = useUserStore()
            userStore.handleAuthExpired()
          })
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
      console.error('🔥 Network Error:', error.request)
    } else {
      ElMessage.error('请求配置错误')
      console.error('🔥 Request Config Error:', error.message)
    }
    
    return Promise.reject(error)
  }
)

// 导出API基础URL供调试使用
if (import.meta.env.DEV) {
  console.log('🌐 API Base URL:', getBaseURL())
}

export default api 