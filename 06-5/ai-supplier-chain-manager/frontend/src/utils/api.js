import api from './axios'

// 认证相关API
export const authApi = {
  // 登录
  login: (data) => api.post('/api/auth/login', data).then(res => res.data),
  
  // 获取当前用户信息
  getCurrentUser: () => api.get('/api/auth/current').then(res => res.data),
  
  // 登出
  logout: () => api.post('/api/auth/logout').then(res => res.data)
}

// 产品相关API
export const productApi = {
  // 获取产品列表
  getProducts: () => api.get('/api/products').then(res => res.data),
  
  // 切换当前产品
  switchProduct: (productId) => api.post('/api/products/switch', { product_id: productId }).then(res => res.data)
}

// 供应商相关API
export const supplierApi = {
  // 获取供应商列表
  getSuppliers: (params) => api.get('/api/suppliers', { params }).then(res => res.data),
  
  // 获取供应商详情
  getSupplierDetail: (id) => api.get(`/api/suppliers/${id}`).then(res => res.data),
  
  // 创建新供应商
  createSupplier: (data) => api.post('/api/suppliers', data).then(res => res.data),
  
  // 更新供应商状态
  updateSupplierStatus: (id, data) => api.put(`/api/suppliers/${id}/status`, data).then(res => res.data),
  
  // 更新供应商信息
  updateSupplier: (id, data) => api.put(`/api/suppliers/${id}`, data).then(res => res.data),
  
  // 获取产品分类列表
  getCategories: () => api.get('/api/categories').then(res => res.data),
  
  // 获取供应商统计信息
  getSupplierStats: () => api.get('/api/suppliers/stats').then(res => res.data)
}

// 导出所有API
export default {
  authApi,
  productApi,
  supplierApi
} 