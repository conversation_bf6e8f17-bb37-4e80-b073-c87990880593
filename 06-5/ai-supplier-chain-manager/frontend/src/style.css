@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Element Plus 样式覆盖 */
.el-button {
  font-weight: 500;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-menu-item.is-active {
  background-color: #e1f5fe !important;
  color: #1976d2 !important;
}

/* 自定义工具类 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shadow-soft {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 防止字体重叠的全局样式 */
.text-2xl, .text-3xl {
  line-height: 1.2 !important;
}

.text-lg {
  line-height: 1.4 !important;
}

.text-sm {
  line-height: 1.5 !important;
}

/* 确保flex容器中的文字不重叠 */
.flex > * {
  flex-shrink: 0;
}

.flex.items-center > * {
  align-self: center;
}