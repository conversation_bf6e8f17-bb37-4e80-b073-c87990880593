from sqlalchemy import Column, Integer, JSON, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app import db
from datetime import datetime, timedelta
import json

class SupplierRating(db.Model):
    __tablename__ = 'supplier_rating'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    supplier_id = Column(Integer, nullable=True, index=True)
    dimension_scores = Column(JSON, nullable=False, default=dict)
    certifications = Column(JSON, nullable=True)
    main_products = Column(JSON, nullable=True)
    patents = Column(JSON, nullable=True)
    evaluated_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    business_info = Column(JSON, nullable=True)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'supplier_id': self.supplier_id,
            'dimension_scores': self.dimension_scores,
            'certifications': self.certifications,
            'main_products': self.main_products,
            'patents': self.patents,
            'evaluated_at': self.evaluated_at.isoformat() if self.evaluated_at else None,
            'business_info': self.business_info
        }
    
    def update_business_info(self, business_info: dict):
        """更新企业信息"""
        if 'error' not in business_info:
            self.business_info = business_info
            self.evaluated_at = datetime.utcnow()
            return True
        return False
    
    def update_certifications(self, certifications: dict):
        """更新认证信息"""
        if 'error' not in certifications:
            self.certifications = certifications
            self.evaluated_at = datetime.utcnow()
            return True
        return False
    
    def is_info_outdated(self, days: int = 30) -> bool:
        """检查信息是否过期"""
        if not self.evaluated_at:
            return True
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return self.evaluated_at < cutoff_date
    
    def get_business_info_status(self) -> str:
        """获取企业信息状态"""
        if not self.business_info:
            return "未获取"
        
        if 'error' in self.business_info:
            return "获取失败"
        
        if self.is_info_outdated():
            return "已过期"
        
        return "正常"
    
    def get_certifications_status(self) -> str:
        """获取认证信息状态"""
        if not self.certifications:
            return "未获取"
        
        if 'error' in self.certifications:
            return "获取失败"
        
        if self.is_info_outdated():
            return "已过期"
        
        return "正常"
    
    def get_valid_certifications_count(self) -> int:
        """获取有效认证数量"""
        if not self.certifications or 'error' in self.certifications:
            return 0
        
        return self.certifications.get('valid_count', 0)
    
    def get_total_certifications_count(self) -> int:
        """获取总认证数量"""
        if not self.certifications or 'error' in self.certifications:
            return 0
        
        return self.certifications.get('total_count', 0)
    
    @classmethod
    def get_or_create_by_supplier_id(cls, supplier_id: int):
        """根据供应商ID获取或创建评级记录"""
        rating = cls.query.filter_by(supplier_id=supplier_id).first()
        if not rating:
            rating = cls(
                supplier_id=supplier_id,
                dimension_scores={}
            )
            db.session.add(rating)
            db.session.flush()  # 获取ID但不提交
        return rating 