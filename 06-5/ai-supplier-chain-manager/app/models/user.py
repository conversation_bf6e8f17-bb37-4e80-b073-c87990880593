from app import db
from werkzeug.security import check_password_hash
from datetime import datetime

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    client_company_id = db.Column(db.Integer, db.<PERSON>ey('client_company.id'), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    status = db.Column(db.Enum('active', 'inactive'), default='active')
    last_login_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系定义
    company = db.relationship('Company', backref='users', lazy='select')
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login_at = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'username': self.username,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'is_admin': self.is_admin,
            'status': self.status,
            'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None,
            'company': self.company.to_dict() if self.company else None
        }
    
    @staticmethod
    def authenticate(username, password):
        """用户认证"""
        user = User.query.filter_by(username=username, status='active').first()
        if user and user.check_password(password):
            user.update_last_login()
            return user
        return None 