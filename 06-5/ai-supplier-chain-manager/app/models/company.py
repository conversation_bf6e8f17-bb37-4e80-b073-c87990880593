from app import db
from datetime import datetime

class Company(db.Model):
    """公司模型"""
    __tablename__ = 'client_company'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    industry = db.Column(db.String(100))
    contact_person = db.Column(db.String(100))
    contact_email = db.Column(db.String(100))
    contact_phone = db.Column(db.String(50))
    signed_date = db.Column(db.Date)
    contract_period = db.Column(db.Integer)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系定义
    products = db.relationship('Product', backref='company', lazy='dynamic')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'industry': self.industry,
            'contact_person': self.contact_person,
            'contact_email': self.contact_email,
            'contact_phone': self.contact_phone,
            'signed_date': self.signed_date.isoformat() if self.signed_date else None,
            'contract_period': self.contract_period,
            'notes': self.notes
        }
    
    def get_products(self):
        """获取公司的所有产品"""
        return [product.to_dict() for product in self.products]
    
    @staticmethod
    def get_all():
        """获取所有公司"""
        return Company.query.all() 