from app import db
from datetime import datetime

class Component(db.Model):
    """物料/组件模型"""
    __tablename__ = 'component'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    spec = db.Column(db.Text)
    category = db.Column(db.String(50), index=True)
    original_supplier = db.Column(db.String(100))
    price = db.Column(db.Numeric(12, 4))
    quantity = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    client_company_id = db.Column(db.Integer, index=True)
    client_product_id = db.Column(db.Integer, index=True)
    material = db.Column(db.String(100))
    component_code = db.Column(db.String(100), index=True)
    tag = db.Column(db.String(100))
    
    # 关联关系
    documents = db.relationship('ComponentDocument', backref='component', lazy='dynamic')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'spec': self.spec,
            'category': self.category,
            'original_supplier': self.original_supplier,
            'price': float(self.price) if self.price else None,
            'quantity': self.quantity,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'client_company_id': self.client_company_id,
            'client_product_id': self.client_product_id,
            'material': self.material,
            'component_code': self.component_code,
            'tag': self.tag
        }

class ComponentDocument(db.Model):
    """组件文档模型"""
    __tablename__ = 'component_documents'
    
    id = db.Column(db.Integer, primary_key=True)
    component_id = db.Column(db.Integer, db.ForeignKey('component.id'), nullable=False, index=True)
    document_name = db.Column(db.String(255), nullable=False)
    oss_path = db.Column(db.String(512), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'component_id': self.component_id,
            'document_name': self.document_name,
            'oss_path': self.oss_path,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def get_file_type(self):
        """获取文件类型"""
        if not self.document_name:
            return 'unknown'
        
        ext = self.document_name.lower().split('.')[-1]
        if ext in ['pdf']:
            return 'pdf'
        elif ext in ['dwg', 'dxf']:
            return 'cad'
        elif ext in ['sldprt', 'sldasm', 'slddrw']:
            return 'solidworks'
        elif ext in ['step', 'stp', 'iges', 'igs']:
            return '3d'
        elif ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
            return 'image'
        elif ext in ['doc', 'docx']:
            return 'word'
        elif ext in ['xls', 'xlsx']:
            return 'excel'
        else:
            return 'other'

class ComponentAnalysis(db.Model):
    """组件分析结果模型"""
    __tablename__ = 'component_analysis'
    
    id = db.Column(db.Integer, primary_key=True)
    component_id = db.Column(db.Integer, nullable=False, index=True)
    analysis = db.Column(db.Text)
    is_success = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    category_id = db.Column(db.Integer, index=True)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'component_id': self.component_id,
            'analysis': self.analysis,
            'is_success': self.is_success,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'category_id': self.category_id
        }

class ProductComponentRelation(db.Model):
    """产品组件关系模型"""
    __tablename__ = 'product_component_relation'
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, nullable=False, index=True)
    component_id = db.Column(db.Integer, nullable=False, index=True)
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit = db.Column(db.String(20), default='个')
    is_key_component = db.Column(db.Boolean, default=False, index=True)
    alternative_allowed = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'component_id': self.component_id,
            'quantity': self.quantity,
            'unit': self.unit,
            'is_key_component': self.is_key_component,
            'alternative_allowed': self.alternative_allowed,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        } 