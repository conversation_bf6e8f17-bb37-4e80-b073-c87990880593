#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import db
from datetime import datetime
import json
import uuid

class AnalysisTask(db.Model):
    """网站分析任务模型"""
    
    __tablename__ = 'analysis_tasks'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    supplier_id = db.Column(db.Integer, db.ForeignKey('search_suppliers.id'), nullable=False)
    task_type = db.Column(db.String(50), nullable=False, default='website_analysis')  # 任务类型
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, running, completed, failed
    progress = db.Column(db.Integer, default=0)  # 进度百分比 0-100
    
    # 任务参数
    parameters = db.Column(db.Text)  # JSON格式存储任务参数
    
    # 结果和错误信息
    result = db.Column(db.Text)  # JSON格式存储分析结果
    error_message = db.Column(db.Text)  # 错误信息
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    
    # 关联关系
    supplier = db.relationship('SearchSupplier', backref='analysis_tasks')
    
    def __init__(self, supplier_id, task_type='website_analysis', parameters=None):
        self.supplier_id = supplier_id
        self.task_type = task_type
        if parameters:
            self.parameters = json.dumps(parameters, ensure_ascii=False)
    
    def set_parameters(self, params):
        """设置任务参数"""
        self.parameters = json.dumps(params, ensure_ascii=False)
    
    def get_parameters(self):
        """获取任务参数"""
        if self.parameters:
            return json.loads(self.parameters)
        return {}
    
    def set_result(self, result):
        """设置任务结果"""
        self.result = json.dumps(result, ensure_ascii=False)
    
    def get_result(self):
        """获取任务结果"""
        if self.result:
            return json.loads(self.result)
        return None
    
    def start_task(self):
        """开始任务"""
        self.status = 'running'
        self.started_at = datetime.utcnow()
        self.progress = 0
        db.session.commit()
    
    def update_progress(self, progress, message=None):
        """更新任务进度"""
        self.progress = min(100, max(0, progress))
        if message:
            # 可以在这里添加进度消息字段
            pass
        db.session.commit()
    
    def complete_task(self, result):
        """完成任务"""
        self.status = 'completed'
        self.progress = 100
        self.completed_at = datetime.utcnow()
        self.set_result(result)
        db.session.commit()
    
    def fail_task(self, error_message):
        """任务失败"""
        self.status = 'failed'
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'supplier_id': self.supplier_id,
            'task_type': self.task_type,
            'status': self.status,
            'progress': self.progress,
            'parameters': self.get_parameters(),
            'result': self.get_result(),
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
        }
    
    @staticmethod
    def get_latest_task(supplier_id, task_type='website_analysis'):
        """获取供应商的最新分析任务"""
        return AnalysisTask.query.filter_by(
            supplier_id=supplier_id,
            task_type=task_type
        ).order_by(AnalysisTask.created_at.desc()).first()
    
    @staticmethod
    def cleanup_old_tasks(days=30):
        """清理旧任务（超过指定天数）"""
        from datetime import timedelta
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        old_tasks = AnalysisTask.query.filter(
            AnalysisTask.created_at < cutoff_date
        ).all()
        
        for task in old_tasks:
            db.session.delete(task)
        
        db.session.commit()
        return len(old_tasks) 