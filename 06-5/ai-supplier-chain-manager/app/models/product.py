from app import db
from datetime import datetime

class Product(db.Model):
    """产品模型"""
    __tablename__ = 'client_product'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    client_id = db.<PERSON>umn(db.<PERSON>teger, db.<PERSON><PERSON>('client_company.id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)
    model = db.Column(db.String(100))
    category = db.Column(db.String(100))
    description = db.Column(db.Text)
    lifecycle_stage = db.Column(db.Enum('Development', 'Production', 'Maintenance', 'End of Life'))
    annual_volume = db.Column(db.Integer)
    priority_level = db.Column(db.Enum('Low', 'Medium', 'High', 'Critical'), default='Medium')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'client_id': self.client_id,
            'name': self.name,
            'model': self.model,
            'category': self.category,
            'description': self.description,
            'lifecycle_stage': self.lifecycle_stage,
            'annual_volume': self.annual_volume,
            'priority_level': self.priority_level,
            'company_name': self.company.name if self.company else None
        }
    
    @staticmethod
    def get_by_company(company_id):
        """根据公司ID获取产品列表"""
        return Product.query.filter_by(client_id=company_id).all()
    
    @staticmethod
    def get_by_id(product_id):
        """根据产品ID获取产品"""
        return Product.query.get(product_id) 