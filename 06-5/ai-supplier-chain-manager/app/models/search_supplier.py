from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app import db
from datetime import datetime

class SearchSupplier(db.Model):
    __tablename__ = 'search_suppliers'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    supplier_name = Column(String(255), nullable=False)
    region = Column(String(100))
    category_id = Column(Integer, ForeignKey('product_categories.id'))
    matching_reason = Column(Text)
    website = Column(String(255))
    phone = Column(String(50))
    email = Column(String(100))
    certifications = Column(Text)
    notes = Column(Text)
    address = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    status = Column(Integer, default=1)
    
    # 关联到产品分类
    category = relationship("ProductCategory", back_populates="suppliers")
    
    def to_dict(self):
        """转换为字典格式"""
        # 获取评级信息
        rating_info = self.get_rating_summary()
        
        return {
            'id': self.id,
            'supplier_name': self.supplier_name,
            'region': self.region,
            'category_id': self.category_id,
            'matching_reason': self.matching_reason,
            'website': self.website,
            'phone': self.phone,
            'email': self.email,
            'certifications': self.certifications,
            'notes': self.notes,
            'address': self.address,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'status': self.status,
            'status_text': self.get_status_text(),
            'category': self.category.to_dict() if self.category else None,
            'rating_info': rating_info
        }
    
    def get_status_text(self):
        """获取状态文本"""
        status_map = {
            0: "验证正常",
            1: "验证pending", 
            2: "网站无效",
            3: "网站被标为黑名单",
            4: "产品不能做",
            5: "报价过高",
            99: "验证通过"
        }
        return status_map.get(self.status, "未知状态")
    
    def get_status_category(self):
        """获取状态分类"""
        if self.status in [0, 1]:
            return "待处理"
        elif self.status == 99:
            return "进行中"
        elif self.status in [2, 3, 4, 5]:
            return "已拒绝"
        else:
            return "未知"
    
    def get_or_create_rating(self):
        """获取或创建评级记录"""
        from app.models.supplier_rating import SupplierRating
        return SupplierRating.get_or_create_by_supplier_id(self.id)
    
    def get_rating_summary(self):
        """获取评级信息摘要"""
        from app.models.supplier_rating import SupplierRating
        
        rating = SupplierRating.query.filter_by(supplier_id=self.id).first()
        if not rating:
            return {
                'has_rating': False,
                'business_info_status': '未获取',
                'certifications_status': '未获取',
                'valid_certs_count': 0,
                'total_certs_count': 0
            }
        
        return {
            'has_rating': True,
            'business_info_status': rating.get_business_info_status(),
            'certifications_status': rating.get_certifications_status(),
            'valid_certs_count': rating.get_valid_certifications_count(),
            'total_certs_count': rating.get_total_certifications_count(),
            'last_updated': rating.evaluated_at.isoformat() if rating.evaluated_at else None
        }

class ProductCategory(db.Model):
    __tablename__ = 'product_categories'
    
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, nullable=False)  # 实际表中的字段
    category = Column(String(100))  # 实际表中的字段名是category，不是name
    prev = Column(Integer, default=0)  # 父级分类ID，0表示一级分类
    feature = Column(Text)  # 实际表中的字段
    
    # 关联到供应商
    suppliers = relationship("SearchSupplier", back_populates="category")
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.category,  # 为了保持API兼容性，将category字段映射为name
            'category': self.category,
            'product_id': self.product_id,
            'prev': self.prev,
            'feature': self.feature
        }
    
    def get_full_path(self, db_session):
        """获取完整的分类路径"""
        if self.prev == 0:
            return self.category
        else:
            parent = db_session.query(ProductCategory).filter_by(id=self.prev).first()
            if parent:
                return f"{parent.category} > {self.category}"
            return self.category 