"""
API请求响应的JSON Schema定义
"""

# 评分请求Schema
SCORE_REQUEST_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "score-request",
    "title": "评分请求",
    "type": "object",
    "required": ["supplier_id"],
    "properties": {
        "supplier_id": {
            "type": "integer",
            "minimum": 1,
            "description": "供应商ID"
        },
        "force_refresh": {
            "type": "boolean",
            "default": False,
            "description": "是否强制刷新评分"
        },
        "include_details": {
            "type": "boolean",
            "default": True,
            "description": "是否包含详细分析"
        }
    }
}

# 批量评分请求Schema
BATCH_SCORE_REQUEST_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "batch-score-request",
    "title": "批量评分请求",
    "type": "object",
    "required": ["supplier_ids"],
    "properties": {
        "supplier_ids": {
            "type": "array",
            "items": {
                "type": "integer",
                "minimum": 1
            },
            "minItems": 1,
            "maxItems": 100,
            "uniqueItems": True,
            "description": "供应商ID列表"
        },
        "force_refresh": {
            "type": "boolean",
            "default": False
        },
        "include_details": {
            "type": "boolean",
            "default": False
        }
    }
}

# API响应Schema
API_RESPONSE_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "api-response",
    "title": "API响应",
    "type": "object",
    "required": ["success"],
    "properties": {
        "success": {
            "type": "boolean",
            "description": "请求是否成功"
        },
        "data": {
            "type": ["object", "array", "null"],
            "description": "响应数据"
        },
        "message": {
            "type": ["string", "null"],
            "maxLength": 500,
            "description": "响应消息"
        },
        "error_code": {
            "type": ["string", "null"],
            "description": "错误代码"
        },
        "timestamp": {
            "type": "string",
            "format": "date-time",
            "description": "响应时间戳"
        }
    }
} 