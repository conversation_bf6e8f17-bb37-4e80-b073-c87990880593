"""
评分相关的JSON Schema定义
"""

# 维度评分Schema
DIMENSION_SCORES_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "dimension-scores",
    "title": "维度评分",
    "type": "object",
    "required": ["basic_strength", "certification", "feature_matching", "innovation"],
    "properties": {
        "basic_strength": {
            "type": "object",
            "required": ["total_score", "sub_scores"],
            "properties": {
                "total_score": {
                    "type": "number",
                    "minimum": 0,
                    "description": "总分"
                },
                "sub_scores": {
                    "type": "object",
                    "required": ["capital_score", "staff_score", "years_score", "status_score", "location_score"],
                    "properties": {
                        "capital_score": {"type": "number", "minimum": 0},
                        "staff_score": {"type": "number", "minimum": 0},
                        "years_score": {"type": "number", "minimum": 0},
                        "status_score": {"type": "number", "minimum": 0},
                        "location_score": {"type": "number", "minimum": 0}
                    }
                },
                "analysis": {"type": "string", "maxLength": 1000}
            }
        },
        "certification": {
            "type": "object",
            "required": ["total_score", "sub_scores"],
            "properties": {
                "total_score": {"type": "number", "minimum": 0},
                "sub_scores": {
                    "type": "object",
                    "required": ["valid_count_score", "quality_score"],
                    "properties": {
                        "valid_count_score": {"type": "number", "minimum": 0},
                        "quality_score": {"type": "number", "minimum": 0}
                    }
                },
                "analysis": {"type": "string", "maxLength": 1000}
            }
        },
        "feature_matching": {
            "type": "object",
            "required": ["total_score", "sub_scores"],
            "properties": {
                "total_score": {"type": "number", "minimum": 0},
                "sub_scores": {
                    "type": "object",
                    "required": ["feature_business_match", "feature_products_match", "business_products_consistency", "keyword_match_score"],
                    "properties": {
                        "feature_business_match": {"type": "number", "minimum": 0},
                        "feature_products_match": {"type": "number", "minimum": 0},
                        "business_products_consistency": {"type": "number", "minimum": 0},
                        "keyword_match_score": {"type": "number", "minimum": 0}
                    }
                },
                "analysis": {"type": "string", "maxLength": 1000},
                "gpt_analysis": {
                    "type": "object",
                    "required": ["technical_capability_match", "manufacturing_process_match", "material_handling_match", "quality_standard_match", "overall_business_match"],
                    "properties": {
                        "technical_capability_match": {
                            "type": "object",
                            "required": ["score", "reason"],
                            "properties": {
                                "score": {"type": "number", "minimum": 0},
                                "reason": {"type": "string", "minLength": 1, "maxLength": 500}
                            }
                        },
                        "manufacturing_process_match": {
                            "type": "object",
                            "required": ["score", "reason"],
                            "properties": {
                                "score": {"type": "number", "minimum": 0},
                                "reason": {"type": "string", "minLength": 1, "maxLength": 500}
                            }
                        },
                        "material_handling_match": {
                            "type": "object",
                            "required": ["score", "reason"],
                            "properties": {
                                "score": {"type": "number", "minimum": 0},
                                "reason": {"type": "string", "minLength": 1, "maxLength": 500}
                            }
                        },
                        "quality_standard_match": {
                            "type": "object",
                            "required": ["score", "reason"],
                            "properties": {
                                "score": {"type": "number", "minimum": 0},
                                "reason": {"type": "string", "minLength": 1, "maxLength": 500}
                            }
                        },
                        "overall_business_match": {
                            "type": "object",
                            "required": ["score", "reason"],
                            "properties": {
                                "score": {"type": "number", "minimum": 0},
                                "reason": {"type": "string", "minLength": 1, "maxLength": 500}
                            }
                        },
                        "weighted_average": {"type": "number", "minimum": 0},
                        "summary": {"type": "string", "maxLength": 1000}
                    }
                }
            }
        },
        "innovation": {
            "type": "object",
            "required": ["total_score", "sub_scores"],
            "properties": {
                "total_score": {"type": "number", "minimum": 0},
                "sub_scores": {
                    "type": "object",
                    "oneOf": [
                        {
                            "description": "新版创新能力评分结构",
                            "required": ["patent_score", "website_innovation_score", "business_scope_score"],
                            "properties": {
                                "patent_score": {"type": "number", "minimum": 0},
                                "website_innovation_score": {"type": "number", "minimum": 0},
                                "business_scope_score": {"type": "number", "minimum": 0}
                            }
                        },
                        {
                            "description": "旧版专利评分结构（兼容性）",
                            "required": ["patent_count_score", "patent_quality_score"],
                            "properties": {
                                "patent_count_score": {"type": "number", "minimum": 0},
                                "patent_quality_score": {"type": "number", "minimum": 0}
                            }
                        }
                    ]
                },
                "analysis": {"type": "string", "maxLength": 1000}
            }
        }
    }
}

# 完整评分结果Schema
EVALUATION_RESULT_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "evaluation-result",
    "title": "完整评分结果",
    "type": "object",
    "required": ["supplier_info", "evaluation_summary", "dimension_scores"],
    "properties": {
        "supplier_info": {
            "type": "object",
            "required": ["id", "name", "category", "region"],
            "properties": {
                "id": {"type": "integer", "minimum": 1},
                "name": {"type": "string", "minLength": 1, "maxLength": 255},
                "category": {"type": "string", "minLength": 1, "maxLength": 100},
                "region": {"type": ["string", "null"], "maxLength": 100}
            }
        },
        "evaluation_summary": {
            "type": "object",
            "required": ["final_score", "grade", "level", "evaluation_date", "version"],
            "properties": {
                "final_score": {"type": "number", "minimum": 0},
                "grade": {"type": "string", "enum": ["A+", "A", "B+", "B", "C", "D"]},
                "level": {"type": "string", "enum": ["优秀", "良好", "中上", "中等", "一般", "较差"]},
                "evaluation_date": {"type": "string", "format": "date-time"},
                "version": {"type": "string", "pattern": "^\\d+\\.\\d+"}
            }
        },
        "dimension_scores": DIMENSION_SCORES_SCHEMA,
        "weight_distribution": {
            "type": "object",
            "required": ["basic_strength", "certification", "feature_matching", "innovation"],
            "properties": {
                "basic_strength": {"type": "number", "minimum": 0, "maximum": 1},
                "certification": {"type": "number", "minimum": 0, "maximum": 1},
                "feature_matching": {"type": "number", "minimum": 0, "maximum": 1},
                "innovation": {"type": "number", "minimum": 0, "maximum": 1}
            }
        },
        "recommendations": {
            "type": "array",
            "items": {"type": "string", "minLength": 1, "maxLength": 200}
        },
        "risk_warnings": {
            "type": "array",
            "items": {"type": "string", "minLength": 1, "maxLength": 200}
        }
    }
} 