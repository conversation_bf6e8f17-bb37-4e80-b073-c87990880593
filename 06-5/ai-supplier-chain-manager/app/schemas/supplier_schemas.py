"""
供应商相关的JSON Schema定义
"""

# 供应商基础信息Schema
SUPPLIER_BASIC_INFO_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "supplier-basic-info",
    "title": "供应商基础信息",
    "type": "object",
    "required": ["id", "supplier_name", "category_id"],
    "properties": {
        "id": {
            "type": "integer",
            "minimum": 1,
            "description": "供应商唯一标识"
        },
        "supplier_name": {
            "type": "string",
            "minLength": 1,
            "maxLength": 255,
            "description": "供应商名称"
        },
        "region": {
            "type": ["string", "null"],
            "maxLength": 100,
            "description": "所在地区"
        },
        "category_id": {
            "type": "integer",
            "minimum": 1,
            "description": "产品分类ID"
        },
        "matching_reason": {
            "type": ["string", "null"],
            "description": "匹配原因"
        },
        "website": {
            "type": ["string", "null"],
            "maxLength": 255,
            "description": "网站地址"
        },
        "phone": {
            "type": ["string", "null"],
            "maxLength": 50,
            "description": "联系电话"
        },
        "email": {
            "type": ["string", "null"],
            "maxLength": 100,
            "description": "邮箱地址"
        },
        "certifications": {
            "type": ["string", "null"],
            "maxLength": 255,
            "description": "认证信息"
        },
        "status": {
            "type": "integer",
            "description": "状态码：0-验证正常, 1-验证pending, 2-网站无效, 3-黑名单, 4-产品不能做, 5-报价过高, 99-验证通过"
        }
    }
}

# 企业信息Schema
BUSINESS_INFO_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "business-info",
    "title": "企业信息",
    "type": "object",
    "required": ["company_name", "data_source"],
    "properties": {
        "company_name": {
            "type": "string",
            "minLength": 1,
            "maxLength": 255,
            "description": "企业名称"
        },
        "registered_capital": {
            "type": ["string", "null"],
            "description": "注册资本"
        },
        "staff_size": {
            "type": ["string", "null"],
            "description": "员工规模"
        },
        "registration_date": {
            "type": ["integer", "null"],
            "minimum": 0,
            "description": "注册日期时间戳"
        },
        "company_status": {
            "type": ["string", "null"],
            "description": "企业状态"
        },
        "business_scope": {
            "type": ["string", "null"],
            "description": "经营范围"
        },
        "data_source": {
            "type": "string",
            "description": "数据来源"
        }
    }
}

# 认证信息Schema
CERTIFICATIONS_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "certifications",
    "title": "认证信息",
    "type": "object",
    "required": ["data_source", "total_count", "valid_count"],
    "properties": {
        "data_source": {
            "type": "string",
            "description": "数据来源"
        },
        "total_count": {
            "type": "integer",
            "minimum": 0,
            "description": "认证总数"
        },
        "valid_count": {
            "type": "integer",
            "minimum": 0,
            "description": "有效认证数"
        },
        "certifications": {
            "type": "array",
            "items": {
                "type": "object",
                "required": ["name", "type", "status"],
                "properties": {
                    "name": {
                        "type": "string",
                        "minLength": 1,
                        "description": "认证名称"
                    },
                    "type": {
                        "type": "string",
                        "description": "认证类型"
                    },
                    "status": {
                        "type": "string",
                        "description": "认证状态"
                    }
                }
            },
            "description": "认证详情列表"
        }
    }
}

# 主要产品Schema
MAIN_PRODUCTS_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "main-products",
    "title": "主要产品",
    "type": "array",
    "items": {
        "type": "object",
        "required": ["name", "description", "importance"],
        "properties": {
            "name": {
                "type": "string",
                "minLength": 1,
                "maxLength": 100,
                "description": "产品名称"
            },
            "description": {
                "type": "string",
                "minLength": 1,
                "maxLength": 1000,
                "description": "产品描述"
            },
            "importance": {
                "type": "string",
                "description": "重要程度"
            }
        }
    }
}

# 专利信息Schema
PATENTS_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "patents",
    "title": "专利信息",
    "type": "array",
    "items": {
        "type": "object",
        "required": ["name", "type", "number"],
        "properties": {
            "name": {
                "type": "string",
                "minLength": 1,
                "maxLength": 200,
                "description": "专利名称"
            },
            "type": {
                "type": "string",
                "description": "专利类型"
            },
            "number": {
                "type": "string",
                "description": "专利号"
            },
            "status": {
                "type": ["string", "null"],
                "description": "专利状态"
            }
        }
    }
} 