from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_migrate import Migrate
import os
from datetime import timedelta

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app.config.from_object(f'app.config.{config_name.title()}Config')
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    
    # CORS配置 - 允许前端访问
    CORS(app, 
     supports_credentials=True,  # 这个要设置为True
     origins=[
         # 本地开发环境
         'http://localhost:3000',
         'http://localhost:3001',
         'http://localhost:3002',
         'http://localhost:5173',  # Vite默认端口
         'http://127.0.0.1:3000',
         'http://127.0.0.1:3001',
         'http://127.0.0.1:3002',
         'http://127.0.0.1:5173',
         'http://*************:3000',
         'http://0.0.0.0:3000',
         # Docker环境端口
         'http://localhost:9003',
         'http://127.0.0.1:9003',
         'http://localhost:9004',
         'http://127.0.0.1:9004',
         # 公网IP
         'http://*************:9003',
         'http://*************:9004',
         # 容器内网络
         'http://frontend:80',
         'http://nginx:80'
     ],
     allow_headers=['Content-Type', 'Authorization', 'X-Requested-With'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     # 添加这些配置
     expose_headers=['Content-Type', 'Authorization'],
     max_age=86400  # 预检请求缓存时间
    )
    
    # Session配置
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'scm-supplier-finder-secret-key')
    app.config['SESSION_PERMANENT'] = False
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
    
    # 添加健康检查路由
    @app.route('/api/')
    @app.route('/api/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'message': '供应商链管理系统API服务正常运行',
            'version': '1.0.0'
        })
    
    # 注册蓝图
    from app.api import suppliers
    app.register_blueprint(suppliers.bp, url_prefix='/api')
    
    from app.api.components import components_bp
    app.register_blueprint(components_bp)

    # 注册供应商评分API蓝图
    from app.api.supplier_scoring import supplier_scoring_bp
    app.register_blueprint(supplier_scoring_bp)
    
        
    # 注册模型（确保迁移能识别）
    from app.models import user, company, product, search_supplier, supplier_rating, component
    
    return app 