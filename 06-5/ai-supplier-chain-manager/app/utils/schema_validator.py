"""
JSON Schema校验工具
"""

import json
import jsonschema
from jsonschema import validate, ValidationError
from functools import wraps
from flask import request, jsonify
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class SchemaValidator:
    """JSON Schema校验器"""
    
    def __init__(self):
        self.cache = {}
    
    def validate_data(self, data, schema):
        """校验数据"""
        try:
            validate(instance=data, schema=schema)
            return {"valid": True, "errors": []}
        except ValidationError as e:
            return {
                "valid": False,
                "errors": [{
                    "path": " -> ".join(str(p) for p in e.path),
                    "message": e.message,
                    "invalid_value": e.instance
                }]
            }
    
    def validate_with_details(self, data, schema):
        """详细校验，返回所有错误"""
        validator = jsonschema.Draft7Validator(schema)
        errors = []
        
        for error in validator.iter_errors(data):
            errors.append({
                "path": " -> ".join(str(p) for p in error.path),
                "message": error.message,
                "invalid_value": error.instance,
                "schema_path": " -> ".join(str(p) for p in error.schema_path)
            })
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "error_count": len(errors)
        }

# 全局校验器实例
validator = SchemaValidator()

def validate_json(schema):
    """JSON Schema校验装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                data = request.get_json()
                if not data:
                    return jsonify({
                        "success": False,
                        "message": "请求体不能为空",
                        "error_code": "EMPTY_REQUEST_BODY",
                        "timestamp": datetime.now().isoformat()
                    }), 400
                
                validation_result = validator.validate_data(data, schema)
                if not validation_result["valid"]:
                    return jsonify({
                        "success": False,
                        "message": f"请求参数校验失败: {validation_result['errors'][0]['message']}",
                        "error_code": "VALIDATION_ERROR",
                        "errors": validation_result["errors"],
                        "timestamp": datetime.now().isoformat()
                    }), 400
                
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Schema validation error: {str(e)}")
                return jsonify({
                    "success": False,
                    "message": f"服务器内部错误: {str(e)}",
                    "error_code": "INTERNAL_ERROR",
                    "timestamp": datetime.now().isoformat()
                }), 500
        return wrapper
    return decorator

def validate_response(schema):
    """响应数据校验装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                response = func(*args, **kwargs)
                
                # 如果是tuple，说明包含状态码
                if isinstance(response, tuple):
                    response_data, status_code = response
                else:
                    response_data = response
                    status_code = 200
                
                # 校验响应数据
                if hasattr(response_data, 'get_json'):
                    json_data = response_data.get_json()
                elif isinstance(response_data, dict):
                    json_data = response_data
                else:
                    return response
                
                validation_result = validator.validate_data(json_data, schema)
                if not validation_result["valid"]:
                    logger.warning(f"Response validation failed: {validation_result['errors']}")
                
                return response
            except Exception as e:
                logger.error(f"Response validation error: {str(e)}")
                return response
        return wrapper
    return decorator

def validate_supplier_data(supplier_data):
    """校验供应商数据"""
    from app.schemas import SUPPLIER_BASIC_INFO_SCHEMA
    return validator.validate_with_details(supplier_data, SUPPLIER_BASIC_INFO_SCHEMA)

def validate_business_info(business_info):
    """校验企业信息"""
    from app.schemas import BUSINESS_INFO_SCHEMA
    return validator.validate_with_details(business_info, BUSINESS_INFO_SCHEMA)

def validate_certifications(certifications):
    """校验认证信息"""
    from app.schemas import CERTIFICATIONS_SCHEMA
    return validator.validate_with_details(certifications, CERTIFICATIONS_SCHEMA)

def validate_evaluation_result(evaluation_result):
    """校验评分结果"""
    from app.schemas import EVALUATION_RESULT_SCHEMA
    return validator.validate_with_details(evaluation_result, EVALUATION_RESULT_SCHEMA) 