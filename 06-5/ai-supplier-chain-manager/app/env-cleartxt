# Rename this file to .env and fill in real credentials.
DEBUG=true

OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_BASE_URL=http://*************:8003/v1

ANTHROPIC_API_KEY = ************************************************************************************************************

# OSS
OSS_ACCESS_KEY_ID=LTAI5tHgiRXgh6QcxxqBtyMg
OSS_ACCESS_KEY_SECRET=******************************
OSS_BUCKET_NAME=yj-center
OSS_ENDPOINT=http://oss-cn-shanghai.aliyuncs.com

# Redis
#REDIS_URL=redis://:video_app_prod:video_app@<EMAIL>:6379/11

# 数据库配置
MYSQL_HOST=rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com
MYSQL_PORT=3306
MYSQL_DATABASE=procurement_system
MYSQL_USER=yj_app
MYSQL_PASSWORD=4iLe5fifhMqOo9Ne
MYSQL_URL=mysql+pymysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}


# 向量数据库配置
MILVUS_URI=http://*************:19530
MILVUS_TOKEN=yjxc:yjxc@123
VECTOR_DB_NAME=yj_center

# MongoDB配置
MONGODB_HOST=*************
MONGODB_PORT=27017
MONGODB_USER=admin
MONGODB_PASSWORD=yjxc@2022
MONGODB_URL=mongodb://${MONGODB_USER}:${MONGODB_PASSWORD}@${MONGODB_HOST}:${MONGODB_PORT}/


# System
TENANT_ID=
ACCOUNT_ID=

# 阿里云消息队列配置
ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5t9efRTsNEBxE8yDJyLh
ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************
ALIBABA_MQ_INSTANCE_ID=MQ_INST_1146510734876082_BZTSgcPi
ALIBABA_MQ_ENDPOINT=ons.cn-shanghai.aliyuncs.com
