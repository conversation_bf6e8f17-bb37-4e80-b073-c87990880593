from flask import jsonify
from app.api import bp

@bp.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'message': '请求的资源不存在'
    }), 404

@bp.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'message': '服务器内部错误'
    }), 500

@bp.errorhandler(400)
def bad_request(error):
    """400错误处理"""
    return jsonify({
        'success': False,
        'message': '请求参数错误'
    }), 400

@bp.errorhandler(401)
def unauthorized(error):
    """401错误处理"""
    return jsonify({
        'success': False,
        'message': '未授权访问'
    }), 401 

def handle_api_error(error):
    """通用API错误处理函数"""
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"API Error: {str(error)}")
    
    # 根据错误类型返回不同的响应
    if hasattr(error, 'code'):
        if error.code == 404:
            return jsonify({
                'success': False,
                'message': '请求的资源不存在',
                'error_code': 'NOT_FOUND'
            }), 404
        elif error.code == 400:
            return jsonify({
                'success': False,
                'message': '请求参数错误',
                'error_code': 'BAD_REQUEST'
            }), 400
        elif error.code == 401:
            return jsonify({
                'success': False,
                'message': '未授权访问',
                'error_code': 'UNAUTHORIZED'
            }), 401
    
    # 默认返回500错误
    return jsonify({
        'success': False,
        'message': '服务器内部错误',
        'error_code': 'INTERNAL_ERROR'
    }), 500