from flask import request, jsonify, session
from app.api import bp
from app.models.product import Product

@bp.route('/products', methods=['GET'])
def get_products():
    """获取当前用户可访问的产品列表"""
    try:
        user_id = session.get('user_id')
        company_id = session.get('company_id')
        
        if not user_id or not company_id:
            return jsonify({
                'success': False,
                'message': '未登录'
            }), 401
        
        products = Product.get_by_company(company_id)
        products_data = [product.to_dict() for product in products]
        
        return jsonify({
            'success': True,
            'data': products_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取产品列表失败：{str(e)}'
        }), 500

@bp.route('/products/switch', methods=['POST'])
def switch_product():
    """切换当前产品"""
    try:
        user_id = session.get('user_id')
        company_id = session.get('company_id')
        
        if not user_id or not company_id:
            return jsonify({
                'success': False,
                'message': '未登录'
            }), 401
        
        data = request.get_json()
        product_id = data.get('product_id')
        
        if not product_id:
            return jsonify({
                'success': False,
                'message': '产品ID不能为空'
            }), 400
        
        # 验证产品是否属于当前用户的公司
        product = Product.query.filter_by(id=product_id, client_id=company_id).first()
        if not product:
            return jsonify({
                'success': False,
                'message': '产品不存在或无权限访问'
            }), 404
        
        # 更新会话中的当前产品
        session['current_product_id'] = product_id
        
        return jsonify({
            'success': True,
            'message': '切换产品成功',
            'data': product.to_dict()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'切换产品失败：{str(e)}'
        }), 500 