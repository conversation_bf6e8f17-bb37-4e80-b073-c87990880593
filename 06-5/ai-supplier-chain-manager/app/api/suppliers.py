from flask import Blueprint, request, jsonify, session
from sqlalchemy import text, and_, or_
from sqlalchemy.orm import joinedload
from app import db
from app.models import SearchSupplier
from app.models.search_supplier import ProductCategory
from app.api import bp
import logging
import json
from datetime import datetime
from app.services.website_analysis_service import WebsiteAnalysisService

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@bp.route('/suppliers', methods=['GET'])
def get_suppliers():
    """获取供应商列表"""
    try:
        logger.debug("开始处理供应商列表请求")
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status_category = request.args.get('status_category', '')  # 待处理、进行中、已拒绝
        category_id = request.args.get('category_id', '', type=str)
        search = request.args.get('search', '')
        
        logger.debug(f"查询参数: page={page}, per_page={per_page}, status_category={status_category}, category_id={category_id}, search={search}")
        
        # 构建查询
        logger.debug("开始构建数据库查询")
        query = db.session.query(SearchSupplier).options(joinedload(SearchSupplier.category))
        logger.debug("基础查询构建完成")
        
        # 状态分类过滤
        if status_category == '待处理':
            logger.debug("应用待处理状态过滤")
            query = query.filter(SearchSupplier.status.in_([0, 1]))
        elif status_category == '进行中':
            logger.debug("应用进行中状态过滤")
            query = query.filter(SearchSupplier.status == 99)
        elif status_category == '已拒绝':
            logger.debug("应用已拒绝状态过滤")
            query = query.filter(SearchSupplier.status.in_([2, 3, 4, 5]))
        
        # 分类过滤
        if category_id:
            logger.debug(f"应用分类过滤: category_id={category_id}")
            query = query.filter(SearchSupplier.category_id == category_id)
        
        # 搜索过滤
        if search:
            logger.debug(f"应用搜索过滤: search={search}")
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    SearchSupplier.supplier_name.like(search_term),
                    SearchSupplier.region.like(search_term),
                    SearchSupplier.website.like(search_term),
                    SearchSupplier.email.like(search_term)
                )
            )
        
        logger.debug("开始执行分页查询")
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        logger.debug(f"分页查询完成，总数: {pagination.total}, 当前页: {pagination.page}")
        
        # 转换数据格式，包含完整分类路径
        logger.debug("开始转换数据格式")
        suppliers_data = []
        for i, supplier in enumerate(pagination.items):
            logger.debug(f"处理第{i+1}个供应商: {supplier.supplier_name}")
            supplier_dict = supplier.to_dict()
            if supplier.category:
                logger.debug(f"获取供应商分类路径: {supplier.category.category}")
                supplier_dict['category_full_path'] = supplier.category.get_full_path(db.session)
            else:
                logger.debug("供应商没有分类信息")
                supplier_dict['category_full_path'] = None
            suppliers_data.append(supplier_dict)
        
        logger.debug(f"数据转换完成，返回{len(suppliers_data)}条记录")
        
        return jsonify({
            'success': True,
            'data': {
                'suppliers': suppliers_data,
                'pagination': {
                    'page': pagination.page,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取供应商列表失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取供应商列表失败: {str(e)}'
        }), 500

@bp.route('/suppliers/<int:supplier_id>', methods=['GET'])
def get_supplier_detail(supplier_id):
    """获取供应商详情"""
    try:
        logger.debug(f"获取供应商详情: supplier_id={supplier_id}")
        
        supplier = db.session.query(SearchSupplier).options(
            joinedload(SearchSupplier.category)
        ).filter_by(id=supplier_id).first()
        
        if not supplier:
            logger.warning(f"供应商不存在: supplier_id={supplier_id}")
            return jsonify({
                'success': False,
                'message': '供应商不存在'
            }), 404
        
        logger.debug(f"找到供应商: {supplier.supplier_name}")
        supplier_dict = supplier.to_dict()
        if supplier.category:
            supplier_dict['category_full_path'] = supplier.category.get_full_path(db.session)
        
        logger.debug("供应商详情获取成功")
        return jsonify({
            'success': True,
            'data': supplier_dict
        })
        
    except Exception as e:
        logger.error(f"获取供应商详情失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取供应商详情失败: {str(e)}'
        }), 500

@bp.route('/suppliers/<int:supplier_id>/status', methods=['PUT'])
def update_supplier_status(supplier_id):
    """更新供应商状态"""
    try:
        logger.debug(f"更新供应商状态: supplier_id={supplier_id}")
        
        data = request.get_json()
        new_status = data.get('status')
        logger.debug(f"新状态: {new_status}")
        
        if new_status is None:
            logger.warning("状态参数为空")
            return jsonify({
                'success': False,
                'message': '状态参数不能为空'
            }), 400
        
        # 验证状态值
        valid_statuses = [0, 1, 2, 3, 4, 5, 99]
        if new_status not in valid_statuses:
            logger.warning(f"无效的状态值: {new_status}")
            return jsonify({
                'success': False,
                'message': '无效的状态值'
            }), 400
        
        supplier = db.session.query(SearchSupplier).filter_by(id=supplier_id).first()
        if not supplier:
            logger.warning(f"供应商不存在: supplier_id={supplier_id}")
            return jsonify({
                'success': False,
                'message': '供应商不存在'
            }), 404
        
        old_status = supplier.status
        supplier.status = new_status
        db.session.commit()
        logger.debug(f"状态更新成功: {old_status} -> {new_status}")
        
        return jsonify({
            'success': True,
            'message': '状态更新成功',
            'data': {
                'status': new_status,
                'status_text': supplier.get_status_text(),
                'status_category': supplier.get_status_category()
            }
        })
        
    except Exception as e:
        logger.error(f"更新状态失败: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新状态失败: {str(e)}'
        }), 500

@bp.route('/suppliers/<int:supplier_id>', methods=['PUT'])
def update_supplier(supplier_id):
    """更新供应商信息"""
    try:
        logger.debug(f"更新供应商信息: supplier_id={supplier_id}")
        
        data = request.get_json()
        logger.debug(f"更新数据: {data}")
        
        supplier = db.session.query(SearchSupplier).filter_by(id=supplier_id).first()
        if not supplier:
            logger.warning(f"供应商不存在: supplier_id={supplier_id}")
            return jsonify({
                'success': False,
                'message': '供应商不存在'
            }), 404
        
        # 更新字段
        updatable_fields = [
            'supplier_name', 'region', 'website', 'phone', 'email', 
            'certifications', 'notes', 'address', 'matching_reason'
        ]
        
        updated_fields = []
        for field in updatable_fields:
            if field in data:
                old_value = getattr(supplier, field)
                new_value = data[field]
                setattr(supplier, field, new_value)
                updated_fields.append(f"{field}: {old_value} -> {new_value}")
        
        logger.debug(f"更新的字段: {updated_fields}")
        
        db.session.commit()
        logger.debug("供应商信息更新成功")
        
        return jsonify({
            'success': True,
            'message': '供应商信息更新成功',
            'data': supplier.to_dict()
        })
        
    except Exception as e:
        logger.error(f"更新供应商信息失败: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新供应商信息失败: {str(e)}'
        }), 500

@bp.route('/categories', methods=['GET'])
def get_categories():
    """获取产品分类列表"""
    try:
        logger.debug("获取产品分类列表")
        
        # 获取所有分类
        categories = db.session.query(ProductCategory).all()
        logger.debug(f"查询到{len(categories)}个分类")
        
        # 构建分类树
        category_tree = []
        category_map = {}
        
        # 先创建所有分类的映射
        for category in categories:
            category_dict = category.to_dict()
            category_dict['children'] = []
            category_map[category.id] = category_dict
        
        logger.debug("分类映射创建完成")
        
        # 构建树形结构
        for category in categories:
            if category.prev == 0:  # 一级分类
                category_tree.append(category_map[category.id])
            else:  # 二级分类
                if category.prev in category_map:
                    category_map[category.prev]['children'].append(category_map[category.id])
        
        logger.debug(f"分类树构建完成，一级分类数量: {len(category_tree)}")
        
        return jsonify({
            'success': True,
            'data': category_tree
        })
        
    except Exception as e:
        logger.error(f"获取分类列表失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取分类列表失败: {str(e)}'
        }), 500

@bp.route('/suppliers/stats', methods=['GET'])
def get_supplier_stats():
    """获取供应商统计信息"""
    try:
        logger.debug("获取供应商统计信息")
        
        # 总数统计
        total_count = db.session.query(SearchSupplier).count()
        logger.debug(f"总供应商数量: {total_count}")
        
        # 状态统计
        status_stats = {}
        status_counts = db.session.query(
            SearchSupplier.status,
            db.func.count(SearchSupplier.id)
        ).group_by(SearchSupplier.status).all()
        
        for status, count in status_counts:
            status_stats[status] = count
            logger.debug(f"状态{status}: {count}个")
        
        # 状态分类统计
        pending_count = status_stats.get(0, 0) + status_stats.get(1, 0)  # 待处理
        processing_count = status_stats.get(99, 0)  # 进行中
        rejected_count = (status_stats.get(2, 0) + status_stats.get(3, 0) + 
                         status_stats.get(4, 0) + status_stats.get(5, 0))  # 已拒绝
        
        logger.debug(f"状态分类统计 - 待处理: {pending_count}, 进行中: {processing_count}, 已拒绝: {rejected_count}")
        
        return jsonify({
            'success': True,
            'data': {
                'total': total_count,
                'status_categories': {
                    '待处理': pending_count,
                    '进行中': processing_count,
                    '已拒绝': rejected_count
                },
                'status_details': status_stats
            }
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取统计信息失败: {str(e)}'
        }), 500

@bp.route('/suppliers', methods=['POST'])
def create_supplier():
    """创建新供应商"""
    try:
        logger.debug("创建新供应商")
        
        data = request.get_json()
        logger.debug(f"创建数据: {data}")
        
        # 验证必填字段
        if not data.get('supplier_name'):
            logger.warning("供应商名称为空")
            return jsonify({
                'success': False,
                'message': '供应商名称不能为空'
            }), 400
        
        # 检查供应商名称是否已存在
        existing_supplier = db.session.query(SearchSupplier).filter_by(
            supplier_name=data['supplier_name']
        ).first()
        
        if existing_supplier:
            logger.warning(f"供应商名称已存在: {data['supplier_name']}")
            return jsonify({
                'success': False,
                'message': '供应商名称已存在'
            }), 400
        
        # 创建新供应商
        supplier = SearchSupplier(
            supplier_name=data['supplier_name'],
            region=data.get('region', ''),
            category_id=data.get('category_id'),
            matching_reason=data.get('matching_reason', ''),
            website=data.get('website', ''),
            phone=data.get('phone', ''),
            email=data.get('email', ''),
            certifications=data.get('certifications', ''),
            notes=data.get('notes', ''),
            address=data.get('address', ''),
            status=data.get('status', 1)  # 默认状态为验证pending
        )
        
        db.session.add(supplier)
        db.session.commit()
        
        logger.debug(f"供应商创建成功: ID={supplier.id}, 名称={supplier.supplier_name}")
        
        # 获取完整的供应商信息（包含分类）
        supplier_with_category = db.session.query(SearchSupplier).options(
            joinedload(SearchSupplier.category)
        ).filter_by(id=supplier.id).first()
        
        supplier_dict = supplier_with_category.to_dict()
        if supplier_with_category.category:
            supplier_dict['category_full_path'] = supplier_with_category.category.get_full_path(db.session)
        
        return jsonify({
            'success': True,
            'message': '供应商创建成功',
            'data': supplier_dict
        })
        
    except Exception as e:
        logger.error(f"创建供应商失败: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建供应商失败: {str(e)}'
        }), 500

@bp.route('/suppliers/<int:supplier_id>/fetch-info', methods=['POST'])
def fetch_supplier_info(supplier_id):
    """从天眼查获取供应商信息"""
    try:
        logger.debug(f"开始获取供应商信息: supplier_id={supplier_id}")
        
        # 获取供应商
        supplier = db.session.query(SearchSupplier).filter_by(id=supplier_id).first()
        if not supplier:
            return jsonify({
                'success': False,
                'message': '供应商不存在'
            }), 404
        
        # 获取或创建评级记录
        rating = supplier.get_or_create_rating()
        
        # 导入天眼查服务
        from app.services.tianyancha_service import TianyanchaService
        tianyancha = TianyanchaService()
        
        # 获取企业基本信息
        logger.info(f"正在获取企业基本信息: {supplier.supplier_name}")
        business_info = tianyancha.get_company_basic_info(supplier.supplier_name)
        
        # 获取认证信息
        logger.info(f"正在获取认证信息: {supplier.supplier_name}")
        certifications = tianyancha.get_company_certifications(supplier.supplier_name)
        
        # 更新评级记录
        business_updated = rating.update_business_info(business_info)
        cert_updated = rating.update_certifications(certifications)
        
        if business_updated or cert_updated:
            db.session.commit()
            logger.info(f"供应商信息更新成功: {supplier.supplier_name}")
        
        return jsonify({
            'success': True,
            'message': '信息获取完成',
            'data': {
                'business_info': business_info,
                'certifications': certifications,
                'business_updated': business_updated,
                'cert_updated': cert_updated
            }
        })
        
    except Exception as e:
        logger.error(f"获取供应商信息失败: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'获取信息失败: {str(e)}'
        }), 500

@bp.route('/suppliers/<int:supplier_id>/rating', methods=['GET'])
def get_supplier_rating(supplier_id):
    """获取供应商评级信息"""
    try:
        logger.debug(f"获取供应商评级信息: supplier_id={supplier_id}")
        
        # 获取供应商
        supplier = db.session.query(SearchSupplier).filter_by(id=supplier_id).first()
        if not supplier:
            return jsonify({
                'success': False,
                'message': '供应商不存在'
            }), 404
        
        # 获取评级记录
        from app.models.supplier_rating import SupplierRating
        rating = SupplierRating.query.filter_by(supplier_id=supplier_id).first()
        
        if not rating:
            return jsonify({
                'success': True,
                'data': {
                    'has_rating': False,
                    'supplier_name': supplier.supplier_name
                }
            })
        
        # *** 修改：包含dimension_scores信息 ***
        rating_data = rating.to_dict()
        
        # 添加评分状态信息
        has_scores = rating.dimension_scores and len(rating.dimension_scores) > 0
        rating_data['has_dimension_scores'] = has_scores
        
        if has_scores:
            # 计算最终得分
            from app.services.supplier_scoring_service import SupplierScoringService
            scoring_service = SupplierScoringService()
            final_score = scoring_service._calculate_final_score_from_dimensions(rating.dimension_scores)
            rating_data['final_score'] = final_score
            rating_data['grade'] = scoring_service._get_grade_from_score(final_score)
        
        return jsonify({
            'success': True,
            'data': {
                'has_rating': True,
                'supplier_name': supplier.supplier_name,
                'rating': rating_data
            }
        })
        
    except Exception as e:
        logger.error(f"获取评级信息失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取评级信息失败: {str(e)}'
        }), 500

@bp.route('/suppliers/batch-fetch-info', methods=['POST'])
def batch_fetch_supplier_info():
    """批量获取供应商信息"""
    try:
        data = request.get_json()
        supplier_ids = data.get('supplier_ids', [])
        
        if not supplier_ids:
            return jsonify({
                'success': False,
                'message': '供应商ID列表不能为空'
            }), 400
        
        logger.info(f"开始批量获取供应商信息: {len(supplier_ids)}个供应商")
        
        # 导入天眼查服务
        from app.services.tianyancha_service import TianyanchaService
        tianyancha = TianyanchaService()
        
        results = []
        success_count = 0
        
        for supplier_id in supplier_ids:
            try:
                # 获取供应商
                supplier = db.session.query(SearchSupplier).filter_by(id=supplier_id).first()
                if not supplier:
                    results.append({
                        'supplier_id': supplier_id,
                        'success': False,
                        'message': '供应商不存在'
                    })
                    continue
                
                # 获取或创建评级记录
                rating = supplier.get_or_create_rating()
                
                # 获取企业信息
                business_info = tianyancha.get_company_basic_info(supplier.supplier_name)
                certifications = tianyancha.get_company_certifications(supplier.supplier_name)
                
                # 更新评级记录
                business_updated = rating.update_business_info(business_info)
                cert_updated = rating.update_certifications(certifications)
                
                if business_updated or cert_updated:
                    db.session.commit()
                    success_count += 1
                
                results.append({
                    'supplier_id': supplier_id,
                    'supplier_name': supplier.supplier_name,
                    'success': True,
                    'business_updated': business_updated,
                    'cert_updated': cert_updated
                })
                
                # 添加延迟避免API限制
                import time
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"处理供应商{supplier_id}失败: {str(e)}")
                results.append({
                    'supplier_id': supplier_id,
                    'success': False,
                    'message': str(e)
                })
                db.session.rollback()
        
        logger.info(f"批量获取完成: 成功{success_count}/{len(supplier_ids)}")
        
        return jsonify({
            'success': True,
            'message': f'批量获取完成: 成功{success_count}/{len(supplier_ids)}',
            'data': {
                'results': results,
                'total_count': len(supplier_ids),
                'success_count': success_count
            }
        })
        
    except Exception as e:
        logger.error(f"批量获取失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'批量获取失败: {str(e)}'
        }), 500

@bp.route('/suppliers/<int:supplier_id>/analyze-website', methods=['POST'])
def analyze_supplier_website(supplier_id):
    """分析供应商网站获取产品和专利信息（同步执行）"""
    try:
        # 获取供应商信息
        supplier = SearchSupplier.query.get_or_404(supplier_id)
        
        # 获取请求参数
        data = request.get_json() or {}
        website_url = data.get('website_url', supplier.website)
        
        logger.info(f"开始分析供应商网站: {supplier.supplier_name}, URL: {website_url}")
        
        # 创建网站分析服务
        from app.services.website_analysis_service import WebsiteAnalysisService
        analysis_service = WebsiteAnalysisService()
        
        # 执行同步分析
        import asyncio
        
        async def run_analysis():
            try:
                # 分析网站
                raw_result = await analysis_service.analyze_website(
                    supplier_name=supplier.supplier_name,
                    supplier_website=website_url
                )
                
                # 获取或创建评级记录
                rating = supplier.get_or_create_rating()
                
                # 直接将GPT的原始分析文本存储到main_products字段
                rating.main_products = raw_result
                
                # 更新评估时间
                rating.evaluated_at = datetime.utcnow()
                db.session.commit()
                
                return {'raw_analysis': raw_result}
                
            finally:
                # 清理资源
                await analysis_service.cleanup()
        
        # 运行异步分析
        if hasattr(asyncio, 'run'):
            # Python 3.7+
            result = asyncio.run(run_analysis())
        else:
            # Python 3.6
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(run_analysis())
        
        logger.info(f"网站分析完成: {supplier.supplier_name}")
        
        return jsonify({
            'success': True,
            'message': '网站分析完成',
            'data': {
                'supplier_id': supplier_id,
                'supplier_name': supplier.supplier_name,
                'website_url': website_url,
                'analysis_result': result
            }
        })
        
    except Exception as e:
        logger.error(f"分析供应商网站失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'网站分析失败: {str(e)}'
        }), 500

@bp.route('/categories/overview', methods=['GET'])
def get_categories_overview():
    """获取所有产品的分类概览"""
    try:
        logger.debug("获取分类概览")
        
        # 获取所有产品信息
        from app.models.product import Product
        products = Product.query.all()
        
        # 获取所有分类信息
        categories = db.session.query(ProductCategory).all()
        
        # 构建产品分类统计
        product_stats = []
        for product in products:
            # 获取该产品的分类
            product_categories = [cat for cat in categories if cat.product_id == product.id]
            
            # 统计一级和二级分类数量
            primary_count = len([cat for cat in product_categories if cat.prev == 0])
            secondary_count = len([cat for cat in product_categories if cat.prev != 0])
            
            product_stats.append({
                'product_id': product.id,
                'product_name': product.name,
                'product_model': product.model,
                'product_category': product.category,
                'primary_categories_count': primary_count,
                'secondary_categories_count': secondary_count,
                'total_categories_count': len(product_categories)
            })
        
        # 全局统计
        total_products = len(products)
        total_categories = len(categories)
        total_primary = len([cat for cat in categories if cat.prev == 0])
        total_secondary = len([cat for cat in categories if cat.prev != 0])
        
        logger.debug(f"分类概览获取成功: {total_products}个产品, {total_categories}个分类")
        
        return jsonify({
            'success': True,
            'data': {
                'summary': {
                    'total_products': total_products,
                    'total_categories': total_categories,
                    'total_primary_categories': total_primary,
                    'total_secondary_categories': total_secondary
                },
                'products': product_stats
            }
        })
        
    except Exception as e:
        logger.error(f"获取分类概览失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取分类概览失败: {str(e)}'
        }), 500

@bp.route('/categories/product/<int:product_id>', methods=['GET'])
def get_product_categories(product_id):
    """获取特定产品的分类详情"""
    try:
        logger.debug(f"获取产品分类详情: product_id={product_id}")
        
        # 获取产品信息
        from app.models.product import Product
        product = Product.query.get(product_id)
        if not product:
            return jsonify({
                'success': False,
                'message': '产品不存在'
            }), 404
        
        # 获取该产品的所有分类
        categories = db.session.query(ProductCategory).filter_by(product_id=product_id).all()
        
        if not categories:
            return jsonify({
                'success': True,
                'data': {
                    'product': product.to_dict(),
                    'categories': {
                        'primary_categories': []
                    }
                }
            })
        
        # 构建分类树
        primary_categories = []
        secondary_categories_map = {}
        
        # 先处理一级分类
        for cat in categories:
            if cat.prev == 0:  # 一级分类
                primary_category = {
                    'id': cat.id,
                    'name': cat.category,
                    'feature': cat.feature,
                    'secondary_categories': []
                }
                primary_categories.append(primary_category)
                secondary_categories_map[cat.id] = primary_category['secondary_categories']
        
        # 再处理二级分类
        for cat in categories:
            if cat.prev != 0:  # 二级分类
                parent_id = cat.prev
                if parent_id in secondary_categories_map:
                    secondary_category = {
                        'id': cat.id,
                        'name': cat.category,
                        'feature': cat.feature,
                        'parent_id': parent_id
                    }
                    secondary_categories_map[parent_id].append(secondary_category)
        
        logger.debug(f"产品分类详情获取成功: {len(primary_categories)}个一级分类")
        
        return jsonify({
            'success': True,
            'data': {
                'product': product.to_dict(),
                'categories': {
                    'primary_categories': primary_categories
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取产品分类详情失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取产品分类详情失败: {str(e)}'
        }), 500

@bp.route('/categories/<int:category_id>', methods=['GET'])
def get_category_detail(category_id):
    """获取分类详情"""
    try:
        logger.debug(f"获取分类详情: category_id={category_id}")
        
        # 获取分类信息
        category = db.session.query(ProductCategory).filter_by(id=category_id).first()
        if not category:
            return jsonify({
                'success': False,
                'message': '分类不存在'
            }), 404
        
        # 获取产品信息
        from app.models.product import Product
        product = Product.query.get(category.product_id)
        
        # 获取父分类信息（如果是二级分类）
        parent_category = None
        if category.prev != 0:
            parent_category = db.session.query(ProductCategory).filter_by(id=category.prev).first()
        
        # 获取子分类信息（如果是一级分类）
        child_categories = []
        if category.prev == 0:
            child_categories = db.session.query(ProductCategory).filter_by(prev=category.id).all()
        
        # 获取该分类下的物料信息
        from app.models.component import Component, ComponentAnalysis
        components = []
        
        # 查询与该分类相关的组件分析记录
        component_analyses = db.session.query(ComponentAnalysis).filter_by(category_id=category_id).all()
        
        for analysis in component_analyses:
            # 获取组件基本信息
            component = Component.query.get(analysis.component_id)
            if component:
                component_data = component.to_dict()
                component_data['analysis'] = analysis.to_dict()
                components.append(component_data)
        
        # 如果没有通过component_analysis找到，尝试通过category字段匹配
        if not components:
            # 使用分类名称模糊匹配组件
            category_components = Component.query.filter(
                Component.category.like(f'%{category.category}%')
            ).limit(20).all()  # 限制数量避免过多数据
            
            for component in category_components:
                component_data = component.to_dict()
                # 查找对应的分析数据
                analysis = ComponentAnalysis.query.filter_by(component_id=component.id).first()
                component_data['analysis'] = analysis.to_dict() if analysis else None
                components.append(component_data)
        
        category_data = {
            'id': category.id,
            'name': category.category,
            'feature': category.feature,
            'product_id': category.product_id,
            'product_name': product.name if product else None,
            'level': 1 if category.prev == 0 else 2,
            'parent_category': {
                'id': parent_category.id,
                'name': parent_category.category
            } if parent_category else None,
            'child_categories': [
                {
                    'id': child.id,
                    'name': child.category,
                    'feature': child.feature
                } for child in child_categories
            ],
            'components': components  # 添加物料信息
        }
        
        logger.debug(f"分类详情获取成功: {category.category}, 包含{len(components)}个物料")
        
        return jsonify({
            'success': True,
            'data': category_data
        })
        
    except Exception as e:
        logger.error(f"获取分类详情失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取分类详情失败: {str(e)}'
        }), 500 