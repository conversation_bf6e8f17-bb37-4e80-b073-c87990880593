from flask import Blueprint, jsonify, request
from app.models.component import Component, ComponentDocument
from app import db

components_bp = Blueprint('components', __name__)

@components_bp.route('/api/components/<int:component_id>', methods=['GET'])
def get_component_detail(component_id):
    """获取物料详情"""
    try:
        # 获取物料基本信息
        component = Component.query.get_or_404(component_id)
        
        # 获取相关文档
        documents = ComponentDocument.query.filter_by(component_id=component_id).all()
        
        # 构建返回数据
        component_data = component.to_dict()
        component_data['documents'] = []
        
        for doc in documents:
            doc_data = doc.to_dict()
            doc_data['file_type'] = doc.get_file_type()
            component_data['documents'].append(doc_data)
        
        return jsonify({
            'success': True,
            'data': component_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@components_bp.route('/api/components', methods=['GET'])
def get_components():
    """获取物料列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        category = request.args.get('category', '')
        
        query = Component.query
        
        # 搜索过滤
        if search:
            query = query.filter(
                db.or_(
                    Component.name.like(f'%{search}%'),
                    Component.spec.like(f'%{search}%'),
                    Component.component_code.like(f'%{search}%')
                )
            )
        
        # 分类过滤
        if category:
            query = query.filter(Component.category == category)
        
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        components = [component.to_dict() for component in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'components': components,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 