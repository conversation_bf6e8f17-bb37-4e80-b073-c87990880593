"""
供应商评分API接口
"""

from flask import Blueprint, request, jsonify
from flask_cors import cross_origin
import logging
from app.services.supplier_scoring_service import SupplierScoringService
from app.api.errors import handle_api_error

logger = logging.getLogger(__name__)

# 创建蓝图
supplier_scoring_bp = Blueprint('supplier_scoring', __name__)

# 初始化评分服务
scoring_service = SupplierScoringService()

@supplier_scoring_bp.route('/api/suppliers/<int:supplier_id>/score', methods=['GET'])
@cross_origin()
def get_supplier_score(supplier_id):
    """
    获取供应商评分结果 - 仅从数据库读取，不进行计算
    """
    try:
        logger.info(f"Getting existing score for supplier {supplier_id}")
        
        # 调用新的仅查询方法
        result = scoring_service.get_supplier_scores(supplier_id)
        
        if result['success']:
            return jsonify(result), 200
        else:
            logger.warning(f"Failed to get score for supplier {supplier_id}: {result['message']}")
            return jsonify(result), 404
            
    except Exception as e:
        logger.error(f"Error in get_supplier_score API: {str(e)}")
        return handle_api_error(e)

@supplier_scoring_bp.route('/api/suppliers/<int:supplier_id>/score', methods=['POST'])
@cross_origin()
def calculate_supplier_score(supplier_id):
    """
    计算供应商评分 - 执行实际的评分计算
    """
    try:
        logger.info(f"Starting scoring calculation for supplier {supplier_id}")
        
        # 调用评分服务进行计算
        result = scoring_service.score_supplier(supplier_id)
        
        if result['success']:
            logger.info(f"Scoring calculation completed for supplier {supplier_id}")
            return jsonify(result), 200
        else:
            logger.warning(f"Scoring calculation failed for supplier {supplier_id}: {result['message']}")
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error in calculate_supplier_score API: {str(e)}")
        return handle_api_error(e)

@supplier_scoring_bp.route('/api/suppliers/batch-score', methods=['POST'])
@cross_origin()
def batch_score_suppliers():
    """
    批量评分供应商
    """
    try:
        data = request.get_json()
        if not data or 'supplier_ids' not in data:
            return jsonify({
                'success': False,
                'message': '请提供supplier_ids参数',
                'error_code': 'MISSING_PARAMETERS'
            }), 400
        
        supplier_ids = data['supplier_ids']
        include_details = data.get('include_details', False)
        
        if not isinstance(supplier_ids, list) or not supplier_ids:
            return jsonify({
                'success': False,
                'message': 'supplier_ids必须是非空数组',
                'error_code': 'INVALID_PARAMETERS'
            }), 400
        
        logger.info(f"Starting batch scoring for {len(supplier_ids)} suppliers")
        
        # 调用批量评分服务
        result = scoring_service.batch_score_suppliers(supplier_ids, include_details=include_details)
        
        logger.info(f"Batch scoring completed: {result['data']['success_count']} success, {result['data']['error_count']} errors")
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error in batch_score_suppliers API: {str(e)}")
        return handle_api_error(e)

@supplier_scoring_bp.route('/api/suppliers/scoring/weights', methods=['GET'])
@cross_origin()
def get_scoring_weights():
    """
    获取评分权重配置
    """
    try:
        result = scoring_service.get_scoring_weights()
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error in get_scoring_weights API: {str(e)}")
        return handle_api_error(e)

@supplier_scoring_bp.route('/api/suppliers/scoring/test-gpt', methods=['GET'])
@cross_origin()
def test_gpt_connection():
    """
    测试GPT连接状态
    """
    try:
        result = scoring_service.test_gpt_connection()
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error in test_gpt_connection API: {str(e)}")
        return handle_api_error(e)

@supplier_scoring_bp.route('/api/suppliers/scoring/available', methods=['GET'])
@cross_origin()
def get_available_suppliers():
    """
    获取可评分的供应商列表
    """
    try:
        limit = request.args.get('limit', 10, type=int)
        result = scoring_service.get_available_suppliers(limit=limit)
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error in get_available_suppliers API: {str(e)}")
        return handle_api_error(e)