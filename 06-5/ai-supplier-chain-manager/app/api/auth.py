from flask import request, jsonify, session
from app.api import bp
from app.models.user import User
from app.models.product import Product

@bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'message': '用户名和密码不能为空'
            }), 400
        
        # 验证用户
        user = User.authenticate(username, password)
        if not user:
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
        
        # 保存会话信息
        session['user_id'] = user.id
        session['company_id'] = user.client_company_id
        
        # 获取用户可访问的产品列表
        products = Product.get_by_company(user.client_company_id)
        products_data = [product.to_dict() for product in products]
        
        # 设置默认当前产品（第一个产品）
        current_product = products_data[0] if products_data else None
        if current_product:
            session['current_product_id'] = current_product['id']
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'data': {
                'user': user.to_dict(),
                'products': products_data,
                'current_product': current_product
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'登录失败：{str(e)}'
        }), 500

@bp.route('/auth/current', methods=['GET'])
def get_current_user():
    """获取当前登录用户信息"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({
                'success': False,
                'message': '未登录'
            }), 401
        
        user = User.query.get(user_id)
        if not user:
            session.clear()
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 401
        
        # 获取产品信息
        products = Product.get_by_company(user.client_company_id)
        products_data = [product.to_dict() for product in products]
        
        # 获取当前产品
        current_product_id = session.get('current_product_id')
        current_product = None
        if current_product_id:
            current_product = Product.get_by_id(current_product_id)
            current_product = current_product.to_dict() if current_product else None
        
        # 如果没有当前产品但有产品列表，设置第一个为当前产品
        if not current_product and products_data:
            current_product = products_data[0]
            session['current_product_id'] = current_product['id']
        
        return jsonify({
            'success': True,
            'data': {
                'user': user.to_dict(),
                'products': products_data,
                'current_product': current_product
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取用户信息失败：{str(e)}'
        }), 500

@bp.route('/auth/logout', methods=['POST'])
def logout():
    """用户登出"""
    try:
        session.clear()
        return jsonify({
            'success': True,
            'message': '登出成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'登出失败：{str(e)}'
        }), 500 