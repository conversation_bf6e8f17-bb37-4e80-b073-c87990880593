import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
from app.services.website_analysis_service import WebsiteAnalysisService


class TestWebsiteAnalysisService:
    """网站分析服务测试类"""
    
    @pytest.fixture
    def service(self):
        """创建测试用的服务实例"""
        return WebsiteAnalysisService()
    
    @pytest.fixture
    def sample_supplier_data(self):
        """测试用的供应商数据"""
        return {
            'supplier_name': '嘉兴坤德精密机械有限公司',
            'website': 'http://www.example-supplier.com'
        }
    
    @pytest.fixture
    def expected_analysis_result(self):
        """期望的分析结果格式"""
        return {
            "company_overview": "专业从事精密机械制造的公司",
            "main_products": [
                {
                    "name": "精密钣金件",
                    "description": "用于医疗体外诊断设备的精密钣金件",
                    "importance": "高"
                },
                {
                    "name": "精密机加件", 
                    "description": "高精度机械加工件",
                    "importance": "高"
                }
            ],
            "product_advantages": "精密制造，质量可靠",
            "technical_capabilities": "拥有先进的精密加工设备",
            "quality_control": "ISO质量管理体系",
            "certifications": [
                {
                    "name": "医疗器械质量管理体系认证",
                    "details": "GB/T 42061-2022/ISO 13485：2016"
                }
            ],
            "innovation_indicators": "持续技术创新",
            "service_highlights": "专业技术支持",
            "patents": [
                {
                    "name": "精密加工工艺专利",
                    "type": "发明专利",
                    "number": "CN123456789A"
                }
            ],
            "analysis": "该供应商在精密机械制造领域具有较强实力"
        }
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service is not None
        assert hasattr(service, 'model_client')
        assert hasattr(service, 'web_expert')
        assert service.playwright_workbench is None  # 初始时应为None
    
    @pytest.mark.asyncio
    async def test_initialize_playwright(self, service):
        """测试Playwright工作台初始化"""
        with patch.object(service, 'create_playwright_workbench') as mock_create:
            mock_workbench = Mock()
            mock_create.return_value = mock_workbench
            
            await service.initialize_playwright()
            
            assert service.playwright_workbench == mock_workbench
            assert service.web_expert.workbench == mock_workbench
            mock_create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_website_with_url(self, service, sample_supplier_data, expected_analysis_result):
        """测试带URL的网站分析"""
        # Mock Playwright初始化
        with patch.object(service, 'initialize_playwright') as mock_init:
            mock_init.return_value = None
            
            # Mock _extract_content_from_task_result方法直接返回JSON字符串
            with patch.object(service, '_extract_content_from_task_result') as mock_extract:
                mock_extract.return_value = json.dumps(expected_analysis_result)
                
                # Mock web_expert.run方法
                with patch.object(service.web_expert, 'run') as mock_run:
                    mock_task_result = Mock()
                    mock_run.return_value = mock_task_result
                    
                    # 执行测试
                    result = await service.analyze_website(
                        supplier_name=sample_supplier_data['supplier_name'],
                        supplier_website=sample_supplier_data['website']
                    )
                    
                    # 验证结果
                    assert result is not None
                    result_data = json.loads(result)
                    assert result_data['company_overview'] == expected_analysis_result['company_overview']
                    assert len(result_data['main_products']) == 2
                    assert result_data['main_products'][0]['name'] == '精密钣金件'
                    
                    # 验证调用参数
                    mock_run.assert_called_once()
                    call_args = mock_run.call_args[1]['task']
                    assert sample_supplier_data['website'] in call_args
                    assert sample_supplier_data['supplier_name'] in call_args
    
    @pytest.mark.asyncio
    async def test_analyze_website_without_url(self, service, sample_supplier_data):
        """测试不带URL的网站分析"""
        with patch.object(service, 'initialize_playwright') as mock_init:
            mock_init.return_value = None
            
            expected_result = {
                "company_overview": "NA",
                "main_products": [{"name": "NA", "description": "NA", "importance": "NA"}],
                "analysis": "无法找到供应商官方网站"
            }
            
            # Mock _extract_content_from_task_result方法直接返回JSON字符串
            with patch.object(service, '_extract_content_from_task_result') as mock_extract:
                mock_extract.return_value = json.dumps(expected_result)
                
                with patch.object(service.web_expert, 'run') as mock_run:
                    mock_task_result = Mock()
                    mock_run.return_value = mock_task_result
                    
                    result = await service.analyze_website(
                        supplier_name=sample_supplier_data['supplier_name'],
                        supplier_website=None
                    )
                    
                    assert result is not None
                    result_data = json.loads(result)
                    assert "搜索引擎查找" in mock_run.call_args[1]['task']
    
    @pytest.mark.asyncio
    async def test_analyze_website_failure(self, service, sample_supplier_data):
        """测试网站分析失败的情况"""
        with patch.object(service, 'initialize_playwright') as mock_init:
            mock_init.return_value = None
            
            with patch.object(service.web_expert, 'run') as mock_run:
                mock_run.side_effect = Exception("网络连接失败")
                
                result = await service.analyze_website(
                    supplier_name=sample_supplier_data['supplier_name'],
                    supplier_website=sample_supplier_data['website']
                )
                
                # 应该返回默认的NA结构
                assert result is not None
                result_data = json.loads(result)
                assert result_data['company_overview'] == 'NA'
                assert result_data['main_products'][0]['name'] == 'NA'
                assert "无法访问或分析供应商网站" in result_data['analysis']
    
    def test_extract_content_from_task_result_string(self, service):
        """测试从字符串类型的TaskResult提取内容"""
        test_content = '{"test": "content"}'
        result = service._extract_content_from_task_result(test_content)
        assert result == test_content
    
    def test_extract_content_from_task_result_object(self, service):
        """测试从对象类型的TaskResult提取内容"""
        mock_result = Mock()
        mock_result.content = '{"test": "content"}'
        
        result = service._extract_content_from_task_result(mock_result)
        assert result == '{"test": "content"}'
    
    def test_extract_content_from_task_result_messages(self, service):
        """测试从带messages的TaskResult提取内容"""
        mock_result = Mock()
        mock_message = Mock()
        mock_message.content = '{"test": "content"}'
        mock_result.messages = [mock_message]
        
        result = service._extract_content_from_task_result(mock_result)
        assert result == '{"test": "content"}'
    
    @pytest.mark.asyncio
    async def test_cleanup(self, service):
        """测试资源清理"""
        # 设置mock对象
        mock_workbench = AsyncMock()
        mock_client = AsyncMock()
        
        service.playwright_workbench = mock_workbench
        service.model_client = mock_client
        
        await service.cleanup()
        
        mock_workbench.stop.assert_called_once()
        mock_client.close.assert_called_once()
    
    def test_validate_analysis_result_structure(self, expected_analysis_result):
        """测试分析结果结构的有效性"""
        # 验证必需字段存在
        required_fields = [
            'company_overview', 'main_products', 'product_advantages',
            'technical_capabilities', 'quality_control', 'certifications',
            'innovation_indicators', 'service_highlights', 'patents', 'analysis'
        ]
        
        for field in required_fields:
            assert field in expected_analysis_result
        
        # 验证main_products结构
        assert isinstance(expected_analysis_result['main_products'], list)
        if expected_analysis_result['main_products']:
            product = expected_analysis_result['main_products'][0]
            assert 'name' in product
            assert 'description' in product
            assert 'importance' in product
        
        # 验证patents结构
        assert isinstance(expected_analysis_result['patents'], list)
        if expected_analysis_result['patents']:
            patent = expected_analysis_result['patents'][0]
            assert 'name' in patent
            assert 'type' in patent
            assert 'number' in patent


# 集成测试
class TestWebsiteAnalysisIntegration:
    """网站分析服务集成测试"""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_website_analysis(self):
        """真实网站分析测试（需要网络连接和OpenAI API）"""
        # 这个测试需要真实的API密钥和网络连接
        # 在CI/CD中可以跳过
        pytest.skip("需要真实的OpenAI API密钥和网络连接")
        
        service = WebsiteAnalysisService()
        try:
            result = await service.analyze_website(
                supplier_name="测试供应商",
                supplier_website="https://www.example.com"
            )
            
            assert result is not None
            result_data = json.loads(result)
            assert 'main_products' in result_data
            assert 'patents' in result_data
            
        finally:
            await service.cleanup()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"]) 