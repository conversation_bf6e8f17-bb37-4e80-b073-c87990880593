#!/usr/bin/env python3
"""
测试后端API的脚本
"""

import sys
import os
# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.append(project_root)

from sqlalchemy import text
from app import create_app, db
from app.models import User, Company, Product

def test_database_connection():
    """测试数据库连接"""
    app = create_app()
    
    with app.app_context():
        try:
            # 测试数据库连接 - 使用text()包装原始SQL
            db.session.execute(text('SELECT 1'))
            print("✅ 数据库连接成功")
            
            # 测试查询用户
            users = User.query.all()
            print(f"✅ 用户表查询成功，共 {len(users)} 个用户")
            
            # 测试查询公司
            companies = Company.query.all()
            print(f"✅ 公司表查询成功，共 {len(companies)} 个公司")
            
            # 测试查询产品
            products = Product.query.all()
            print(f"✅ 产品表查询成功，共 {len(products)} 个产品")
            
            # 显示测试用户信息
            jojo_user = User.query.filter_by(username='jojo').first()
            if jojo_user:
                print(f"✅ 找到测试用户: {jojo_user.username} ({jojo_user.full_name})")
                print(f"   所属公司: {jojo_user.company.name}")
                company_products = Product.get_by_company(jojo_user.client_company_id)
                print(f"   可访问产品: {[p.name for p in company_products]}")
            else:
                print("❌ 未找到测试用户 jojo")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 开始测试后端...")
    print("=" * 50)
    
    success = test_database_connection()
    
    print("=" * 50)
    if success:
        print("✅ 后端测试通过！")
        print("\n🔧 接下来的步骤:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 更新jojo用户密码: python app/tests/update_user_password.py jojo 123456")
        print("3. 启动Flask应用: python app.py")
        print("4. 访问 http://localhost:5000/api/auth/current 测试API")
    else:
        print("❌ 后端测试失败！")
        print("请检查数据库连接配置和环境变量")

if __name__ == '__main__':
    main() 