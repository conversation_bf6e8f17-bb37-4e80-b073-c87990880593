#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的网站分析功能测试
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.search_supplier import SearchSupplier
from app.services.website_analysis_service import WebsiteAnalysisService

async def test_website_analysis():
    """测试网站分析功能"""
    print("🚀 开始测试网站分析功能")
    
    # 创建Flask应用上下文
    app = create_app()
    
    with app.app_context():
        try:
            # 查找一个有网站的供应商
            supplier = SearchSupplier.query.filter(
                SearchSupplier.website.isnot(None),
                SearchSupplier.website != ''
            ).first()
            
            if not supplier:
                print("❌ 没有找到有网站的供应商")
                return
            
            print(f"📋 测试供应商: {supplier.supplier_name}")
            print(f"🌐 网站: {supplier.website}")
            
            # 创建网站分析服务
            analysis_service = WebsiteAnalysisService()
            
            # 执行分析
            print("🔍 开始分析网站...")
            raw_result = await analysis_service.analyze_website(
                supplier_name=supplier.supplier_name,
                supplier_website=supplier.website
            )
            
            print("✅ 原始分析结果获取成功")
            print(f"📄 原始结果长度: {len(raw_result)} 字符")
            
            # 解析结果
            print("🔧 解析分析结果...")
            parsed_result = analysis_service.parse_and_validate_result(raw_result)
            
            print("✅ 结果解析成功")
            print(f"📊 解析结果: {parsed_result}")
            
            # 清理资源
            await analysis_service.cleanup()
            print("🧹 资源清理完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 50)
    print("网站分析功能测试")
    print("=" * 50)
    
    # 运行异步测试
    asyncio.run(test_website_analysis())
    
    print("=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main() 