#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实的网站分析测试
这个测试会实际调用OpenAI API和Playwright MCP
需要真实的API密钥和网络连接
"""

import asyncio
import json
import logging
import pytest
import sys
import os

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.website_analysis_service import WebsiteAnalysisService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestRealWebsiteAnalysis:
    """真实网站分析测试类"""
    
    @pytest.mark.asyncio
    @pytest.mark.real_api
    async def test_real_website_analysis_with_url(self):
        """测试真实的网站分析 - 带URL"""
        service = WebsiteAnalysisService()
        
        try:
            # 使用真实的供应商网站进行测试
            supplier_name = "嘉兴坤德精密机械有限公司"
            supplier_website = "http://kundard.com/"  # 使用真实的坤德网站
            
            logger.info(f"开始分析供应商: {supplier_name}")
            logger.info(f"网站URL: {supplier_website}")
            
            # 执行真实的网站分析
            result = await service.analyze_website(
                supplier_name=supplier_name,
                supplier_website=supplier_website
            )
            
            logger.info("分析完成，原始结果:")
            logger.info(result)
            
            # 验证结果
            assert result is not None
            assert isinstance(result, str)
            
            # 解析结果
            parsed_result = service.parse_and_validate_result(result)
            
            logger.info("解析后的结果:")
            logger.info(json.dumps(parsed_result, ensure_ascii=False, indent=2))
            
            # 验证结果结构
            required_fields = [
                'company_overview', 'main_products', 'product_advantages',
                'technical_capabilities', 'quality_control', 'certifications',
                'innovation_indicators', 'service_highlights', 'patents', 'analysis'
            ]
            
            for field in required_fields:
                assert field in parsed_result, f"缺少必需字段: {field}"
            
            # 验证数组字段
            assert isinstance(parsed_result['main_products'], list)
            assert isinstance(parsed_result['patents'], list)
            assert isinstance(parsed_result['certifications'], list)
            
            logger.info("✅ 真实网站分析测试通过")
            
        except Exception as e:
            logger.error(f"❌ 真实网站分析测试失败: {str(e)}")
            raise
        finally:
            await service.cleanup()
    
    @pytest.mark.asyncio
    @pytest.mark.real_api
    async def test_real_website_analysis_without_url(self):
        """测试真实的网站分析 - 不带URL（搜索模式）"""
        service = WebsiteAnalysisService()
        
        try:
            supplier_name = "华为技术有限公司"
            
            logger.info(f"开始搜索并分析供应商: {supplier_name}")
            
            # 执行真实的网站分析（搜索模式）
            result = await service.analyze_website(
                supplier_name=supplier_name,
                supplier_website=None
            )
            
            logger.info("搜索分析完成，原始结果:")
            logger.info(result)
            
            # 验证结果
            assert result is not None
            assert isinstance(result, str)
            
            # 解析结果
            parsed_result = service.parse_and_validate_result(result)
            
            logger.info("解析后的结果:")
            logger.info(json.dumps(parsed_result, ensure_ascii=False, indent=2))
            
            logger.info("✅ 搜索模式网站分析测试通过")
            
        except Exception as e:
            logger.error(f"❌ 搜索模式网站分析测试失败: {str(e)}")
            raise
        finally:
            await service.cleanup()
    
    @pytest.mark.asyncio
    @pytest.mark.real_api
    async def test_playwright_initialization(self):
        """测试Playwright工作台的真实初始化"""
        service = WebsiteAnalysisService()
        
        try:
            logger.info("测试Playwright工作台初始化...")
            
            # 初始化Playwright
            await service.initialize_playwright()
            
            # 验证初始化结果
            if service.playwright_workbench is not None:
                logger.info("✅ Playwright工作台初始化成功")
                assert service.web_expert.workbench == service.playwright_workbench
            else:
                logger.warning("⚠️ Playwright工作台初始化失败，但这可能是环境问题")
                # 在某些环境中可能无法初始化Playwright，这不一定是错误
            
        except Exception as e:
            logger.error(f"❌ Playwright初始化测试失败: {str(e)}")
            # 不抛出异常，因为这可能是环境问题
        finally:
            await service.cleanup()


# 手动测试函数
async def manual_test():
    """手动测试函数，可以直接运行"""
    print("🚀 开始手动测试网站分析功能...")
    
    service = WebsiteAnalysisService()
    
    try:
        # 测试1: 分析坤德网站
        print("\n📝 测试1: 分析嘉兴坤德精密机械网站")
        result1 = await service.analyze_website(
            supplier_name="嘉兴坤德精密机械有限公司",
            supplier_website="http://kundard.com/"
        )
        
        print("原始结果:")
        print(result1[:500] + "..." if len(result1) > 500 else result1)
        
        parsed1 = service.parse_and_validate_result(result1)
        print("\n解析后的主要产品:")
        for product in parsed1.get('main_products', []):
            print(f"- {product.get('name', 'N/A')}: {product.get('description', 'N/A')}")
        
        # 测试2: 搜索模式
        print("\n📝 测试2: 搜索华为公司")
        result2 = await service.analyze_website(
            supplier_name="华为技术有限公司",
            supplier_website=None
        )
        
        parsed2 = service.parse_and_validate_result(result2)
        print("分析结果:")
        print(f"公司概述: {parsed2.get('company_overview', 'N/A')}")
        print(f"产品数量: {len(parsed2.get('main_products', []))}")
        print(f"专利数量: {len(parsed2.get('patents', []))}")
        
        print("\n✅ 手动测试完成")
        
    except Exception as e:
        print(f"\n❌ 手动测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        await service.cleanup()


if __name__ == "__main__":
    # 运行手动测试
    asyncio.run(manual_test()) 