#!/usr/bin/env python3
"""
供应商评分系统测试
测试真实数据库连接和GPT接口调用
"""

import os
import sys
import json
import logging
from datetime import datetime
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 加载环境变量 - 指定app目录下的.env文件
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_result(result, title="结果"):
    """格式化打印结果"""
    print(f"\n{title}:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

def test_database_connection():
    """测试数据库连接"""
    print_section("测试数据库连接")
    
    try:
        from app.services.supplier_scoring_service import SupplierScoringService
        
        service = SupplierScoringService()
        
        # 获取可用供应商列表
        suppliers_result = service.get_available_suppliers(limit=5)
        print_result(suppliers_result, "可用供应商列表")
        
        return suppliers_result.get('success', False), suppliers_result.get('data', [])
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': '数据库连接失败'
        }
        print_result(error_result, "数据库连接错误")
        return False, []

def test_gpt_connection():
    """测试GPT连接"""
    print_section("测试GPT连接")
    
    try:
        from app.services.supplier_scoring_service import SupplierScoringService
        
        service = SupplierScoringService()
        gpt_result = service.test_gpt_connection()
        print_result(gpt_result, "GPT连接测试")
        
        return gpt_result.get('success', False)
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': 'GPT连接测试失败'
        }
        print_result(error_result, "GPT连接错误")
        return False

def test_single_supplier_scoring(supplier_id):
    """测试单个供应商评分"""
    print_section(f"测试供应商评分 - ID: {supplier_id}")
    
    try:
        from app.services.supplier_scoring_service import SupplierScoringService
        
        service = SupplierScoringService()
        
        # 执行评分
        start_time = datetime.now()
        result = service.score_supplier(supplier_id)
        end_time = datetime.now()
        
        # 添加执行时间
        result['execution_time'] = str(end_time - start_time)
        
        print_result(result, f"供应商 {supplier_id} 评分结果")
        
        # 如果成功，显示评分摘要
        if result.get('success') and 'data' in result:
            summary = {
                '供应商信息': result['data']['supplier_info'],
                '评分摘要': result['data']['evaluation_summary'],
                '维度得分': {
                    name: f"{scores['total_score']:.2f}分"
                    for name, scores in result['data']['dimension_scores'].items()
                },
                '数据完整性': result['data'].get('data_completeness', {}).get('overall_score', 0)
            }
            print_result(summary, "评分摘要")
        
        return result.get('success', False)
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': f'供应商 {supplier_id} 评分失败'
        }
        print_result(error_result, "评分错误")
        return False

def test_batch_scoring(supplier_ids):
    """测试批量评分"""
    print_section(f"测试批量评分 - 供应商数量: {len(supplier_ids)}")
    
    try:
        from app.services.supplier_scoring_service import SupplierScoringService
        
        service = SupplierScoringService()
        
        # 执行批量评分
        start_time = datetime.now()
        result = service.batch_score_suppliers(supplier_ids, include_details=False)
        end_time = datetime.now()
        
        # 添加执行时间
        result['execution_time'] = str(end_time - start_time)
        
        print_result(result, "批量评分结果")
        
        # 显示成功的评分摘要
        if result.get('success') and 'data' in result:
            successful_results = result['data']['results']
            if successful_results:
                print(f"\n成功评分的供应商摘要:")
                for i, supplier_result in enumerate(successful_results[:3]):  # 只显示前3个
                    if supplier_result.get('success') and 'data' in supplier_result:
                        data = supplier_result['data']
                        summary = {
                            'ID': data['supplier_info']['id'],
                            '名称': data['supplier_info']['name'],
                            '最终得分': data['evaluation_summary']['final_score'],
                            '评级': data['evaluation_summary']['grade']
                        }
                        print(f"  {i+1}. {summary}")
        
        return result.get('success', False)
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': '批量评分失败'
        }
        print_result(error_result, "批量评分错误")
        return False

def test_scoring_weights():
    """测试评分权重配置"""
    print_section("测试评分权重配置")
    
    try:
        from app.services.supplier_scoring_service import SupplierScoringService
        
        service = SupplierScoringService()
        weights_result = service.get_scoring_weights()
        print_result(weights_result, "评分权重配置")
        
        return weights_result.get('success', False)
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': '获取权重配置失败'
        }
        print_result(error_result, "权重配置错误")
        return False

def test_data_validation():
    """测试数据验证功能"""
    print_section("测试数据验证功能")
    
    try:
        from app.services.supplier_scoring.validators import DataValidator
        from app.utils.schema_validator import validate_supplier_data
        
        validator = DataValidator()
        
        # 测试数据
        test_supplier_data = {
            'id': 1,
            'supplier_name': '测试供应商',
            'category_id': 1,
            'region': '上海',
            'status': 1
        }
        
        # 验证供应商数据
        valid, errors = validator.validate_supplier_data(test_supplier_data)
        validation_result = {
            'valid': valid,
            'errors': errors,
            'test_data': test_supplier_data
        }
        print_result(validation_result, "数据验证测试")
        
        # 测试Schema验证
        schema_result = validate_supplier_data(test_supplier_data)
        print_result(schema_result, "Schema验证测试")
        
        return valid
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': '数据验证测试失败'
        }
        print_result(error_result, "数据验证错误")
        return False

def main():
    """主测试函数"""
    print_section("供应商评分系统测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = {}
    
    # 1. 测试数据库连接
    db_success, suppliers = test_database_connection()
    test_results['database_connection'] = db_success
    
    if not db_success:
        print("\n❌ 数据库连接失败，无法继续测试")
        return
    
    # 2. 测试GPT连接
    gpt_success = test_gpt_connection()
    test_results['gpt_connection'] = gpt_success
    
    # 3. 测试数据验证
    validation_success = test_data_validation()
    test_results['data_validation'] = validation_success
    
    # 4. 测试评分权重配置
    weights_success = test_scoring_weights()
    test_results['scoring_weights'] = weights_success
    
    # 5. 测试单个供应商评分
    if suppliers:
        # 选择第一个有评级数据的供应商
        test_supplier_id = None
        for supplier in suppliers:
            if supplier.get('has_rating_data', False):
                test_supplier_id = supplier['id']
                break
        
        if not test_supplier_id and suppliers:
            # 如果没有有评级数据的，就选第一个
            test_supplier_id = suppliers[0]['id']
        
        if test_supplier_id:
            single_scoring_success = test_single_supplier_scoring(test_supplier_id)
            test_results['single_scoring'] = single_scoring_success
            
            # 6. 测试批量评分（选择前3个供应商）
            batch_supplier_ids = [s['id'] for s in suppliers[:3]]
            batch_scoring_success = test_batch_scoring(batch_supplier_ids)
            test_results['batch_scoring'] = batch_scoring_success
        else:
            print("\n⚠️  没有可用的供应商进行评分测试")
            test_results['single_scoring'] = False
            test_results['batch_scoring'] = False
    else:
        print("\n⚠️  没有获取到供应商数据")
        test_results['single_scoring'] = False
        test_results['batch_scoring'] = False
    
    # 输出测试总结
    print_section("测试总结")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    print(f"总测试项目: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！供应商评分系统运行正常。")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 项测试失败，请检查相关配置。")

if __name__ == "__main__":
    main() 