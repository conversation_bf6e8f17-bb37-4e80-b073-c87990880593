#!/usr/bin/env python3
"""
更新用户密码的脚本
用于设置标准的密码哈希，方便测试登录功能
"""

import sys
import os
# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.append(project_root)

from werkzeug.security import generate_password_hash
from app import create_app, db
from app.models.user import User

def update_user_password(username, new_password):
    """更新用户密码"""
    app = create_app()
    
    with app.app_context():
        user = User.query.filter_by(username=username).first()
        if not user:
            print(f"用户 {username} 不存在")
            return False
        
        # 生成新的密码哈希
        password_hash = generate_password_hash(new_password)
        
        # 更新密码
        user.password_hash = password_hash
        db.session.commit()
        
        print(f"用户 {username} 的密码已更新为: {new_password}")
        return True

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("使用方法: python update_user_password.py <用户名> <新密码>")
        print("示例: python app/tests/update_user_password.py jojo 123456")
        sys.exit(1)
    
    username = sys.argv[1]
    new_password = sys.argv[2]
    
    success = update_user_password(username, new_password)
    if success:
        print("密码更新成功！")
    else:
        print("密码更新失败！")

if __name__ == '__main__':
    main() 