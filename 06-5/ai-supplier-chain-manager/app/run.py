#!/usr/bin/env python3
"""
Flask应用启动文件
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到路径，确保能正确导入app包
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# 配置详细的日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'app_{datetime.now().strftime("%Y%m%d")}.log')
    ]
)

logger = logging.getLogger(__name__)

from app import create_app, db
from app.models import User, Company, Product

logger.info("开始创建Flask应用...")
app = create_app()
logger.info("Flask应用创建成功")

@app.shell_context_processor
def make_shell_context():
    """为flask shell命令提供上下文"""
    return {
        'db': db,
        'User': User,
        'Company': Company,
        'Product': Product
    }

if __name__ == '__main__':
    # 从环境变量获取端口，默认为5000
    port = int(os.environ.get('BACKEND_PORT', 5000))
    
    logger.info("启动供应商查找系统后端服务...")
    logger.info(f"服务地址: http://localhost:{port}")
    logger.info(f"API文档: http://localhost:{port}/api/")
    logger.info("=" * 50)
    
    # 测试数据库连接
    try:
        with app.app_context():
            logger.info("测试数据库连接...")
            result = db.session.execute(db.text("SELECT 1")).fetchone()
            logger.info(f"数据库连接成功: {result}")
            
            # 检查表是否存在
            logger.info("检查数据表...")
            tables = db.session.execute(db.text("SHOW TABLES")).fetchall()
            logger.info(f"数据库中的表: {[table[0] for table in tables]}")
            
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}", exc_info=True)
    
    logger.info("正在启动Flask服务器...")
    app.run(debug=True, port=port, host='0.0.0.0') 