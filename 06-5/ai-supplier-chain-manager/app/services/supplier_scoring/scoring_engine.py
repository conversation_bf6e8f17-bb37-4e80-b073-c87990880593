"""
评分引擎
全部采用GPT进行评估，不包含任何硬编码计算逻辑
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from .gpt_analyzer import GPTAnalyzer

logger = logging.getLogger(__name__)

class ScoringEngine:
    """评分引擎 - 全GPT评估版本"""
    
    def __init__(self):
        self.gpt_analyzer = GPTAnalyzer()
    
    def calculate_basic_strength_score(self, business_info: Dict) -> Dict:
        """使用GPT计算企业基础实力得分"""
        try:
            # 调用GPT分析企业基础实力
            result = self.gpt_analyzer.analyze_basic_strength(business_info)
            # 标准化分数
            result = self._normalize_dimension_scores(result)
            return result
        except Exception as e:
            logger.error(f"GPT basic strength analysis failed: {e}")
            return {
                'total_score': 50,
                'sub_scores': {
                    'capital_score': 50,
                    'staff_score': 50,
                    'years_score': 50,
                    'status_score': 50,
                    'location_score': 50
                },
                'analysis': 'GPT分析失败，使用默认评分'
            }
    
    def calculate_certification_score(self, certifications: Dict, website_analysis: str = None) -> Dict:
        """使用GPT计算资质认证得分，包含网站分析的资质相关信息"""
        if not certifications or 'error' in certifications:
            return {
                'total_score': 0,
                'sub_scores': {
                    'valid_count_score': 0,
                    'quality_score': 0
                },
                'analysis': '未获取到认证信息或获取失败'
            }
        
        try:
            # 调用GPT分析认证信息，包含网站分析内容
            result = self.gpt_analyzer.analyze_certifications(certifications, website_analysis)
            # 标准化分数
            result = self._normalize_dimension_scores(result)
            return result
        except Exception as e:
            logger.error(f"GPT certification analysis failed: {e}")
            return {
                'total_score': 30,
                'sub_scores': {
                    'valid_count_score': 30,
                    'quality_score': 30
                },
                'analysis': 'GPT分析失败，使用默认评分'
            }
    
    def calculate_feature_matching_score(self, category_feature: str, business_scope: str, 
                                       main_products) -> Dict:
        """使用GPT计算特征匹配得分"""
        try:
            # 使用GPT分析特征匹配
            gpt_analysis = self.gpt_analyzer.analyze_feature_matching(
                category_feature, business_scope, main_products
            )
            
            # 提取各维度得分
            feature_business_match = gpt_analysis.get('technical_capability_match', {}).get('score', 50)
            feature_products_match = gpt_analysis.get('overall_business_match', {}).get('score', 50)
            manufacturing_match = gpt_analysis.get('manufacturing_process_match', {}).get('score', 50)
            material_match = gpt_analysis.get('material_handling_match', {}).get('score', 50)
            quality_match = gpt_analysis.get('quality_standard_match', {}).get('score', 50)
            
            # 计算业务一致性得分（经营范围与主要产品的一致性）
            business_products_consistency = (feature_business_match + feature_products_match) / 2
            
            # 计算关键词匹配得分（基于所有维度的平均）
            keyword_match_score = (feature_business_match + feature_products_match + manufacturing_match + material_match + quality_match) / 5
            
            # 计算总分（使用GPT的加权平均）
            total_score = gpt_analysis.get('weighted_average', 50)
            
            analysis = gpt_analysis.get('summary', '特征匹配分析完成')
            
            result = {
                'total_score': round(total_score, 2),
                'sub_scores': {
                    'feature_business_match': round(feature_business_match, 2),
                    'feature_products_match': round(feature_products_match, 2),
                    'business_products_consistency': round(business_products_consistency, 2),
                    'keyword_match_score': round(keyword_match_score, 2)
                },
                'analysis': analysis,
                'gpt_analysis': gpt_analysis
            }
            
            # 标准化分数
            result = self._normalize_dimension_scores(result)
            return result
        except Exception as e:
            logger.error(f"GPT feature matching analysis failed: {e}")
            return {
                'total_score': 50,
                'sub_scores': {
                    'feature_business_match': 50,
                    'feature_products_match': 50,
                    'business_products_consistency': 50,
                    'keyword_match_score': 50
                },
                'analysis': 'GPT分析失败，使用默认评分'
            }
    
    def calculate_innovation_score(self, patents: List[Dict], website_analysis: str = None, business_scope: str = None) -> Dict:
        """使用GPT计算技术创新得分，基于专利信息、网站分析内容和经营范围"""
        try:
            # 调用GPT分析创新能力，包含专利、网站分析和经营范围
            result = self.gpt_analyzer.analyze_innovation(patents, website_analysis, business_scope)
            # 标准化分数
            result = self._normalize_dimension_scores(result)
            return result
        except Exception as e:
            logger.error(f"GPT innovation analysis failed: {e}")
            return {
                'total_score': 30,
                'sub_scores': {
                    'patent_score': 15,
                    'website_innovation_score': 15,
                    'business_scope_score': 15
                },
                'analysis': 'GPT分析失败，使用默认评分'
            }
    
    def calculate_comprehensive_score(self, supplier_data: Dict) -> Dict:
        """使用4次分开询问加一次汇总的方式计算综合评分"""
        try:
            # 第1步：企业基础实力分析
            logger.info("Step 1: Analyzing basic strength...")
            basic_strength = self.calculate_basic_strength_score(
                supplier_data.get('business_info', {})
            )
            
            # 第2步：认证信息分析
            logger.info("Step 2: Analyzing certifications...")
            certification = self.calculate_certification_score(
                supplier_data.get('certifications', {}),
                supplier_data.get('website_analysis', '')
            )
            
            # 第3步：特征匹配度分析
            logger.info("Step 3: Analyzing feature matching...")
            feature_matching = self.calculate_feature_matching_score(
                supplier_data.get('category_feature', ''),
                supplier_data.get('business_info', {}).get('business_scope', ''),
                supplier_data.get('main_products', [])
            )
            
            # 第4步：专利创新分析
            logger.info("Step 4: Analyzing innovation...")
            innovation = self.calculate_innovation_score(
                supplier_data.get('patents', []),
                supplier_data.get('website_analysis', ''),
                supplier_data.get('business_info', {}).get('business_scope', '')
            )
            
            # 第5步：综合汇总分析
            logger.info("Step 5: Comprehensive evaluation...")
            
            # 准备汇总数据
            summary_data = {
                'supplier_name': supplier_data.get('supplier_name', '未知公司'),
                'basic_strength_score': basic_strength['total_score'],
                'certification_score': certification['total_score'],
                'feature_matching_score': feature_matching['total_score'],
                'innovation_score': innovation['total_score'],
                'basic_strength_analysis': basic_strength.get('analysis', ''),
                'certification_analysis': certification.get('analysis', ''),
                'feature_matching_analysis': feature_matching.get('analysis', ''),
                'innovation_analysis': innovation.get('analysis', ''),
                'category_feature': supplier_data.get('category_feature', ''),
                'business_info': supplier_data.get('business_info', {}),
                'certifications': supplier_data.get('certifications', {}),
                'main_products': supplier_data.get('main_products', []),
                'patents': supplier_data.get('patents', [])
            }
            
            # 使用GPT进行综合汇总评估
            gpt_evaluation = self.gpt_analyzer.summary_evaluation(summary_data)
            
            # 从GPT汇总结果中提取最终得分，如果失败则使用权重计算
            if gpt_evaluation and 'final_score' in gpt_evaluation:
                final_score = gpt_evaluation.get('final_score', 50)
                calculation_detail = gpt_evaluation.get('calculation_detail', '基于GPT综合汇总计算')
                version = '2.1'  # 4+1评估版本
            else:
                # 降级方案：按权重计算
                final_score = (
                    basic_strength['total_score'] * 0.25 +
                    certification['total_score'] * 0.1875 +
                    feature_matching['total_score'] * 0.50 +
                    innovation['total_score'] * 0.0625
                )
                calculation_detail = f'权重计算：{basic_strength["total_score"]:.1f}×0.25 + {certification["total_score"]:.1f}×0.1875 + {feature_matching["total_score"]:.1f}×0.50 + {innovation["total_score"]:.1f}×0.0625 = {final_score:.1f}分'
                version = '2.1-fallback'
            
            # 标准化最终得分
            final_score = self._normalize_score(final_score)
            
            # 获取评级
            grade_info = self._get_score_grade(final_score)
            
            return {
                'supplier_info': {
                    'id': supplier_data.get('id'),
                    'name': supplier_data.get('supplier_name'),
                    'category': supplier_data.get('category_name', ''),
                    'region': supplier_data.get('region')
                },
                'evaluation_summary': {
                    'final_score': round(final_score, 2),
                    'grade': grade_info['grade'],
                    'level': grade_info['level'],
                    'evaluation_date': datetime.now().isoformat(),
                    'version': version,
                    'calculation_detail': calculation_detail
                },
                'dimension_scores': {
                    'basic_strength': basic_strength,
                    'certification': certification,
                    'feature_matching': feature_matching,
                    'innovation': innovation
                },
                'weight_distribution': {
                    'basic_strength': 0.25,
                    'certification': 0.1875,
                    'feature_matching': 0.50,
                    'innovation': 0.0625
                },
                'gpt_evaluation': gpt_evaluation,
                'recommendations': gpt_evaluation.get('recommendations', ["建议补充更多企业信息"]),
                'risk_warnings': gpt_evaluation.get('risk_warnings', [])
            }
            
        except Exception as e:
            logger.error(f"Comprehensive evaluation failed: {e}")
            # 完全降级方案：使用默认评分
            return {
                'supplier_info': {
                    'id': supplier_data.get('id'),
                    'name': supplier_data.get('supplier_name'),
                    'category': supplier_data.get('category_name', ''),
                    'region': supplier_data.get('region')
                },
                'evaluation_summary': {
                    'final_score': 45.0,
                    'grade': 'C',
                    'level': '一般',
                    'evaluation_date': datetime.now().isoformat(),
                    'version': '2.1-emergency',
                    'calculation_detail': '系统异常，使用紧急默认评分'
                },
                'dimension_scores': {
                    'basic_strength': {'total_score': 50, 'sub_scores': {}, 'analysis': '系统异常'},
                    'certification': {'total_score': 30, 'sub_scores': {}, 'analysis': '系统异常'},
                    'feature_matching': {'total_score': 50, 'sub_scores': {}, 'analysis': '系统异常'},
                    'innovation': {'total_score': 30, 'sub_scores': {}, 'analysis': '系统异常'}
                },
                'weight_distribution': {
                    'basic_strength': 0.25,
                    'certification': 0.1875,
                    'feature_matching': 0.50,
                    'innovation': 0.0625
                },
                'recommendations': ["系统异常，建议稍后重试"],
                'risk_warnings': ["评分系统异常，结果可能不准确"]
            }
    
    def _get_score_grade(self, score: float) -> Dict:
        """根据分数获取评级等级"""
        if score >= 90:
            return {'grade': 'A+', 'level': '优秀', 'color': '#00C851'}
        elif score >= 80:
            return {'grade': 'A', 'level': '良好', 'color': '#33B679'}
        elif score >= 70:
            return {'grade': 'B+', 'level': '中上', 'color': '#FF8A00'}
        elif score >= 60:
            return {'grade': 'B', 'level': '中等', 'color': '#FF8A00'}
        elif score >= 50:
            return {'grade': 'C', 'level': '一般', 'color': '#FF4444'}
        else:
            return {'grade': 'D', 'level': '较差', 'color': '#CC0000'}
    
    def _normalize_score(self, score: float) -> float:
        """标准化分数：超过100分的调整为90分"""
        if score > 100:
            return 90.0
        return max(0.0, score)  # 确保分数不小于0
    
    def _normalize_dimension_scores(self, dimension_result: Dict) -> Dict:
        """标准化维度评分结果"""
        if not isinstance(dimension_result, dict):
            return dimension_result
        
        # 标准化总分
        if 'total_score' in dimension_result:
            dimension_result['total_score'] = self._normalize_score(dimension_result['total_score'])
        
        # 标准化子分数
        if 'sub_scores' in dimension_result and isinstance(dimension_result['sub_scores'], dict):
            for key, value in dimension_result['sub_scores'].items():
                if isinstance(value, (int, float)):
                    dimension_result['sub_scores'][key] = self._normalize_score(value)
        
        return dimension_result