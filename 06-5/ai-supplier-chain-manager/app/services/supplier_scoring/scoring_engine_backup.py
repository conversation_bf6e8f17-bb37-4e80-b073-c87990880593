"""
评分引擎
处理纯数值计算、权重分配和最终得分计算
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from .gpt_analyzer import GPTAnalyzer

logger = logging.getLogger(__name__)

class ScoringEngine:
    """评分引擎"""
    
    def __init__(self):
        self.gpt_analyzer = GPTAnalyzer()
        
        # 权重配置（调整后的版本）
        self.weights = {
            'basic_strength': 0.20,      # 企业基础实力：20%
            'certification': 0.15,       # 资质认证：15%
            'feature_matching': 0.50,    # 特征匹配：50%（核心）
            'innovation': 0.05,          # 技术创新：5%
            'reputation': 0.10           # 商业信誉：10%
        }
        
        # 特征匹配内部权重
        self.feature_weights = {
            'feature_products_match': 0.40,      # 20% / 50% = 40%
            'feature_business_match': 0.30,      # 15% / 50% = 30%
            'business_products_consistency': 0.20, # 10% / 50% = 20%
            'overall_matching': 0.10             # 5% / 50% = 10%
        }
    
    def calculate_basic_strength_score(self, business_info: Dict) -> Dict:
        """计算企业基础实力得分"""
        scores = {}
        
        # 注册资本评分
        scores['capital_score'] = self._score_registered_capital(
            business_info.get('registered_capital')
        )
        
        # 员工规模评分
        scores['staff_score'] = self._score_staff_size(
            business_info.get('staff_size')
        )
        
        # 经营年限评分
        scores['years_score'] = self._score_operating_years(
            business_info.get('registration_date')
        )
        
        # 企业状态评分
        scores['status_score'] = self._score_company_status(
            business_info.get('company_status')
        )
        
        # 地理位置评分（简化版）
        scores['location_score'] = self._score_location(
            business_info.get('address', '')
        )
        
        # 计算总分
        total_score = sum(scores.values()) / len(scores)
        
        # 生成分析
        analysis = self._generate_basic_strength_analysis(scores, business_info)
        
        return {
            'total_score': round(total_score, 2),
            'sub_scores': scores,
            'analysis': analysis
        }
    
    def calculate_certification_score(self, certifications: Dict) -> Dict:
        """计算资质认证得分"""
        if not certifications or 'error' in certifications:
            return {
                'total_score': 0,
                'sub_scores': {
                    'valid_count_score': 0,
                    'quality_score': 0
                },
                'analysis': '未获取到认证信息或获取失败'
            }
        
        # 有效认证数量评分
        valid_count = certifications.get('valid_count', 0)
        valid_count_score = min(100, valid_count * 10)  # 每个有效认证10分，最高100分
        
        # 认证质量评分
        quality_score = self._score_certification_quality(certifications)
        
        # 总分
        total_score = (valid_count_score + quality_score) / 2
        
        analysis = f"有效认证{valid_count}个，认证质量{'良好' if quality_score > 70 else '一般' if quality_score > 40 else '较差'}"
        
        return {
            'total_score': round(total_score, 2),
            'sub_scores': {
                'valid_count_score': round(valid_count_score, 2),
                'quality_score': round(quality_score, 2)
            },
            'analysis': analysis
        }
    
    def calculate_feature_matching_score(self, category_feature: str, business_scope: str, 
                                       main_products: List[Dict]) -> Dict:
        """计算特征匹配得分"""
        try:
            # 使用GPT分析特征匹配
            gpt_analysis = self.gpt_analyzer.analyze_feature_matching(
                category_feature, business_scope, main_products
            )
        except Exception as e:
            logger.error(f"GPT analysis failed: {e}")
            # 使用降级方案
            gpt_analysis = self.gpt_analyzer.fallback_keyword_matching(
                category_feature, business_scope, main_products
            )
        
        # 提取各维度得分
        feature_business_match = gpt_analysis.get('technical_capability_match', {}).get('score', 50)
        feature_products_match = gpt_analysis.get('overall_business_match', {}).get('score', 50)
        
        # 计算业务范围与产品一致性
        business_products_consistency = self._calculate_business_products_consistency(
            business_scope, main_products
        )
        
        # 关键词匹配得分
        keyword_match_score = self._calculate_keyword_match(
            category_feature, business_scope, main_products
        )
        
        # 计算总分（使用内部权重）
        total_score = (
            feature_products_match * self.feature_weights['feature_products_match'] +
            feature_business_match * self.feature_weights['feature_business_match'] +
            business_products_consistency * self.feature_weights['business_products_consistency'] +
            gpt_analysis.get('weighted_average', 50) * self.feature_weights['overall_matching']
        )
        
        analysis = gpt_analysis.get('summary', '特征匹配分析完成')
        
        return {
            'total_score': round(total_score, 2),
            'sub_scores': {
                'feature_business_match': round(feature_business_match, 2),
                'feature_products_match': round(feature_products_match, 2),
                'business_products_consistency': round(business_products_consistency, 2),
                'keyword_match_score': round(keyword_match_score, 2)
            },
            'analysis': analysis,
            'gpt_analysis': gpt_analysis
        }
    
    def calculate_innovation_score(self, patents: List[Dict]) -> Dict:
        """计算技术创新得分"""
        if not patents:
            return {
                'total_score': 0,
                'sub_scores': {
                    'patent_count_score': 0,
                    'patent_quality_score': 0
                },
                'analysis': '未获取到专利信息'
            }
        
        # 专利数量评分
        patent_count = len(patents)
        patent_count_score = min(100, patent_count * 5)  # 每个专利5分，最高100分
        
        # 专利质量评分
        patent_quality_score = self._score_patent_quality(patents)
        
        # 总分
        total_score = (patent_count_score + patent_quality_score) / 2
        
        invention_count = len([p for p in patents if p.get('type') == '发明专利'])
        analysis = f"共{patent_count}项专利，其中发明专利{invention_count}项"
        
        return {
            'total_score': round(total_score, 2),
            'sub_scores': {
                'patent_count_score': round(patent_count_score, 2),
                'patent_quality_score': round(patent_quality_score, 2)
            },
            'analysis': analysis
        }
    
    def calculate_reputation_score(self, supplier_data: Dict) -> Dict:
        """计算商业信誉得分"""
        # 网站状态评分
        website_score = self._score_website_status(supplier_data.get('status', 1))
        
        # 业务范围匹配度（简化计算）
        business_scope_match = 75  # 默认值，可以根据实际情况调整
        
        # 行业地位评分（基于企业规模和成立时间）
        industry_position = self._score_industry_position(supplier_data)
        
        # 总分
        total_score = (website_score + business_scope_match + industry_position) / 3
        
        analysis = f"网站状态{'正常' if website_score > 80 else '异常'}，行业地位{'较高' if industry_position > 70 else '一般'}"
        
        return {
            'total_score': round(total_score, 2),
            'sub_scores': {
                'website_score': round(website_score, 2),
                'business_scope_match': round(business_scope_match, 2),
                'industry_position': round(industry_position, 2)
            },
            'analysis': analysis
        }
    
    def calculate_comprehensive_score(self, supplier_data: Dict) -> Dict:
        """计算综合评分"""
        # 获取各维度评分
        basic_strength = self.calculate_basic_strength_score(
            supplier_data.get('business_info', {})
        )
        
        certification = self.calculate_certification_score(
            supplier_data.get('certifications', {})
        )
        
        feature_matching = self.calculate_feature_matching_score(
            supplier_data.get('category_feature', ''),
            supplier_data.get('business_info', {}).get('business_scope', ''),
            supplier_data.get('main_products', [])
        )
        
        innovation = self.calculate_innovation_score(
            supplier_data.get('patents', [])
        )
        
        reputation = self.calculate_reputation_score(supplier_data)
        
        # 计算最终得分
        final_score = (
            basic_strength['total_score'] * self.weights['basic_strength'] +
            certification['total_score'] * self.weights['certification'] +
            feature_matching['total_score'] * self.weights['feature_matching'] +
            innovation['total_score'] * self.weights['innovation'] +
            reputation['total_score'] * self.weights['reputation']
        )
        
        # 获取评级
        grade_info = self._get_score_grade(final_score)
        
        # 使用GPT生成综合评估
        try:
            gpt_evaluation = self.gpt_analyzer.comprehensive_evaluation(supplier_data)
        except Exception as e:
            logger.error(f"GPT comprehensive evaluation failed: {e}")
            gpt_evaluation = {
                "strengths": ["企业信息完整"],
                "weaknesses": ["需要更多分析数据"],
                "recommendations": ["建议补充更多企业信息"],
                "risk_warnings": ["信息不足，评估可能不准确"],
                "overall_assessment": "评估完成，建议进一步核实信息"
            }
        
        return {
            'supplier_info': {
                'id': supplier_data.get('id'),
                'name': supplier_data.get('supplier_name'),
                'category': supplier_data.get('category_name', ''),
                'region': supplier_data.get('region')
            },
            'evaluation_summary': {
                'final_score': round(final_score, 2),
                'grade': grade_info['grade'],
                'level': grade_info['level'],
                'evaluation_date': datetime.now().isoformat(),
                'version': '1.0'
            },
            'dimension_scores': {
                'basic_strength': basic_strength,
                'certification': certification,
                'feature_matching': feature_matching,
                'innovation': innovation,
                'reputation': reputation
            },
            'weight_distribution': self.weights,
            'recommendations': gpt_evaluation.get('recommendations', []),
            'risk_warnings': gpt_evaluation.get('risk_warnings', [])
        }
    
    # 私有方法：具体评分逻辑
    
    def _score_registered_capital(self, capital_str: Optional[str]) -> float:
        """注册资本评分"""
        if not capital_str:
            return 30
        
        try:
            # 提取数字和单位
            import re
            match = re.search(r'([\d.]+)\s*([万千亿]?)', capital_str)
            if not match:
                return 30
            
            amount = float(match.group(1))
            unit = match.group(2)
            
            # 转换为万元
            if unit == '千':
                amount = amount / 10
            elif unit == '亿':
                amount = amount * 10000
            elif unit == '万' or not unit:
                pass  # 已经是万元
            
            # 评分
            if amount >= 5000:
                return 95
            elif amount >= 1000:
                return 80
            elif amount >= 500:
                return 60
            else:
                return 40
                
        except Exception:
            return 30
    
    def _score_staff_size(self, staff_size: Optional[str]) -> float:
        """员工规模评分"""
        if not staff_size:
            return 30
        
        size_scores = {
            '5000人以上': 95,
            '1000-4999人': 85,
            '500-999人': 75,
            '100-499人': 60,
            '50-99人': 45,
            '1-49人': 35
        }
        
        return size_scores.get(staff_size, 30)
    
    def _score_operating_years(self, registration_date: Optional[int]) -> float:
        """经营年限评分"""
        if not registration_date:
            return 30
        
        try:
            reg_date = datetime.fromtimestamp(registration_date)
            years = (datetime.now() - reg_date).days / 365.25
            
            if years >= 20:
                return 95
            elif years >= 10:
                return 80
            elif years >= 5:
                return 60
            else:
                return 40
                
        except Exception:
            return 30
    
    def _score_company_status(self, status: Optional[str]) -> float:
        """企业状态评分"""
        if status in ['存续', '开业']:
            return 100
        else:
            return 0
    
    def _score_location(self, address: str) -> float:
        """地理位置评分（简化版）"""
        if not address:
            return 50
        
        # 简单的地区评分
        tier1_cities = ['北京', '上海', '广州', '深圳']
        tier2_cities = ['杭州', '南京', '苏州', '成都', '武汉', '西安']
        
        for city in tier1_cities:
            if city in address:
                return 90
        
        for city in tier2_cities:
            if city in address:
                return 75
        
        return 60
    
    def _score_certification_quality(self, certifications: Dict) -> float:
        """认证质量评分"""
        cert_list = certifications.get('certifications', [])
        if not cert_list:
            return 0
        
        quality_weights = {
            'ISO13485': 1.5,  # 医疗器械质量管理
            'ISO9001': 1.2,   # 质量管理
            'ISO14001': 1.1,  # 环境管理
            'CE': 1.3,        # 欧盟认证
            'FDA': 1.4,       # 美国FDA
            '其他': 1.0
        }
        
        total_weight = 0
        valid_count = 0
        
        for cert in cert_list:
            if cert.get('status') == '有效':
                cert_type = cert.get('type', '其他')
                weight = quality_weights.get(cert_type, 1.0)
                total_weight += weight
                valid_count += 1
        
        if valid_count == 0:
            return 0
        
        avg_weight = total_weight / valid_count
        return min(100, avg_weight * 50)  # 转换为0-100分
    
    def _calculate_business_products_consistency(self, business_scope: str, 
                                               main_products: List[Dict]) -> float:
        """计算业务范围与产品一致性"""
        if not business_scope or not main_products:
            return 50
        
        # 简单的关键词匹配
        import re
        business_words = set(re.findall(r'[\u4e00-\u9fff]+', business_scope))
        
        product_words = set()
        for product in main_products:
            if isinstance(product, dict):
                name = product.get('name', '')
                desc = product.get('description', '')
                words = re.findall(r'[\u4e00-\u9fff]+', f"{name} {desc}")
                product_words.update(words)
        
        if not business_words or not product_words:
            return 50
        
        common_words = business_words.intersection(product_words)
        consistency = len(common_words) / min(len(business_words), len(product_words))
        
        return min(100, consistency * 100)
    
    def _calculate_keyword_match(self, category_feature: str, business_scope: str, 
                               main_products: List[Dict]) -> float:
        """计算关键词匹配得分"""
        if not category_feature:
            return 50
        
        import re
        feature_words = set(re.findall(r'[\u4e00-\u9fff]+', category_feature))
        feature_words = {w for w in feature_words if len(w) >= 2}
        
        if not feature_words:
            return 50
        
        # 业务范围匹配
        business_match = 0
        if business_scope:
            business_words = set(re.findall(r'[\u4e00-\u9fff]+', business_scope))
            common = feature_words.intersection(business_words)
            business_match = len(common) / len(feature_words) * 100
        
        # 产品匹配
        product_match = 0
        if main_products:
            all_product_text = " ".join([
                f"{p.get('name', '')} {p.get('description', '')}" 
                for p in main_products if isinstance(p, dict)
            ])
            product_words = set(re.findall(r'[\u4e00-\u9fff]+', all_product_text))
            common = feature_words.intersection(product_words)
            product_match = len(common) / len(feature_words) * 100
        
        return (business_match + product_match) / 2
    
    def _score_patent_quality(self, patents: List[Dict]) -> float:
        """专利质量评分"""
        if not patents:
            return 0
        
        quality_weights = {
            '发明专利': 3.0,
            '实用新型': 1.5,
            '外观设计': 1.0,
            'NA': 0.5
        }
        
        total_weight = 0
        for patent in patents:
            patent_type = patent.get('type', 'NA')
            weight = quality_weights.get(patent_type, 1.0)
            
            # 有效专利加权
            if patent.get('status') == '有效':
                weight *= 1.2
            
            total_weight += weight
        
        # 转换为0-100分
        avg_weight = total_weight / len(patents)
        return min(100, avg_weight * 20)
    
    def _score_website_status(self, status: int) -> float:
        """网站状态评分"""
        status_scores = {
            0: 100,   # 验证正常
            99: 100,  # 验证通过
            1: 60,    # 验证pending
            2: 20,    # 网站无效
            3: 0,     # 黑名单
            4: 30,    # 产品不能做
            5: 40     # 报价过高
        }
        
        return status_scores.get(status, 50)
    
    def _score_industry_position(self, supplier_data: Dict) -> float:
        """行业地位评分"""
        business_info = supplier_data.get('business_info', {})
        
        # 基于注册资本
        capital_score = self._score_registered_capital(business_info.get('registered_capital'))
        
        # 基于员工规模
        staff_score = self._score_staff_size(business_info.get('staff_size'))
        
        # 基于经营年限
        years_score = self._score_operating_years(business_info.get('registration_date'))
        
        # 综合评分
        return (capital_score + staff_score + years_score) / 3
    
    def _get_score_grade(self, score: float) -> Dict:
        """根据分数获取评级等级"""
        if score >= 90:
            return {'grade': 'A+', 'level': '优秀'}
        elif score >= 80:
            return {'grade': 'A', 'level': '良好'}
        elif score >= 70:
            return {'grade': 'B+', 'level': '中上'}
        elif score >= 60:
            return {'grade': 'B', 'level': '中等'}
        elif score >= 50:
            return {'grade': 'C', 'level': '一般'}
        else:
            return {'grade': 'D', 'level': '较差'}
    
    def _generate_basic_strength_analysis(self, scores: Dict, business_info: Dict) -> str:
        """生成企业基础实力分析"""
        analysis_parts = []
        
        if scores['capital_score'] > 80:
            analysis_parts.append("注册资本充足")
        elif scores['capital_score'] > 60:
            analysis_parts.append("注册资本适中")
        else:
            analysis_parts.append("注册资本偏少")
        
        if scores['staff_score'] > 80:
            analysis_parts.append("员工规模较大")
        elif scores['staff_score'] > 60:
            analysis_parts.append("员工规模适中")
        else:
            analysis_parts.append("员工规模较小")
        
        if scores['years_score'] > 80:
            analysis_parts.append("经营历史悠久")
        elif scores['years_score'] > 60:
            analysis_parts.append("有一定经营经验")
        else:
            analysis_parts.append("成立时间较短")
        
        return "，".join(analysis_parts) 