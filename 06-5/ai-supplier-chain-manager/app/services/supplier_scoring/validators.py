"""
数据验证器
处理数据预处理、清洗和完整性检查
"""

import logging
from typing import Dict, List, Optional, Tuple
from app.utils.schema_validator import validator
from app.schemas import (
    SUPPLIER_BASIC_INFO_SCHEMA,
    BUSINESS_INFO_SCHEMA,
    CERTIFICATIONS_SCHEMA,
    MAIN_PRODUCTS_SCHEMA,
    PATENTS_SCHEMA
)

logger = logging.getLogger(__name__)

class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.validator = validator
    
    def validate_supplier_data(self, supplier_data: Dict) -> Tuple[bool, List[str]]:
        """验证供应商数据完整性"""
        errors = []
        
        # 检查必需字段
        required_fields = ['id', 'supplier_name', 'category_id']
        for field in required_fields:
            if field not in supplier_data or supplier_data[field] is None:
                errors.append(f"缺少必需字段: {field}")
        
        # 验证基础信息格式 - 创建一个副本，移除评级相关字段
        basic_info = {k: v for k, v in supplier_data.items() 
                     if k not in ['business_info', 'main_products', 'patents']}
        
        # 如果certifications是对象（评级数据），转换为字符串用于基础信息验证
        if isinstance(basic_info.get('certifications'), dict):
            basic_info['certifications'] = f"认证数量: {basic_info['certifications'].get('total_count', 0)}"
        
        validation_result = self.validator.validate_data(basic_info, SUPPLIER_BASIC_INFO_SCHEMA)
        if not validation_result['valid']:
            errors.extend([error['message'] for error in validation_result['errors']])
        
        return len(errors) == 0, errors
    
    def validate_business_info(self, business_info: Dict) -> Tuple[bool, List[str]]:
        """验证企业信息"""
        if not business_info:
            return False, ["企业信息为空"]
        
        errors = []
        
        # 检查数据来源
        if 'data_source' not in business_info:
            errors.append("缺少数据来源信息")
        
        # 验证格式
        validation_result = self.validator.validate_data(business_info, BUSINESS_INFO_SCHEMA)
        if not validation_result['valid']:
            errors.extend([error['message'] for error in validation_result['errors']])
        
        return len(errors) == 0, errors
    
    def validate_certifications(self, certifications: Dict) -> Tuple[bool, List[str]]:
        """验证认证信息"""
        if not certifications:
            return True, []  # 认证信息可以为空
        
        if 'error' in certifications:
            return False, [f"认证信息获取失败: {certifications['error']}"]
        
        errors = []
        
        # 验证格式
        validation_result = self.validator.validate_data(certifications, CERTIFICATIONS_SCHEMA)
        if not validation_result['valid']:
            errors.extend([error['message'] for error in validation_result['errors']])
        
        return len(errors) == 0, errors
    
    def validate_main_products(self, main_products: List[Dict]) -> Tuple[bool, List[str]]:
        """验证主要产品信息"""
        if not main_products:
            return True, []  # 产品信息可以为空
        
        errors = []
        
        # 验证格式
        validation_result = self.validator.validate_data(main_products, MAIN_PRODUCTS_SCHEMA)
        if not validation_result['valid']:
            errors.extend([error['message'] for error in validation_result['errors']])
        
        return len(errors) == 0, errors
    
    def validate_patents(self, patents: List[Dict]) -> Tuple[bool, List[str]]:
        """验证专利信息"""
        if not patents:
            return True, []  # 专利信息可以为空
        
        errors = []
        
        # 验证格式
        validation_result = self.validator.validate_data(patents, PATENTS_SCHEMA)
        if not validation_result['valid']:
            errors.extend([error['message'] for error in validation_result['errors']])
        
        return len(errors) == 0, errors
    
    def preprocess_supplier_data(self, supplier_data: Dict) -> Dict:
        """预处理供应商数据"""
        processed_data = supplier_data.copy()
        
        # 清理字符串字段
        string_fields = ['supplier_name', 'region', 'website', 'phone', 'email']
        for field in string_fields:
            if field in processed_data and processed_data[field]:
                processed_data[field] = str(processed_data[field]).strip()
        
        # 标准化状态字段
        if 'status' in processed_data:
            processed_data['status'] = int(processed_data['status']) if processed_data['status'] is not None else 1
        
        return processed_data
    
    def preprocess_business_info(self, business_info: Dict) -> Dict:
        """预处理企业信息"""
        if not business_info:
            return {}
        
        processed_info = business_info.copy()
        
        # 清理字符串字段
        string_fields = ['company_name', 'registered_capital', 'staff_size', 'company_status', 'business_scope']
        for field in string_fields:
            if field in processed_info and processed_info[field]:
                processed_info[field] = str(processed_info[field]).strip()
        
        # 标准化注册资本格式
        if 'registered_capital' in processed_info and processed_info['registered_capital']:
            processed_info['registered_capital'] = self._standardize_capital(processed_info['registered_capital'])
        
        # 标准化员工规模
        if 'staff_size' in processed_info and processed_info['staff_size']:
            processed_info['staff_size'] = self._standardize_staff_size(processed_info['staff_size'])
        
        return processed_info
    
    def preprocess_certifications(self, certifications: Dict) -> Dict:
        """预处理认证信息"""
        if not certifications or 'error' in certifications:
            return certifications
        
        processed_certs = certifications.copy()
        
        # 确保数值字段为整数
        for field in ['total_count', 'valid_count']:
            if field in processed_certs:
                try:
                    processed_certs[field] = int(processed_certs[field])
                except (ValueError, TypeError):
                    processed_certs[field] = 0
        
        # 处理认证列表
        if 'certifications' in processed_certs and isinstance(processed_certs['certifications'], list):
            processed_list = []
            for cert in processed_certs['certifications']:
                if isinstance(cert, dict):
                    processed_cert = self._preprocess_certification_item(cert)
                    processed_list.append(processed_cert)
            processed_certs['certifications'] = processed_list
        
        return processed_certs
    
    def preprocess_main_products(self, main_products: List[Dict]) -> List[Dict]:
        """预处理主要产品信息"""
        if not main_products:
            return []
        
        processed_products = []
        for product in main_products:
            if isinstance(product, dict):
                processed_product = product.copy()
                
                # 只做基本的字符串清理
                for field in ['name', 'description', 'importance']:
                    if field in processed_product and processed_product[field]:
                        processed_product[field] = str(processed_product[field]).strip()
                
                processed_products.append(processed_product)
        
        return processed_products
    
    def preprocess_patents(self, patents: List[Dict]) -> List[Dict]:
        """预处理专利信息"""
        if not patents:
            return []
        
        processed_patents = []
        for patent in patents:
            if isinstance(patent, dict):
                processed_patent = patent.copy()
                
                # 只做基本的字符串清理
                for field in ['name', 'type', 'number', 'status']:
                    if field in processed_patent and processed_patent[field]:
                        processed_patent[field] = str(processed_patent[field]).strip()
                
                processed_patents.append(processed_patent)
        
        return processed_patents
    
    def check_data_completeness(self, supplier_data: Dict) -> Dict:
        """检查数据完整性"""
        completeness = {
            'basic_info': True,
            'business_info': False,
            'certifications': False,
            'main_products': False,
            'patents': False,
            'category_feature': False,
            'overall_score': 0
        }
        
        # 检查基础信息
        required_basic_fields = ['id', 'supplier_name', 'category_id']
        for field in required_basic_fields:
            if field not in supplier_data or not supplier_data[field]:
                completeness['basic_info'] = False
                break
        
        # 检查企业信息
        business_info = supplier_data.get('business_info', {})
        if business_info and 'error' not in business_info:
            required_business_fields = ['company_name', 'business_scope']
            completeness['business_info'] = all(
                field in business_info and business_info[field] 
                for field in required_business_fields
            )
        
        # 检查认证信息
        certifications = supplier_data.get('certifications', {})
        if certifications and 'error' not in certifications:
            completeness['certifications'] = 'valid_count' in certifications
        
        # 检查产品信息
        main_products = supplier_data.get('main_products', [])
        completeness['main_products'] = len(main_products) > 0
        
        # 检查专利信息
        patents = supplier_data.get('patents', [])
        completeness['patents'] = len(patents) > 0
        
        # 检查分类特征
        category_feature = supplier_data.get('category_feature', '')
        completeness['category_feature'] = bool(category_feature and category_feature.strip())
        
        # 计算总体完整性得分
        scores = [
            completeness['basic_info'] * 30,      # 基础信息权重30%
            completeness['business_info'] * 25,   # 企业信息权重25%
            completeness['certifications'] * 15,  # 认证信息权重15%
            completeness['main_products'] * 15,   # 产品信息权重15%
            completeness['patents'] * 10,         # 专利信息权重10%
            completeness['category_feature'] * 5  # 分类特征权重5%
        ]
        completeness['overall_score'] = sum(scores)
        
        return completeness
    
    # 私有方法
    
    def _standardize_capital(self, capital_str: str) -> str:
        """标准化注册资本格式"""
        if not capital_str:
            return capital_str
        
        # 移除多余空格
        capital_str = capital_str.strip()
        
        # 标准化单位
        replacements = {
            '万元': '万',
            '千万': '千万',
            '亿元': '亿',
            '万人民币': '万',
            '万美元': '万美元',
            '万欧元': '万欧元'
        }
        
        for old, new in replacements.items():
            capital_str = capital_str.replace(old, new)
        
        return capital_str
    
    def _standardize_staff_size(self, staff_size: str) -> str:
        """标准化员工规模格式"""
        if not staff_size:
            return staff_size
        
        # 只做基本的清理，保留原始表述让大模型处理
        return staff_size.strip()
    
    def _preprocess_certification_item(self, cert: Dict) -> Dict:
        """预处理单个认证项"""
        processed_cert = cert.copy()
        
        # 只做基本的字符串清理
        string_fields = ['name', 'type', 'status', 'number', 'issuer']
        for field in string_fields:
            if field in processed_cert and processed_cert[field]:
                processed_cert[field] = str(processed_cert[field]).strip()
        
        return processed_cert 