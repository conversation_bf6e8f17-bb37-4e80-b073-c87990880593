"""
GPT分析服务
统一处理所有GPT相关的文本分析，包括特征解析、匹配度分析等
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Optional, Tuple
from openai import OpenAI
import time
from dotenv import load_dotenv

# 加载环境变量 - 指定app目录下的.env文件
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'app', '.env'))

logger = logging.getLogger(__name__)

class GPTAnalyzer:
    """GPT分析服务"""
    
    # ==================== PROMPT 模板配置区域 ====================
    
    # 系统角色提示词
    SYSTEM_PROMPT = """你是一位专业的供应商分析专家，请按照要求分析数据并返回JSON格式结果。

重要提示：在评分时，请充分利用0-100分的完整区间，避免过度集中在中等分数段。对于不同供应商要体现出明显的区分度，勇于给出高分（90-100分）和低分（20-50分），以反映真实的匹配程度差异。"""
    
    # 产品分类特征解析提示词
    FEATURE_PARSING_PROMPT = """
请分析以下产品分类特征描述，提取出结构化的技术要求信息：

产品分类特征：
{feature_text}

请从以下维度提取关键信息，并以JSON格式输出：

{{
  "technical_requirements": ["技术要求1", "技术要求2", ...],
  "material_requirements": ["材料要求1", "材料要求2", ...],
  "process_requirements": ["工艺要求1", "工艺要求2", ...],
  "quality_standards": ["质量标准1", "质量标准2", ...],
  "supplier_capabilities": ["供应商能力要求1", "供应商能力要求2", ...]
}}

请确保提取的信息准确、完整，避免遗漏重要的技术要求。
"""
    
    # 特征匹配度分析提示词
    FEATURE_MATCHING_PROMPT = """
你是专业的供应商技术匹配专家，请分析供应商与产品分类需求的匹配度。

产品分类技术特征：
{category_feature}

供应商经营范围：
{business_scope}

供应商主要产品：
{products_text}

请从以下5个维度进行评分（每个维度0-100分）：

1. 核心技术能力匹配度：供应商技术能力是否满足产品分类的技术要求
2. 制造工艺匹配度：供应商制造工艺能力是否符合要求
3. 材料处理能力匹配度：供应商材料处理能力是否满足要求
4. 质量标准匹配度：供应商质量控制能力是否达标
5. 整体业务匹配度：综合评估供应商与产品需求的适配性

**重要评分指导原则：**
1. **请充分利用0-100分的完整评分区间**，不要过度集中在70-90分区间
2. **严格区分不同匹配程度**，相似的供应商也要体现出明显差异
3. **对于明显不匹配的情况，请勇于给出低分**（20-50分）
4. **对于高度匹配的情况，请给出高分**（90-100分）
5. **避免"中庸"评分**，要么匹配度很高，要么有明显不足

请以以下JSON格式输出评分结果：
{{
  "technical_capability_match": {{
    "score": 分数,
    "reason": "详细理由"
  }},
  "manufacturing_process_match": {{
    "score": 分数,
    "reason": "详细理由"
  }},
  "material_handling_match": {{
    "score": 分数,
    "reason": "详细理由"
  }},
  "quality_standard_match": {{
    "score": 分数,
    "reason": "详细理由"
  }},
  "overall_business_match": {{
    "score": 分数,
    "reason": "详细理由"
  }},
  "weighted_average": 加权平均分,
  "summary": "总体匹配度评估总结"
}}

**严格评分标准（请严格执行，增强区分度）：**
- 95-100分：**完全匹配**，供应商在该维度具备超强能力，完全满足甚至超越要求
- 85-94分：**高度匹配**，供应商具备良好能力，基本满足要求且有一定优势
- 70-84分：**基本匹配**，供应商具备基础能力，勉强满足要求但有改进空间
- 55-69分：**部分匹配**，供应商能力不足，存在明显短板和风险
- 30-54分：**匹配度低**，供应商在该维度明显不符合要求
- 0-29分：**完全不匹配**，供应商完全不具备相关能力

**评分参考示例：**
- 如果供应商业务完全不相关，请给出20-40分
- 如果供应商业务部分相关但缺乏核心能力，请给出50-65分  
- 如果供应商具备基本能力但不突出，请给出70-80分
- 如果供应商具备良好匹配度和一定优势，请给出85-92分
- 如果供应商完全符合要求且能力突出，请给出95-100分
"""
    
    # 企业基础实力分析提示词
    BASIC_STRENGTH_PROMPT = """
你是专业的企业实力评估专家，请分析以下企业基础信息并进行评分：

企业信息：
- 企业名称：{company_name}
- 注册资本：{registered_capital}
- 员工规模：{staff_size}
- 企业状态：{company_status}
- 成立时间：{registration_date}
- 企业地址：{address}
- 经营范围：{business_scope}

请从以下5个维度进行评分（每个维度0-100分）：

1. 注册资本实力：评估企业资本规模和财务基础
2. 人员规模实力：评估企业人力资源规模和组织能力
3. 经营年限实力：评估企业经营历史和市场经验
4. 企业状态评估：评估企业当前运营状态和合规性
5. 地理位置优势：评估企业所在地区的产业优势和便利性

请以以下JSON格式输出评分结果：
{{
  "total_score": 总体得分,
  "sub_scores": {{
    "capital_score": 注册资本得分,
    "staff_score": 员工规模得分,
    "years_score": 经营年限得分,
    "status_score": 企业状态得分,
    "location_score": 地理位置得分
  }},
  "analysis": "企业基础实力分析总结（100字以内）"
}}

评分标准：
- 90-100分：实力突出，优势明显
- 80-89分：实力良好，具备优势
- 70-79分：实力一般，基本合格
- 60-69分：实力不足，存在短板
- 0-59分：实力较差，明显不足

**特别说明 - 经营年限评分标准：**
- 成立时间10年以上：80-100分（经验丰富，值得信赖）
- 成立时间5-10年：60-79分（有一定经验）
- 成立时间3-5年：40-59分（经验较少）
- 成立时间3年以下：20-39分（新成立企业）
"""
    
    # 认证信息分析提示词
    CERTIFICATIONS_PROMPT = """
你是专业的质量认证评估专家，请分析以下企业认证信息并进行评分：

{cert_text}

请从以下2个维度进行评分（每个维度0-100分）：

1. 认证数量评估：基于有效认证的数量进行评分
2. 认证质量评估：基于认证类型的权威性和行业价值进行评分

请以以下JSON格式输出评分结果：
{{
  "total_score": 总体得分,
  "sub_scores": {{
    "valid_count_score": 认证数量得分,
    "quality_score": 认证质量得分
  }},
  "analysis": "认证信息分析总结（100字以内）"
}}

评分标准：
- 90-100分：认证体系完善，权威性强
- 80-89分：认证较为完整，质量良好
- 70-79分：认证基本齐全，质量一般
- 60-69分：认证不够完整，质量不足
- 0-59分：认证缺失或质量较差

重要认证类型参考（权重较高）：
- ISO13485（医疗器械质量管理）
- ISO9001（质量管理体系）
- ISO14001（环境管理体系）
- CE认证（欧盟合格认证）
- FDA认证（美国食品药品监督管理局）
"""
    
    # 专利信息分析提示词
    PATENTS_PROMPT = """
你是专业的技术创新评估专家，请分析以下企业专利信息并进行评分：

{patents_text}

请从以下2个维度进行评分（每个维度0-100分）：

1. 专利数量评估：基于专利总数进行评分
2. 专利质量评估：基于专利类型和技术含量进行评分

请以以下JSON格式输出评分结果：
{{
  "total_score": 总体得分,
  "sub_scores": {{
    "patent_count_score": 专利数量得分,
    "patent_quality_score": 专利质量得分
  }},
  "analysis": "专利信息分析总结（100字以内）"
}}

评分标准：
- 90-100分：专利丰富，技术含量高
- 80-89分：专利较多，技术水平良好
- 70-79分：专利一般，技术水平中等
- 60-69分：专利较少，技术水平不足
- 0-59分：专利缺失或技术含量低

专利类型权重参考：
- 发明专利：技术含量最高，权重最大
- 实用新型：技术含量中等，权重中等
- 外观设计：技术含量较低，权重较小
"""
    
    # 综合评估分析提示词
    COMPREHENSIVE_EVALUATION_PROMPT = """
你是资深的供应商评估专家，请基于以下供应商信息进行综合评估并计算最终得分：

公司名称：{company_name}

{business_text}

{cert_text}

{products_text}

{patents_text}

{category_text}

请按照以下权重体系进行评分和计算最终得分：

**评分维度和权重：**

1. **企业基础实力维度（20%权重）**
   - 注册资本规模：>5000万(90-100分)，1000-5000万(70-89分)，500-1000万(50-69分)，<500万(30-49分)
   - 员工规模：>1000人(90-100分)，500-1000人(70-89分)，100-500人(50-69分)，<100人(30-49分)
   - 经营年限：成立时间10年以上(80-100分)，5-10年(60-79分)，3-5年(40-59分)，3年以下(20-39分)
   - 企业状态：存续/开业(100分)，其他状态(0分)
   - 地理位置优势：基于产业集群、交通便利性评估

2. **资质认证维度（15%权重）**
   - 有效认证数量：每个有效认证+10分，最高100分
   - 认证质量：ISO13485(权重1.5)，ISO9001(权重1.2)，其他认证(权重1.0)

3. **产品技术维度（40%权重）**
   - 产品与分类特征匹配度：GPT语义分析产品与分类特征的匹配程度
   - 经营范围与分类特征匹配度：业务范围与技术要求的契合度
   - 技术能力、工艺能力、材料处理能力综合评估

4. **技术创新维度（5%权重）**
   - 专利数量和质量：发明专利权重高于实用新型

请以JSON格式输出评估结果：
{{
  "dimension_scores": {{
    "basic_strength_score": 企业基础实力得分(0-100),
    "certification_score": 资质认证得分(0-100),
    "product_technology_score": 产品技术得分(0-100),
    "innovation_score": 技术创新得分(0-100)
  }},
  "final_score": 最终加权得分,
  "calculation_detail": "得分计算过程说明",
  "strengths": ["优势1", "优势2", "优势3"],
  "weaknesses": ["劣势1", "劣势2", "劣势3"],
  "recommendations": ["建议1", "建议2", "建议3"],
  "risk_warnings": ["风险1", "风险2"],
  "overall_assessment": "综合评估总结（150字以内）"
}}

**计算公式：**
最终得分 = 企业基础实力得分 × 0.20 + 资质认证得分 × 0.15 + 产品技术得分 × 0.40 + 技术创新得分 × 0.05

请确保严格按照权重体系计算最终得分，并提供详细的计算过程说明。
"""
    
    # 汇总分析提示词（新增：用于4+1模式的第5步）
    SUMMARY_EVALUATION_PROMPT = """
你是资深的供应商评估专家，现在需要基于前4步的详细分析结果进行最终汇总评估。

**供应商基本信息：**
公司名称：{supplier_name}

**前4步分析结果：**

1. **企业基础实力分析结果：**
   - 得分：{basic_strength_score}分
   - 分析：{basic_strength_analysis}

2. **认证信息分析结果：**
   - 得分：{certification_score}分  
   - 分析：{certification_analysis}

3. **特征匹配度分析结果：**
   - 得分：{feature_matching_score}分
   - 分析：{feature_matching_analysis}

4. **专利创新分析结果：**
   - 得分：{innovation_score}分
   - 分析：{innovation_analysis}

**权重体系：**
- 企业基础实力：20%权重
- 资质认证：15%权重  
- 产品技术：40%权重
- 技术创新：5%权重

**计算公式：**
最终得分 = {basic_strength_score} × 0.20 + {certification_score} × 0.15 + {feature_matching_score} × 0.40 + {innovation_score} × 0.05

请基于以上分析结果，进行综合汇总评估，并以JSON格式输出：

{{
  "final_score": 最终加权得分,
  "calculation_detail": "详细的计算过程说明",
  "strengths": ["综合优势1", "综合优势2", "综合优势3"],
  "weaknesses": ["综合劣势1", "综合劣势2", "综合劣势3"],
  "recommendations": ["综合建议1", "综合建议2", "综合建议3"],
  "risk_warnings": ["风险警告1", "风险警告2"],
  "overall_assessment": "基于4个维度分析的综合评估总结（200字以内）",
  "dimension_summary": {{
    "basic_strength_summary": "企业基础实力维度总结",
    "certification_summary": "认证维度总结", 
    "feature_matching_summary": "产品技术维度总结",
    "innovation_summary": "技术创新维度总结"
  }}
}}

请确保：
1. 严格按照权重公式计算最终得分
2. 综合考虑各维度的分析结果
3. 提供具有针对性的建议和风险警告
4. 总结要客观、准确、有价值
"""
    
    # ==================== 类初始化和工具方法 ====================
    
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
        )
        self.model = "gpt-4o"
        self.cache = {}
        self.rate_limit_delay = 1  # 秒
        
    def _get_cache_key(self, prompt: str, data: str) -> str:
        """生成缓存键"""
        import hashlib
        content = f"{prompt}:{data}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _call_gpt(self, prompt: str, max_retries: int = 3, temperature: float = 0.3) -> Optional[str]:
        """调用GPT API"""
        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": self.SYSTEM_PROMPT},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=temperature,
                    max_tokens=2000
                )
                
                result = response.choices[0].message.content.strip()
                
                # 尝试解析JSON以验证格式
                try:
                    json.loads(result)
                    return result
                except json.JSONDecodeError:
                    # 如果不是有效JSON，尝试提取JSON部分
                    if "```json" in result:
                        start = result.find("```json") + 7
                        end = result.find("```", start)
                        if end > start:
                            json_part = result[start:end].strip()
                            json.loads(json_part)  # 验证
                            return json_part
                    raise
                    
            except Exception as e:
                logger.warning(f"GPT API call attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(self.rate_limit_delay * (attempt + 1))
                else:
                    logger.error(f"GPT API call failed after {max_retries} attempts: {str(e)}")
                    return None
        
        return None
    
    def parse_category_feature(self, feature_text: str) -> Dict:
        """解析产品分类特征"""
        cache_key = self._get_cache_key("parse_feature", feature_text)
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        prompt = self.FEATURE_PARSING_PROMPT.format(feature_text=feature_text)
        
        result_str = self._call_gpt(prompt)
        if result_str:
            try:
                result = json.loads(result_str)
                self.cache[cache_key] = result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse GPT response for feature parsing: {e}")
        
        # 返回默认结构
        return {
            "technical_requirements": [],
            "material_requirements": [],
            "process_requirements": [],
            "quality_standards": [],
            "supplier_capabilities": []
        }
    
    def analyze_feature_matching(self, category_feature: str, business_scope: str, main_products) -> Dict:
        """分析特征匹配度"""
        # 处理主要产品数据 - 现在main_products可能是markdown文本或字典列表
        products_text = ""
        if main_products:
            if isinstance(main_products, str):
                # 如果是字符串（网站分析的markdown文本），直接使用
                products_text = main_products
            elif isinstance(main_products, list):
                # 如果是列表（旧格式的结构化产品数据），转换为文本
                products_list = []
                for product in main_products:
                    if isinstance(product, dict):
                        name = product.get('name', '')
                        desc = product.get('description', '')
                        products_list.append(f"{name}: {desc}")
                    elif isinstance(product, str):
                        products_list.append(product)
                products_text = "; ".join(products_list)
            else:
                # 其他类型，尝试转换为字符串
                products_text = str(main_products)
        
        cache_key = self._get_cache_key("feature_matching", f"{category_feature}|{business_scope}|{products_text}")
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        prompt = self.FEATURE_MATCHING_PROMPT.format(
            category_feature=category_feature,
            business_scope=business_scope or "未提供",
            products_text=products_text or "未提供"
        )
        
        result_str = self._call_gpt(prompt, temperature=0.6)
        if result_str:
            try:
                result = json.loads(result_str)
                # 计算加权平均分
                if 'weighted_average' not in result:
                    scores = [
                        result.get('technical_capability_match', {}).get('score', 0),
                        result.get('manufacturing_process_match', {}).get('score', 0),
                        result.get('material_handling_match', {}).get('score', 0),
                        result.get('quality_standard_match', {}).get('score', 0),
                        result.get('overall_business_match', {}).get('score', 0)
                    ]
                    result['weighted_average'] = sum(scores) / len(scores) if scores else 0
                
                self.cache[cache_key] = result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse GPT response for feature matching: {e}")
        
        # 返回默认结构
        return {
            "technical_capability_match": {"score": 50, "reason": "GPT分析失败，使用默认评分"},
            "manufacturing_process_match": {"score": 50, "reason": "GPT分析失败，使用默认评分"},
            "material_handling_match": {"score": 50, "reason": "GPT分析失败，使用默认评分"},
            "quality_standard_match": {"score": 50, "reason": "GPT分析失败，使用默认评分"},
            "overall_business_match": {"score": 50, "reason": "GPT分析失败，使用默认评分"},
            "weighted_average": 50,
            "summary": "由于GPT分析服务异常，使用默认评分"
        }
    
    def analyze_basic_strength(self, business_info: Dict) -> Dict:
        """分析企业基础实力"""
        cache_key = self._get_cache_key("basic_strength", json.dumps(business_info, sort_keys=True))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        prompt = self.BASIC_STRENGTH_PROMPT.format(company_name=business_info.get('company_name', '未知'),
                                                  registered_capital=business_info.get('registered_capital', '未知'),
                                                  staff_size=business_info.get('staff_size', '未知'),
                                                  company_status=business_info.get('company_status', '未知'),
                                                  registration_date=business_info.get('registration_date', '未知'),
                                                  address=business_info.get('address', '未知'),
                                                  business_scope=business_info.get('business_scope', '未知'))
        
        result_str = self._call_gpt(prompt)
        if result_str:
            try:
                result = json.loads(result_str)
                self.cache[cache_key] = result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse GPT response for basic strength: {e}")
        
        # 返回默认结构
        return {
            "total_score": 50,
            "sub_scores": {
                "capital_score": 50,
                "staff_score": 50,
                "years_score": 50,
                "status_score": 50,
                "location_score": 50
            },
            "analysis": "GPT分析失败，使用默认评分"
        }
    
    def analyze_certifications(self, certifications: Dict, website_analysis: str = None) -> Dict:
        """分析认证信息，包含网站分析中的资质相关内容"""
        cache_key = self._get_cache_key("certifications", str(certifications) + str(website_analysis or ''))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 构建认证信息文本
        cert_text = "认证信息：\n"
        if certifications and 'certifications' in certifications:
            for cert in certifications['certifications']:
                cert_text += f"- {cert.get('name', '未知认证')} (状态: {cert.get('status', '未知')}, 有效期: {cert.get('validity', '未知')})\n"
        else:
            cert_text += "未获取到具体认证信息\n"
        
        # 添加网站分析中的资质相关信息
        website_cert_text = ""
        if website_analysis:
            website_cert_text = f"""
网站分析中的资质相关信息：
{website_analysis}

请从上述网站分析内容中，只提取与资质认证、质量体系、行业标准、合规性相关的信息进行分析。
忽略产品介绍、技术细节、公司历史等非资质相关内容。
"""
        
        prompt = self.CERTIFICATIONS_PROMPT.format(
            cert_text=cert_text + website_cert_text
        )
        
        try:
            response = self._call_gpt(prompt)
            if response:
                result = json.loads(response)
                self.cache[cache_key] = result
                return result
        except Exception as e:
            logger.error(f"Error in analyze_certifications: {e}")
        
        # 降级处理
        return self._get_default_certification_result()
    
    def analyze_patents(self, patents: List[Dict]) -> Dict:
        """分析专利信息"""
        cache_key = self._get_cache_key("patents", json.dumps(patents, sort_keys=True))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 构建专利信息文本
        patents_text = f"专利总数：{len(patents)}\n\n专利详情：\n"
        
        patent_types = {}
        for i, patent in enumerate(patents[:20]):  # 只取前20个专利
            if isinstance(patent, dict):
                patent_type = patent.get('type', '未知类型')
                patent_types[patent_type] = patent_types.get(patent_type, 0) + 1
                patents_text += f"- {patent.get('name', '未知专利')} ({patent_type}) - {patent.get('status', '未知状态')}\n"
        
        # 添加专利类型统计
        patents_text += f"\n专利类型统计：\n"
        for ptype, count in patent_types.items():
            patents_text += f"- {ptype}：{count}项\n"
        
        prompt = self.PATENTS_PROMPT.format(patents_text=patents_text)
        
        result_str = self._call_gpt(prompt)
        if result_str:
            try:
                result = json.loads(result_str)
                self.cache[cache_key] = result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse GPT response for patents: {e}")
        
        # 返回默认结构
        return {
            "total_score": 30,
            "sub_scores": {
                "patent_count_score": 30,
                "patent_quality_score": 30
            },
            "analysis": "GPT分析失败，使用默认评分"
        }
    
    def analyze_innovation(self, patents: List[Dict], website_analysis: str = None, business_scope: str = None) -> Dict:
        """综合分析创新能力，基于专利信息、网站分析内容和经营范围"""
        cache_key = self._get_cache_key("innovation", str(patents) + str(website_analysis or '') + str(business_scope or ''))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 构建专利信息文本
        patents_text = "专利信息：\n"
        if patents:
            for patent in patents[:10]:  # 只取前10个专利
                if isinstance(patent, dict):
                    patents_text += f"- {patent.get('name', '未知专利')} ({patent.get('type', '未知类型')})\n"
        else:
            patents_text += "未获取到专利信息\n"
        
        # 构建网站分析中的创新相关信息
        website_innovation_text = ""
        if website_analysis:
            website_innovation_text = f"""
网站分析中的创新相关信息：
{website_analysis}

请从上述网站分析内容中，重点关注以下创新相关信息：
- 技术创新能力和研发实力
- 新产品开发和技术突破
- 研发团队和技术人员
- 创新项目和技术合作
- 技术优势和核心竞争力
忽略基本的产品介绍和公司概况。
"""
        
        # 构建经营范围信息
        business_scope_text = ""
        if business_scope:
            business_scope_text = f"""
企业经营范围：
{business_scope}

请从经营范围中分析企业的技术领域和创新潜力。
"""
        
        # 构建完整的创新分析prompt
        innovation_prompt = f"""
你是专业的技术创新评估专家，请综合分析以下企业的创新能力并进行评分：

{patents_text}

{website_innovation_text}

{business_scope_text}

请从以下3个维度进行评分（每个维度0-100分）：

1. 专利创新能力：基于专利数量、类型和技术含量评估
2. 网站展示的创新实力：基于网站分析中的技术创新信息评估
3. 经营范围创新潜力：基于经营范围分析企业的技术领域和创新方向

请以以下JSON格式输出评分结果：
{{
  "total_score": 总体得分,
  "sub_scores": {{
    "patent_score": 专利创新得分,
    "website_innovation_score": 网站创新展示得分,
    "business_scope_score": 经营范围创新潜力得分
  }},
  "analysis": "创新能力分析总结（150字以内）"
}}

评分标准：
- 90-100分：创新能力突出，技术领先
- 80-89分：创新能力良好，技术先进
- 70-79分：创新能力一般，技术成熟
- 60-69分：创新能力不足，技术落后
- 0-59分：创新能力缺失，技术薄弱

权重分配：
- 专利创新能力：40%
- 网站创新展示：35%
- 经营范围创新潜力：25%
"""
        
        try:
            response = self._call_gpt(innovation_prompt)
            if response:
                result = json.loads(response)
                self.cache[cache_key] = result
                return result
        except Exception as e:
            logger.error(f"Error in analyze_innovation: {e}")
        
        # 降级处理
        return self._get_default_innovation_result()
    
    def comprehensive_evaluation(self, supplier_data: Dict) -> Dict:
        """综合评估分析 - 按照权重体系计算最终得分"""
        cache_key = self._get_cache_key("comprehensive", json.dumps(supplier_data, sort_keys=True))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 提取关键信息
        company_name = supplier_data.get('supplier_name', '未知公司')
        business_info = supplier_data.get('business_info', {})
        certifications = supplier_data.get('certifications', {})
        main_products = supplier_data.get('main_products', [])
        patents = supplier_data.get('patents', [])
        category_feature = supplier_data.get('category_feature', '')
        
        # 构建分析文本
        business_text = ""
        if business_info:
            business_text = f"""
企业基础信息：
- 企业名称：{business_info.get('company_name', '未知')}
- 注册资本：{business_info.get('registered_capital', '未知')}
- 员工规模：{business_info.get('staff_size', '未知')}
- 企业状态：{business_info.get('company_status', '未知')}
- 成立时间：{business_info.get('registration_date', '未知')}
- 企业地址：{business_info.get('address', '未知')}
- 经营范围：{business_info.get('business_scope', '未知')}
"""
        
        cert_text = ""
        if certifications and 'error' not in certifications:
            cert_text = f"""
认证信息：
- 有效认证数量：{certifications.get('valid_count', 0)}个
- 总认证数量：{certifications.get('total_count', 0)}个
- 数据来源：{certifications.get('data_source', '未知')}
"""
            cert_list = certifications.get('certifications', [])
            if cert_list:
                cert_text += "认证详情：\n"
                for cert in cert_list[:5]:  # 只取前5个认证
                    if isinstance(cert, dict):
                        cert_text += f"- {cert.get('name', '未知认证')} ({cert.get('type', '未知类型')}) - {cert.get('status', '未知状态')}\n"
        else:
            cert_text = "认证信息：未获取到认证信息"
        
        products_text = ""
        if main_products:
            if isinstance(main_products, str):
                # 如果是字符串（网站分析的markdown文本），直接使用
                products_text = f"主要产品：\n{main_products}"
            elif isinstance(main_products, list):
                # 如果是列表（旧格式的结构化产品数据），转换为文本
                products_text = "主要产品：\n"
                for product in main_products[:5]:  # 只取前5个产品
                    if isinstance(product, dict):
                        name = product.get('name', '')
                        desc = product.get('description', '')
                        importance = product.get('importance', '')
                        products_text += f"- {name}: {desc} (重要程度: {importance})\n"
                    elif isinstance(product, str):
                        products_text += f"- {product}\n"
            else:
                # 其他类型，尝试转换为字符串
                products_text = f"主要产品：\n{str(main_products)}"
        else:
            products_text = "主要产品：未获取到产品信息"
        
        patents_text = ""
        if patents:
            patent_count = len(patents)
            invention_count = len([p for p in patents if p.get('type') == '发明专利'])
            utility_count = len([p for p in patents if p.get('type') == '实用新型'])
            design_count = len([p for p in patents if p.get('type') == '外观设计'])
            patents_text = f"""
专利信息：
- 专利总数：{patent_count}项
- 发明专利：{invention_count}项
- 实用新型：{utility_count}项
- 外观设计：{design_count}项
"""
        else:
            patents_text = "专利信息：未获取到专利信息"
        
        category_text = f"产品分类特征：{category_feature}" if category_feature else "产品分类特征：未提供"
        
        prompt = self.COMPREHENSIVE_EVALUATION_PROMPT.format(company_name=company_name,
                                                            business_text=business_text,
                                                            cert_text=cert_text,
                                                            products_text=products_text,
                                                            patents_text=patents_text,
                                                            category_text=category_text)
        
        result_str = self._call_gpt(prompt)
        if result_str:
            try:
                result = json.loads(result_str)
                self.cache[cache_key] = result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse GPT response for comprehensive evaluation: {e}")
        
        # 返回默认结构
        return {
            "dimension_scores": {
                "basic_strength_score": 50,
                "certification_score": 30,
                "product_technology_score": 50,
                "innovation_score": 30
            },
            "final_score": 45,  # 50*0.2 + 30*0.15 + 50*0.4 + 30*0.05
            "calculation_detail": "GPT分析失败，使用默认权重计算：50×0.20 + 30×0.15 + 50×0.40 + 30×0.05 = 45分",
            "strengths": ["企业信息完整"],
            "weaknesses": ["需要更多分析数据"],
            "recommendations": ["建议补充更多企业信息"],
            "risk_warnings": ["GPT分析服务异常，评估可能不准确"],
            "overall_assessment": "由于GPT分析服务异常，无法提供详细评估，使用默认评分体系计算得分"
        }
    
    def summary_evaluation(self, summary_data: Dict) -> Dict:
        """汇总分析 - 基于4个维度的分析结果进行最终汇总评估"""
        cache_key = self._get_cache_key("summary", json.dumps(summary_data, sort_keys=True))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        prompt = self.SUMMARY_EVALUATION_PROMPT.format(
            supplier_name=summary_data.get('supplier_name', '未知公司'),
            basic_strength_score=summary_data.get('basic_strength_score', 0),
            basic_strength_analysis=summary_data.get('basic_strength_analysis', '无分析'),
            certification_score=summary_data.get('certification_score', 0),
            certification_analysis=summary_data.get('certification_analysis', '无分析'),
            feature_matching_score=summary_data.get('feature_matching_score', 0),
            feature_matching_analysis=summary_data.get('feature_matching_analysis', '无分析'),
            innovation_score=summary_data.get('innovation_score', 0),
            innovation_analysis=summary_data.get('innovation_analysis', '无分析')
        )
        
        result_str = self._call_gpt(prompt)
        if result_str:
            try:
                result = json.loads(result_str)
                self.cache[cache_key] = result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse GPT response for summary evaluation: {e}")
        
        # 返回默认结构
        basic_strength_score = summary_data.get('basic_strength_score', 50)
        certification_score = summary_data.get('certification_score', 30)
        feature_matching_score = summary_data.get('feature_matching_score', 50)
        innovation_score = summary_data.get('innovation_score', 30)
        
        final_score = (
            basic_strength_score * 0.20 +
            certification_score * 0.15 +
            feature_matching_score * 0.40 +
            innovation_score * 0.05
        )
        
        return {
            "final_score": round(final_score, 2),
            "calculation_detail": f"权重计算：{basic_strength_score}×0.20 + {certification_score}×0.15 + {feature_matching_score}×0.40 + {innovation_score}×0.05 = {final_score:.1f}分",
            "strengths": ["企业信息完整"],
            "weaknesses": ["GPT汇总分析失败"],
            "recommendations": ["建议补充更多企业信息"],
            "risk_warnings": ["GPT汇总服务异常，使用权重计算"],
            "overall_assessment": "由于GPT汇总分析异常，使用权重公式计算最终得分",
            "dimension_summary": {
                "basic_strength_summary": "企业基础实力维度分析完成",
                "certification_summary": "认证维度分析完成",
                "feature_matching_summary": "产品技术维度分析完成",
                "innovation_summary": "技术创新维度分析完成"
            }
        }
    
    def fallback_keyword_matching(self, category_feature: str, business_scope: str, main_products) -> Dict:
        """降级方案：简单关键词匹配"""
        logger.info("Using fallback keyword matching")
        
        # 提取关键词
        feature_keywords = set()
        if category_feature:
            # 简单的关键词提取
            import re
            words = re.findall(r'[\u4e00-\u9fff]+', category_feature)
            feature_keywords.update([w for w in words if len(w) >= 2])
        
        # 计算匹配度
        business_match = 0
        products_match = 0
        
        if business_scope and feature_keywords:
            business_words = set(re.findall(r'[\u4e00-\u9fff]+', business_scope))
            common_words = feature_keywords.intersection(business_words)
            business_match = min(100, len(common_words) * 20)
        
        if main_products and feature_keywords:
            # 处理不同格式的main_products
            if isinstance(main_products, str):
                # 如果是字符串（网站分析的markdown文本）
                all_product_text = main_products
            elif isinstance(main_products, list):
                # 如果是列表（旧格式的结构化产品数据）
                all_product_text = " ".join([
                    f"{p.get('name', '')} {p.get('description', '')}" 
                    for p in main_products if isinstance(p, dict)
                ] + [
                    str(p) for p in main_products if isinstance(p, str)
                ])
            else:
                # 其他类型，转换为字符串
                all_product_text = str(main_products)
            
            product_words = set(re.findall(r'[\u4e00-\u9fff]+', all_product_text))
            common_words = feature_keywords.intersection(product_words)
            products_match = min(100, len(common_words) * 15)
        
        avg_score = (business_match + products_match) / 2
        
        return {
            "technical_capability_match": {"score": avg_score, "reason": "基于关键词匹配的技术能力评估"},
            "manufacturing_process_match": {"score": avg_score, "reason": "基于关键词匹配的工艺能力评估"},
            "material_handling_match": {"score": avg_score, "reason": "基于关键词匹配的材料处理评估"},
            "quality_standard_match": {"score": avg_score, "reason": "基于关键词匹配的质量标准评估"},
            "overall_business_match": {"score": avg_score, "reason": "基于关键词匹配的整体业务评估"},
            "weighted_average": avg_score,
            "summary": f"使用关键词匹配降级方案，匹配度为{avg_score:.1f}分"
        }
    
    def _get_default_certification_result(self) -> Dict:
        """获取默认的认证分析结果"""
        return {
            "total_score": 30,
            "sub_scores": {
                "valid_count_score": 30,
                "quality_score": 30
            },
            "analysis": "GPT分析失败，使用默认评分"
        }
    
    def _get_default_innovation_result(self) -> Dict:
        """获取默认的创新能力分析结果"""
        return {
            "total_score": 30,
            "sub_scores": {
                "patent_score": 15,
                "website_innovation_score": 15,
                "business_scope_score": 15
            },
            "analysis": "GPT分析失败，使用默认评分"
        } 