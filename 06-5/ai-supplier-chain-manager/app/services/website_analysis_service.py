#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import logging
import asyncio
import re
import traceback
from typing import Optional
from dotenv import load_dotenv
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.tools.mcp import McpWorkbench, StdioServerParams

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

# Web Expert 系统提示配置
WEB_EXPERT_SYSTEM_PROMPT = """你是一位专业的供应商网站分析专家，专门负责从供应商的官方网站分析其产品信息和技术能力。

你有能力使用Playwright浏览器访问和分析网站。当提供URL时，你应该使用以下步骤操作：

1. 使用Playwright工具访问网站
- 你可以通过workbench附加的工具访问网站
- 如果需要获取页面内容，可以使用DOM相关工具
- 如果需要进行交互，可以使用鼠标和键盘相关工具

2. 重点分析产品信息，包括：
- 浏览"产品"、"产品中心"、"产品目录"、"解决方案"等栏目
- 详细收集每个主要产品的名称、特点、参数和用途
- 识别供应商的核心产品线和主打产品
- 了解产品的技术规格和性能指标
- 尝试确定各产品在供应商业务中的重要性
- 寻找产品应用场景和典型客户案例

3. 顺便查看专利信息：
- 查找"知识产权"、"专利"、"研发实力"等相关页面
- 收集专利名称、类型、编号等信息（如果有的话）

4. 其他相关信息：
- 公司简介和背景
- 技术能力和研发实力
- 质量控制体系和生产能力
- 认证和资质信息

请基于实际网站内容进行分析，避免生成虚假或推测性的信息。如果无法访问网站或获取特定信息，请明确说明。

**重要要求：**
- 不要在分析结果中包含任何代码示例、技术实现细节或访问过程描述
- 不要描述如何使用工具或访问网站的技术步骤
- 直接提供网站分析的实际内容和结果
- 专注于产品信息、公司能力和业务内容的分析

输出格式要求：
请严格按照以下Markdown格式输出分析结果：

## 公司概述
[公司基本情况描述]

## 主要产品分析
### 1. [产品名称]
- **描述**：[产品详细描述]
- **特点**：[产品主要特点和优势]
- **用途**：[产品应用场景和用途]
- **技术规格**：[技术参数和规格，如果有的话]

### 2. [产品名称]
- **描述**：[产品详细描述]
- **特点**：[产品主要特点和优势]
- **用途**：[产品应用场景和用途]
- **技术规格**：[技术参数和规格，如果有的话]

[继续列出其他主要产品...]

## 技术能力
[技术实力、研发能力、生产能力等描述]

## 专利信息
[专利相关信息，如果网站上没有相关信息，请明确说明]

## 其他信息
### 认证和资质
[相关认证和资质信息]

### 质量控制
[质量控制体系信息]

### 典型客户案例
[客户案例信息，如果有的话]

## 分析总结
[整体评价和关键发现，包括公司在行业中的定位、核心竞争力等]

如果无法访问网站或网站未提供有用信息，请说明具体原因。重点关注产品信息的完整性和准确性。

注意：请严格按照上述Markdown格式输出，确保标题层级正确，使用适当的加粗、列表等格式。不要包含任何代码、技术实现或访问过程的描述。"""


class WebsiteAnalysisService:
    """网站分析服务 - 用于分析供应商网站获取产品和专利信息"""
    
    def __init__(self, model_client: Optional[OpenAIChatCompletionClient] = None):
        """
        初始化网站分析服务
        
        Args:
            model_client: OpenAI 模型客户端，如果不提供则使用默认配置
        """
        # 初始化模型客户端
        if model_client is None:
            # 从环境变量获取API密钥
            api_key = os.environ.get('OPENAI_API_KEY')
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
                
            self.model_client = OpenAIChatCompletionClient(
                model="gpt-4o-2024-05-13",
                api_key=api_key,
                timeout=120,
                max_retries=3,
                model_info={
                    "vision": True,
                    "function_calling": True,
                    "json_output": True,
                    "family": "gpt-4o",
                    "structured_output": True
                }
            )
        else:
            self.model_client = model_client
            
        # 初始化 Web Expert 代理
        self.web_expert = AssistantAgent(
            "web_expert",
            model_client=self.model_client,
            system_message=WEB_EXPERT_SYSTEM_PROMPT
        )
        
        # Playwright 工作台（需要时初始化）
        self.playwright_workbench = None
        
    async def initialize_playwright(self):
        """初始化 Playwright 工作台"""
        if self.playwright_workbench is None:
            try:
                logger.info("正在初始化 Playwright MCP 工作台...")
                self.playwright_workbench = await self.create_playwright_workbench()
                if self.playwright_workbench:
                    logger.info("Playwright 工作台初始化成功")
                    # 将工作台附加到 web_expert
                    self.web_expert.workbench = self.playwright_workbench
                    
                    # 测试工作台连接
                    await self.test_playwright_connection()
                else:
                    logger.error("Playwright 工作台初始化失败")
                    raise Exception("Playwright 工作台初始化失败")
            except Exception as e:
                logger.error(f"初始化 Playwright 工作台时出错: {str(e)}")
                raise
    
    async def test_playwright_connection(self):
        """测试 Playwright 连接"""
        try:
            if self.playwright_workbench:
                # 获取可用工具列表
                tools = await self.playwright_workbench.list_tools()
                logger.info(f"Playwright 工具数量: {len(tools)}")
                
                # 处理工具列表格式
                tool_names = []
                for tool in tools:
                    if hasattr(tool, 'name'):
                        tool_names.append(tool.name)
                    elif isinstance(tool, dict) and 'name' in tool:
                        tool_names.append(tool['name'])
                    else:
                        tool_names.append(str(tool))
                
                logger.info(f"Playwright 可用工具: {tool_names}")
                
                if not tools:
                    logger.warning("Playwright 工作台没有可用工具")
                else:
                    logger.info("Playwright 工作台连接正常")
            else:
                logger.error("Playwright 工作台未初始化")
        except Exception as e:
            logger.error(f"测试 Playwright 连接失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    async def create_playwright_workbench(self):
        """创建 Playwright 工作台"""
        try:
            logger.info("创建 Playwright MCP 服务器...")
            playwright_params = StdioServerParams(
                command="npx",
                args=["-y", "@playwright/mcp@latest", "--headless", "--no-sandbox"],
                read_timeout_seconds=180  # 增加到3分钟
            )
            
            workbench = McpWorkbench(server_params=playwright_params)
            logger.info("启动 Playwright MCP 服务器...")
            await workbench.start()
            logger.info("Playwright MCP 服务器启动成功")
            return workbench
        except Exception as e:
            logger.error(f"创建 Playwright 工作台失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    async def analyze_website(self, supplier_name: str, supplier_website: Optional[str] = None) -> str:
        """
        分析供应商网站获取产品和专利信息
        
        Args:
            supplier_name: 供应商名称
            supplier_website: 供应商网站URL（可选）
            
        Returns:
            分析结果（JSON格式的字符串）
        """
        # 确保 Playwright 已初始化
        await self.initialize_playwright()
        
        # 验证工具是否可用
        if not self.playwright_workbench:
            raise Exception("Playwright 工作台未能正确初始化")
        
        # 获取可用工具
        tools = await self.playwright_workbench.list_tools()
        if not tools:
            raise Exception("Playwright 工作台没有可用工具")
        
        # 处理工具列表格式
        tool_names = []
        for tool in tools:
            if hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif isinstance(tool, dict) and 'name' in tool:
                tool_names.append(tool['name'])
            else:
                tool_names.append(str(tool))
        
        logger.info(f"可用的 Playwright 工具: {tool_names}")
        
        # 构建强制使用工具的分析提示
        if supplier_website:
            web_prompt = f"""你必须使用Playwright工具访问网站 {supplier_website} 来分析供应商 {supplier_name}。

重要指示：
1. 你必须首先使用可用的Playwright工具访问网站URL: {supplier_website}
2. 使用DOM相关工具获取页面内容
3. 重点查找产品相关信息：
   - 产品展示页面
   - 产品目录或产品中心
   - 技术规格和参数
   - 应用案例
4. 顺便查看专利和知识产权信息（如果有的话）
5. 如果无法访问网站，请明确说明具体的错误原因

**重要要求：**
- 不要在分析结果中包含任何代码示例、技术实现细节或访问过程描述
- 不要描述如何使用工具或访问网站的技术步骤
- 直接提供网站分析的实际内容和结果
- 专注于产品信息、公司能力和业务内容的分析

请务必使用工具访问网站，不要基于假设生成内容。

访问网站后，请严格按照Markdown格式输出分析结果，重点描述产品信息。"""
            logger.info(f"将使用URL: {supplier_website} 进行网站分析")
        else:
            web_prompt = f"""你必须使用Playwright工具搜索并访问供应商 {supplier_name} 的官方网站。

重要指示：
1. 首先使用搜索功能查找 {supplier_name} 的官方网站
2. 访问找到的官方网站
3. 使用DOM工具获取页面内容
4. 重点分析产品信息和技术能力
5. 顺便查看专利信息（如果有的话）

**重要要求：**
- 不要在分析结果中包含任何代码示例、技术实现细节或访问过程描述
- 不要描述如何使用工具或访问网站的技术步骤
- 直接提供网站分析的实际内容和结果
- 专注于产品信息、公司能力和业务内容的分析

请务必使用工具进行实际的网站访问和搜索，不要基于假设生成内容。

请严格按照Markdown格式输出分析结果，重点描述产品信息。"""
            logger.info("未提供供应商网站URL，将尝试搜索")
        
        # 执行网站分析
        logger.info("开始执行网站分析任务")
        try:
            # 强制要求使用工具
            web_result = await self.web_expert.run(task=web_prompt)
            
            # 从 TaskResult 中提取内容
            web_info = self._extract_content_from_task_result(web_result)
            logger.info("网站分析专家完成信息收集")
            
            # 验证结果是否包含实际内容
            if self._is_placeholder_result(web_info):
                logger.warning("检测到占位符结果，可能没有真正访问网站")
                # 尝试再次分析
                return await self._retry_analysis_with_explicit_tools(supplier_name, supplier_website)
            
            return web_info
        except Exception as e:
            logger.error(f"执行网站分析任务失败: {str(e)}")
            raise
    
    def _is_placeholder_result(self, result: str) -> bool:
        """检查结果是否为占位符（全是NA）或包含不当内容"""
        # 现在使用文本格式，简单检查是否包含"无法访问"等关键词
        if not result or len(result.strip()) < 50:
            return True
        
        failure_keywords = [
            "无法访问", "无法分析", "信息不可用", "NA", 
            "访问失败", "网站无法打开", "页面加载失败"
        ]
        
        # 检查是否包含不希望的代码或技术实现内容
        unwanted_keywords = [
            "from playwright", "sync_playwright", "def fetch_website_content",
            "browser.new_page()", "page.goto(", "page.content()",
            "import", "def ", "class ", "browser.close()",
            "网站访问与信息收集", "访问过程中", "使用Playwright"
        ]
        
        # 如果结果主要包含失败关键词，认为是占位符结果
        failure_count = sum(1 for keyword in failure_keywords if keyword in result)
        
        # 如果包含不希望的技术实现内容，也认为是无效结果
        unwanted_count = sum(1 for keyword in unwanted_keywords if keyword in result)
        
        return failure_count > 2 or unwanted_count > 0
    
    async def _retry_analysis_with_explicit_tools(self, supplier_name: str, supplier_website: Optional[str]) -> str:
        """使用明确的工具调用重试分析"""
        logger.info("重试分析，使用明确的工具调用")
        
        # 获取工具列表
        tools = await self.playwright_workbench.list_tools()
        
        # 处理工具列表格式
        tool_names = []
        for tool in tools:
            if hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif isinstance(tool, dict) and 'name' in tool:
                tool_names.append(tool['name'])
            else:
                tool_names.append(str(tool))
        
        retry_prompt = f"""你现在必须使用以下可用的Playwright工具来访问网站：
可用工具: {tool_names}

任务：分析供应商 {supplier_name}
网站URL: {supplier_website if supplier_website else '需要搜索'}

步骤：
1. 如果有URL，使用导航工具访问 {supplier_website}
2. 如果没有URL，使用搜索工具查找官方网站
3. 使用页面内容获取工具读取页面信息
4. 重点分析产品信息和技术能力
5. 顺便查看专利信息（如果有的话）
6. 严格按照Markdown格式输出分析结果

**重要要求：**
- 不要在分析结果中包含任何代码示例、技术实现细节或访问过程描述
- 不要描述如何使用工具或访问网站的技术步骤
- 直接提供网站分析的实际内容和结果
- 专注于产品信息、公司能力和业务内容的分析

重要：你必须实际调用工具，不能生成假设的内容！"""

        try:
            result = await self.web_expert.run(task=retry_prompt)
            return self._extract_content_from_task_result(result)
        except Exception as e:
            logger.error(f"重试分析失败: {str(e)}")
            return self._get_default_na_result()
    
    def _get_default_na_result(self) -> str:
        """获取默认的失败结果"""
        return """## 公司概述
无法访问或分析供应商网站，所有信息不可用。

## 主要产品分析
无法获取产品信息。

## 技术能力
无法获取技术能力信息。

## 专利信息
无法获取专利信息。

## 其他信息
### 认证和资质
无法获取认证和资质信息。

### 质量控制
无法获取质量控制信息。

### 典型客户案例
无法获取客户案例信息。

## 分析总结
由于无法访问供应商网站，无法进行有效的产品信息分析。建议检查网站URL是否正确或网站是否可正常访问。"""
    
    def _extract_content_from_task_result(self, task_result):
        """
        从 TaskResult 中正确提取内容
        
        Args:
            task_result: TaskResult 对象
            
        Returns:
            提取后的内容
        """
        try:
            # 如果已经是字符串，直接返回
            if isinstance(task_result, str):
                return self._extract_text_from_content(task_result)
                
            # 尝试访问 content 属性（如果是 TextMessage）
            if hasattr(task_result, 'content'):
                return self._extract_text_from_content(task_result.content)
                
            # 尝试检查是否有 messages 属性（TaskResult 通常有）
            if hasattr(task_result, 'messages') and task_result.messages:
                # 通常最后一条消息是代理的回复
                last_message = task_result.messages[-1]
                if hasattr(last_message, 'content'):
                    return self._extract_text_from_content(last_message.content)
                    
            # 尝试访问 result 属性
            if hasattr(task_result, 'result'):
                return self._extract_text_from_content(task_result.result)
                
            # 如果上述方法都失败，尝试将整个对象转为字符串
            result_str = str(task_result)
            # 如果字符串表示是 TaskResult 格式，尝试提取内容
            if "TextMessage" in result_str and "content=" in result_str:
                logger.warning(f"使用字符串处理方式提取内容，这不是理想方法")
                
                # 尝试从 repr 中提取最后一个消息内容
                matches = re.findall(r"source='[^']+', models_usage=[^,]+, metadata={}, content='([^']+)'", result_str)
                if matches:
                    return self._extract_text_from_content(matches[-1].replace('\\n', '\n'))
                    
            # 所有方法都失败
            logger.warning("无法从 TaskResult 中提取有效内容，返回原始字符串表示")
            return self._extract_text_from_content(str(task_result))
            
        except Exception as e:
            logger.error(f"从 TaskResult 提取内容时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return self._get_default_na_result()
    
    def _extract_text_from_content(self, text: str) -> str:
        """
        从文本中提取分析内容
        
        Args:
            text: 包含分析结果的文本
            
        Returns:
            提取的分析文本
        """
        if not text:
            return self._get_default_na_result()
        
        # 直接返回文本内容，不需要JSON解析
        return text.strip()
    
    def parse_and_validate_result(self, raw_result: str) -> str:
        """
        处理分析结果（现在直接返回原始文本）
        
        Args:
            raw_result: 原始分析结果字符串
            
        Returns:
            处理后的文本
        """
        try:
            # 直接返回原始文本，不进行JSON解析和验证
            if not raw_result or not raw_result.strip():
                return self._get_default_na_result()
            
            return raw_result.strip()
            
        except Exception as e:
            logger.error(f"处理分析结果失败: {str(e)}")
            return self._get_default_na_result()
    
    async def cleanup(self):
        """清理资源"""
        logger.info("开始清理资源...")
        
        # 清理 Playwright 工作台
        if self.playwright_workbench:
            try:
                logger.info("正在关闭 Playwright 工作台...")
                await self.playwright_workbench.stop()
                self.playwright_workbench = None
                logger.info("Playwright 工作台已关闭")
            except Exception as e:
                logger.error(f"关闭 Playwright 工作台时出错: {str(e)}")
        
        # 清理模型客户端
        if self.model_client:
            try:
                logger.info("正在关闭模型客户端...")
                # 检查是否有 close 方法
                if hasattr(self.model_client, 'close'):
                    await self.model_client.close()
                self.model_client = None
                logger.info("模型客户端已关闭")
            except Exception as e:
                logger.error(f"关闭模型客户端时出错: {str(e)}")
        
        # 等待一小段时间确保所有异步操作完成
        await asyncio.sleep(0.1)
        logger.info("资源清理完成")


# 使用示例和测试函数
async def test_website_analysis():
    """测试网站分析功能"""
    service = WebsiteAnalysisService()
    
    try:
        # 测试分析
        result = await service.analyze_website(
            supplier_name="嘉兴坤德精密机械有限公司",
            supplier_website="https://www.example.com"  # 替换为真实URL进行测试
        )
        
        print("分析结果:")
        print(result)
        
        # 解析和验证结果
        parsed_result = service.parse_and_validate_result(result)
        print("\n解析后的结果:")
        print(parsed_result)
        
    finally:
        await service.cleanup()


if __name__ == "__main__":
    asyncio.run(test_website_analysis()) 