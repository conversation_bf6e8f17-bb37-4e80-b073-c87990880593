"""
供应商评分服务
整合数据获取、验证、分析和评分的完整流程
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from dotenv import load_dotenv
import os

# 加载环境变量 - 指定app目录下的.env文件
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'app', '.env'))

from app import db
from app.models.search_supplier import SearchSupplier, ProductCategory
from app.models.supplier_rating import SupplierRating
from app.config import Config
from app.services.supplier_scoring import GPTAnalyzer, ScoringEngine, DataValidator
from app.utils.schema_validator import validate_evaluation_result

logger = logging.getLogger(__name__)

class SupplierScoringService:
    """供应商评分服务"""
    
    def __init__(self):
        self.gpt_analyzer = GPTAnalyzer()
        self.scoring_engine = ScoringEngine()
        self.data_validator = DataValidator()
        
        # 创建独立的数据库会话（用于测试）
        self.engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
        self.SessionLocal = sessionmaker(bind=self.engine)
    
    def score_supplier(self, supplier_id: int, force_refresh: bool = False) -> Dict:
        """
        为单个供应商评分 - 每次都重新计算
        
        Args:
            supplier_id: 供应商ID
            force_refresh: 保留参数兼容性，但不再使用
            
        Returns:
            评分结果字典
        """
        try:
            # 获取供应商数据
            supplier_data = self._get_supplier_data(supplier_id)
            if not supplier_data:
                return {
                    'success': False,
                    'message': f'未找到供应商ID: {supplier_id}',
                    'error_code': 'SUPPLIER_NOT_FOUND'
                }
            
            # 数据验证和预处理
            validation_result = self._validate_and_preprocess_data(supplier_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': f'数据验证失败: {validation_result["errors"][0]}',
                    'error_code': 'DATA_VALIDATION_ERROR',
                    'errors': validation_result['errors']
                }
            
            processed_data = validation_result['data']
            
            # 检查数据完整性
            completeness = self.data_validator.check_data_completeness(processed_data)
            if completeness['overall_score'] < 50:
                logger.warning(f"Supplier {supplier_id} data completeness low: {completeness['overall_score']}")
            
            # 计算评分
            evaluation_result = self.scoring_engine.calculate_comprehensive_score(processed_data)
            
            # 验证评分结果格式
            validation = validate_evaluation_result(evaluation_result)
            if not validation['valid']:
                logger.error(f"Evaluation result validation failed: {validation['errors']}")
                # 继续处理，但记录错误
            
            # 添加数据完整性信息
            evaluation_result['data_completeness'] = completeness
            
            # 保存评分结果到数据库
            try:
                self._save_scores_to_db(supplier_id, evaluation_result)
                logger.info(f"Saved dimension scores for supplier {supplier_id}")
            except Exception as save_error:
                logger.error(f"Failed to save dimension scores: {save_error}")
                # 不影响返回结果，只记录错误
            
            return {
                'success': True,
                'data': evaluation_result,
                'message': '评分完成',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error scoring supplier {supplier_id}: {str(e)}")
            return {
                'success': False,
                'message': f'评分过程中发生错误: {str(e)}',
                'error_code': 'SCORING_ERROR'
            }

    def get_supplier_scores(self, supplier_id: int) -> Dict:
        """
        获取供应商已有的评分结果 - 仅从数据库读取，不进行计算
        
        Args:
            supplier_id: 供应商ID
            
        Returns:
            评分结果字典或空结果
        """
        try:
            rating = SupplierRating.query.filter_by(supplier_id=supplier_id).first()
            if not rating or not rating.dimension_scores:
                return {
                    'success': True,
                    'data': None,
                    'message': '暂无评分结果',
                    'has_scores': False
                }
            
            # 检查dimension_scores是否为空
            if not rating.dimension_scores or len(rating.dimension_scores) == 0:
                return {
                    'success': True,
                    'data': None,
                    'message': '暂无评分结果',
                    'has_scores': False
                }
            
            # 检查是否是新格式的完整评分结果
            saved_data = rating.dimension_scores
            
            # 如果保存的数据包含evaluation_summary，说明是完整的评分结果
            if 'evaluation_summary' in saved_data and 'final_score' in saved_data['evaluation_summary']:
                return {
                    'success': True,
                    'data': saved_data,
                    'message': '评分结果获取成功',
                    'has_scores': True,
                    'timestamp': datetime.now().isoformat()
                }
            
            # 兼容旧格式：如果只有维度评分，则构建评分结果
            # 获取供应商基本信息
            supplier_data = self._get_supplier_data(supplier_id)
            if not supplier_data:
                return {
                    'success': False,
                    'message': f'未找到供应商ID: {supplier_id}',
                    'error_code': 'SUPPLIER_NOT_FOUND'
                }
            
            # 使用权重计算最终得分（旧数据兼容）
            final_score = self._calculate_final_score_from_dimensions(saved_data)
            grade_info = self.scoring_engine._get_score_grade(final_score)
            
            result = {
                'supplier_info': {
                    'id': supplier_data.get('id'),
                    'name': supplier_data.get('supplier_name'),
                    'category': supplier_data.get('category_name', ''),
                    'region': supplier_data.get('region')
                },
                'evaluation_summary': {
                    'final_score': final_score,
                    'grade': grade_info['grade'],
                    'level': grade_info['level'],
                    'evaluation_date': rating.evaluated_at.isoformat() if rating.evaluated_at else None,
                    'version': '1.1-legacy'
                },
                'dimension_scores': saved_data,
                'weight_distribution': {
                    'basic_strength': 0.25,
                    'certification': 0.1875,
                    'feature_matching': 0.50,
                    'innovation': 0.0625
                },
                'recommendations': [],
                'risk_warnings': []
            }
            
            return {
                'success': True,
                'data': result,
                'message': '评分结果获取成功（兼容模式）',
                'has_scores': True,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting supplier scores for {supplier_id}: {str(e)}")
            return {
                'success': False,
                'message': f'获取评分结果失败: {str(e)}',
                'error_code': 'GET_SCORES_ERROR'
            }

    # *** 新增方法：保存评分结果到数据库 ***
    def _save_scores_to_db(self, supplier_id: int, evaluation_result: Dict):
        """保存评分结果到数据库"""
        from app import db
        
        try:
            # 获取或创建评级记录
            rating = SupplierRating.get_or_create_by_supplier_id(supplier_id)
            
            # 保存完整的评分结果到dimension_scores字段
            # 这样既保持了向后兼容，又包含了完整的评分信息
            rating.dimension_scores = evaluation_result
            rating.evaluated_at = datetime.now()
            
            # 提交到数据库
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            raise e

    # *** 新增辅助方法 ***
    def _calculate_final_score_from_dimensions(self, dimension_scores: Dict) -> float:
        """从维度评分计算最终得分"""
        # 修正权重体系，确保总和为1.0
        weights = {
            'basic_strength': 0.25,    # 原来0.20，调整为0.25
            'certification': 0.1875,   # 原来0.15，调整为0.1875
            'feature_matching': 0.50,  # 原来0.40，调整为0.50
            'innovation': 0.0625       # 原来0.05，调整为0.0625
        }
        
        final_score = 0
        
        for dimension, weight in weights.items():
            if dimension in dimension_scores:
                final_score += dimension_scores[dimension].get('total_score', 0) * weight
        
        return round(final_score, 2)

    def _get_grade_from_score(self, score: float) -> str:
        """根据分数获取评级"""
        grade_info = self.scoring_engine._get_score_grade(score)
        return grade_info['grade']

    def _get_level_from_score(self, score: float) -> str:
        """根据分数获取等级"""
        grade_info = self.scoring_engine._get_score_grade(score)
        return grade_info['level']
    
    def batch_score_suppliers(self, supplier_ids: List[int], 
                            include_details: bool = False) -> Dict:
        """
        批量评分供应商
        
        Args:
            supplier_ids: 供应商ID列表
            include_details: 是否包含详细分析
            
        Returns:
            批量评分结果
        """
        results = []
        errors = []
        
        for supplier_id in supplier_ids:
            try:
                result = self.score_supplier(supplier_id)
                if result['success']:
                    # 如果不需要详细信息，只保留摘要
                    if not include_details and 'data' in result:
                        summary_data = {
                            'supplier_info': result['data']['supplier_info'],
                            'evaluation_summary': result['data']['evaluation_summary']
                        }
                        result['data'] = summary_data
                    results.append(result)
                else:
                    errors.append({
                        'supplier_id': supplier_id,
                        'error': result['message']
                    })
            except Exception as e:
                logger.error(f"Error in batch scoring supplier {supplier_id}: {str(e)}")
                errors.append({
                    'supplier_id': supplier_id,
                    'error': str(e)
                })
        
        return {
            'success': True,
            'data': {
                'results': results,
                'total_count': len(supplier_ids),
                'success_count': len(results),
                'error_count': len(errors),
                'errors': errors
            },
            'message': f'批量评分完成，成功{len(results)}个，失败{len(errors)}个',
            'timestamp': datetime.now().isoformat()
        }
    
    def get_supplier_score_summary(self, supplier_id: int) -> Dict:
        """获取供应商评分摘要"""
        try:
            result = self.score_supplier(supplier_id)
            if not result['success']:
                return result
            
            data = result['data']
            summary = {
                'supplier_info': data['supplier_info'],
                'evaluation_summary': data['evaluation_summary'],
                'dimension_summary': {
                    name: {
                        'score': scores['total_score'],
                        'analysis': scores.get('analysis', '')
                    }
                    for name, scores in data['dimension_scores'].items()
                },
                'data_completeness': data.get('data_completeness', {})
            }
            
            return {
                'success': True,
                'data': summary,
                'message': '获取评分摘要成功'
            }
            
        except Exception as e:
            logger.error(f"Error getting score summary for supplier {supplier_id}: {str(e)}")
            return {
                'success': False,
                'message': f'获取评分摘要失败: {str(e)}',
                'error_code': 'GET_SUMMARY_ERROR'
            }
    
    def _get_supplier_data(self, supplier_id: int) -> Optional[Dict]:
        """获取供应商完整数据"""
        session = self.SessionLocal()
        try:
            # 获取供应商基础信息
            supplier = session.query(SearchSupplier).filter_by(id=supplier_id).first()
            if not supplier:
                return None
            
            # 获取产品分类信息
            category = None
            if supplier.category_id:
                category = session.query(ProductCategory).filter_by(id=supplier.category_id).first()
            
            # 获取评级信息
            rating = session.query(SupplierRating).filter_by(supplier_id=supplier_id).first()
            
            # 安全处理JSON字段的函数
            def safe_json_field(field_value):
                """安全处理JSON字段，确保null值被正确处理"""
                if field_value is None:
                    return {}
                if isinstance(field_value, str):
                    try:
                        import json
                        return json.loads(field_value.replace('null', 'None'))
                    except:
                        return {}
                if isinstance(field_value, dict):
                    return field_value
                return {}
            
            # 构建完整数据
            supplier_data = {
                'id': supplier.id,
                'supplier_name': supplier.supplier_name,
                'region': supplier.region,
                'category_id': supplier.category_id,
                'matching_reason': supplier.matching_reason,
                'website': supplier.website,
                'phone': supplier.phone,
                'email': supplier.email,
                'certifications_text': supplier.certifications,
                'notes': supplier.notes,
                'address': supplier.address,
                'status': supplier.status,
                'category_name': category.category if category else '',
                'category_feature': category.feature if category else '',
                'business_info': {},
                'certifications': {},
                'main_products': [],
                'patents': []
            }
            
            # 添加评级数据，使用安全处理
            if rating:
                supplier_data.update({
                    'business_info': safe_json_field(rating.business_info),
                    'certifications': safe_json_field(rating.certifications),
                    'main_products': rating.main_products or [],
                    'patents': rating.patents or [],
                    'website_analysis': rating.main_products or ''  # 网站分析内容现在存储在main_products字段中
                })
            else:
                supplier_data['website_analysis'] = ''
            
            return supplier_data
            
        except Exception as e:
            logger.error(f"Error getting supplier data for ID {supplier_id}: {str(e)}")
            return None
        finally:
            session.close()
    
    def _validate_and_preprocess_data(self, supplier_data: Dict) -> Dict:
        """验证和预处理数据"""
        errors = []
        
        try:
            # 预处理数据
            processed_data = self.data_validator.preprocess_supplier_data(supplier_data)
            
            # 预处理企业信息
            if 'business_info' in processed_data:
                processed_data['business_info'] = self.data_validator.preprocess_business_info(
                    processed_data['business_info']
                )
            
            # 预处理认证信息
            if 'certifications' in processed_data:
                processed_data['certifications'] = self.data_validator.preprocess_certifications(
                    processed_data['certifications']
                )
            
            # 预处理产品信息
            if 'main_products' in processed_data:
                processed_data['main_products'] = self.data_validator.preprocess_main_products(
                    processed_data['main_products']
                )
            
            # 预处理专利信息
            if 'patents' in processed_data:
                processed_data['patents'] = self.data_validator.preprocess_patents(
                    processed_data['patents']
                )
            
            # 验证数据
            valid, validation_errors = self.data_validator.validate_supplier_data(processed_data)
            if not valid:
                errors.extend(validation_errors)
            
            # 验证企业信息（如果存在）
            if processed_data.get('business_info'):
                valid, validation_errors = self.data_validator.validate_business_info(
                    processed_data['business_info']
                )
                if not valid:
                    errors.extend([f"企业信息: {error}" for error in validation_errors])
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'data': processed_data
            }
            
        except Exception as e:
            logger.error(f"Error in data validation and preprocessing: {str(e)}")
            return {
                'valid': False,
                'errors': [f'数据处理异常: {str(e)}'],
                'data': supplier_data
            }
    
    def test_gpt_connection(self) -> Dict:
        """测试GPT连接"""
        try:
            test_result = self.gpt_analyzer.parse_category_feature("测试产品特征：高精度加工")
            return {
                'success': True,
                'message': 'GPT连接正常',
                'test_result': test_result
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'GPT连接失败: {str(e)}',
                'error_code': 'GPT_CONNECTION_ERROR'
            }
    
    def get_scoring_weights(self) -> Dict:
        """获取评分权重配置"""
        return {
            'success': True,
            'data': {
                'main_weights': {
                    'basic_strength': 0.25,
                    'certification': 0.1875,
                    'feature_matching': 0.50,
                    'innovation': 0.0625
                },
                'feature_weights': {
                    'feature_business_match': 0.25,
                    'feature_products_match': 0.25,
                    'business_products_consistency': 0.25,
                    'keyword_match_score': 0.25
                },
                'description': {
                    'basic_strength': '企业基础实力（注册资本、员工规模、经营年限等）',
                    'certification': '资质认证（认证数量和质量）',
                    'feature_matching': '特征匹配（产品分类特征与供应商能力匹配度）',
                    'innovation': '技术创新（专利数量和质量）'
                }
            }
        }
    
    def get_available_suppliers(self, limit: int = 10) -> Dict:
        """获取可用于测试的供应商列表"""
        session = self.SessionLocal()
        try:
            suppliers = session.query(SearchSupplier).limit(limit).all()
            supplier_list = []
            
            for supplier in suppliers:
                # 检查是否有评级数据
                rating = session.query(SupplierRating).filter_by(supplier_id=supplier.id).first()
                has_rating_data = rating is not None
                
                supplier_list.append({
                    'id': supplier.id,
                    'name': supplier.supplier_name,
                    'region': supplier.region,
                    'category_id': supplier.category_id,
                    'status': supplier.status,
                    'has_rating_data': has_rating_data
                })
            
            return {
                'success': True,
                'data': supplier_list,
                'message': f'获取到{len(supplier_list)}个供应商'
            }
            
        except Exception as e:
            logger.error(f"Error getting available suppliers: {str(e)}")
            return {
                'success': False,
                'message': f'获取供应商列表失败: {str(e)}',
                'error_code': 'GET_SUPPLIERS_ERROR'
            }
        finally:
            session.close() 