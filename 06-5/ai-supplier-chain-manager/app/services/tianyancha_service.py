import requests
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional
import time

logger = logging.getLogger(__name__)

class TianyanchaService:
    """天眼查API服务类"""
    
    def __init__(self):
        self.token = os.environ.get('TIANYANCHA_TOKEN', '54255c51-a635-4b6b-8d4a-eb882b61385f')
        self.base_url = "http://open.api.tianyancha.com/services/open"
        self.headers = {'Authorization': self.token}
        
    def get_company_basic_info(self, company_name: str) -> Dict:
        """获取企业基本信息"""
        try:
            url = f"{self.base_url}/ic/baseinfoV3/2.0"
            params = {'keyword': company_name}
            
            logger.info(f"正在获取企业基本信息: {company_name}")
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error_code') == 0:
                    return self.parse_business_info(data.get('result', {}))
                else:
                    logger.error(f"天眼查API返回错误: {data.get('reason', '未知错误')}")
                    return {'error': data.get('reason', '未知错误')}
            else:
                logger.error(f"HTTP请求失败: {response.status_code}")
                return {'error': f'HTTP请求失败: {response.status_code}'}
                
        except Exception as e:
            logger.error(f"获取企业基本信息失败: {str(e)}")
            return {'error': str(e)}
    
    def get_company_certifications(self, company_name: str, cert_types: List[str] = None) -> Dict:
        """获取企业认证信息"""
        if cert_types is None:
            cert_types = [
                "医疗器械质量管理体系认证"
            ]
        
        all_certifications = []
        
        for cert_type in cert_types:
            try:
                url = f"{self.base_url}/m/certificate/2.0"
                params = {
                    'name': company_name,
                    'certificateName': cert_type
                }
                
                logger.info(f"正在获取认证信息: {company_name} - {cert_type}")
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('error_code') == 0:
                        result = data.get('result', {})
                        if result.get('items'):
                            all_certifications.extend(result['items'])
                
                # 添加延迟避免API限制
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"获取认证信息失败 {cert_type}: {str(e)}")
                continue
        
        return self.parse_certifications({'items': all_certifications})
    
    def parse_business_info(self, raw_data: Dict) -> Dict:
        """解析企业基本信息为标准格式"""
        try:
            if not raw_data:
                return {'error': '无企业基本信息数据'}
            
            # 天眼查基本信息API返回的是企业基本信息
            business_info = {
                "company_name": raw_data.get('name', ''),
                "unified_credit_code": raw_data.get('creditCode', ''),
                "legal_representative": raw_data.get('legalPersonName', ''),
                "registered_capital": raw_data.get('regCapital', ''),
                "registration_date": raw_data.get('estiblishTime', ''),
                "business_scope": raw_data.get('businessScope', ''),
                "address": raw_data.get('regLocation', ''),
                "company_status": raw_data.get('regStatus', ''),
                "company_type": raw_data.get('type', ''),
                "industry": raw_data.get('industry', ''),
                "staff_size": raw_data.get('staffNumRange', ''),
                "data_source": "tianyancha",
                "last_updated": datetime.utcnow().isoformat()
            }
            
            logger.info(f"成功解析企业基本信息: {business_info['company_name']}")
            return business_info
            
        except Exception as e:
            logger.error(f"解析企业基本信息失败: {str(e)}")
            return {'error': f'解析失败: {str(e)}'}
    
    def parse_certifications(self, raw_data: Dict) -> Dict:
        """解析认证信息为标准格式"""
        try:
            items = raw_data.get('items', [])
            
            certifications = []
            valid_count = 0
            
            for item in items:
                # 解析详细信息
                detail_dict = {}
                for detail in item.get('detail', []):
                    detail_dict[detail.get('title', '')] = detail.get('content', '')
                
                # 判断证书是否有效
                cert_status = detail_dict.get('证书状态', '').strip()
                expire_date = item.get('endDate', '')
                is_valid = self._is_cert_valid(cert_status, expire_date)
                
                if is_valid:
                    valid_count += 1
                
                cert_info = {
                    "cert_no": item.get('certNo', ''),
                    "cert_name": item.get('certificateName', ''),
                    "cert_status": cert_status,
                    "issue_date": item.get('startDate', ''),
                    "expire_date": expire_date,
                    "issuing_authority": detail_dict.get('发证机构-机构名称', ''),
                    "business_scope": detail_dict.get('认证覆盖的业务范围', ''),
                    "standard": detail_dict.get('认证依据', ''),
                    "is_valid": is_valid,
                    "coverage_staff": detail_dict.get('获证组织-本证书体系覆盖人数', ''),
                    "organization_address": detail_dict.get('获证组织-组织地址', ''),
                    "cert_id": item.get('id', '')
                }
                
                certifications.append(cert_info)
            
            result = {
                "total_count": len(certifications),
                "valid_count": valid_count,
                "certifications": certifications,
                "data_source": "tianyancha",
                "last_updated": datetime.utcnow().isoformat()
            }
            
            logger.info(f"成功解析认证信息: 总数{len(certifications)}, 有效{valid_count}")
            return result
            
        except Exception as e:
            logger.error(f"解析认证信息失败: {str(e)}")
            return {'error': f'解析失败: {str(e)}'}
    
    def _is_cert_valid(self, cert_status: str, expire_date: str) -> bool:
        """判断证书是否有效"""
        try:
            # 检查证书状态
            if cert_status in ['撤销', '暂停', '注销']:
                return False
            
            # 检查是否过期
            if expire_date:
                expire_dt = datetime.strptime(expire_date, '%Y-%m-%d')
                if expire_dt < datetime.now():
                    return False
            
            return True
            
        except Exception:
            return False 