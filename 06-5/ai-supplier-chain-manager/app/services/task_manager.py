#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import threading
import logging
from typing import Optional
from app import db
from app.models.analysis_task import AnalysisTask
from app.models.search_supplier import SearchSupplier
from app.models.supplier_rating import SupplierRating
from app.services.website_analysis_service import WebsiteAnalysisService
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class TaskManager:
    """任务管理器 - 处理异步分析任务"""
    
    def __init__(self):
        self.running = False
        self.worker_thread = None
        self.analysis_service = None
    
    def start(self):
        """启动任务管理器"""
        if not self.running:
            self.running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            logger.info("任务管理器已启动")
    
    def stop(self):
        """停止任务管理器"""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("任务管理器已停止")
    
    def _worker_loop(self):
        """工作线程循环"""
        logger.info("任务处理工作线程已启动")
        
        while self.running:
            try:
                # 查找待处理的任务
                pending_task = AnalysisTask.query.filter_by(status='pending').first()
                
                if pending_task:
                    logger.info(f"处理任务: {pending_task.id}")
                    self._process_task(pending_task)
                else:
                    # 没有任务时休眠
                    threading.Event().wait(5)  # 每5秒检查一次
                    
            except Exception as e:
                logger.error(f"任务处理循环出错: {str(e)}")
                threading.Event().wait(10)  # 出错时等待10秒
    
    def _process_task(self, task: AnalysisTask):
        """处理单个任务"""
        try:
            # 开始任务
            task.start_task()
            logger.info(f"开始处理任务 {task.id}: {task.task_type}")
            
            if task.task_type == 'website_analysis':
                self._process_website_analysis_task(task)
            else:
                task.fail_task(f"未知的任务类型: {task.task_type}")
                
        except Exception as e:
            logger.error(f"处理任务 {task.id} 失败: {str(e)}")
            task.fail_task(str(e))
    
    def _process_website_analysis_task(self, task: AnalysisTask):
        """处理网站分析任务"""
        try:
            # 获取供应商信息
            supplier = SearchSupplier.query.get(task.supplier_id)
            if not supplier:
                task.fail_task("供应商不存在")
                return
            
            # 更新进度
            task.update_progress(10)
            
            # 获取任务参数
            params = task.get_parameters()
            website_url = params.get('website_url', supplier.website)
            
            # 创建分析服务（在新的事件循环中运行）
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 更新进度
                task.update_progress(20)
                
                # 执行网站分析
                result = loop.run_until_complete(
                    self._run_website_analysis(supplier.supplier_name, website_url, task)
                )
                
                # 更新进度
                task.update_progress(80)
                
                # 保存结果到数据库
                self._save_analysis_result(supplier, result)
                
                # 完成任务
                task.update_progress(100)
                task.complete_task(result)
                
                logger.info(f"网站分析任务 {task.id} 完成")
                
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"网站分析任务 {task.id} 失败: {str(e)}")
            task.fail_task(str(e))
    
    async def _run_website_analysis(self, supplier_name: str, website_url: Optional[str], task: AnalysisTask):
        """运行网站分析"""
        try:
            # 创建分析服务
            if not self.analysis_service:
                self.analysis_service = WebsiteAnalysisService()
            
            # 更新进度
            task.update_progress(30)
            
            # 执行分析
            logger.info(f"开始分析供应商网站: {supplier_name}, URL: {website_url}")
            raw_result = await self.analysis_service.analyze_website(
                supplier_name=supplier_name,
                supplier_website=website_url
            )
            
            # 更新进度
            task.update_progress(90)
            
            # 直接返回原始GPT分析文本，不进行解析
            return raw_result
            
        except Exception as e:
            logger.error(f"网站分析执行失败: {str(e)}")
            raise
        finally:
            # 清理资源
            if self.analysis_service:
                try:
                    await self.analysis_service.cleanup()
                except:
                    pass
                self.analysis_service = None
    
    def _save_analysis_result(self, supplier: SearchSupplier, result: dict):
        """保存分析结果到数据库"""
        try:
            # 获取或创建评级记录
            rating = supplier.get_or_create_rating()
            
            # 如果result是字符串（原始GPT文本），直接保存到main_products
            if isinstance(result, str):
                rating.main_products = result
            # 如果result是字典且包含raw_analysis，保存原始文本
            elif isinstance(result, dict) and 'raw_analysis' in result:
                rating.main_products = result['raw_analysis']
            # 否则将整个结果转为字符串保存
            else:
                rating.main_products = str(result)
            
            rating.evaluated_at = datetime.utcnow()
            db.session.commit()
            
            logger.info(f"分析结果已保存到供应商 {supplier.id} 的评级记录")
            
        except Exception as e:
            logger.error(f"保存分析结果失败: {str(e)}")
            db.session.rollback()
            raise

# 全局任务管理器实例
task_manager = TaskManager()

def start_task_manager():
    """启动任务管理器"""
    task_manager.start()

def stop_task_manager():
    """停止任务管理器"""
    task_manager.stop()

def create_website_analysis_task(supplier_id: int, website_url: Optional[str] = None) -> str:
    """创建网站分析任务"""
    try:
        # 检查是否已有进行中的任务
        existing_task = AnalysisTask.query.filter_by(
            supplier_id=supplier_id,
            task_type='website_analysis',
            status='running'
        ).first()
        
        if existing_task:
            return existing_task.id
        
        # 创建新任务
        parameters = {}
        if website_url:
            parameters['website_url'] = website_url
        
        task = AnalysisTask(
            supplier_id=supplier_id,
            task_type='website_analysis',
            parameters=parameters
        )
        
        db.session.add(task)
        db.session.commit()
        
        logger.info(f"创建网站分析任务: {task.id} for 供应商 {supplier_id}")
        return task.id
        
    except Exception as e:
        logger.error(f"创建分析任务失败: {str(e)}")
        db.session.rollback()
        raise

def get_task_status(task_id: str) -> Optional[dict]:
    """获取任务状态"""
    task = AnalysisTask.query.get(task_id)
    if task:
        return task.to_dict()
    return None 