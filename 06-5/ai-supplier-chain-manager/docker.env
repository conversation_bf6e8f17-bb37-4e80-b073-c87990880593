# 生产环境配置
COMPOSE_PROJECT_NAME=scm-supplier-finder

# 服务端口配置
NGINX_HTTP_PORT=9004
NGINX_HTTPS_PORT=9443
BACKEND_PORT=9002
FRONTEND_PORT=9003

# Flask应用配置
FLASK_ENV=production
SECRET_KEY=scm-supplier-finder-secret-key-production-2024

# 数据库配置
MYSQL_HOST=rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com
MYSQL_PORT=3306
MYSQL_DATABASE=procurement_system
MYSQL_USER=yj_app
MYSQL_PASSWORD=4iLe5fifhMqOo9Ne

# 日志配置
LOG_LEVEL=INFO

# 其他配置
TZ=Asia/Shanghai 