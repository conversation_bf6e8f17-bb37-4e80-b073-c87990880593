services:
  # 后端Flask应用
  backend:
    build: 
      context: ./app
      dockerfile: Dockerfile
    container_name: scm-backend
    ports:
      - "9002:5000"
    environment:
      - FLASK_ENV=production
      - BACKEND_PORT=5000
      # 数据库配置
      - MYSQL_HOST=rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=procurement_system
      - MYSQL_USER=yj_app
      - MYSQL_PASSWORD=4iLe5fifhMqOo9Ne
      # 其他配置
      - SECRET_KEY=scm-supplier-finder-secret-key-production
    volumes:
      - ./app/logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    networks:
      - scm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端Vue应用
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile
    container_name: scm-frontend
    ports:
      - "9003:80"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - scm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  scm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  app-logs:
    driver: local 