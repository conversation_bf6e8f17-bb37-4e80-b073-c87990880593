# Docker 部署指南

## 快速开始

```bash
# 1. 给脚本执行权限
chmod +x deploy.sh

# 2. 执行部署
./deploy.sh
```

## 访问地址

- **主入口**: http://localhost:9004
- **前端**: http://localhost:9003  
- **后端API**: http://localhost:9002/api/
- **健康检查**: http://localhost:9004/health

## 管理命令

```bash
# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建
docker-compose build --no-cache && docker-compose up -d
```

## 端口配置

| 服务 | 端口 | 说明 |
|------|------|------|
| Nginx | 9004 | 主入口 |
| 前端 | 9003 | Vue应用 |
| 后端 | 9002 | Flask API |

## 故障排除

1. **端口被占用**: 修改 `docker-compose.yml` 中的端口映射
2. **启动失败**: 运行 `docker-compose logs` 查看错误
3. **数据库连接**: 检查 `docker-compose.yml` 中的数据库配置