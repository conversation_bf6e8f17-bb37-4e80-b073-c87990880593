<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="121ea267-76b6-41e1-b316-778c7a4c9b80" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/training/professional_supply_chain_analysis.py" beforeDir="false" afterPath="$PROJECT_DIR$/training/professional_supply_chain_analysis.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="2y7O8vUOFZhzxXPlulvSchbbsrH" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "/Users/<USER>/Desktop/llm/yj/aopu/06-5/ai-supplier-chain-manager"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-d7ad00fb9fc3-c546a90a8094-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-242.23726.102" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="121ea267-76b6-41e1-b316-778c7a4c9b80" name="更改" comment="" />
      <created>1749181988386</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749181988386</updated>
    </task>
    <servers />
  </component>
</project>