#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import logging
from openai import OpenAI

# 设置日志
logger = logging.getLogger(__name__)

CLAUDE_FORWARD_URL = "http://43.130.31.174:8092/claude_forward"
CLAUDE_API_KEY = "************************************************************************************************************"
CLAUDE_MODEL = "claude-sonnet-4-20250514"

# OpenAI配置
OPENAI_API_KEY = "********************************************************************************************************************************************************************"  # 请替换为实际的API Key
OPENAI_MODEL = "gpt-4o"

def call_claude(prompt):
    """转发prompt并返回response"""
    logger.debug(f"调用Claude API，prompt长度: {len(prompt)} 字符")
    logger.debug(f"Claude API URL: {CLAUDE_FORWARD_URL}")
    logger.debug(f"Claude模型: {CLAUDE_MODEL}")
    
    payload = {
        "api_key": CLAUDE_API_KEY,
        "model": CLAUDE_MODEL,
        "messages": [{"role": "user", "content": prompt}]
    }
    
    logger.debug(f"API请求payload: {json.dumps(payload, ensure_ascii=False)[:500]}...")
    
    try:
        logger.info("开始发送Claude API请求...")
        response = requests.post(
            CLAUDE_FORWARD_URL, 
            headers={'Content-Type': 'application/json'}, 
            data=json.dumps(payload), 
            timeout=180
        )
        
        logger.info(f"Claude API响应状态码: {response.status_code}")
        logger.debug(f"Claude API响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            logger.info("Claude API调用成功")
            logger.debug(f"响应数据类型: {type(response_data)}")
            logger.debug(f"响应数据键: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")
            logger.debug(f"完整响应: {json.dumps(response_data, ensure_ascii=False)[:1000]}...")
            return response_data
        else:
            error_msg = f"API错误: {response.status_code}"
            logger.error(f"Claude API调用失败: {error_msg}")
            try:
                error_content = response.text
                logger.error(f"错误响应内容: {error_content}")
            except:
                logger.error("无法获取错误响应内容")
            return {"error": error_msg}
            
    except requests.exceptions.Timeout:
        error_msg = "Claude API请求超时"
        logger.error(error_msg)
        return {"error": error_msg}
    except requests.exceptions.ConnectionError:
        error_msg = "Claude API连接失败"
        logger.error(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Claude API调用异常: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def call_claude_with_search(prompt):
    """转发prompt并返回搜索结果"""
    logger.debug(f"调用Claude带搜索API，prompt长度: {len(prompt)} 字符")
    logger.debug(f"Claude API URL: {CLAUDE_FORWARD_URL}")
    logger.debug(f"Claude模型: {CLAUDE_MODEL}")
    
    payload = {
        "api_key": CLAUDE_API_KEY,
        "model": CLAUDE_MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "tools": [{"type": "web_search_20250305", "name": "web_search", "max_uses": 5}]
    }
    
    logger.debug(f"带搜索工具的API请求payload: {json.dumps(payload, ensure_ascii=False)[:500]}...")
    
    try:
        logger.info("开始发送Claude带搜索工具的API请求...")
        response = requests.post(
            CLAUDE_FORWARD_URL, 
            headers={'Content-Type': 'application/json'}, 
            data=json.dumps(payload), 
            timeout=180
        )
        
        logger.info(f"Claude带搜索API响应状态码: {response.status_code}")
        logger.debug(f"Claude带搜索API响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            logger.info("Claude带搜索API调用成功")
            logger.debug(f"响应数据类型: {type(response_data)}")
            logger.debug(f"响应数据键: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")
            logger.debug(f"完整响应: {json.dumps(response_data, ensure_ascii=False)[:1000]}...")
            return response_data
        else:
            error_msg = f"API错误: {response.status_code}"
            logger.error(f"Claude带搜索API调用失败: {error_msg}")
            try:
                error_content = response.text
                logger.error(f"错误响应内容: {error_content}")
            except:
                logger.error("无法获取错误响应内容")
            return {"error": error_msg}
            
    except requests.exceptions.Timeout:
        error_msg = "Claude带搜索API请求超时"
        logger.error(error_msg)
        return {"error": error_msg}
    except requests.exceptions.ConnectionError:
        error_msg = "Claude带搜索API连接失败"
        logger.error(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Claude带搜索API调用异常: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def call_openai(prompt, system_prompt=None):
    """调用OpenAI API"""
    try:
        client = OpenAI(api_key=OPENAI_API_KEY)
        
        # 构建消息列表
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        response = client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=messages,
            timeout=240.0
        )
        
        # 提取响应内容
        content = response.choices[0].message.content
        
        return {
            "content": content,
            "model": OPENAI_MODEL,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
        
    except Exception as e:
        return {"error": f"OpenAI API错误: {str(e)}"} 