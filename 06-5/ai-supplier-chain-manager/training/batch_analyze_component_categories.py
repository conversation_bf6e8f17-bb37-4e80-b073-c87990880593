#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量分析component和categories的二级分类关系
使用Claude API分析组件特征，匹配到合适的产品分类
"""

import mysql.connector
import json
import logging
from typing import List, Dict, Any, Tuple
from claude_utils import call_claude
import time

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com',
    'port': 3306,
    'database': 'procurement_system',
    'user': 'yj_app',
    'password': '4iLe5fifhMqOo9Ne',
    'charset': 'utf8mb4',
    'autocommit': False
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            logger.debug("数据库连接成功")
            return connection
    except mysql.connector.Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def get_components_with_analysis(limit=50, offset=0):
    """获取有分析结果的组件数据"""
    conn = get_db_connection()
    if not conn:
        return []

    try:
        cursor = conn.cursor(dictionary=True)
        query = """
        SELECT 
            c.id, c.name, c.spec, c.category, c.material, 
            ca.analysis, ca.category_id as current_category_id
        FROM component c
        JOIN component_analysis ca ON c.id = ca.component_id
        WHERE ca.analysis IS NOT NULL AND ca.is_success = 1
        ORDER BY c.id
        LIMIT %s OFFSET %s
        """
        cursor.execute(query, (limit, offset))
        components = cursor.fetchall()
        logger.info(f"获取到{len(components)}个有分析结果的组件")
        return components
    except Exception as e:
        logger.error(f"查询组件时出错: {e}")
        return []
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def get_components_without_category_id(limit=50, offset=0):
    """获取分析成功但没有category_id的组件数据"""
    conn = get_db_connection()
    if not conn:
        return []

    try:
        cursor = conn.cursor(dictionary=True)
        query = """
        SELECT 
            c.id, c.name, c.spec, c.category, c.material, 
            ca.analysis, ca.category_id as current_category_id
        FROM component c
        JOIN component_analysis ca ON c.id = ca.component_id
        WHERE ca.analysis IS NOT NULL 
        AND ca.is_success = 1 
        AND ca.category_id IS NULL
        ORDER BY c.id
        LIMIT %s OFFSET %s
        """
        cursor.execute(query, (limit, offset))
        components = cursor.fetchall()
        logger.info(f"获取到{len(components)}个分析成功但未分类的组件")
        return components
    except Exception as e:
        logger.error(f"查询未分类组件时出错: {e}")
        return []
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def get_product_categories():
    """获取所有产品分类信息"""
    conn = get_db_connection()
    if not conn:
        return {}

    try:
        cursor = conn.cursor(dictionary=True)
        
        # 获取一级分类
        query_primary = """
        SELECT id, category, feature 
        FROM product_categories 
        WHERE prev = 0 
        ORDER BY id
        """
        cursor.execute(query_primary)
        primary_categories = cursor.fetchall()
        
        # 获取二级分类
        query_secondary = """
        SELECT id, category, prev, feature 
        FROM product_categories 
        WHERE prev != 0 
        ORDER BY prev, id
        """
        cursor.execute(query_secondary)
        secondary_categories = cursor.fetchall()
        
        # 构建分类层次结构
        categories_structure = {}
        
        # 添加一级分类
        for primary in primary_categories:
            categories_structure[primary['id']] = {
                'id': primary['id'],
                'name': primary['category'],
                'feature': primary['feature'],
                'level': 'primary',
                'children': []
            }
        
        # 添加二级分类
        for secondary in secondary_categories:
            parent_id = secondary['prev']
            if parent_id in categories_structure:
                categories_structure[parent_id]['children'].append({
                    'id': secondary['id'],
                    'name': secondary['category'],
                    'feature': secondary['feature'],
                    'level': 'secondary',
                    'parent_id': parent_id
                })
        
        logger.info(f"获取到{len(primary_categories)}个一级分类，{len(secondary_categories)}个二级分类")
        return categories_structure
    
    except Exception as e:
        logger.error(f"查询分类时出错: {e}")
        return {}
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def analyze_component_category_mapping(components: List[Dict], categories_structure: Dict) -> Dict:
    """使用Claude分析组件与分类的映射关系"""
    
    # 只提取二级分类信息，简化格式
    categories_info = "# 可选择的二级分类列表:\n"
    secondary_categories_list = []
    
    for primary_id, primary_info in categories_structure.items():
        if primary_info['children']:
            for secondary in primary_info['children']:
                categories_info += f"- ID {secondary['id']}: {secondary['name']} - {secondary['feature']}\n"
                secondary_categories_list.append(secondary['id'])
    
    # 构建组件信息字符串，突出分析内容
    components_info = "# 待分析的组件列表:\n"
    for i, comp in enumerate(components, 1):
        components_info += f"\n## 组件 {i} (ID: {comp['id']})\n"
        components_info += f"**名称**: {comp['name']}\n"
        components_info += f"**规格**: {comp['spec']}\n"
        components_info += f"**材料**: {comp['material'] or '未知'}\n"
        components_info += f"**当前分类**: {comp['category'] or '未分类'}\n"
        # 只保留分析内容的关键摘要，避免内容过长
        analysis_summary = comp['analysis'][:200] + "..." if len(comp['analysis']) > 200 else comp['analysis']
        components_info += f"**分析摘要**: {analysis_summary}\n"
    
    # 构建Claude提示词
    json_example = '''```json
{
    "component_mappings": [
        {
            "component_id": 组件ID,
            "component_name": "组件名称",
            "recommended_category_id": 推荐的二级分类ID,
            "category_name": "分类名称",
            "confidence": "高/中/低",
            "reasoning": "简洁的推荐理由"
        }
    ],
    "analysis_summary": "本批次分析总结"
}
```'''

    prompt = f"""你是一个专业的工业分类专家，请根据组件的详细分析信息，为每个组件匹配最合适的二级分类。

{categories_info}

{components_info}

# 分析要求
请仔细阅读每个组件的基本信息和分析摘要，重点关注：
1. 材料特性和工艺
2. 功能用途和应用场景  
3. 技术参数和性能指标
4. 制造工艺和精度要求

# 输出格式
请返回JSON格式的结果：

{json_example}

**重要要求**:
- recommended_category_id 必须从上述二级分类ID中选择
- 每个组件都必须匹配到一个二级分类
- 推荐理由要基于组件的基本信息和分析摘要
- 优先考虑材料、工艺、功能的匹配度
- 保持推荐理由简洁明了
"""

    try:
        logger.info("开始调用Claude API分析组件分类映射关系...")
        response = call_claude(prompt)
        
        # 提取响应内容
        content = ""
        if isinstance(response, dict) and "error" in response:
            logger.error(f"Claude API调用失败: {response['error']}")
            return {}
        
        # 正确处理嵌套的响应结构
        if isinstance(response, dict):
            if 'response' in response and isinstance(response['response'], dict):
                # 处理 {'response': {'content': [{'text': '...'}]}} 结构
                response_data = response['response']
                if 'content' in response_data and isinstance(response_data['content'], list):
                    if response_data['content'] and 'text' in response_data['content'][0]:
                        content = response_data['content'][0]['text']
                        logger.debug("✅ 成功提取嵌套响应结构中的text内容")
                    else:
                        content = str(response)
                else:
                    content = str(response)
            elif 'content' in response:
                # 处理 {'content': [{'text': '...'}]} 或 {'content': '...'} 结构
                if isinstance(response['content'], list):
                    content = response['content'][0].get('text', '') if response['content'] else ''
                else:
                    content = response['content']
            elif 'choices' in response:
                # 处理OpenAI格式的响应
                content = response['choices'][0]['message']['content']
            else:
                content = str(response)
        else:
            content = str(response)
        
        logger.info(f"Claude API调用成功，响应长度: {len(content)}")
        logger.debug(f"Claude响应内容: {content[:500]}...")
        
        # 解析JSON响应
        try:
            # 尝试从content中提取JSON
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                logger.info("✅ 找到markdown格式的JSON")
            else:
                # 如果没有找到markdown格式的JSON，尝试整个content
                json_str = content.strip()
                logger.info("⚠️ 未找到markdown格式，使用整个content")
            
            logger.debug(f"准备解析的JSON字符串: {json_str[:200]}...")
            result = json.loads(json_str)
            logger.info("✅ 成功解析Claude响应为JSON")
            
            # 验证推荐的category_id是否都是有效的二级分类ID
            if 'component_mappings' in result:
                valid_mappings = []
                for mapping in result['component_mappings']:
                    recommended_id = mapping.get('recommended_category_id')
                    if recommended_id in secondary_categories_list:
                        valid_mappings.append(mapping)
                        logger.debug(f"✅ 组件{mapping.get('component_id')}推荐的分类ID {recommended_id} 有效")
                    else:
                        logger.warning(f"❌ 组件{mapping.get('component_id')}推荐的分类ID {recommended_id} 不是有效的二级分类ID")
                
                result['component_mappings'] = valid_mappings
                logger.info(f"验证完成：{len(valid_mappings)}/{len(result['component_mappings'])}个推荐有效")
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ 解析Claude响应JSON失败: {e}")
            logger.error("🔍 Claude原始响应内容:")
            logger.error("=" * 80)
            logger.error(content)
            logger.error("=" * 80)
            logger.error(f"🔍 尝试解析的JSON字符串:")
            logger.error("-" * 40)
            logger.error(json_str)
            logger.error("-" * 40)
            
            # 尝试其他解析方法
            logger.info("🔄 尝试其他解析方法...")
            
            # 方法1: 尝试提取所有可能的JSON块
            json_blocks = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', content, re.DOTALL)
            if json_blocks:
                logger.info(f"找到{len(json_blocks)}个可能的JSON块")
                for i, block in enumerate(json_blocks):
                    try:
                        result = json.loads(block)
                        logger.info(f"✅ 成功解析第{i+1}个JSON块")
                        # 同样需要验证二级分类ID
                        if 'component_mappings' in result:
                            valid_mappings = []
                            for mapping in result['component_mappings']:
                                recommended_id = mapping.get('recommended_category_id')
                                if recommended_id in secondary_categories_list:
                                    valid_mappings.append(mapping)
                            result['component_mappings'] = valid_mappings
                        return result
                    except json.JSONDecodeError:
                        continue
            
            # 方法2: 尝试找到以{开头的内容
            start_idx = content.find('{')
            if start_idx != -1:
                json_candidate = content[start_idx:]
                # 找到匹配的结束大括号
                brace_count = 0
                end_idx = -1
                for i, char in enumerate(json_candidate):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i + 1
                            break
                
                if end_idx != -1:
                    json_candidate = json_candidate[:end_idx]
                    try:
                        result = json.loads(json_candidate)
                        logger.info("✅ 成功解析提取的JSON内容")
                        # 同样需要验证二级分类ID
                        if 'component_mappings' in result:
                            valid_mappings = []
                            for mapping in result['component_mappings']:
                                recommended_id = mapping.get('recommended_category_id')
                                if recommended_id in secondary_categories_list:
                                    valid_mappings.append(mapping)
                            result['component_mappings'] = valid_mappings
                        return result
                    except json.JSONDecodeError:
                        logger.error("❌ 提取的JSON内容仍然解析失败")
            
            logger.error("❌ 所有解析方法都失败了")
            return {}
            
    except Exception as e:
        logger.error(f"调用Claude API时出错: {e}")
        return {}

def update_component_category_id(component_id: int, new_category_id: int) -> bool:
    """更新组件的category_id"""
    conn = get_db_connection()
    if not conn:
        return False

    try:
        cursor = conn.cursor()
        
        # 先查询当前的category_id
        check_query = "SELECT category_id FROM component_analysis WHERE component_id = %s"
        cursor.execute(check_query, (component_id,))
        result = cursor.fetchone()
        
        if not result:
            logger.warning(f"❌ 组件ID {component_id} 未找到对应的分析记录")
            return False
        
        current_category_id = result[0]
        
        # 如果category_id相同，视为确认当前分类正确
        if current_category_id == new_category_id:
            logger.info(f"✅ 组件ID {component_id} 分类确认无需变更: category_id {new_category_id}")
            return True
        
        # 执行更新
        update_query = """
        UPDATE component_analysis 
        SET category_id = %s 
        WHERE component_id = %s
        """
        cursor.execute(update_query, (new_category_id, component_id))
        conn.commit()
        
        rows_affected = cursor.rowcount
        if rows_affected > 0:
            logger.info(f"✅ 数据库更新成功：组件ID {component_id} -> category_id {current_category_id} → {new_category_id}")
            return True
        else:
            logger.warning(f"❌ 组件ID {component_id} 更新失败")
            return False
            
    except Exception as e:
        logger.error(f"更新组件category_id时出错: {e}")
        conn.rollback()
        return False
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def batch_process_components_preview(batch_size=5, max_batches=10):
    """
    批量处理组件分类映射 - 预览版本（只打印结果，不更新数据库）
    """
    logger.info("🔍 开始批量分析组件分类映射 - 预览模式（只显示结果，不更新数据库）")
    
    # 获取分类结构
    categories_structure = get_product_categories()
    if not categories_structure:
        logger.error("无法获取产品分类信息，终止处理")
        return
    
    offset = 0
    total_processed = 0
    batch_count = 0
    
    print("\n" + "="*80)
    print("📋 组件分类映射预览结果")
    print("="*80)
    
    while batch_count < max_batches:
        # 获取一批组件
        components = get_components_with_analysis(limit=batch_size, offset=offset)
        if not components:
            logger.info("没有更多组件需要处理")
            break
        
        logger.info(f"🔄 分析第{offset + 1}到{offset + len(components)}个组件...")
        
        # 使用Claude分析这批组件
        analysis_result = analyze_component_category_mapping(components, categories_structure)
        
        if analysis_result and 'component_mappings' in analysis_result:
            mappings = analysis_result['component_mappings']
            
            print(f"\n📦 批次 {batch_count + 1} 分析结果：")
            print("-" * 60)
            
            for mapping in mappings:
                component_id = mapping.get('component_id')
                component_name = mapping.get('component_name', '')
                new_category_id = mapping.get('recommended_category_id')
                category_name = mapping.get('category_name', '')
                confidence = mapping.get('confidence', '未知')
                reasoning = mapping.get('reasoning', '')
                
                print(f"🔹 组件: {component_name} (ID: {component_id})")
                print(f"   推荐分类: {category_name} (ID: {new_category_id})")
                print(f"   置信度: {confidence}")
                print(f"   理由: {reasoning}")
                print()
                
                time.sleep(0.1)
            
            # 显示分析总结
            if 'analysis_summary' in analysis_result:
                print(f"📝 本批次总结: {analysis_result['analysis_summary']}")
                print()
        else:
            logger.warning("Claude分析结果为空或格式不正确")
        
        total_processed += len(components)
        offset += batch_size
        batch_count += 1
        
        # 添加延时避免频繁调用Claude API
        time.sleep(2)
    
    print("="*80)
    print(f"🎯 预览完成：共分析了{total_processed}个组件，{batch_count}个批次")
    print("="*80)

def batch_process_components_execute(batch_size=5, max_batches=10):
    """
    批量处理组件分类映射 - 执行版本（实际更新数据库）
    """
    logger.info("🚀 开始批量处理组件分类映射 - 执行模式（将更新数据库）")
    
    # 获取分类结构
    categories_structure = get_product_categories()
    if not categories_structure:
        logger.error("无法获取产品分类信息，终止处理")
        return
    
    offset = 0
    total_processed = 0
    total_updated = 0
    batch_count = 0
    
    print("\n" + "="*80)
    print("💾 开始执行数据库更新")
    print("="*80)
    
    while batch_count < max_batches:
        # 获取一批组件
        components = get_components_with_analysis(limit=batch_size, offset=offset)
        if not components:
            logger.info("没有更多组件需要处理")
            break
        
        logger.info(f"🔄 处理第{offset + 1}到{offset + len(components)}个组件...")
        
        # 使用Claude分析这批组件
        analysis_result = analyze_component_category_mapping(components, categories_structure)
        
        if analysis_result and 'component_mappings' in analysis_result:
            mappings = analysis_result['component_mappings']
            
            logger.info(f"Claude推荐了{len(mappings)}个组件的分类映射")
            
            # 更新数据库
            for mapping in mappings:
                component_id = mapping.get('component_id')
                component_name = mapping.get('component_name', '')
                new_category_id = mapping.get('recommended_category_id')
                category_name = mapping.get('category_name', '')
                confidence = mapping.get('confidence', '未知')
                reasoning = mapping.get('reasoning', '')
                
                if component_id and new_category_id:
                    print(f"🔄 正在更新: {component_name} (ID: {component_id}) -> {category_name} (ID: {new_category_id})")
                    
                    if update_component_category_id(component_id, new_category_id):
                        total_updated += 1
                        print(f"   ✅ 更新成功 (置信度: {confidence})")
                    else:
                        print(f"   ❌ 更新失败")
                    
                    # 添加延时避免频繁操作数据库
                    time.sleep(0.1)
        else:
            logger.warning("Claude分析结果为空或格式不正确")
        
        total_processed += len(components)
        offset += batch_size
        batch_count += 1
        
        # 添加延时避免频繁调用Claude API
        time.sleep(2)
    
    print("="*80)
    print(f"🎯 执行完成：共处理{total_processed}个组件，成功更新{total_updated}个")
    print("="*80)

def batch_process_unclassified_components(batch_size=5, max_batches=10):
    """
    批量处理未分类组件 - 专门处理is_success=1但category_id为NULL的组件
    """
    logger.info("🔍 开始批量处理未分类组件 - 专门处理分析成功但未分配分类的组件")
    
    # 获取分类结构
    categories_structure = get_product_categories()
    if not categories_structure:
        logger.error("无法获取产品分类信息，终止处理")
        return
    
    offset = 0
    total_processed = 0
    total_updated = 0
    batch_count = 0
    
    print("\n" + "="*80)
    print("🏷️  开始处理未分类组件")
    print("="*80)
    
    while batch_count < max_batches:
        # 获取一批未分类组件
        components = get_components_without_category_id(limit=batch_size, offset=offset)
        if not components:
            logger.info("没有更多未分类组件需要处理")
            break
        
        logger.info(f"🔄 处理第{offset + 1}到{offset + len(components)}个未分类组件...")
        
        # 使用Claude分析这批组件
        analysis_result = analyze_component_category_mapping(components, categories_structure)
        
        if analysis_result and 'component_mappings' in analysis_result:
            mappings = analysis_result['component_mappings']
            
            logger.info(f"Claude为{len(mappings)}个未分类组件推荐了分类")
            
            # 更新数据库
            for mapping in mappings:
                component_id = mapping.get('component_id')
                component_name = mapping.get('component_name', '')
                new_category_id = mapping.get('recommended_category_id')
                category_name = mapping.get('category_name', '')
                confidence = mapping.get('confidence', '未知')
                reasoning = mapping.get('reasoning', '')
                
                if component_id and new_category_id:
                    print(f"🏷️  正在分类: {component_name} (ID: {component_id}) -> {category_name} (ID: {new_category_id})")
                    
                    if update_component_category_id(component_id, new_category_id):
                        total_updated += 1
                        print(f"   ✅ 分类成功 (置信度: {confidence})")
                        print(f"   📝 理由: {reasoning}")
                    else:
                        print(f"   ❌ 分类失败")
                    
                    # 添加延时避免频繁操作数据库
                    time.sleep(0.1)
                    print()
        else:
            logger.warning("Claude分析结果为空或格式不正确")
        
        total_processed += len(components)
        offset += batch_size
        batch_count += 1
        
        # 添加延时避免频繁调用Claude API
        time.sleep(2)
    
    print("="*80)
    print(f"🎯 未分类组件处理完成：共处理{total_processed}个组件，成功分类{total_updated}个")
    print("="*80)

def verify_updates():
    """验证更新结果"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        cursor = conn.cursor(dictionary=True)
        
        # 统计category_id的分布
        query = """
        SELECT 
            ca.category_id,
            pc.category as category_name,
            COUNT(*) as component_count
        FROM component_analysis ca
        LEFT JOIN product_categories pc ON ca.category_id = pc.id
        WHERE ca.category_id IS NOT NULL
        GROUP BY ca.category_id, pc.category
        ORDER BY ca.category_id
        """
        cursor.execute(query)
        results = cursor.fetchall()
        
        print("\n" + "="*80)
        print("📊 数据库中的category_id分布统计")
        print("="*80)
        
        for row in results:
            category_name = row['category_name'] or '❌ 未找到分类名称'
            print(f"ID {row['category_id']:3d}: {category_name:<30} - {row['component_count']:3d}个组件")
        
        print("="*80)
        
    except Exception as e:
        logger.error(f"验证更新结果时出错: {e}")
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def main():
    """主函数"""
    print("🔧 组件分类批量分析工具")
    print("请选择执行模式：")
    print("1. 预览模式 - 只显示Claude的推荐结果，不更新数据库")
    print("2. 执行模式 - 实际更新数据库的category_id（全表处理）")
    print("3. 验证模式 - 查看当前数据库中的分类分布")
    print("4. 未分类处理 - 专门处理is_success=1但category_id为NULL的组件")
    
    choice = input("请输入选择 (1/2/3/4): ").strip()
    
    try:
        if choice == "1":
            logger.info("用户选择预览模式")
            batch_process_components_preview(batch_size=5, max_batches=2)  # 每批5个组件，只做2个batch
        elif choice == "2":
            logger.info("用户选择执行模式")
            confirm = input("⚠️  确认要更新数据库吗？这将修改component_analysis表的category_id字段 (y/N): ")
            if confirm.lower() == 'y':
                batch_process_components_execute(batch_size=5, max_batches=10)  # 每批5个组件，最多10个batch
                verify_updates()
            else:
                print("❌ 用户取消执行")
        elif choice == "3":
            logger.info("用户选择验证模式")
            verify_updates()
        elif choice == "4":
            logger.info("用户选择未分类处理模式")
            confirm = input("⚠️  确认要为未分类组件分配category_id吗？ (y/N): ")
            if confirm.lower() == 'y':
                batch_process_unclassified_components(batch_size=5, max_batches=20)  # 每批5个组件，最多20个batch
                verify_updates()
            else:
                print("❌ 用户取消执行")
        else:
            print("❌ 无效选择")
            
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main() 