#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商数据导入脚本
从supplier_results目录下的markdown文件中解析供应商数据并导入到数据库
"""

import os
import re
import json
import mysql.connector
from typing import List, Dict, Optional
import logging
from datetime import datetime

# 导入配置
# 直接使用阿里云RDS配置，与其他training脚本保持一致
DB_CONFIG = {
    'host': 'rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com',
    'port': 3306,
    'database': 'procurement_system',
    'user': 'yj_app',
    'password': '4iLe5fifhMqOo9Ne',
    'charset': 'utf8mb4'
}

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupplierImporter:
    def __init__(self, db_config: Dict[str, str] = None):
        """
        初始化供应商导入器
        
        Args:
            db_config: 数据库配置字典，如果未提供则使用默认配置
        """
        self.db_config = db_config or DB_CONFIG
        self.connection = None
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            logger.info("数据库连接成功")
        except mysql.connector.Error as e:
            logger.error(f"数据库连接失败: {e}")
            raise
            
    def close_db(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
            
    def extract_category_id_from_filename(self, filename: str) -> Optional[int]:
        """
        从文件名中提取分类ID
        
        Args:
            filename: 文件名，格式如 Cyclone3000_LED照明控制板_123_20250604_113802.md
            
        Returns:
            分类ID，如果提取失败返回None
        """
        # 匹配格式：Cyclone3000_{分类名称}_{分类ID}_{时间戳}.md
        pattern = r'Cyclone3000_.*?_(\d+)_\d+_\d+\.md'
        match = re.search(pattern, filename)
        if match:
            return int(match.group(1))
        return None
        
    def parse_markdown_file(self, file_path: str) -> Optional[Dict]:
        """
        解析markdown文件，提取供应商数据
        
        Args:
            file_path: markdown文件路径
            
        Returns:
            包含分类ID和供应商列表的字典，如果解析失败返回None
        """
        try:
            filename = os.path.basename(file_path)
            category_id = self.extract_category_id_from_filename(filename)
            
            if category_id is None:
                logger.warning(f"无法从文件名提取分类ID: {filename}")
                return None
                
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找JSON数据块
            json_pattern = r'```json\s*(\{.*?\})\s*```'
            json_matches = re.findall(json_pattern, content, re.DOTALL)
            
            if not json_matches:
                logger.warning(f"文件中未找到JSON数据: {filename}")
                return None
                
            # 解析JSON数据
            suppliers_data = []
            for json_str in json_matches:
                try:
                    data = json.loads(json_str)
                    if 'suppliers' in data:
                        suppliers_data.extend(data['suppliers'])
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败 {filename}: {e}")
                    continue
                    
            return {
                'category_id': category_id,
                'suppliers': suppliers_data
            }
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {e}")
            return None
            
    def extract_region_from_address(self, address: str) -> str:
        """
        从地址中提取地区信息
        
        Args:
            address: 完整地址
            
        Returns:
            地区信息（省市）
        """
        if not address:
            return ""
            
        # 提取省市信息的正则表达式
        patterns = [
            r'([\u4e00-\u9fa5]+省[\u4e00-\u9fa5]+市)',  # 省市格式
            r'([\u4e00-\u9fa5]+市)',  # 直辖市格式
            r'(广东省[\u4e00-\u9fa5]+)',  # 广东省特殊处理
            r'(江苏省[\u4e00-\u9fa5]+)',  # 江苏省特殊处理
            r'(浙江省[\u4e00-\u9fa5]+)',  # 浙江省特殊处理
        ]
        
        for pattern in patterns:
            match = re.search(pattern, address)
            if match:
                return match.group(1)
                
        # 如果没有匹配到，返回地址前50个字符
        return address[:50] if len(address) > 50 else address
        
    def clean_phone_number(self, phone: str) -> str:
        """
        清理和标准化电话号码
        
        Args:
            phone: 原始电话号码
            
        Returns:
            清理后的电话号码
        """
        if not phone:
            return ""
            
        # 移除常见的无用文本
        cleaned = phone.replace("通过官网查询", "").replace("未公开具体电话", "").replace("需要进一步查询", "")
        
        # 提取电话号码格式
        phone_pattern = r'(\+?86-?\d{3,4}-?\d{4,8}|\d{3,4}-?\d{7,8})'
        match = re.search(phone_pattern, cleaned)
        if match:
            return match.group(1)
            
        return cleaned.strip()[:50]  # 限制长度
        
    def clean_email(self, email: str) -> str:
        """
        清理和验证邮箱地址
        
        Args:
            email: 原始邮箱地址
            
        Returns:
            清理后的邮箱地址
        """
        if not email:
            return ""
            
        # 移除常见的无用文本
        cleaned = email.replace("通过官网联系", "").replace("未公开具体邮箱", "").replace("需要进一步查询", "")
        
        # 验证邮箱格式
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        match = re.search(email_pattern, cleaned)
        if match:
            return match.group(0)
            
        return cleaned.strip()[:100]  # 限制长度
        
    def insert_suppliers(self, suppliers_data: List[Dict]) -> int:
        """
        批量插入供应商数据到数据库
        
        Args:
            suppliers_data: 供应商数据列表
            
        Returns:
            成功插入的记录数
        """
        if not self.connection:
            raise Exception("数据库未连接")
            
        cursor = self.connection.cursor()
        insert_count = 0
        
        insert_query = """
        INSERT INTO search_suppliers (
            supplier_name, region, category_id, matching_reason, 
            website, phone, email, certifications, notes, address, status
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        for supplier in suppliers_data:
            try:
                # 数据清理和标准化
                supplier_name = supplier.get('company_name', '').strip()
                if not supplier_name:
                    logger.warning("供应商名称为空，跳过该记录")
                    continue
                    
                address = supplier.get('address', '').strip()
                region = self.extract_region_from_address(address)
                phone = self.clean_phone_number(supplier.get('phone', ''))
                email = self.clean_email(supplier.get('email', ''))
                website = supplier.get('website', '').strip()
                certifications = supplier.get('certifications', '').strip()
                matching_reason = supplier.get('matching_reason', '').strip()
                notes = supplier.get('specialties', '') or supplier.get('notes', '')
                notes = notes.strip()
                
                # 限制字段长度
                supplier_name = supplier_name[:100]
                region = region[:100]
                website = website[:255]
                phone = phone[:50]
                email = email[:100]
                certifications = certifications[:255]
                notes = notes[:255]
                address = address[:255]
                
                values = (
                    supplier_name, region, supplier.get('category_id'), matching_reason,
                    website, phone, email, certifications, notes, address, 0  # status默认为0
                )
                
                cursor.execute(insert_query, values)
                insert_count += 1
                
            except Exception as e:
                logger.error(f"插入供应商数据失败: {supplier.get('company_name', 'Unknown')}, 错误: {e}")
                continue
                
        self.connection.commit()
        cursor.close()
        
        return insert_count
        
    def process_files(self, directory_path: str):
        """
        处理目录下的所有markdown文件
        
        Args:
            directory_path: 包含markdown文件的目录路径
        """
        if not os.path.exists(directory_path):
            logger.error(f"目录不存在: {directory_path}")
            return
            
        md_files = [f for f in os.listdir(directory_path) if f.endswith('.md')]
        logger.info(f"找到 {len(md_files)} 个markdown文件")
        
        total_suppliers = 0
        processed_files = 0
        
        for filename in md_files:
            file_path = os.path.join(directory_path, filename)
            logger.info(f"处理文件: {filename}")
            
            parsed_data = self.parse_markdown_file(file_path)
            if parsed_data is None:
                continue
                
            # 为每个供应商添加分类ID
            suppliers = parsed_data['suppliers']
            for supplier in suppliers:
                supplier['category_id'] = parsed_data['category_id']
                
            # 插入数据库
            inserted_count = self.insert_suppliers(suppliers)
            total_suppliers += inserted_count
            processed_files += 1
            
            logger.info(f"文件 {filename} 处理完成，插入 {inserted_count} 个供应商")
            
        logger.info(f"处理完成！总共处理 {processed_files} 个文件，插入 {total_suppliers} 个供应商")

def main():
    """主函数"""
    # 供应商结果文件目录 - 相对于training目录
    supplier_results_dir = os.path.join(os.path.dirname(__file__), "output", "supplier_results")
    
    # 创建导入器实例，使用默认的数据库配置
    importer = SupplierImporter()
    
    try:
        # 连接数据库
        importer.connect_db()
        
        # 处理文件
        importer.process_files(supplier_results_dir)
        
    except Exception as e:
        logger.error(f"导入过程中发生错误: {e}")
    finally:
        # 关闭数据库连接
        importer.close_db()

if __name__ == "__main__":
    main() 