#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于产品分类特征的供应商搜索脚本
根据product_categories表中每个分类的feature描述，使用AI搜索相关供应商
直接将搜索结果保存为markdown文件，不进行任何处理

功能：
1. 读取product_categories表中的分类信息
2. 根据每个分类的feature特征描述，使用Claude+搜索工具查找相关供应商
3. 将Claude的原始搜索结果保存为格式化的markdown文件
4. 按分类生成独立的md文件，保存在output/supplier_results目录
5. 支持500错误自动重试机制（30秒间隔，最多重试3次）
6. 默认API调用间隔10秒，避免频率限制

修改说明：
- 移除了JSON解析和数据库保存功能
- 直接保存Claude的原始搜索结果到md文件，包含分类信息和格式化标题
- 每个分类生成一个独立的md文件，包含分类信息、技术特征和搜索结果
- 添加了500错误重试机制和异常处理
- API调用间隔从2秒改为10秒

状态值定义（已废弃，仅用于Excel生成功能）:
0: 验证正常
1: 验证pending
2: 网站无效
3: 网站被标为黑名单
4: 产品不能做
5: 报价过高
99: 验证通过
"""

import os
import sys
import mysql.connector
import pandas as pd
import json
import time
import logging
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.chart import BarChart, PieChart, Reference
import argparse
import re

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': "rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com",
    'port': 3306,
    'user': "yj_app",
    'password': "4iLe5fifhMqOo9Ne",
    'database': "procurement_system",
    'charset': 'utf8mb4'
}

# 状态值定义
STATUS_MAPPING = {
    0: "验证正常",
    1: "验证pending",
    2: "网站无效",
    3: "网站被标为黑名单",
    4: "产品不能做",
    5: "报价过高",
    99: "验证通过"
}

# 导入Claude工具函数
from claude_utils import call_claude_with_search

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except mysql.connector.Error as e:
        logger.error(f"数据库连接错误: {e}")
        return None

def get_product_categories():
    """获取所有产品分类信息"""
    conn = get_db_connection()
    if not conn:
        logger.error("无法连接到数据库")
        return []
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        query = """
        SELECT pc.*, cp.name as product_name, cp.model as product_model,
               parent.category as parent_category
        FROM product_categories pc
        JOIN client_product cp ON pc.product_id = cp.id
        LEFT JOIN product_categories parent ON pc.prev = parent.id
        ORDER BY pc.product_id, pc.prev, pc.id
        """
        
        cursor.execute(query)
        categories = cursor.fetchall()
        
        logger.info(f"获取到 {len(categories)} 个产品分类")
        return categories
        
    except Exception as e:
        logger.error(f"查询产品分类时出错: {e}")
        return []
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def get_category_by_id(category_id):
    """根据分类ID获取特定分类信息"""
    conn = get_db_connection()
    if not conn:
        logger.error("无法连接到数据库")
        return None
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        query = """
        SELECT pc.*, cp.name as product_name, cp.model as product_model,
               parent.category as parent_category
        FROM product_categories pc
        JOIN client_product cp ON pc.product_id = cp.id
        LEFT JOIN product_categories parent ON pc.prev = parent.id
        WHERE pc.id = %s
        """
        
        cursor.execute(query, (category_id,))
        category = cursor.fetchone()
        
        if category:
            logger.info(f"找到分类: {category['category']} (ID: {category_id})")
        else:
            logger.warning(f"未找到分类ID: {category_id}")
        
        return category
        
    except Exception as e:
        logger.error(f"查询分类ID {category_id} 时出错: {e}")
        return None
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def create_search_prompt(category_info):
    """为分类创建搜索提示词"""
    category_name = category_info['category']
    feature = category_info['feature'] or ""
    product_name = category_info['product_name']
    parent_category = category_info.get('parent_category', '')
    
    # 构建分类层次信息
    category_hierarchy = f"{parent_category} > {category_name}" if parent_category else category_name
    
    prompt = f"""
请帮我搜索并找到专门生产或供应以下产品分类的中国供应商：

产品：{product_name}
分类：{category_hierarchy}
技术特征：{feature}

请搜索并提供以下信息的供应商（家，优先选择有实力的制造商）：
1. 公司名称（中文全称）
2. 官方网站（完整URL）
3. 联系电话（包括区号）
4. 邮箱地址
5. 公司地址（省市区详细地址）
6. 主要认证（如ISO、CE、FDA等）
7. 公司规模和成立年份
8. 专业领域和产品优势

请重点关注：
- 有相关技术实力和生产能力的制造商
- 有相关行业认证的企业
- 在该领域有一定知名度的公司
- 能够提供该类产品的专业供应商

请以JSON格式返回结果，格式如下：
{{
    "suppliers": [
        {{
            "company_name": "公司名称",
            "website": "http://www.example.com",
            "phone": "021-12345678",
            "email": "<EMAIL>",
            "address": "详细地址",
            "certifications": "ISO9001, CE",
            "company_size": "规模描述",
            "founded_year": "成立年份",
            "specialties": "专业领域描述",
            "matching_reason": "为什么适合这个分类的原因"
        }}
    ]
}}
"""
    
    return prompt

def extract_content_from_claude_response(response):
    """
    从Claude响应中只提取文本内容，忽略工具调用和搜索过程
    """
    logger.debug("📥 开始从Claude响应中提取文本内容")
    
    # 输出响应的详细信息用于调试
    logger.debug(f"响应类型: {type(response)}")
    if isinstance(response, dict):
        logger.debug(f"响应字典的键: {list(response.keys())}")
    
    # 如果输入是字典，从嵌套结构中提取内容
    if isinstance(response, dict):
        logger.debug("响应是字典格式")
        
        # 检查是否有response字段包装
        actual_response = response
        if 'response' in response:
            actual_response = response['response']
            logger.debug("找到response包装，使用内部response")
        
        # 检查Claude转发服务的响应格式
        if 'content' in actual_response:
            content = actual_response['content']
            logger.debug(f"找到content字段，类型: {type(content)}")
            
            if isinstance(content, list) and len(content) > 0:
                logger.debug(f"content是数组，包含 {len(content)} 个元素")
                
                # 只提取type为text的内容
                text_parts = []
                for i, item in enumerate(content):
                    logger.debug(f"content[{i}]: 类型={type(item)}")
                    if isinstance(item, dict):
                        item_type = item.get('type', 'unknown')
                        logger.debug(f"content[{i}] type: {item_type}")
                        
                        if item_type == 'text':
                            text_content = item.get('text', '')
                            if text_content:
                                text_parts.append(text_content)
                                logger.debug(f"content[{i}] 提取text长度: {len(text_content)} 字符")
                        else:
                            logger.debug(f"content[{i}] 跳过类型: {item_type}")
                
                if text_parts:
                    full_text = '\n'.join(text_parts)
                    logger.info(f"✅ 成功提取到文本内容，总长度: {len(full_text)} 字符")
                    return full_text.strip()
                else:
                    logger.warning("⚠️ content数组中没有找到text类型的元素")
            
            elif isinstance(content, str):
                logger.info(f"✅ content是字符串，长度: {len(content)} 字符")
                return content.strip()
            else:
                logger.warning(f"⚠️ content类型不是list或str: {type(content)}")
        
        # 兼容其他响应格式
        elif 'choices' in actual_response and len(actual_response['choices']) > 0:
            choice = actual_response['choices'][0]
            if 'message' in choice and 'content' in choice['message']:
                content = choice['message']['content']
                logger.info(f"从choices格式提取内容，长度: {len(content)} 字符")
                return content.strip()
        else:
            logger.warning(f"⚠️ 响应中没有找到content或choices字段")
        
        # 如果都没有找到文本内容，返回空字符串
        logger.warning("未找到文本内容，返回空字符串")
        return ""
    
    # 如果输入是字符串，直接返回
    elif isinstance(response, str):
        logger.debug("响应是字符串格式")
        return response.strip()
    
    # 其他类型返回空字符串
    logger.warning(f"未知响应类型: {type(response)}，返回空字符串")
    return ""

def convert_claude_response_to_json(response_text, category_info):
    """
    使用GPT将Claude响应转换为标准JSON格式
    参考classify_product_claude.py的convert_markdown_to_json函数
    """
    try:
        logger.info("📝 开始使用GPT将Claude响应转换为供应商JSON")
        
        # 构建转换系统提示词
        system_prompt = """你是一个专业的数据格式转换助手。你需要将Claude关于供应商搜索的响应文本转换为标准JSON格式。

重要要求：
1. 必须返回完整有效的JSON格式，不要添加任何解释文字
2. 从文本中提取所有供应商信息
3. 确保所有字段都是字符串格式
4. 如果某些信息缺失，用空字符串""填充
5. 确保JSON格式完全正确，不要有语法错误"""

        # 构建转换提示词
        conversion_prompt = f"""
请将以下Claude关于供应商搜索的响应文本转换为标准JSON格式。

分类信息：
- 分类名称：{category_info['category']}
- 产品名称：{category_info['product_name']}

Claude响应文本：
```
{response_text}
```

请严格按照以下JSON格式输出，从响应文本中提取所有供应商信息：

{{
    "suppliers": [
        {{
            "company_name": "公司名称",
            "website": "官方网站URL",
            "phone": "联系电话",
            "email": "邮箱地址",
            "address": "详细地址",
            "certifications": "认证信息",
            "company_size": "公司规模",
            "founded_year": "成立年份",
            "specialties": "专业领域",
            "matching_reason": "匹配原因"
        }}
    ]
}}

请只返回JSON内容，不要添加任何其他文字或解释。
如果文本中没有明确的供应商信息，请返回空的suppliers数组。
"""
        
        # 调用GPT进行转换
        from claude_utils import call_openai
        conversion_result = call_openai(conversion_prompt, system_prompt)
        
        logger.info("🤖 ===== GPT供应商信息转换响应开始 =====")
        logger.debug(f"转换响应类型: {type(conversion_result)}")
        
        if "error" in conversion_result:
            logger.error(f"GPT转换错误: {conversion_result['error']}")
            return []
            
        # 提取JSON文本
        json_text = conversion_result.get('content', '')
        logger.info(f"转换后JSON文本长度: {len(json_text)} 字符")
        logger.debug(f"转换后JSON内容: {json_text}")
        logger.info("🤖 ===== GPT供应商信息转换响应结束 =====")
        
        # 解析JSON
        suppliers = parse_supplier_json(json_text)
        
        if suppliers:
            logger.info(f"✅ GPT供应商信息转换成功，解析出 {len(suppliers)} 个供应商")
            return suppliers
        else:
            logger.warning("⚠️ GPT转换成功但未解析出供应商信息")
            return []
            
    except Exception as e:
        logger.error(f"❌ GPT供应商信息转换过程失败: {e}")
        import traceback
        logger.error(f"转换异常堆栈: {traceback.format_exc()}")
        return []

def parse_supplier_json(json_text):
    """
    解析GPT转换后的供应商JSON文本
    参考classify_product_claude.py的parse_converted_json函数
    """
    # 如果输入已经是字典，直接处理
    if isinstance(json_text, dict):
        logger.info("✅ 输入已经是字典，直接处理")
        if 'suppliers' in json_text:
            return json_text['suppliers']
        return []
    
    # 从文本中提取JSON部分
    text_content = str(json_text)
    
    # 尝试提取JSON代码块
    if "```json" in text_content:
        try:
            json_start = text_content.find("```json") + 7
            json_end = text_content.find("```", json_start)
            
            if json_end == -1:
                logger.warning("⚠️ 找不到JSON结束标记")
                extracted = text_content[json_start:].strip()
            else:
                extracted = text_content[json_start:json_end].strip()
                
            logger.debug(f"提取的JSON长度: {len(extracted)} 字符")
            text_content = extracted
        except Exception as e:
            logger.error(f"提取JSON代码块时出错: {e}")
    elif "{" in text_content:
        # 尝试直接提取JSON部分
        start_idx = text_content.find("{")
        end_idx = text_content.rfind("}") + 1
        if start_idx != -1 and end_idx > start_idx:
            text_content = text_content[start_idx:end_idx]
    
    # 解析JSON
    try:
        logger.debug(f"📝 尝试解析供应商JSON，长度: {len(text_content)} 字符")
        
        result = json.loads(text_content)
        logger.info("✅ 供应商JSON解析成功")
        
        # 提取suppliers数组
        if isinstance(result, dict) and 'suppliers' in result:
            suppliers = result['suppliers']
            if isinstance(suppliers, list):
                logger.info(f"✅ 成功解析出 {len(suppliers)} 个供应商")
                return suppliers
        
        logger.warning("⚠️ JSON解析成功但未找到suppliers数组")
        return []
        
    except json.JSONDecodeError as e:
        logger.error(f"❌ 供应商JSON解析失败: {e}")
        try:
            # 尝试修复常见JSON错误
            fixed_json = text_content.replace("'", '"').replace("False", "false").replace("True", "true")
            result = json.loads(fixed_json)
            if isinstance(result, dict) and 'suppliers' in result:
                suppliers = result['suppliers']
                logger.info(f"✅ JSON修复成功，解析出 {len(suppliers)} 个供应商")
                return suppliers
        except Exception as e2:
            logger.error(f"❌ JSON修复也失败: {e2}")
        
        return []

def save_suppliers_to_db(suppliers, category_id):
    """将搜索到的供应商保存到数据库"""
    if not suppliers:
        return 0
    
    conn = get_db_connection()
    if not conn:
        logger.error("无法连接到数据库保存供应商")
        return 0
    
    try:
        cursor = conn.cursor()
        
        insert_query = """
        INSERT INTO search_suppliers 
        (supplier_name, region, category_id, matching_reason, website, phone, email, certifications, notes, address, status) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 1)
        """
        
        saved_count = 0
        for supplier in suppliers:
            try:
                # 提取地区信息（从地址中提取省份）
                address = supplier.get('address', '')
                region = extract_region_from_address(address)
                
                # 准备插入数据
                supplier_data = (
                    supplier.get('company_name', ''),
                    region,
                    category_id,
                    supplier.get('matching_reason', ''),
                    supplier.get('website', ''),
                    supplier.get('phone', ''),
                    supplier.get('email', ''),
                    supplier.get('certifications', ''),
                    f"公司规模: {supplier.get('company_size', '')}; 成立年份: {supplier.get('founded_year', '')}; 专业领域: {supplier.get('specialties', '')}",
                    address
                )
                
                cursor.execute(insert_query, supplier_data)
                saved_count += 1
                
            except Exception as e:
                logger.warning(f"保存供应商 {supplier.get('company_name', 'Unknown')} 时出错: {e}")
                continue
        
        conn.commit()
        logger.info(f"成功保存 {saved_count} 个供应商到数据库")
        return saved_count
        
    except Exception as e:
        logger.error(f"保存供应商到数据库时出错: {e}")
        conn.rollback()
        return 0
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def extract_region_from_address(address):
    """从地址中提取地区信息"""
    if not address:
        return "未知"
    
    # 常见省市匹配
    provinces = ['北京', '上海', '天津', '重庆', '广东', '江苏', '浙江', '山东', '河南', '湖北', '湖南', 
                '四川', '河北', '安徽', '福建', '江西', '辽宁', '黑龙江', '吉林', '山西', '陕西', 
                '甘肃', '青海', '新疆', '西藏', '宁夏', '内蒙古', '广西', '海南', '贵州', '云南']
    
    for province in provinces:
        if province in address:
            return province
    
    return "未知"

def search_suppliers_for_category(category_info, delay=10):
    """为指定分类搜索供应商并直接保存到md文件"""
    category_name = category_info['category']
    category_id = category_info['id']
    product_name = category_info['product_name']
    
    logger.info(f"开始为分类 '{category_name}' (ID: {category_id}) 搜索供应商...")
    
    # 创建搜索提示词
    prompt = create_search_prompt(category_info)
    logger.debug(f"生成的搜索提示词长度: {len(prompt)} 字符")
    
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 调用Claude进行搜索
            logger.info(f"正在调用Claude API搜索分类 '{category_name}' (尝试 {retry_count + 1}/{max_retries})...")
            response = call_claude_with_search(prompt)
            
            if 'error' in response:
                error_msg = response.get('error', '')
                logger.error(f"AI搜索出错: {error_msg}")
                
                # 检查是否是5开头的服务器错误
                if any(f'5{i}' in str(error_msg) for i in range(10)) or 'Internal Server Error' in str(error_msg) or 'Bad Gateway' in str(error_msg) or 'Service Unavailable' in str(error_msg) or 'Gateway Timeout' in str(error_msg):
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"检测到5xx服务器错误，等待30秒后重试 ({retry_count}/{max_retries})...")
                        time.sleep(30)
                        continue
                    else:
                        logger.error(f"已达到最大重试次数，跳过分类 '{category_name}'")
                        return 0
                else:
                    logger.error(f"非5xx错误，跳过分类 '{category_name}': {error_msg}")
                    return 0
            
            # 提取响应内容 - 确保只提取纯文本
            content = extract_content_from_claude_response(response)
            
            if not content:
                logger.warning(f"未获取到 '{category_name}' 的搜索结果")
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"内容为空，等待10秒后重试 ({retry_count}/{max_retries})...")
                    time.sleep(10)
                    continue
                else:
                    return 0
            
            # 检查content是否是字符串，如果不是则转换
            if not isinstance(content, str):
                logger.warning(f"提取的内容不是字符串类型，类型为: {type(content)}")
                content = str(content)
            
            logger.info(f"✅ 成功获取 '{category_name}' 的搜索结果，长度: {len(content)} 字符")
            
            # 创建输出目录
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output", "supplier_results")
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成md文件名（安全的文件名）
            safe_product_name = re.sub(r'[^\w\s-]', '', product_name).strip()
            safe_category_name = re.sub(r'[^\w\s-]', '', category_name).strip()
            safe_product_name = re.sub(r'[-\s]+', '_', safe_product_name)
            safe_category_name = re.sub(r'[-\s]+', '_', safe_category_name)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            md_filename = f"{safe_product_name}_{safe_category_name}_{category_id}_{timestamp}.md"
            md_filepath = os.path.join(output_dir, md_filename)
            
            # 保存完整的格式化内容到md文件
            with open(md_filepath, 'w', encoding='utf-8') as f:
                f.write(f"# 供应商搜索结果 - {category_name}\n\n")
                f.write(f"**产品名称:** {product_name}\n")
                f.write(f"**分类ID:** {category_id}\n")
                f.write(f"**分类名称:** {category_name}\n")
                
                # 添加技术特征信息
                feature = category_info.get('feature', '')
                if feature:
                    f.write(f"**技术特征:** {feature}\n")
                
                parent_category = category_info.get('parent_category', '')
                if parent_category:
                    f.write(f"**父级分类:** {parent_category}\n")
                
                f.write(f"**生成时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("---\n\n")
                f.write("## 搜索结果\n\n")
                f.write(content.strip())
            
            logger.info(f"✅ 完整搜索结果已保存到: {md_filepath}")
            
            # 添加延迟避免API限制
            logger.debug(f"等待 {delay} 秒后处理下一个分类...")
            time.sleep(delay)
            
            return 1  # 返回1表示成功处理
            
        except Exception as e:
            logger.error(f"搜索分类 '{category_name}' 时出现异常: {e}")
            retry_count += 1
            if retry_count < max_retries:
                logger.warning(f"出现异常，等待10秒后重试 ({retry_count}/{max_retries})...")
                time.sleep(10)
                continue
            else:
                logger.error(f"已达到最大重试次数，跳过分类 '{category_name}'")
                import traceback
                logger.error(f"完整异常堆栈: {traceback.format_exc()}")
                return 0
    
    return 0

def search_all_categories(start_from=None, max_categories=None, delay=10):
    """搜索所有分类的供应商并保存到md文件"""
    logger.info("开始批量搜索所有分类的供应商...")
    
    # 获取所有分类
    categories = get_product_categories()
    if not categories:
        logger.error("未获取到任何产品分类")
        return
    
    # 过滤起始位置
    if start_from:
        categories = [cat for cat in categories if cat['id'] >= start_from]
    
    # 限制处理数量
    if max_categories:
        categories = categories[:max_categories]
    
    logger.info(f"将处理 {len(categories)} 个分类，API调用间隔: {delay}秒")
    
    total_saved_files = 0
    success_count = 0
    failed_count = 0
    
    for i, category in enumerate(categories, 1):
        try:
            category_name = category['category']
            logger.info(f"[{i}/{len(categories)}] 处理分类: {category_name}")
            
            result = search_suppliers_for_category(category, delay)
            
            if result > 0:
                total_saved_files += result
                success_count += 1
                logger.info(f"✅ 分类 '{category_name}' 搜索结果已保存到md文件")
            else:
                failed_count += 1
                logger.warning(f"❌ 分类 '{category_name}' 搜索失败")
                
        except Exception as e:
            failed_count += 1
            logger.error(f"❌ 处理分类 '{category['category']}' 时出错: {e}")
            continue
    
    # 输出统计结果
    logger.info("="*80)
    logger.info("📊 批量搜索完成！")
    logger.info("="*80)
    logger.info(f"✅ 成功处理分类: {success_count}")
    logger.info(f"❌ 失败分类: {failed_count}")
    logger.info(f"📁 生成md文件数量: {total_saved_files}")
    logger.info(f"📂 md文件保存目录: output/supplier_results/")
    logger.info("="*80)

def clear_existing_suppliers(product_id=None):
    """清除现有的供应商数据"""
    conn = get_db_connection()
    if not conn:
        logger.error("无法连接到数据库")
        return False
    
    try:
        cursor = conn.cursor()
        
        if product_id:
            # 只删除指定产品的供应商
            query = """
            DELETE ss FROM search_suppliers ss
            JOIN product_categories pc ON ss.category_id = pc.id
            WHERE pc.product_id = %s
            """
            cursor.execute(query, (product_id,))
        else:
            # 删除所有供应商
            query = "DELETE FROM search_suppliers"
            cursor.execute(query)
        
        deleted_count = cursor.rowcount
        conn.commit()
        
        logger.info(f"清除了 {deleted_count} 条现有供应商记录")
        return True
        
    except Exception as e:
        logger.error(f"清除供应商数据时出错: {e}")
        conn.rollback()
        return False
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

# ==================== Excel 生成相关函数 ====================

def get_data_from_db():
    """从数据库获取Excel生成所需的数据"""
    conn = get_db_connection()
    if not conn:
        logger.error("无法连接到数据库")
        return None, None, None
    
    try:
        # 获取供应商和分类数据
        query_suppliers = """
        SELECT s.*, pc.category, pc.product_id, pc.feature, pc.prev, 
               cp.name as product_name, cp.model as product_model,
               parent.category as parent_category
        FROM search_suppliers s
        JOIN product_categories pc ON s.category_id = pc.id
        JOIN client_product cp ON pc.product_id = cp.id
        LEFT JOIN product_categories parent ON pc.prev = parent.id
        """
        
        # 获取分类数据
        query_categories = """
        SELECT pc.*, cp.name as product_name,
               parent.category as parent_category
        FROM product_categories pc
        JOIN client_product cp ON pc.product_id = cp.id
        LEFT JOIN product_categories parent ON pc.prev = parent.id
        """
        
        df_suppliers = pd.read_sql(query_suppliers, conn)
        df_categories = pd.read_sql(query_categories, conn)
        
        # 获取组件数据和分类关系
        try:
            query_components_with_categories = """
            SELECT c.*, ca.category_id, pc.category as pc_category, pc.prev,
                   parent.category as parent_category, cp.name as product_name
            FROM component c
            JOIN component_analysis ca ON c.id = ca.component_id
            JOIN product_categories pc ON ca.category_id = pc.id
            JOIN client_product cp ON pc.product_id = cp.id
            LEFT JOIN product_categories parent ON pc.prev = parent.id
            WHERE ca.is_success = 1
            """
            
            df_components = pd.read_sql(query_components_with_categories, conn)
            logger.info(f"获取到 {len(df_components)} 条带分类信息的组件数据")
            
            return df_suppliers, df_categories, df_components
            
        except Exception as e:
            logger.warning(f"获取组件数据时出错: {e}")
            df_components = pd.DataFrame()
            return df_suppliers, df_categories, df_components
    
    except Exception as e:
        logger.error(f"查询数据时出错: {e}")
        return None, None, None
    
    finally:
        if conn.is_connected():
            conn.close()

def apply_excel_styling(wb):
    """为Excel工作簿应用样式"""
    # 定义样式
    header_font = Font(bold=True, size=12, color="FFFFFF")
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    centered = Alignment(horizontal="center", vertical="center", wrap_text=True)
    border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    
    # 遍历每个工作表应用样式
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        
        # 设置表头样式
        for cell in ws[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = centered
            cell.border = border
        
        # 设置数据单元格样式
        for row in ws.iter_rows(min_row=2):
            for cell in row:
                cell.alignment = Alignment(vertical="center", wrap_text=True)
                cell.border = border
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column_letter].width = min(adjusted_width, 50)

def generate_supplier_status_sheet(df_suppliers, wb):
    """生成供应商状态分析工作表"""
    ws = wb.create_sheet("供应商状态分析")
    ws.append(["状态代码", "状态描述", "供应商数量", "占比"])
    
    # 按状态分组并计数
    status_counts = df_suppliers['status'].value_counts().reset_index()
    status_counts.columns = ['status', 'count']
    total = status_counts['count'].sum()
    
    # 添加状态描述并计算占比
    for _, row in status_counts.iterrows():
        status_code = row['status']
        status_desc = STATUS_MAPPING.get(status_code, f"未知状态({status_code})")
        count = row['count']
        percentage = (count / total) * 100
        
        ws.append([status_code, status_desc, count, f"{percentage:.2f}%"])

def generate_supplier_overview_sheet(df_suppliers, df_categories, wb):
    """生成供应商总览工作表"""
    ws = wb.create_sheet("供应商总览")
    ws.append(["产品", "一级分类", "二级分类", "供应商名称", "所在地区", "网站", "状态", "联系电话", "邮箱", "认证"])
    
    # 获取所有一级分类和二级分类
    all_category_data = []
    
    # 遍历产品和分类
    for _, row in df_categories.iterrows():
        product_name = row['product_name']
        category_id = row['id']
        is_primary = (row['prev'] == 0) or pd.isna(row['prev'])
        
        # 确定一级分类和二级分类
        if is_primary:
            primary_category = row['category']
            secondary_category = ""
        else:
            parent_row = df_categories[df_categories['id'] == row['prev']]
            if not parent_row.empty:
                primary_category = parent_row.iloc[0]['category']
                secondary_category = row['category']
            else:
                primary_category = "未知"
                secondary_category = row['category']
        
        # 获取该分类的供应商
        suppliers = df_suppliers[df_suppliers['category_id'] == category_id]
        
        if suppliers.empty:
            all_category_data.append({
                'product_name': product_name,
                'primary_category': primary_category,
                'secondary_category': secondary_category,
                'suppliers': []
            })
        else:
            all_category_data.append({
                'product_name': product_name,
                'primary_category': primary_category,
                'secondary_category': secondary_category,
                'suppliers': suppliers.to_dict('records')
            })
    
    # 按产品、一级分类、二级分类排序
    all_category_data.sort(key=lambda x: (x['product_name'], x['primary_category'], x['secondary_category']))
    
    # 添加到工作表
    for category_item in all_category_data:
        product_name = category_item['product_name']
        primary_category = category_item['primary_category']
        secondary_category = category_item['secondary_category']
        suppliers = category_item['suppliers']
        
        if not suppliers:
            ws.append([
                product_name, 
                primary_category, 
                secondary_category, 
                "", "", "", "", "", "", ""
            ])
        else:
            for supplier in suppliers:
                status_text = STATUS_MAPPING.get(supplier['status'], f"未知状态({supplier['status']})")
                
                ws.append([
                    product_name,
                    primary_category,
                    secondary_category,
                    supplier['supplier_name'],
                    supplier['region'],
                    supplier['website'],
                    status_text,
                    supplier['phone'],
                    supplier['email'],
                    supplier['certifications']
                ])

def generate_category_coverage_sheet(df_suppliers, df_categories, wb):
    """生成分类覆盖度分析工作表"""
    ws = wb.create_sheet("分类覆盖度分析")
    ws.append(["产品", "分类", "总供应商数", "正常供应商数", "覆盖度级别", "覆盖率"])
    
    coverage_data = []
    for (product, category, category_id), group in df_suppliers.groupby(['product_name', 'category', 'category_id']):
        total_suppliers = len(group)
        normal_suppliers = len(group[group['status'].isin([0, 99])])
        
        coverage_rate = (normal_suppliers / total_suppliers) * 100 if total_suppliers > 0 else 0
        
        if normal_suppliers == 0:
            coverage_level = "无覆盖"
        elif normal_suppliers <= 2:
            coverage_level = "低覆盖"
        elif normal_suppliers <= 5:
            coverage_level = "中等覆盖"
        else:
            coverage_level = "高覆盖"
        
        coverage_data.append([
            product, 
            category, 
            total_suppliers, 
            normal_suppliers, 
            coverage_level, 
            f"{coverage_rate:.2f}%"
        ])
    
    coverage_data.sort(key=lambda x: x[3], reverse=True)
    
    for row in coverage_data:
        ws.append(row)

def generate_material_category_relation_sheet(df_categories, wb):
    """生成物料与分类关系工作表"""
    ws = wb.create_sheet("物料分类关系")
    ws.append(["产品", "一级分类", "二级分类", "物料特征描述"])
    
    # 获取所有一级分类
    primary_categories = df_categories[(df_categories['prev'] == 0) | (df_categories['prev'].isna())]
    
    relation_data = []
    
    # 遍历一级分类
    for _, primary in primary_categories.iterrows():
        primary_id = primary['id']
        product_name = primary['product_name']
        
        # 添加一级分类
        relation_data.append([
            product_name,
            primary['category'],
            "",  # 没有二级分类
            primary['feature'] if not pd.isna(primary['feature']) else ""
        ])
        
        # 找出此一级分类下的所有二级分类
        secondary_categories = df_categories[df_categories['prev'] == primary_id]
        
        # 添加二级分类
        for _, secondary in secondary_categories.iterrows():
            relation_data.append([
                product_name,
                primary['category'],
                secondary['category'],
                secondary['feature'] if not pd.isna(secondary['feature']) else ""
            ])
    
    # 按产品和分类排序
    relation_data.sort(key=lambda x: (x[0], x[1], x[2]))
    
    # 添加到工作表
    for row in relation_data:
        ws.append(row)

def create_summary_sheet(df_suppliers, df_categories, wb):
    """创建汇总工作表"""
    ws = wb.create_sheet("汇总", 0)  # 将此工作表放在第一位
    
    ws.append(["供应商分析报表汇总"])
    ws.append(["生成日期", datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
    ws.append([""])
    
    ws.append(["指标", "数值"])
    
    # 基本统计
    ws.append(["供应商总数", len(df_suppliers)])
    ws.append(["产品总数", len(df_suppliers['product_name'].unique())])
    ws.append(["分类总数", len(df_suppliers['category'].unique())])
    ws.append(["地区总数", len(df_suppliers['region'].unique())])
    ws.append([""])
    
    # 分类层级统计
    primary_categories = df_categories[(df_categories['prev'] == 0) | (df_categories['prev'].isna())]
    secondary_categories = df_categories[~((df_categories['prev'] == 0) | (df_categories['prev'].isna()))]
    
    ws.append(["分类层级统计", ""])
    ws.append(["一级分类数量", len(primary_categories)])
    ws.append(["二级分类数量", len(secondary_categories)])
    ws.append(["平均每个一级分类下的二级分类数", 
               f"{len(secondary_categories) / len(primary_categories):.2f}" if len(primary_categories) > 0 else "N/A"])
    ws.append([""])
    
    # 按状态统计供应商数量
    ws.append(["供应商状态统计", ""])
    for status_code, status_name in STATUS_MAPPING.items():
        count = len(df_suppliers[df_suppliers['status'] == status_code])
        if count > 0:
            ws.append([status_name, count])

def generate_excel_report():
    """生成Excel报表"""
    logger.info("开始生成供应商分析Excel报表...")
    
    # 获取数据
    df_suppliers, df_categories, df_components = get_data_from_db()
    if df_suppliers is None:
        logger.error("无法获取数据，报表生成失败")
        return None
    
    # 创建工作簿
    wb = openpyxl.Workbook()
    
    # 删除默认工作表
    if "Sheet" in wb.sheetnames:
        wb.remove(wb["Sheet"])
    
    # 生成各工作表
    generate_supplier_overview_sheet(df_suppliers, df_categories, wb)
    generate_supplier_status_sheet(df_suppliers, wb)
    generate_category_coverage_sheet(df_suppliers, df_categories, wb)
    generate_material_category_relation_sheet(df_categories, wb)
    
    # 创建汇总工作表
    create_summary_sheet(df_suppliers, df_categories, wb)
    
    # 应用样式
    apply_excel_styling(wb)
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output")
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"suppliers_analysis_{timestamp}.xlsx")
    
    # 保存Excel
    wb.save(output_file)
    logger.info(f"报表已生成: {output_file}")
    
    return output_file

def search_single_category(category_id, delay=10):
    """搜索指定分类ID的供应商并保存到md文件"""
    logger.info(f"开始搜索指定分类ID: {category_id}")
    
    # 获取分类信息
    category_info = get_category_by_id(category_id)
    if not category_info:
        logger.error(f"未找到分类ID: {category_id}")
        return False
    
    # 搜索供应商
    result = search_suppliers_for_category(category_info, delay)
    
    if result > 0:
        logger.info(f"✅ 分类 '{category_info['category']}' (ID: {category_id}) 搜索完成")
        return True
    else:
        logger.error(f"❌ 分类 '{category_info['category']}' (ID: {category_id}) 搜索失败")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于产品分类特征的供应商搜索脚本')
    parser.add_argument('--product_id', type=int, help='指定产品ID，只搜索该产品的分类')
    parser.add_argument('--category_id', type=int, help='指定分类ID，只搜索该分类的供应商')
    parser.add_argument('--start_from', type=int, help='从指定分类ID开始搜索')
    parser.add_argument('--max_categories', type=int, help='最大处理分类数量')
    parser.add_argument('--clear', action='store_true', help='清除现有供应商数据')
    parser.add_argument('--excel_only', action='store_true', help='只生成Excel报表，不搜索供应商')
    parser.add_argument('--delay', type=int, default=10, help='API调用间隔秒数，默认10秒')
    
    args = parser.parse_args()
    
    logger.info("🚀 启动基于产品分类特征的供应商搜索系统")
    logger.info(f"参数: product_id={args.product_id}, category_id={args.category_id}, start_from={args.start_from}, max_categories={args.max_categories}")
    logger.info(f"清除现有数据: {args.clear}, 只生成Excel: {args.excel_only}, API延迟: {args.delay}秒")
    
    # 测试Claude API连接
    if not args.excel_only:
        logger.info("🔗 测试Claude API连接...")
        try:
            test_response = call_claude_with_search("Hello, this is a test message.")
            logger.info(f"✅ Claude API连接成功，响应类型: {type(test_response)}")
            logger.debug(f"测试响应: {test_response}")
        except Exception as e:
            logger.error(f"❌ Claude API连接失败: {e}")
            logger.error("请检查claude_utils.py中的API配置")
            return
    
    # 只生成Excel报表
    if args.excel_only:
        logger.info("📊 只生成Excel报表模式")
        output_file = generate_excel_report()
        if output_file:
            logger.info(f"✅ Excel报表生成完成: {output_file}")
        else:
            logger.error("❌ Excel报表生成失败")
        return
    
    # 清除现有数据
    if args.clear:
        logger.info("🗑️ 清除现有供应商数据...")
        if clear_existing_suppliers(args.product_id):
            logger.info("✅ 现有数据清除完成")
        else:
            logger.error("❌ 数据清除失败")
            return
    
    # 搜索指定分类ID的供应商
    if args.category_id:
        logger.info(f"🎯 单个分类搜索模式 - 分类ID: {args.category_id}")
        try:
            success = search_single_category(args.category_id, args.delay)
            if success:
                logger.info("✅ 指定分类供应商搜索完成！")
            else:
                logger.error("❌ 指定分类供应商搜索失败！")
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断了搜索过程")
        except Exception as e:
            logger.error(f"❌ 单个分类搜索出错: {e}")
        return
    
    # 搜索供应商
    try:
        search_all_categories(args.start_from, args.max_categories, args.delay)
        logger.info("✅ 供应商搜索完成！所有结果已保存为md文件")
            
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断了搜索过程")
    except Exception as e:
        logger.error(f"❌ 主流程执行出错: {e}")

if __name__ == "__main__":
    main() 