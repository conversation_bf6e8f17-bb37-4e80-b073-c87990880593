#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业供应链数据分析Excel生成脚本
按照一级分类+组件+标签+供应商的维度聚合数据，生成专业分析报表

状态值定义:
0: 验证正常
1: 验证pending
2: 网站无效
3: 网站被标为黑名单
4: 产品不能做
5: 报价过高
99: 验证通过
"""

import os
import mysql.connector
import pandas as pd
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.chart import BarChart, PieChart, Reference
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('supply_chain_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': "rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com",
    'port': 3306,
    'user': "yj_app",
    'password': "4iLe5fifhMqOo9Ne",
    'database': "procurement_system",
    'charset': 'utf8mb4'
}

# 状态值定义
STATUS_MAPPING = {
    0: "验证正常",
    1: "验证pending",
    2: "网站无效",
    3: "网站被标为黑名单",
    4: "产品不能做",
    5: "报价过高",
    99: "验证通过"
}

class SupplyChainAnalyzer:
    """供应链数据分析器"""
    
    def __init__(self):
        self.connection = None
        self.output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output")
        os.makedirs(self.output_dir, exist_ok=True)
    
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            self.connection = mysql.connector.connect(**DB_CONFIG)
            logger.info("数据库连接成功")
            return True
        except mysql.connector.Error as e:
            logger.error(f"数据库连接错误: {e}")
            return False
    
    def get_suppliers_with_categories(self):
        """获取供应商和分类数据"""
        logger.info("获取供应商和分类数据...")
        
        query = """
        SELECT s.*, pc.category, pc.product_id, pc.feature, pc.prev, 
               cp.name as product_name, cp.model as product_model,
               parent.category as parent_category
        FROM search_suppliers s
        JOIN product_categories pc ON s.category_id = pc.id
        JOIN client_product cp ON pc.product_id = cp.id
        LEFT JOIN product_categories parent ON pc.prev = parent.id
        WHERE (s.is_deleted = 0 OR s.is_deleted IS NULL)
        ORDER BY cp.name, parent.category, pc.category, s.supplier_name
        """
        
        try:
            df = pd.read_sql(query, self.connection)
            logger.info(f"获取到 {len(df)} 条供应商分类数据")
            return df
        except Exception as e:
            logger.error(f"获取供应商数据失败: {e}")
            return pd.DataFrame()
    
    def get_components_with_categories(self):
        """获取组件和分类数据"""
        logger.info("获取组件和分类数据...")
        
        query = """
        SELECT c.*, ca.category_id, pc.category as pc_category, pc.prev,
               parent.category as parent_category, cp.name as product_name,
               pc.feature as category_feature
        FROM component c
        JOIN component_analysis ca ON c.id = ca.component_id
        JOIN product_categories pc ON ca.category_id = pc.id
        JOIN client_product cp ON pc.product_id = cp.id
        LEFT JOIN product_categories parent ON pc.prev = parent.id
        WHERE ca.is_success = 1
        ORDER BY cp.name, parent.category, pc.category, c.name
        """
        
        try:
            df = pd.read_sql(query, self.connection)
            logger.info(f"获取到 {len(df)} 条组件分类数据")
            return df
        except Exception as e:
            logger.error(f"获取组件数据失败: {e}")
            return pd.DataFrame()
    
    def get_category_hierarchy(self):
        """获取分类层级结构"""
        logger.info("获取分类层级结构...")
        
        query = """
        SELECT pc.*, cp.name as product_name,
               parent.category as parent_category
        FROM product_categories pc
        JOIN client_product cp ON pc.product_id = cp.id
        LEFT JOIN product_categories parent ON pc.prev = parent.id
        ORDER BY cp.name, parent.category, pc.category
        """
        
        try:
            df = pd.read_sql(query, self.connection)
            logger.info(f"获取到 {len(df)} 条分类数据")
            return df
        except Exception as e:
            logger.error(f"获取分类数据失败: {e}")
            return pd.DataFrame()
    
    def apply_excel_styling(self, wb):
        """为Excel工作簿应用专业样式"""
        logger.info("应用Excel样式...")
        
        # 定义样式
        header_font = Font(bold=True, size=12, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        centered = Alignment(horizontal="center", vertical="center", wrap_text=True)
        border = Border(
            left=Side(style='thin'), 
            right=Side(style='thin'), 
            top=Side(style='thin'), 
            bottom=Side(style='thin')
        )
        
        # 遍历每个工作表应用样式
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            
            # 设置表头样式
            if ws.max_row > 0:
                for cell in ws[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = centered
                    cell.border = border
            
            # 设置数据单元格样式
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(vertical="center", wrap_text=True)
                    cell.border = border
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2) * 1.2
                ws.column_dimensions[column_letter].width = min(adjusted_width, 50)
    
    def generate_supply_chain_overview_sheet(self, df_suppliers, df_components, wb):
        """生成供应链总览工作表"""
        logger.info("生成供应链总览工作表...")
        
        ws = wb.create_sheet("供应链数据总览")
        ws.append(["产品", "一级分类", "二级分类", "组件名称", "组件规格", "标签", 
                   "原始供应商", "推荐供应商", "供应商类型", "地区", "网站", "状态", "联系方式"])
        
        # 处理组件数据
        aggregated_data = []
        
        for _, component in df_components.iterrows():
            # 确定分类层级
            if component['prev'] == 0 or pd.isna(component['prev']):
                primary_category = component['pc_category']
                secondary_category = ""
            else:
                primary_category = component['parent_category']
                secondary_category = component['pc_category']
            
            # 添加原始供应商数据
            if pd.notna(component.get('original_supplier')) and component['original_supplier'].strip():
                aggregated_data.append([
                    component.get('product_name', ''),
                    primary_category,
                    secondary_category,
                    component.get('name', ''),
                    component.get('spec', ''),
                    component.get('tag', ''),
                    component['original_supplier'],
                    '',  # 推荐供应商为空
                    '原始供应商',
                    '',  # 地区
                    '',  # 网站
                    '未验证',  # 状态
                    ''   # 联系方式
                ])
            
            # 添加推荐供应商数据
            category_id = component['category_id']
            matching_suppliers = df_suppliers[df_suppliers['category_id'] == category_id]
            
            for _, supplier in matching_suppliers.iterrows():
                status_text = STATUS_MAPPING.get(supplier['status'], f"未知状态({supplier['status']})")
                contact_info = f"{supplier.get('phone', '')} {supplier.get('email', '')}".strip()
                
                aggregated_data.append([
                    component.get('product_name', ''),
                    primary_category,
                    secondary_category,
                    component.get('name', ''),
                    component.get('spec', ''),
                    component.get('tag', ''),
                    '',  # 原始供应商为空
                    supplier['supplier_name'],
                    '推荐供应商',
                    supplier.get('region', ''),
                    supplier.get('website', ''),
                    status_text,
                    contact_info
                ])
        
        # 排序并添加到工作表
        aggregated_data.sort(key=lambda x: (x[0], x[1], x[2], x[3]))
        
        for row in aggregated_data:
            ws.append(row)
        
        logger.info(f"供应链总览: 添加了 {len(aggregated_data)} 条记录")
    
    def generate_category_supplier_mapping_sheet(self, df_suppliers, wb):
        """生成分类供应商对应关系工作表"""
        logger.info("生成分类供应商对应关系工作表...")
        
        ws = wb.create_sheet("分类供应商对应")
        ws.append(["产品", "一级分类", "二级分类", "供应商名称", "地区", "网站", 
                   "电话", "邮箱", "地址", "认证", "状态", "匹配原因"])
        
        mapping_data = []
        
        for _, supplier in df_suppliers.iterrows():
            # 确定分类层级
            if supplier['prev'] == 0 or pd.isna(supplier['prev']):
                primary_category = supplier['category']
                secondary_category = ""
            else:
                primary_category = supplier['parent_category']
                secondary_category = supplier['category']
            
            status_text = STATUS_MAPPING.get(supplier['status'], f"未知状态({supplier['status']})")
            
            mapping_data.append([
                supplier.get('product_name', ''),
                primary_category,
                secondary_category,
                supplier['supplier_name'],
                supplier.get('region', ''),
                supplier.get('website', ''),
                supplier.get('phone', ''),
                supplier.get('email', ''),
                supplier.get('address', ''),
                supplier.get('certifications', ''),
                status_text,
                supplier.get('matching_reason', '')
            ])
        
        # 排序并添加到工作表
        mapping_data.sort(key=lambda x: (x[0], x[1], x[2], x[3]))
        
        for row in mapping_data:
            ws.append(row)
        
        logger.info(f"分类供应商对应: 添加了 {len(mapping_data)} 条记录")
    
    def generate_primary_category_supplier_sheet(self, df_suppliers, wb):
        """生成一级分类供应商汇总工作表"""
        logger.info("生成一级分类供应商汇总工作表...")
        
        ws = wb.create_sheet("一级分类供应商汇总")
        ws.append(["产品", "一级分类", "供应商名称", "涉及二级分类", "地区", "网站", 
                   "联系方式", "状态", "备注"])
        
        # 按一级分类汇总供应商
        primary_mapping = {}
        
        for _, supplier in df_suppliers.iterrows():
            # 确定一级分类
            if supplier['prev'] == 0 or pd.isna(supplier['prev']):
                primary_category = supplier['category']
                secondary_category = ""
            else:
                primary_category = supplier['parent_category']
                secondary_category = supplier['category']
            
            key = (supplier.get('product_name', ''), primary_category, supplier['supplier_name'])
            
            if key not in primary_mapping:
                status_text = STATUS_MAPPING.get(supplier['status'], f"未知状态({supplier['status']})")
                contact_info = f"{supplier.get('phone', '')} {supplier.get('email', '')}".strip()
                
                primary_mapping[key] = {
                    'product_name': supplier.get('product_name', ''),
                    'primary_category': primary_category,
                    'supplier_name': supplier['supplier_name'],
                    'secondary_categories': [secondary_category] if secondary_category else [],
                    'region': supplier.get('region', ''),
                    'website': supplier.get('website', ''),
                    'contact_info': contact_info,
                    'status': status_text,
                    'notes': supplier.get('notes', '')
                }
            else:
                # 添加二级分类
                if secondary_category and secondary_category not in primary_mapping[key]['secondary_categories']:
                    primary_mapping[key]['secondary_categories'].append(secondary_category)
        
        # 转换为列表并排序
        summary_data = []
        for key, data in primary_mapping.items():
            secondary_list = '; '.join(data['secondary_categories']) if data['secondary_categories'] else '直接归属'
            
            summary_data.append([
                data['product_name'],
                data['primary_category'],
                data['supplier_name'],
                secondary_list,
                data['region'],
                data['website'],
                data['contact_info'],
                data['status'],
                data['notes']
            ])
        
        # 排序并添加到工作表
        summary_data.sort(key=lambda x: (x[0], x[1], x[2]))
        
        for row in summary_data:
            ws.append(row)
        
        logger.info(f"一级分类供应商汇总: 添加了 {len(summary_data)} 条记录")
    
    def generate_component_analysis_sheet(self, df_components, df_suppliers, wb):
        """生成组件供应商分析工作表"""
        logger.info("生成组件供应商分析工作表...")
        
        ws = wb.create_sheet("组件供应商分析")
        ws.append(["产品", "一级分类", "二级分类", "组件名称", "组件标签", "原始供应商", 
                   "推荐供应商数量", "正常供应商数量", "供应商覆盖度", "风险等级"])
        
        analysis_data = []
        
        for _, component in df_components.iterrows():
            # 确定分类层级
            if component['prev'] == 0 or pd.isna(component['prev']):
                primary_category = component['pc_category']
                secondary_category = ""
            else:
                primary_category = component['parent_category']
                secondary_category = component['pc_category']
            
            # 获取该组件分类的推荐供应商
            category_id = component['category_id']
            matching_suppliers = df_suppliers[df_suppliers['category_id'] == category_id]
            
            total_suppliers = len(matching_suppliers)
            normal_suppliers = len(matching_suppliers[matching_suppliers['status'].isin([0, 99])])
            
            # 计算覆盖度和风险等级
            if total_suppliers == 0:
                coverage = "无供应商"
                risk_level = "高风险"
            elif normal_suppliers == 0:
                coverage = "无有效供应商"
                risk_level = "高风险"
            elif normal_suppliers == 1:
                coverage = "单一供应商"
                risk_level = "中等风险"
            elif normal_suppliers <= 3:
                coverage = "低覆盖"
                risk_level = "中等风险"
            elif normal_suppliers <= 5:
                coverage = "中等覆盖"
                risk_level = "低风险"
            else:
                coverage = "高覆盖"
                risk_level = "极低风险"
            
            analysis_data.append([
                component.get('product_name', ''),
                primary_category,
                secondary_category,
                component.get('name', ''),
                component.get('tag', ''),
                component.get('original_supplier', ''),
                total_suppliers,
                normal_suppliers,
                coverage,
                risk_level
            ])
        
        # 排序并添加到工作表
        analysis_data.sort(key=lambda x: (x[0], x[1], x[2], x[3]))
        
        for row in analysis_data:
            ws.append(row)
        
        logger.info(f"组件供应商分析: 添加了 {len(analysis_data)} 条记录")
    
    def generate_supplier_status_analysis_sheet(self, df_suppliers, wb):
        """生成供应商状态分析工作表"""
        logger.info("生成供应商状态分析工作表...")
        
        ws = wb.create_sheet("供应商状态分析")
        ws.append(["状态代码", "状态描述", "供应商数量", "占比"])
        
        # 按状态分组并计数
        status_counts = df_suppliers['status'].value_counts().reset_index()
        status_counts.columns = ['status', 'count']
        total = status_counts['count'].sum()
        
        # 添加状态描述并计算占比
        for _, row in status_counts.iterrows():
            status_code = row['status']
            status_desc = STATUS_MAPPING.get(status_code, f"未知状态({status_code})")
            count = row['count']
            percentage = (count / total) * 100
            
            ws.append([status_code, status_desc, count, f"{percentage:.2f}%"])
        
        # 添加图表
        chart = PieChart()
        chart.title = "供应商状态分布"
        chart.style = 10
        
        data = Reference(ws, min_col=3, min_row=1, max_row=len(status_counts)+1)
        labels = Reference(ws, min_col=2, min_row=2, max_row=len(status_counts)+1)
        chart.add_data(data, titles_from_data=True)
        chart.set_categories(labels)
        
        ws.add_chart(chart, "F2")
        
        logger.info(f"供应商状态分析: 添加了 {len(status_counts)} 种状态统计")
    
    def generate_category_coverage_analysis_sheet(self, df_suppliers, df_categories, wb):
        """生成分类覆盖度分析工作表"""
        logger.info("生成分类覆盖度分析工作表...")
        
        ws = wb.create_sheet("分类覆盖度分析")
        ws.append(["产品", "一级分类", "二级分类", "总供应商数", "正常供应商数", 
                   "覆盖度级别", "风险评估", "建议"])
        
        coverage_data = []
        
        # 按分类分组分析
        for category_id in df_categories['id'].unique():
            category_info = df_categories[df_categories['id'] == category_id].iloc[0]
            suppliers = df_suppliers[df_suppliers['category_id'] == category_id]
            
            # 确定分类层级
            if category_info['prev'] == 0 or pd.isna(category_info['prev']):
                primary_category = category_info['category']
                secondary_category = ""
            else:
                parent_category = df_categories[df_categories['id'] == category_info['prev']]
                if not parent_category.empty:
                    primary_category = parent_category.iloc[0]['category']
                    secondary_category = category_info['category']
                else:
                    primary_category = "未知"
                    secondary_category = category_info['category']
            
            total_suppliers = len(suppliers)
            normal_suppliers = len(suppliers[suppliers['status'].isin([0, 99])])
            
            # 覆盖度评估
            if total_suppliers == 0:
                coverage_level = "无覆盖"
                risk_assessment = "极高风险"
                suggestion = "急需寻找供应商"
            elif normal_suppliers == 0:
                coverage_level = "无有效覆盖"
                risk_assessment = "高风险"
                suggestion = "需要验证现有供应商或寻找新供应商"
            elif normal_suppliers == 1:
                coverage_level = "单一供应商"
                risk_assessment = "中高风险"
                suggestion = "建议增加备用供应商"
            elif normal_suppliers <= 3:
                coverage_level = "低覆盖"
                risk_assessment = "中等风险"
                suggestion = "建议增加1-2个供应商"
            elif normal_suppliers <= 5:
                coverage_level = "中等覆盖"
                risk_assessment = "低风险"
                suggestion = "供应商数量适中"
            else:
                coverage_level = "高覆盖"
                risk_assessment = "极低风险"
                suggestion = "供应商充足，可优化筛选"
            
            coverage_data.append([
                category_info.get('product_name', ''),
                primary_category,
                secondary_category,
                total_suppliers,
                normal_suppliers,
                coverage_level,
                risk_assessment,
                suggestion
            ])
        
        # 按风险等级排序（高风险在前）
        risk_order = {"极高风险": 0, "高风险": 1, "中高风险": 2, "中等风险": 3, "低风险": 4, "极低风险": 5}
        coverage_data.sort(key=lambda x: risk_order.get(x[6], 9))
        
        for row in coverage_data:
            ws.append(row)
        
        logger.info(f"分类覆盖度分析: 添加了 {len(coverage_data)} 条分类分析")
    
    def create_summary_sheet(self, df_suppliers, df_components, df_categories, wb):
        """创建汇总工作表"""
        logger.info("创建汇总工作表...")
        
        ws = wb.create_sheet("汇总统计", 0)  # 将此工作表放在第一位
        
        ws.append(["供应链数据分析报表汇总"])
        ws.append(["生成日期", datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
        ws.append([""])
        
        ws.append(["指标", "数值"])
        
        # 基本统计
        ws.append(["供应商总数", len(df_suppliers)])
        ws.append(["组件总数", len(df_components)])
        ws.append(["分类总数", len(df_categories)])
        ws.append(["产品总数", len(df_suppliers['product_name'].unique())])
        ws.append([""])
        
        # 供应商状态统计
        ws.append(["供应商状态统计", ""])
        for status_code, status_name in STATUS_MAPPING.items():
            count = len(df_suppliers[df_suppliers['status'] == status_code])
            if count > 0:
                ws.append([status_name, count])
        
        # 组件标签统计
        ws.append([""])
        ws.append(["组件标签统计", ""])
        tag_counts = df_components['tag'].value_counts()
        for tag, count in tag_counts.items():
            ws.append([tag if pd.notna(tag) else "未分类", count])
        
        # 覆盖度统计
        ws.append([""])
        ws.append(["供应商覆盖情况", ""])
        
        no_suppliers = 0
        low_coverage = 0
        good_coverage = 0
        
        for category_id in df_categories['id'].unique():
            suppliers = df_suppliers[df_suppliers['category_id'] == category_id]
            normal_suppliers = len(suppliers[suppliers['status'].isin([0, 99])])
            
            if normal_suppliers == 0:
                no_suppliers += 1
            elif normal_suppliers <= 2:
                low_coverage += 1
            else:
                good_coverage += 1
        
        total_categories = len(df_categories)
        ws.append(["无有效供应商分类", no_suppliers])
        ws.append(["低覆盖分类", low_coverage])
        ws.append(["良好覆盖分类", good_coverage])
        ws.append(["分类覆盖率", f"{((total_categories - no_suppliers) / total_categories * 100):.2f}%" if total_categories > 0 else "0%"])
        
        logger.info("汇总统计表创建完成")
    
    def generate_report(self):
        """生成完整的供应链分析报表"""
        logger.info("开始生成供应链分析报表...")
        
        try:
            # 连接数据库
            if not self.get_db_connection():
                return False
            
            # 获取数据
            df_suppliers = self.get_suppliers_with_categories()
            df_components = self.get_components_with_categories()
            df_categories = self.get_category_hierarchy()
            
            if df_suppliers.empty:
                logger.error("无法获取供应商数据")
                return False
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            
            # 删除默认工作表
            if "Sheet" in wb.sheetnames:
                wb.remove(wb["Sheet"])

            print(f"df_suppliers: {df_suppliers}")
            print(f"df_components: {df_components}")
            # print(f"df_categories: {df_categories}")
            
            # 生成各个分析工作表
            self.generate_supply_chain_overview_sheet(df_suppliers, df_components, wb)
            self.generate_category_supplier_mapping_sheet(df_suppliers, wb)
            self.generate_primary_category_supplier_sheet(df_suppliers, wb)
            self.generate_component_analysis_sheet(df_components, df_suppliers, wb)
            self.generate_supplier_status_analysis_sheet(df_suppliers, wb)
            self.generate_category_coverage_analysis_sheet(df_suppliers, df_categories, wb)
            
            # 创建汇总工作表
            self.create_summary_sheet(df_suppliers, df_components, df_categories, wb)
            
            # 应用样式
            self.apply_excel_styling(wb)
            
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"专业供应链分析报表_{timestamp}.xlsx")
            
            # 保存Excel
            wb.save(output_file)
            logger.info(f"报表已生成: {output_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"报表生成失败: {e}")
            return False
        finally:
            if self.connection and self.connection.is_connected():
                self.connection.close()
                logger.info("数据库连接已关闭")

def main():
    """主函数"""
    analyzer = SupplyChainAnalyzer()
    success = analyzer.generate_report()
    
    if success:
        print("✅ 专业供应链分析报表生成成功！")
    else:
        print("❌ 报表生成失败，请检查日志。")

if __name__ == "__main__":
    main() 