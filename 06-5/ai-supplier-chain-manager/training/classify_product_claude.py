#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
产品分类框架 - Claude版本：
1. 根据产品ID查询关联的组件ID
2. 根据组件ID查询分析结果
3. 使用Claude模型对产品进行大类分类（不对单个组件进行分类）
4. 将分类结果存储到product_categories表中
"""

import os
import logging
import json
import time
import mysql.connector
import requests
from datetime import datetime
import argparse
from typing import List, Dict, Any, Optional, Tuple
from dotenv import load_dotenv
from claude_utils import call_claude, call_openai

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logging.getLogger().setLevel(logging.DEBUG)

# 定义常量
BATCH_SIZE = 30  # 批处理大小，用于处理大量组件时避免超出上下文限制

# 数据库配置
DB_CONFIG = {
    'host': 'rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com',
    'port': 3306,
    'database': 'procurement_system',
    'user': 'yj_app',
    'password': '4iLe5fifhMqOo9Ne',
    'charset': 'utf8mb4',
    'autocommit': False,
    'pool_name': 'classification_pool',
    'pool_size': 5,
    'pool_reset_session': True,
    'connect_timeout': 30,
    'sql_mode': 'TRADITIONAL'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            # 设置会话超时和自动重连
            cursor = connection.cursor()
            cursor.execute("SET SESSION wait_timeout = 28800")  # 8小时
            cursor.execute("SET SESSION interactive_timeout = 28800")
            cursor.close()
            logger.debug("数据库连接成功")
            return connection
    except mysql.connector.Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def post_claude(prompt, model='claude_sonnet'):
    """调用Claude转发接口 - 使用claude_utils转发服务"""
    logger.info("post_claude - 使用转发服务")
    
    max_retries = 3
    retry_delay = 20  # 20秒
    
    for attempt in range(max_retries):
        try:
            # 使用claude_utils中的转发服务
            logger.debug(f"调用claude_utils.call_claude，提示词长度: {len(prompt)} 字符 (尝试 {attempt + 1}/{max_retries})")
            response = call_claude(prompt)
            logger.debug(f"Claude转发服务响应类型: {type(response)}")
            logger.debug(f"Claude转发服务响应键: {list(response.keys()) if isinstance(response, dict) else 'not a dict'}")
            logger.debug(f"Claude转发服务完整响应: {response}")
            
            if "error" in response:
                error_msg = response['error']
                logger.error(f"Claude转发服务错误: {error_msg}")
                
                # 检查是否是500错误
                if "500" in str(error_msg) and attempt < max_retries - 1:
                    logger.warning(f"遇到500错误，等待{retry_delay}秒后重试 (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    continue
                else:
                    raise Exception(f"Claude转发服务错误: {error_msg}")
            
            # 从转发服务响应中提取结果
            content = response.get('content', '')
            if not content and 'choices' in response:
                # 兼容不同的响应格式
                content = response['choices'][0]['message']['content']
                logger.debug("使用choices格式提取内容")
            elif not content and 'response' in response:
                content = response['response']
                logger.debug("使用response格式提取内容")
            elif not content and 'answer' in response:
                content = response['answer']
                logger.debug("使用answer格式提取内容")
            
            logger.debug(f"提取到的完整内容: {content}")
            
            logger.info(f"Claude API调用成功，返回内容类型: {type(content)}")
            return content
            
        except Exception as e:
            error_str = str(e)
            logger.error(f"调用Claude转发服务失败: {e}")
            
            # 检查是否是500错误且还有重试机会
            if "500" in error_str and attempt < max_retries - 1:
                logger.warning(f"遇到500错误，等待{retry_delay}秒后重试 (尝试 {attempt + 1}/{max_retries})")
                time.sleep(retry_delay)
                continue
            else:
                # 最后一次尝试失败或非500错误，抛出异常
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                raise Exception(f"调用Claude转发服务失败: {e}")
    
    # 如果所有重试都失败了
    logger.error(f"所有{max_retries}次重试都失败了")
    raise Exception(f"调用Claude转发服务失败: 所有{max_retries}次重试都失败")

# 产品分类Claude系统提示
CATEGORY_SYSTEM_PROMPT = """你是一位专业的医疗器械工程和采购领域专家，擅长对组件进行分类和归类。

任务：
1. 分析提供的组件数据
2. 创建或维护合理的两级分类体系
3. 为每个分类提取详细的共性特征
4. 确保分类体系符合医疗器械行业标准和采购需求

你将收到一个详细的分类指南文档，该文档包含关于分类方法、特征提取和输出格式的具体要求。请严格按照该指南操作，确保分类结果的一致性和实用性。

分类的目的是帮助采购和供应链管理，使相似特性的组件可以被统一考虑和处理。

请始终返回有效的JSON格式结果。"""

def get_components_by_product_id(product_id: int) -> List[Dict[str, Any]]:
    """
    根据产品ID获取相关的组件信息

    Args:
        product_id: 产品ID

    Returns:
        组件信息列表
    """
    conn = get_db_connection()
    if not conn:
        return []

    try:
        cursor = conn.cursor(dictionary=True)
        
        # 查询产品关联的组件
        query = """
        SELECT c.*, pcr.quantity, pcr.is_key_component 
        FROM component c
        JOIN product_component_relation pcr ON c.id = pcr.component_id
        WHERE pcr.product_id = %s
        LIMIT 330
        """
        cursor.execute(query, (product_id,))
        components = cursor.fetchall()
        
        logger.info(f"找到产品ID={product_id}关联的{len(components)}个组件")
        return components
    except Exception as e:
        logger.error(f"查询组件时出错: {e}")
        return []
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def get_product_info(product_id: int) -> Dict[str, Any]:
    """
    获取产品信息

    Args:
        product_id: 产品ID

    Returns:
        产品信息字典
    """
    conn = get_db_connection()
    if not conn:
        return {}

    try:
        cursor = conn.cursor(dictionary=True)
        
        # 查询产品信息
        query = """
        SELECT id, name, model, category, description, lifecycle_stage, annual_volume, priority_level
        FROM client_product
        WHERE id = %s
        """
        cursor.execute(query, (product_id,))
        product = cursor.fetchone()
        
        if product:
            logger.info(f"找到产品: {product['name']} (ID: {product_id})")
        else:
            logger.warning(f"未找到产品ID: {product_id}")
            
        return product or {}
    except Exception as e:
        logger.error(f"查询产品信息时出错: {e}")
        return {}
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def get_component_analysis(component_ids: List[int]) -> Dict[int, str]:
    """
    获取组件的分析数据

    Args:
        component_ids: 组件ID列表

    Returns:
        组件ID到分析数据的映射
    """
    if not component_ids:
        return {}

    conn = get_db_connection()
    if not conn:
        return {}

    try:
        cursor = conn.cursor(dictionary=True)
        
        # 构建IN子句的参数
        placeholders = ', '.join(['%s'] * len(component_ids))
        
        # 查询组件分析
        query = f"""
        SELECT component_id, analysis 
        FROM component_analysis 
        WHERE component_id IN ({placeholders}) AND is_success = 1
        """
        cursor.execute(query, tuple(component_ids))
        analysis_records = cursor.fetchall()
        
        # 创建组件ID到分析数据的映射
        analysis_map = {record['component_id']: record['analysis'] for record in analysis_records}
        
        logger.info(f"找到{len(analysis_map)}个组件的分析数据")
        return analysis_map
    except Exception as e:
        logger.error(f"查询组件分析时出错: {e}")
        return {}
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def classify_product_with_claude(product_info: Dict[str, Any], components: List[Dict[str, Any]], analysis_map: Dict[int, str]) -> Dict[str, Any]:
    """
    使用Claude对组件进行分类，生成两级分类体系

    Args:
        product_info: 产品信息
        components: 组件信息列表
        analysis_map: 组件ID到分析数据的映射

    Returns:
        分类结果字典
    """
    logger.debug(f"进入classify_product_with_claude函数，组件数量: {len(components)}，分析数据数量: {len(analysis_map)}")
    
    # 如果没有产品信息、组件或分析数据，直接返回空结果
    if not product_info or not components:
        logger.warning("没有足够的产品或组件数据进行分类")
        return {}

    try:
        logger.debug(f"准备Claude分类查询，产品信息: {json.dumps(product_info, ensure_ascii=False)[:100]}...")
        
        # 打印分析数据的键
        if analysis_map:
            logger.debug(f"分析数据包含的组件ID: {list(analysis_map.keys())}")
            logger.debug(f"第一个分析数据示例 (ID {list(analysis_map.keys())[0]}): {list(analysis_map.values())[0][:200]}...")
        else:
            logger.warning("分析数据为空，无法进行分类")
            
        analysis_text = list(analysis_map.values())
        logger.debug(f"总共有 {len(analysis_text)} 条分析文本")
        
        # 添加批处理逻辑，避免超出上下文限制
        classification_result = None
        
        # 如果分析文本数量小于等于批处理大小，直接处理
        if len(analysis_text) <= BATCH_SIZE:
            logger.debug(f"分析文本数量 {len(analysis_text)} 小于批处理大小 {BATCH_SIZE}，直接处理")
            
            # 准备Claude查询文本
            query_text = f"""
            请对以下医疗产品的组件进行分类，生成合理的两级分类体系。

            产品信息：
            - 产品ID：{product_info.get('id', '未知')}
            - 产品名称：{product_info.get('name', '未知')}
            - 产品描述：{product_info.get('description', '未知')}
            - 产品类别：{product_info.get('category', '未知')}
            
            组件分析数据：
            {analysis_text}
            
            # 医疗器械组件分类系统指南

            ## 任务定义
            你是一个高级分类系统，负责为医疗器械组件创建或维护科学的分类体系。你的目标是通过分析组件数据，识别共性特征，并建立或完善一个两级分类架构。

            ## 分类方法论

            ### 数据驱动原则
            - 所有分类决策必须基于组件的实际特性数据
            - 使用归纳法从组件特征中提取分类，而非演绎法强行套用预设框架
            - 每次决策都需要明确数据支持，避免主观假设

            ### 分类层级设计
            - 一级分类：基于最核心的物理属性、功能、或制造工艺
            - 二级分类：基于更具体的特性变体或应用场景
            - 每个分类必须有清晰、可操作的边界定义

            ### 分类数量控制
            - **一级分类数量：严格控制在4-6个以内**
            - **合并原则：优先按材料特性和制造工艺进行高层次抽象**
            - **避免过度细分：相似材料或工艺的组件应合并到同一个一级分类**
            - **示例合并逻辑：**
              * 所有金属加工件（铝合金、钢材、不锈钢）→ 精密金属组件
              * 所有塑料制品（ABS、PEEK、POM等）→ 工程塑料组件  
              * 所有电子设备（PCB、传感器、控制器）→ 电子控制系统
              * 所有机械传动（电机、导轨、泵类）→ 机电传动系统

            ### 演进式分类逻辑
            - 分类系统应随数据动态演进，而非静态不变
            - 新数据可能引入新分类，也可能修正现有分类边界
            - 评估每个组件与现有分类的匹配度（高/中/低）

            ## 共性特征提取

            ### 特征提取范围
            - **物理特性**: 材料成分、物理性质、几何特征、表面特性
            - **功能特性**: 主要功能、次要功能、性能参数、兼容性
            - **工艺特性**: 制造方法、精度要求、表面处理、质量标准
            - **应用特性**: 使用环境、接口方式、安全要求、生命周期

            ### 特征描述标准
            - 特征描述必须具体、精确、可量化
            - 使用专业术语，避免笼统表述
            - 每个类别的共性特征应有明显差异性
            - 描述应包含与供应链管理相关的关键信息

            ### 必须包含的关键特征信息
            每个组件类别的共性特征描述必须包含以下核心信息（如适用）：
            1. **材质规格**：具体材料类型、牌号、等级（如"316L不锈钢"、"医用级PEEK"）
            2. **尺寸特征**：典型尺寸范围、公差等级（如"直径1-5mm，公差±0.01mm"）
            3. **几何形状**：基本形状特征、结构特点（如"主体为圆柱形，带有凸缘接口"）
            4. **表面处理**：表面处理工艺、表面质量（如"电解抛光"、"阳极氧化"）
            5. **用途描述**：在医疗设备中的具体应用场景（如"用于血液透析设备的流量控制"）
            6. **性能参数**：关键性能指标、工作条件（如"耐压10MPa"、"使用温度5-40℃"）

            ### 特征合并方法
            - 识别不同组件间的共同特性，保留频繁出现的特征
            - 合并相似特征的描述，避免冗余
            - 保留特殊、关键或独特的特征说明
            - 确保最终特征描述全面但简洁

            ## 分类实施标准

            ### 分类匹配评估
            - **高匹配度(>80%)**: 组件特征与分类描述高度一致
            - **中匹配度(60%-80%)**: 主要特征一致，部分特征有差异
            - **低匹配度(<60%)**: 多数特征不匹配，可能需要新分类

            ### 分类质量控制
            - 每个分类至少包含3个以上特征描述
            - 避免过度分类（太多小类）和不足分类（类别太宽泛）
            - 确保分类之间有足够区分度
            - 优先考虑对采购决策有实际影响的特征
            
            请按照以下Markdown格式返回结果：
            
            # 产品分类结果
            
            **产品ID:** {product_info.get('id', 'unknown')}
            **产品名称:** {product_info.get('name', '未知产品')}
            **分类日期:** {datetime.now().strftime('%Y-%m-%d')}
            **组件总数:** {len(components)}
            
            ## 一级分类
            
            ### 1. 一级分类名称
            **共同特征:** 基于物理属性、功能或制造工艺的核心特征描述...
            
            #### 二级分类
            - **二级分类名称1:** 基于具体特性变体或应用场景的特征描述...
            - **二级分类名称2:** 避免过度细分，确保分类有明显区分度...
            
            ### 2. 另一个一级分类名称
            **共同特征:** 必须包含材质规格、尺寸特征、几何形状、表面处理、用途描述、性能参数等...
            
            #### 二级分类
            - **二级分类名称1:** 基于数据驱动的分类决策...
            
            重要提示：
            - 只输出Markdown格式，不要输出JSON或其他格式
            - 严格遵循分类系统指南的科学原则
            - 使用归纳法从组件特征中提取分类
            - 避免过度分类，确保分类间有足够区分度
            - 特征描述必须具体、精确、可量化
            """
            
            # 合并system prompt到用户消息中
            full_prompt = f"{CATEGORY_SYSTEM_PROMPT}\n\n{query_text}"
            
            # 调用Claude转发接口
            result_text = post_claude(full_prompt, 'claude_sonnet')
            logger.info("🤖 ===== CLAUDE原始响应开始 =====")
            logger.info(f"响应类型: {type(result_text)}")
            if isinstance(result_text, dict):
                logger.info(f"响应键: {list(result_text.keys())}")
                # 如果是字典，显示完整结构但简化内容显示
                if 'content' in result_text and isinstance(result_text['content'], list):
                    content = result_text['content'][0] if result_text['content'] else {}
                    if 'text' in content:
                        text_content = content['text']
                        logger.info(f"文本内容长度: {len(text_content)} 字符")
                        logger.info(f"完整文本内容: {text_content}")
                    else:
                        logger.info(f"完整响应: {result_text}")
                else:
                    logger.info(f"完整响应: {result_text}")
            else:
                logger.info(f"响应长度: {len(str(result_text))} 字符")
                logger.info(f"完整响应内容: {result_text}")
            logger.info("🤖 ===== CLAUDE原始响应结束 =====")
            
            # 提取Markdown内容
            markdown_content = extract_markdown_from_response(result_text)
            logger.info(f"📝 提取到Markdown内容，长度: {len(markdown_content)} 字符")
            
            if markdown_content.strip():
                # 最后转换为JSON格式用于存储
                classification_result = convert_markdown_to_json(markdown_content, product_info, components)
                logger.info("✅ Markdown转JSON成功" if classification_result else "❌ Markdown转JSON失败")
            else:
                logger.warning("Markdown内容为空")
                classification_result = {}
            
        else:
            # 分批处理逻辑，让模型自身演进分类框架
            logger.debug(f"分析文本数量 {len(analysis_text)} 大于批处理大小 {BATCH_SIZE}，进行分批处理")
            
            # 第一批：使用代表性样本建立初始分类框架
            first_batch = analysis_text[:BATCH_SIZE]
            logger.debug(f"处理第一批 {len(first_batch)} 条分析文本以建立初始分类框架")
            
            # 准备第一批的查询文本
            query_text = f"""
            请对以下医疗产品的组件进行分类，生成合理的两级分类体系。

            产品信息：
            - 产品ID：{product_info.get('id', '未知')}
            - 产品名称：{product_info.get('name', '未知')}
            - 产品描述：{product_info.get('description', '未知')}
            - 产品类别：{product_info.get('category', '未知')}
            
            组件分析数据（第一批）：
            {first_batch}
            
            # 医疗器械组件分类系统指南

            ## 任务定义
            你是一个高级分类系统，负责为医疗器械组件创建或维护科学的分类体系。你的目标是通过分析组件数据，识别共性特征，并建立或完善一个两级分类架构。

            ## 分类方法论

            ### 数据驱动原则
            - 所有分类决策必须基于组件的实际特性数据
            - 使用归纳法从组件特征中提取分类，而非演绎法强行套用预设框架
            - 每次决策都需要明确数据支持，避免主观假设

            ### 分类层级设计
            - 一级分类：基于最核心的物理属性、功能、或制造工艺
            - 二级分类：基于更具体的特性变体或应用场景
            - 每个分类必须有清晰、可操作的边界定义

            ### 分类数量控制
            - **一级分类数量：严格控制在3-4个以内**
            - **合并原则：优先按材料特性和制造工艺进行高层次抽象**
            - **避免过度细分：相似材料或工艺的组件应合并到同一个一级分类**
            - **强制合并规则：如果当前框架已有4个一级分类，新组件必须归入现有分类，不能新增**

            ### 分类匹配评估
            - **高匹配度(>80%)**: 组件特征与分类描述高度一致
            - **中匹配度(60%-80%)**: 主要特征一致，部分特征有差异
            - **低匹配度(<60%)**: 多数特征不匹配，可能需要新分类

            ### 分类逻辑
            - 分类系统应随数据动态演进，而非静态不变
            - 新数据可能引入新分类，也可能修正现有分类边界
            - 评估每个组件与现有分类的匹配度（高/中/低）

            ## 共性特征提取

            ### 特征提取范围
            - **物理特性**: 材料成分、物理性质、几何特征、表面特性
            - **功能特性**: 主要功能、次要功能、性能参数、兼容性
            - **工艺特性**: 制造方法、精度要求、表面处理、质量标准
            - **应用特性**: 使用环境、接口方式、安全要求、生命周期

            ### 特征描述标准
            - 特征描述必须具体、精确、可量化
            - 使用专业术语，避免笼统表述
            - 每个类别的共性特征应有明显差异性
            - 描述应包含与供应链管理相关的关键信息

            ### 必须包含的关键特征信息
            每个组件类别的共性特征描述必须包含以下核心信息（如适用）：
            1. **材质规格**：具体材料类型、牌号、等级（如"316L不锈钢"、"医用级PEEK"）
            2. **尺寸特征**：典型尺寸范围、公差等级（如"直径1-5mm，公差±0.01mm"）
            3. **几何形状**：基本形状特征、结构特点（如"主体为圆柱形，带有凸缘接口"）
            4. **表面处理**：表面处理工艺、表面质量（如"电解抛光"、"阳极氧化"）
            5. **用途描述**：在医疗设备中的具体应用场景（如"用于血液透析设备的流量控制"）
            6. **性能参数**：关键性能指标、工作条件（如"耐压10MPa"、"使用温度5-40℃"）

            ### 特征合并方法
            - 识别不同组件间的共同特性，保留频繁出现的特征
            - 合并相似特征的描述，避免冗余
            - 保留特殊、关键或独特的特征说明
            - 确保最终特征描述全面但简洁

            ## 分类实施标准

            ### 分类匹配评估
            - **高匹配度(>80%)**: 组件特征与分类描述高度一致
            - **中匹配度(60%-80%)**: 主要特征一致，部分特征有差异
            - **低匹配度(<60%)**: 多数特征不匹配，可能需要新分类

            ### 分类质量控制
            - 每个分类至少包含3个以上特征描述
            - 避免过度分类（太多小类）和不足分类（类别太宽泛）
            - 确保分类之间有足够区分度
            - 优先考虑对采购决策有实际影响的特征
            
            请按照以下Markdown格式返回结果：
            
            # 产品分类结果
            
            **产品ID:** {product_info.get('id', 'unknown')}
            **产品名称:** {product_info.get('name', '未知产品')}
            **分类日期:** {datetime.now().strftime('%Y-%m-%d')}
            
            ## 一级分类
            
            ### 1. 一级分类名称
            **共同特征:** 基于物理属性、功能或制造工艺的核心特征描述...
            
            #### 二级分类
            - **二级分类名称1:** 基于具体特性变体或应用场景的特征描述...
            - **二级分类名称2:** 避免过度细分，确保分类有明显区分度...
            
            ### 2. 另一个一级分类名称
            **共同特征:** 必须包含材质规格、尺寸特征、几何形状、表面处理、用途描述、性能参数等...
            
            #### 二级分类
            - **二级分类名称1:** 基于数据驱动的分类决策...
            
            重要提示：
            - 只输出Markdown格式，不要输出JSON或其他格式
            - 严格遵循分类系统指南的科学原则
            - 使用归纳法从组件特征中提取分类
            - 避免过度分类，确保分类间有足够区分度
            - 特征描述必须具体、精确、可量化
            """
            
            # 合并system prompt到用户消息中
            full_prompt = f"{CATEGORY_SYSTEM_PROMPT}\n\n{query_text}"
            
            # 调用Claude转发接口
            result_text = post_claude(full_prompt, 'claude_sonnet')
            logger.info("🤖 ===== CLAUDE原始响应开始 =====")
            logger.info(f"响应类型: {type(result_text)}")
            if isinstance(result_text, dict):
                logger.info(f"响应键: {list(result_text.keys())}")
                # 如果是字典，显示完整结构但简化内容显示
                if 'content' in result_text and isinstance(result_text['content'], list):
                    content = result_text['content'][0] if result_text['content'] else {}
                    if 'text' in content:
                        text_content = content['text']
                        logger.info(f"文本内容长度: {len(text_content)} 字符")
                        logger.info(f"完整文本内容: {text_content}")
                    else:
                        logger.info(f"完整响应: {result_text}")
                else:
                    logger.info(f"完整响应: {result_text}")
            else:
                logger.info(f"响应长度: {len(str(result_text))} 字符")
                logger.info(f"完整响应内容: {result_text}")
            logger.info("🤖 ===== CLAUDE原始响应结束 =====")
            
            # 提取初始Markdown内容
            current_markdown = extract_markdown_from_response(result_text)
            logger.info(f"📝 提取到初始Markdown内容，长度: {len(current_markdown)} 字符")
            
            if not current_markdown.strip():
                logger.error("初始分类框架生成失败")
                return {}
            
            logger.info("✅ 初始分类框架创建成功")
            
            # 计算剩余批次
            remaining_batches = [analysis_text[i:i + BATCH_SIZE] for i in range(BATCH_SIZE, len(analysis_text), BATCH_SIZE)]
            logger.debug(f"将剩余 {len(analysis_text) - BATCH_SIZE} 条分析文本分为 {len(remaining_batches)} 个批次")
            
            # 逐批处理，Claude自身演进分类框架
            for i, batch in enumerate(remaining_batches):
                logger.info(f"处理第 {i+2} 批，包含 {len(batch)} 条分析文本")
                
                # 准备Claude演进查询文本
                query_text = f"""
                我有一个医疗组件分类框架和一批新的组件数据。请基于新数据演进和改进这个分类框架。
                
                现有分类框架（Markdown格式）:
                ```
                {current_markdown}
                ```
                
                新的组件数据:
                {batch}
                
                # 医疗器械组件分类系统指南

                ## 演进原则
                请严格遵循以下科学分类原则来演进分类框架：

                ### 数据驱动原则
                - 所有分类决策必须基于新组件的实际特性数据
                - 使用归纳法从组件特征中提取分类，而非演绎法强行套用预设框架
                - 每次决策都需要明确数据支持，避免主观假设

                ### 分类层级设计
                - 一级分类：基于最核心的物理属性、功能、或制造工艺
                - 二级分类：基于更具体的特性变体或应用场景
                - 每个分类必须有清晰、可操作的边界定义

                ### 分类数量控制
                - **一级分类数量：严格控制在3-4个以内**
                - **合并原则：优先按材料特性和制造工艺进行高层次抽象**
                - **避免过度细分：相似材料或工艺的组件应合并到同一个一级分类**
                - **强制合并规则：如果当前框架已有4个一级分类，新组件必须归入现有分类，不能新增**

                ### 分类匹配评估
                - **高匹配度(>80%)**: 组件特征与分类描述高度一致
                - **中匹配度(60%-80%)**: 主要特征一致，部分特征有差异
                - **低匹配度(<60%)**: 多数特征不匹配，可能需要新分类

                ### 分类逻辑
                - 分类系统应随数据动态演进，而非静态不变
                - 新数据可能引入新分类，也可能修正现有分类边界
                - 评估每个组件与现有分类的匹配度（高/中/低）

                ## 演进任务
                请分析这批新数据，并更新分类框架。你需要：
                1. 评估每个组件与现有分类的匹配程度（高/中/低匹配度）
                2. 改进每个分类的共性特征描述，使其更全面准确
                3. **严格控制一级分类数量在3-4个以内，优先将新组件归入现有分类**
                4. 如果发现现有框架无法覆盖的组件类型（低匹配度），考虑扩展现有分类而非新增分类
                5. 保持分类层级结构和分类逻辑的一致性
                6. 特征描述控制在200字以内，重点突出关键信息
                
                请返回完整的更新后分类框架，使用相同的Markdown格式。
                
                重要提示：
                - 只输出Markdown格式，不要输出JSON或其他格式
                - 保持与初始框架相同的Markdown结构
                - 确保分类体系的完整性和一致性
                """
                
                # 添加延迟，避免API调用过快
                time.sleep(3)
                
                logger.info("向Claude发送分类框架演进请求...")
                try:
                    # 合并system prompt到用户消息中
                    full_prompt = f"{CATEGORY_SYSTEM_PROMPT}\n\n{query_text}"
                    
                    # 调用Claude转发接口
                    result_text = post_claude(full_prompt, 'claude_sonnet')
                    logger.info("🤖 ===== CLAUDE原始响应开始 =====")
                    logger.info(f"响应类型: {type(result_text)}")
                    if isinstance(result_text, dict):
                        logger.info(f"响应键: {list(result_text.keys())}")
                        # 如果是字典，显示完整结构但简化内容显示
                        if 'content' in result_text and isinstance(result_text['content'], list):
                            content = result_text['content'][0] if result_text['content'] else {}
                            if 'text' in content:
                                text_content = content['text']
                                logger.info(f"文本内容长度: {len(text_content)} 字符")
                                logger.info(f"完整文本内容: {text_content}")
                            else:
                                logger.info(f"完整响应: {result_text}")
                        else:
                            logger.info(f"完整响应: {result_text}")
                    else:
                        logger.info(f"响应长度: {len(str(result_text))} 字符")
                        logger.info(f"完整响应内容: {result_text}")
                    logger.info("🤖 ===== CLAUDE原始响应结束 =====")
                    
                except Exception as e:
                    logger.error(f"调用Claude API失败: {e}")
                    continue  # 失败时保持现有框架，处理下一批
                
                if not result_text:
                    logger.error("Claude返回了空的响应内容")
                    continue
                    
                # 提取演进后的Markdown内容
                updated_markdown = extract_markdown_from_response(result_text)
                logger.info(f"📝 提取到演进后Markdown内容，长度: {len(updated_markdown)} 字符")
                
                if updated_markdown.strip():
                    current_markdown = updated_markdown
                    logger.info(f"✅ 分类框架已成功演进")
                else:
                    logger.warning(f"第 {i+2} 批响应无效，保持原有框架")
            
            # 所有批次处理完成后，将最终的Markdown转换为JSON
            if current_markdown.strip():
                classification_result = convert_markdown_to_json(current_markdown, product_info, components)
                logger.info("✅ 最终Markdown转JSON成功" if classification_result else "❌ 最终Markdown转JSON失败")
            else:
                logger.error("最终Markdown内容为空")
                classification_result = {}
            
        return classification_result or {}
    
    except Exception as e:
        logger.error(f"组件分类失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        
        # 返回空结果
        return {}

def extract_markdown_from_response(response_text) -> str:
    """
    从Claude响应中提取Markdown部分
    
    Args:
        response_text: Claude响应文本或字典
        
    Returns:
        提取的Markdown文本
    """
    # 如果输入已经是字典，需要从嵌套结构中提取内容
    if isinstance(response_text, dict):
        logger.info("📥 开始从字典响应中提取Markdown")
        
        # 检查Claude转发服务的响应格式
        if 'content' in response_text:
            content = response_text['content']
            
            if isinstance(content, list) and len(content) > 0:
                first_item = content[0]
                
                if isinstance(first_item, dict) and 'text' in first_item:
                    text_content = first_item['text']
                    logger.info(f"提取到text内容，长度: {len(text_content)} 字符")
                    return text_content.strip()
                    
        # 如果没有content字段，直接转换整个字典
        return str(response_text)
    
    # 如果输入是字符串，直接返回
    return str(response_text).strip()

def convert_markdown_to_json(markdown_content, product_info, components) -> Dict[str, Any]:
    """
    将Markdown内容转换为JSON格式
    
    Args:
        markdown_content: 提取的Markdown文本
        product_info: 产品信息
        components: 组件信息列表

    Returns:
        解析后的JSON字典
    """
    # 使用GPT将Markdown转换为JSON
    try:
        logger.info("📝 开始使用GPT将Markdown转换为JSON")
        
        # 构建转换系统提示词
        system_prompt = """你是一个专业的数据格式转换助手。你需要将Markdown格式的产品分类结果精确转换为JSON格式。

重要要求：
1. 必须返回完整有效的JSON格式，不要添加任何解释文字
2. common_features必须是字符串格式，不要使用对象
3. 保持原始分类结构和内容的完整性
4. 精确统计分类数量信息
5. 确保JSON格式完全正确，不要有语法错误"""

        # 构建转换提示词
        conversion_prompt = f"""
        请将以下Markdown格式的产品分类结果转换为标准JSON格式。

        Markdown内容：
        ```
        {markdown_content}
        ```

        请严格按照以下JSON格式输出：
        
        {{
            "product_classification": {{
                "product_id": "产品ID",
                "product_name": "产品名称",
                "classification_date": "分类日期",
                "primary_categories": [
                    {{
                        "name": "一级分类名称",
                        "common_features": "共同特征描述（字符串格式）",
                        "secondary_categories": [
                            {{
                                "name": "二级分类名称",
                                "common_features": "特征描述（字符串格式）"
                            }}
                        ]
                    }}
                ],
                "classification_summary": {{
                    "total_components": {len(components)},
                    "primary_categories_count": "实际一级分类数量",
                    "secondary_categories_count": "实际二级分类数量"
                }}
            }}
        }}

        请只返回JSON内容，不要添加任何其他文字或解释。
        """
        
        # 调用GPT进行转换
        conversion_result = call_openai(conversion_prompt, system_prompt)
        logger.info("🤖 ===== GPT格式转换响应开始 =====")
        logger.info(f"转换响应类型: {type(conversion_result)}")
        
        if "error" in conversion_result:
            logger.error(f"GPT转换错误: {conversion_result['error']}")
            return {}
            
        # 提取JSON文本
        json_text = conversion_result.get('content', '')
        logger.info(f"转换后JSON文本长度: {len(json_text)} 字符")
        logger.info("🤖 ===== GPT格式转换响应结束 =====")
        
        # 解析JSON
        json_result = parse_converted_json(json_text)
        
        if json_result:
            logger.info("✅ GPT Markdown转JSON成功")
            return json_result
        else:
            logger.error("❌ GPT JSON解析失败")
            return {}
            
    except Exception as e:
        logger.error(f"❌ GPT Markdown转JSON过程失败: {e}")
        return {}

def parse_converted_json(json_text) -> Dict[str, Any]:
    """
    解析转换后的JSON文本
    
    Args:
        json_text: JSON文本或已解析的字典
        
    Returns:
        解析后的字典对象
    """
    # 如果输入已经是字典，直接返回
    if isinstance(json_text, dict):
        logger.info("✅ 输入已经是字典，直接返回")
        return json_text
    
    # 从文本中提取JSON部分
    text_content = str(json_text)
    
    # 尝试提取JSON代码块
    if "```json" in text_content:
        try:
            json_start = text_content.find("```json") + 7
            json_end = text_content.find("```", json_start)
            
            if json_end == -1:
                logger.warning("⚠️ 找不到JSON结束标记")
                extracted = text_content[json_start:].strip()
            else:
                extracted = text_content[json_start:json_end].strip()
                
            logger.info(f"提取的JSON长度: {len(extracted)} 字符")
            
            # 检测JSON是否被截断
            if not extracted.rstrip().endswith('}'):
                logger.error("🚨 检测到JSON被截断！缺少结束花括号")
                logger.error(f"截断位置在第 {len(extracted.split(chr(10)))} 行")
                logger.error(f"JSON内容长度: {len(extracted)} 字符")
                logger.error(f"最后50个字符: {extracted[-50:]}")
            
            text_content = extracted
        except Exception as e:
            logger.error(f"提取JSON时出错: {e}")
    elif "{" in text_content:
        # 尝试直接提取JSON部分
        start_idx = text_content.find("{")
        end_idx = text_content.rfind("}") + 1
        if start_idx != -1 and end_idx > start_idx:
            text_content = text_content[start_idx:end_idx]
    
    # 解析JSON
    try:
        logger.info(f"📝 尝试解析JSON字符串，长度: {len(text_content)} 字符")
        
        result = json.loads(text_content)
        logger.info("✅ JSON解析成功")
        
        # 检查关键字段
        if isinstance(result, dict):
            if "product_classification" in result:
                classification = result["product_classification"]
                if "primary_categories" in classification:
                    logger.info(f"✅ 找到完整分类结构，包含 {len(classification['primary_categories'])} 个一级分类")
            elif "primary_categories" in result:
                logger.info(f"✅ 找到分类结构，包含 {len(result['primary_categories'])} 个一级分类")
        
        return result
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON解析失败: {e}")
        try:
            # 尝试修复常见JSON错误
            fixed_json = text_content.replace("'", '"').replace("False", "false").replace("True", "true")
            result = json.loads(fixed_json)
            logger.info("✅ JSON修复成功")
            return result
        except Exception as e2:
            logger.error(f"❌ JSON修复也失败: {e2}")
            return {}

def classify_product_main(product_id: int) -> Tuple[bool, Dict[str, Any]]:
    """
    对指定产品进行分类的主函数

    Args:
        product_id: 产品ID

    Returns:
        成功标志和分类结果
    """
    logger.debug(f"开始执行产品ID={product_id}的分类流程")
    
    # 1. 获取产品信息
    logger.debug(f"获取产品ID={product_id}的基本信息...")
    product_info = get_product_info(product_id)
    if not product_info:
        logger.warning(f"产品ID={product_id}不存在")
        return False, {}
    
    logger.debug(f"产品信息: {json.dumps(product_info, ensure_ascii=False)}")
    
    # 2. 获取产品的组件
    logger.debug(f"获取产品ID={product_id}关联的组件...")
    components = get_components_by_product_id(product_id)
    if not components:
        logger.warning(f"产品ID={product_id}没有关联的组件")
        return False, {}
    
    logger.debug(f"找到{len(components)}个关联组件, 前3个组件ID: {[comp['id'] for comp in components[:3]]}")
    
    # 3. 获取组件分析数据
    logger.debug(f"获取组件的分析数据...")
    component_ids = [comp['id'] for comp in components]
    logger.debug(f"组件ID列表: {component_ids}")
    
    analysis_map = get_component_analysis(component_ids)
    logger.debug(f"获取到{len(analysis_map)}个组件的分析数据, 覆盖率: {(len(analysis_map) / len(component_ids)) * 100:.2f}%")
    
    # 打印有分析数据和无分析数据的组件ID
    with_analysis = list(analysis_map.keys())
    without_analysis = [cid for cid in component_ids if cid not in analysis_map]
    logger.debug(f"有分析数据的组件ID: {with_analysis[:10]}...（共{len(with_analysis)}个）")
    if without_analysis:
        logger.debug(f"无分析数据的组件ID: {without_analysis[:10]}...（共{len(without_analysis)}个）")
    
    # 4. 使用Claude对组件进行分类
    logger.debug(f"开始调用Claude进行分类...")
    classification_result = classify_product_with_claude(product_info, components, analysis_map)
    
    # 检查分类结果
    if not classification_result:
        logger.warning(f"产品ID={product_id}的组件分类结果为空")
        return False, {}
        
    logger.debug(f"分类结果类型: {type(classification_result)}, 键: {list(classification_result.keys())}")
    
    # 5. 存储分类结果
    logger.debug(f"开始存储分类结果...")
    success = store_category_result(product_id, classification_result)
    
    if success:
        logger.debug(f"分类结果存储成功")
    else:
        logger.warning(f"分类结果存储失败")
        
    return success, classification_result

def store_category_result(product_id: int, classification_result: Dict[str, Any]) -> bool:
    """
    将分类结果存储到product_categories表
    
    数据库设计说明：
    - product_categories表采用扁平化设计，所有分类都存在同一张表
    - prev字段表示层级关系：0表示一级分类，其他值表示父分类ID
    - 写入策略：先删除旧数据，再批量插入新数据，保证数据一致性

    Args:
        product_id: 产品ID
        classification_result: Claude分类结果

    Returns:
        是否成功
    """
    logger.debug(f"开始存储产品ID={product_id}的分类结果")
    logger.debug(f"分类结果类型: {type(classification_result)}, 键: {list(classification_result.keys()) if isinstance(classification_result, dict) else 'not a dict'}")
    
    # 验证分类结果
    if not classification_result:
        logger.warning("分类结果为空，无法存储")
        return False
        
    if not isinstance(classification_result, dict):
        logger.warning(f"分类结果不是字典类型，而是 {type(classification_result)}")
        return False
    
    # 检查新的JSON格式，提取实际的分类数据
    if "product_classification" in classification_result:
        classification_data = classification_result["product_classification"]
        logger.debug("检测到新格式的分类结果 (product_classification)")
    else:
        classification_data = classification_result
        logger.debug("使用旧格式的分类结果")
        
    # 检查字段
    if "primary_categories" not in classification_data and "primary_category" not in classification_data:
        logger.warning("分类结果缺少'primary_categories'或'primary_category'字段")
        logger.debug(f"分类结果中的字段: {list(classification_data.keys())}")
        return False
    
    # 连接数据库
    logger.debug("连接数据库...")
    conn = get_db_connection()
    if not conn:
        logger.error("无法连接到数据库")
        return False

    try:
        cursor = conn.cursor()
        
        # 开始事务
        conn.start_transaction()
        logger.debug("开始数据库事务")
        
        # 删除该产品的现有分类
        logger.debug(f"删除产品ID={product_id}的现有分类...")
        delete_query = "DELETE FROM product_categories WHERE product_id = %s"
        cursor.execute(delete_query, (product_id,))
        deleted_count = cursor.rowcount
        logger.debug(f"删除了{deleted_count}条现有分类记录")
        
        # 插入新的分类记录
        insert_query = """
        INSERT INTO product_categories 
        (product_id, category, prev, feature) 
        VALUES (%s, %s, %s, %s)
        """
        
        inserted_count = 0
        
        # 检查分类类型并执行对应的插入逻辑
        if "primary_categories" in classification_data:
            # 两级分类情况
            logger.debug("使用两级分类结构存储...")
            primary_categories = classification_data["primary_categories"]
            logger.debug(f"一级分类数量: {len(primary_categories)}")
            
            # 存储一级分类名称到ID的映射
            primary_category_ids = {}
            
            # 第一步：插入所有一级分类记录
            logger.debug("第一步：插入一级分类...")
            for i, primary_category in enumerate(primary_categories):
                primary_name = primary_category.get("name", "")
                primary_features = primary_category.get("common_features", "")
                
                # 处理common_features字段：如果是字典，转换为字符串
                if isinstance(primary_features, dict):
                    # 将字典转换为格式化的字符串
                    features_text = ""
                    for key, value in primary_features.items():
                        features_text += f"{key}: {value}\n"
                    primary_features = features_text.strip()
                    logger.debug(f"一级分类'{primary_name}'的特征从字典转换为字符串")
                
                if not primary_name:
                    logger.warning(f"第{i+1}个一级分类名称为空，跳过")
                    continue
                    
                # 一级分类的prev值为0
                cursor.execute(insert_query, (product_id, primary_name, 0, primary_features))
                # 获取自动生成的ID
                primary_id = cursor.lastrowid
                # 存储映射关系，用于二级分类
                primary_category_ids[primary_name] = primary_id
                inserted_count += 1
                logger.debug(f"插入一级分类: {primary_name} (ID: {primary_id})")
            
            # 第二步：插入所有二级分类记录
            logger.debug("第二步：插入二级分类...")
            for primary_category in primary_categories:
                primary_name = primary_category.get("name", "")
                if not primary_name or primary_name not in primary_category_ids:
                    continue
                    
                primary_id = primary_category_ids[primary_name]
                
                # 添加该一级分类下的二级分类记录
                secondary_categories = primary_category.get("secondary_categories", [])
                for j, secondary_category in enumerate(secondary_categories):
                    secondary_name = secondary_category.get("name", "")
                    secondary_features = secondary_category.get("common_features", "")
                    
                    # 处理common_features字段：如果是字典，转换为字符串
                    if isinstance(secondary_features, dict):
                        # 将字典转换为格式化的字符串
                        features_text = ""
                        for key, value in secondary_features.items():
                            features_text += f"{key}: {value}\n"
                        secondary_features = features_text.strip()
                        logger.debug(f"二级分类'{secondary_name}'的特征从字典转换为字符串")
                    
                    if not secondary_name:
                        logger.warning(f"一级分类'{primary_name}'下第{j+1}个二级分类名称为空，跳过")
                        continue
                        
                    # 二级分类的prev值为其所属一级分类的ID
                    cursor.execute(insert_query, (product_id, secondary_name, primary_id, secondary_features))
                    inserted_count += 1
                    logger.debug(f"插入二级分类: {secondary_name} (父分类: {primary_name}, prev: {primary_id})")
            
            # 提交事务
            conn.commit()
            logger.info(f"事务提交成功，共插入{inserted_count}条分类记录")
            
            # 验证插入结果
            cursor.execute("SELECT COUNT(*) FROM product_categories WHERE product_id = %s", (product_id,))
            final_count = cursor.fetchone()[0]
            logger.info(f"验证：数据库中产品ID={product_id}共有{final_count}条分类记录")
            
            if final_count != inserted_count:
                logger.warning(f"插入数量({inserted_count})与数据库记录数({final_count})不匹配")
            
            return True
            
        elif "primary_category" in classification_data:
            # 单一分类情况
            logger.debug("使用单一分类结构存储...")
            primary_category = classification_data["primary_category"]
            primary_features = classification_data.get("common_features", "")
            
            if not primary_category:
                logger.error("单一分类名称为空")
                conn.rollback()
                return False
                
            logger.debug(f"分类名称: {primary_category}")
            
            # 一级分类的prev值为0
            cursor.execute(insert_query, (product_id, primary_category, 0, primary_features))
            inserted_count = 1
            
            # 提交事务
            conn.commit()
            logger.info(f"成功存储产品ID={product_id}的单一分类结果: {primary_category}")
            return True
        else:
            logger.error("分类结果中既没有'primary_categories'也没有'primary_category'字段")
            conn.rollback()
            return False
        
    except mysql.connector.Error as e:
        logger.error(f"数据库操作错误: {e}")
        if conn:
            conn.rollback()
            logger.debug("事务已回滚")
        return False
    except Exception as e:
        logger.error(f"存储分类结果时出错: {e}")
        import traceback
        logger.debug(f"详细错误: {traceback.format_exc()}")
        if conn:
            conn.rollback()
            logger.debug("事务已回滚")
        return False
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()
            logger.debug("数据库连接已关闭")

def test_classify_product(product_id: int) -> None:
    """
    测试产品分类功能，不存储到数据库，只打印结果
    
    Args:
        product_id: 产品ID
    """
    logger.info(f"🧪 开始测试产品ID={product_id}的分类功能")
    
    # 1. 获取产品信息
    logger.info(f"📋 获取产品ID={product_id}的基本信息...")
    product_info = get_product_info(product_id)
    if not product_info:
        print(f"❌ 产品ID={product_id}不存在")
        return
    
    print(f"✅ 产品信息:")
    print(f"   - 产品名称: {product_info.get('name', '未知')}")
    print(f"   - 产品型号: {product_info.get('model', '未知')}")
    print(f"   - 产品描述: {product_info.get('description', '未知')}")
    print(f"   - 生命周期: {product_info.get('lifecycle_stage', '未知')}")
    print(f"   - 优先级: {product_info.get('priority_level', '未知')}")
    
    # 2. 获取产品的组件
    logger.info(f"🔧 获取产品ID={product_id}关联的组件...")
    components = get_components_by_product_id(product_id)
    if not components:
        print(f"❌ 产品ID={product_id}没有关联的组件")
        return
    
    print(f"✅ 找到 {len(components)} 个关联组件:")
    for i, comp in enumerate(components[:5]):  # 只显示前5个
        print(f"   {i+1}. {comp.get('name', '未知名称')} (ID: {comp['id']})")
    if len(components) > 5:
        print(f"   ... 还有 {len(components) - 5} 个组件")
    
    # 3. 获取组件分析数据
    logger.info(f"📊 获取组件的分析数据...")
    component_ids = [comp['id'] for comp in components]
    analysis_map = get_component_analysis(component_ids)
    
    coverage_rate = (len(analysis_map) / len(component_ids)) * 100 if component_ids else 0
    print(f"✅ 分析数据覆盖率: {coverage_rate:.1f}% ({len(analysis_map)}/{len(component_ids)})")
    
    if not analysis_map:
        print("❌ 没有可用的组件分析数据，无法进行分类")
        return
    
    # 显示分析数据示例
    print(f"📄 分析数据示例:")
    for i, (comp_id, analysis) in enumerate(list(analysis_map.items())[:2]):
        print(f"   组件ID {comp_id}: {analysis[:100]}...")
        if i >= 1:  # 只显示前2个
            break
    
    # 4. 使用Claude进行分类测试
    print(f"\n🤖 开始调用Claude进行分类...")
    try:
        classification_result = classify_product_with_claude(product_info, components, analysis_map)
        
        if not classification_result:
            print("❌ Claude分类失败，返回空结果")
            return
        
        print(f"✅ Claude分类成功!")
        
        # 打印分类结果
        print(f"\n📊 分类结果:")
        print("=" * 60)
        
        # 适配新的JSON格式
        classification_data = classification_result
        if "product_classification" in classification_result:
            classification_data = classification_result["product_classification"]
            print(f"🆔 产品ID: {classification_data.get('product_id', '未知')}")
            print(f"📅 分类日期: {classification_data.get('classification_date', '未知')}")
        
        if "primary_categories" in classification_data:
            # 两级分类结构
            primary_categories = classification_data["primary_categories"]
            print(f"🎯 创建了 {len(primary_categories)} 个一级分类:")
            
            for i, primary_cat in enumerate(primary_categories):
                print(f"\n📁 一级分类 {i+1}: {primary_cat.get('name', '未知')}")
                print(f"   特征: {primary_cat.get('common_features', '无')[:100]}...")
                
                secondary_cats = primary_cat.get('secondary_categories', [])
                if secondary_cats:
                    print(f"   📂 包含 {len(secondary_cats)} 个二级分类:")
                    for j, secondary_cat in enumerate(secondary_cats):
                        print(f"      {j+1}. {secondary_cat.get('name', '未知')}")
                        print(f"         特征: {secondary_cat.get('common_features', '无')[:80]}...")
        
        elif "primary_category" in classification_data:
            # 单一分类结构
            print(f"📁 单一分类: {classification_data.get('primary_category', '未知')}")
            print(f"💡 分类理由: {classification_data.get('reasoning', '未提供')}")
            print(f"📈 置信度: {classification_data.get('confidence', 0)}")
        
        # 打印分类摘要（如果存在）
        if "classification_summary" in classification_data:
            summary = classification_data["classification_summary"]
            print(f"\n📋 分类摘要:")
            print(f"   - 总组件数: {summary.get('total_components', 0)}")
            print(f"   - 一级分类数: {summary.get('primary_categories_count', 0)}")
            print(f"   - 二级分类数: {summary.get('secondary_categories_count', 0)}")
        
        # 保存测试结果到JSON文件（可选）
        output_dir = os.path.join(os.path.dirname(__file__), "test_output")
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        output_file = os.path.join(output_dir, f"test_product_{product_id}_classification.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(classification_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {output_file}")
        
        # 打印完整JSON结果
        print(f"\n🔍 完整分类结果 (JSON格式):")
        print("-" * 60)
        print(json.dumps(classification_result, ensure_ascii=False, indent=2))
        print("-" * 60)
        
    except Exception as e:
        print(f"❌ 分类过程出错: {e}")
        logger.error(f"测试分类时出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='产品分类框架 - Claude版本')
    parser.add_argument('--product_id', type=int, required=True, help='产品ID')
    parser.add_argument('--debug', action='store_true', help='启用详细调试日志')
    parser.add_argument('--test', action='store_true', help='测试模式：不存储到数据库，只打印结果')
    args = parser.parse_args()
    
    # 如果启用调试模式，设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("已启用详细调试日志")
    
    # 测试模式
    if args.test:
        logger.info(f"🧪 进入测试模式 - 产品ID={args.product_id}")
        test_classify_product(args.product_id)
        return
    
    # 正常模式：执行产品分类并存储
    logger.info(f"🚀 开始使用Claude转发接口执行产品ID={args.product_id}的分类任务")
    success, result = classify_product_main(args.product_id)
    
    if success:
        logger.info(f"✅ 产品ID={args.product_id}分类成功")
        
        # 适配新的JSON格式
        classification_data = result
        if "product_classification" in result:
            classification_data = result["product_classification"]
        
        # 检查分类结果类型并打印相应信息
        if "primary_categories" in classification_data:
            # 两级分类结果
            primary_count = len(classification_data.get("primary_categories", []))
            secondary_count = sum(len(cat.get("secondary_categories", [])) for cat in classification_data.get("primary_categories", []))
            logger.info(f"📊 创建了{primary_count}个一级分类和{secondary_count}个二级分类")
            
            # 打印分类摘要
            for i, category in enumerate(classification_data.get("primary_categories", [])):
                logger.info(f"📁 一级分类{i+1}: {category.get('name')}")
                for j, subcategory in enumerate(category.get("secondary_categories", [])):
                    logger.info(f"  |-📂 二级分类{j+1}: {subcategory.get('name')}")
        elif "primary_category" in classification_data:
            # 单一分类结果
            logger.info(f"📁 产品分类: {classification_data.get('primary_category', '未知')}")
            logger.info(f"💡 分类理由: {classification_data.get('reasoning', '未提供')}")
            logger.info(f"📈 置信度: {classification_data.get('confidence', 0)}")
        
        # 保存结果到JSON文件
        output_dir = os.path.join(os.path.dirname(__file__), "output")
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        output_file = os.path.join(output_dir, f"product_{args.product_id}_classification_result_claude.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, cls=json.JSONEncoder)
        
        logger.info(f"💾 分类结果已保存到: {output_file}")
        
        # 控制台输出
        print("\n🎯 分类结果 (JSON格式):")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        logger.error(f"❌ 产品ID={args.product_id}分类失败")
        print("\n❌ 分类失败，请检查日志了解详细原因")

if __name__ == "__main__":
    main() 