#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文档分析脚本
用于分析数据库中的PDF文档，提取规格、材质等技术信息
修改版：分析product_id=2的前300个最贵物料的技术文档
"""

import os
import sys
import json
import httpx
import mysql.connector
from mysql.connector import Error
from google import genai
from google.genai import types
from datetime import datetime
import argparse
import time
import urllib.parse

# 配置信息
GEMINI_API_KEY = "AIzaSyD8S7u1BWo9VuAjc5nr8TRF6OzO2bYVl5Q"

# 数据库配置
DB_CONFIG = {
    'host': 'rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com',
    'port': 3306,
    'database': 'procurement_system',
    'user': 'yj_app',
    'password': '4iLe5fifhMqOo9Ne',
    'charset': 'utf8mb4',
    'autocommit': False,
    'pool_name': 'doc_analysis_pool',
    'pool_size': 5,
    'pool_reset_session': True,
    'connect_timeout': 30,
    'sql_mode': 'TRADITIONAL'
}

def setup_gemini_client():
    """初始化Gemini客户端"""
    try:
        # 直接创建客户端，API密钥通过环境变量或参数传递
        client = genai.Client(api_key=GEMINI_API_KEY)
        print("✅ Gemini客户端初始化成功")
        return client
    except Exception as e:
        print(f"❌ Gemini客户端初始化失败: {e}")
        return None

def get_database_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            # 设置会话超时和自动重连
            cursor = connection.cursor()
            cursor.execute("SET SESSION wait_timeout = 28800")  # 8小时
            cursor.execute("SET SESSION interactive_timeout = 28800")
            cursor.close()
            print("✅ 数据库连接成功")
            return connection
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def ensure_db_connection(connection):
    """确保数据库连接有效"""
    try:
        if not connection or not connection.is_connected():
            print("⚠️ 数据库连接已断开，尝试重新连接...")
            if connection:
                connection.close()
            connection = get_database_connection()
            if connection:
                print("✅ 数据库重新连接成功")
            return connection
        return connection
    except Exception as e:
        print(f"⚠️ 检查数据库连接时出错: {e}")
        try:
            connection = get_database_connection()
            return connection
        except:
            return None

def get_top_expensive_components_with_docs(connection, limit=300):
    """获取product_id=2的前300个最贵的有文档的物料"""
    try:
        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT DISTINCT c.id, c.name, c.spec, c.category, c.material, 
               c.component_code, c.price, c.client_product_id
        FROM component c 
        INNER JOIN component_documents cd ON c.id = cd.component_id 
        WHERE c.client_product_id = 2 
        AND c.price IS NOT NULL
        GROUP BY c.id, c.name, c.spec, c.category, c.material, c.component_code, c.price, c.client_product_id
        ORDER BY c.price DESC 
        LIMIT %s
        """
        
        cursor.execute(query, (limit,))
        results = cursor.fetchall()
        
        print(f"✅ 找到 {len(results)} 个符合条件的物料")
        return results
        
    except Error as e:
        print(f"❌ 查询物料失败: {e}")
        return []
    finally:
        if cursor:
            cursor.close()

def get_component_document_by_priority(connection, component_id):
    """按优先级获取物料文档：pdf > image > docx"""
    try:
        cursor = connection.cursor(dictionary=True)
        
        # 优先级1: PDF文档
        pdf_query = """
        SELECT * FROM component_documents 
        WHERE component_id = %s 
        AND (document_name LIKE '%.pdf' OR document_name LIKE '%.PDF')
        ORDER BY id ASC 
        LIMIT 1
        """
        cursor.execute(pdf_query, (component_id,))
        pdf_doc = cursor.fetchone()
        
        if pdf_doc:
            return pdf_doc, 'pdf'
        
        # 优先级2: 图片文档
        image_query = """
        SELECT * FROM component_documents 
        WHERE component_id = %s 
        AND (document_name LIKE '%.jpg' OR document_name LIKE '%.jpeg' 
             OR document_name LIKE '%.png' OR document_name LIKE '%.JPG' 
             OR document_name LIKE '%.JPEG' OR document_name LIKE '%.PNG'
             OR document_name LIKE '%.bmp' OR document_name LIKE '%.BMP'
             OR document_name LIKE '%.gif' OR document_name LIKE '%.GIF')
        ORDER BY id ASC 
        LIMIT 1
        """
        cursor.execute(image_query, (component_id,))
        image_doc = cursor.fetchone()
        
        if image_doc:
            return image_doc, 'image'
        
        # 优先级3: Word文档
        docx_query = """
        SELECT * FROM component_documents 
        WHERE component_id = %s 
        AND (document_name LIKE '%.docx' OR document_name LIKE '%.DOCX' 
             OR document_name LIKE '%.doc' OR document_name LIKE '%.DOC')
        ORDER BY id ASC 
        LIMIT 1
        """
        cursor.execute(docx_query, (component_id,))
        docx_doc = cursor.fetchone()
        
        if docx_doc:
            return docx_doc, 'docx'
        
        return None, None
        
    except Error as e:
        print(f"❌ 查询文档失败: {e}")
        return None, None
    finally:
        if cursor:
            cursor.close()

def download_document_content(doc_url):
    """下载文档内容"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            print(f"📥 正在下载文档: {doc_url}")
            
            # 对URL进行编码处理，但保留协议部分
            if '://' in doc_url:
                scheme, rest = doc_url.split('://', 1)
                if '/' in rest:
                    domain, path = rest.split('/', 1)
                    # 对路径部分进行编码，但保留已经编码的部分
                    encoded_path = urllib.parse.quote(path, safe='/:?=&')
                    encoded_url = f"{scheme}://{domain}/{encoded_path}"
                else:
                    encoded_url = doc_url
            else:
                encoded_url = urllib.parse.quote(doc_url, safe='/:?=&')
            
            response = httpx.get(encoded_url, timeout=30.0)
            response.raise_for_status()
            print(f"✅ 文档下载成功，文件大小: {len(response.content)} bytes")
            return response.content
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                print(f"❌ 文档不存在 (404): {doc_url}")
                break  # 404错误不需要重试
            else:
                print(f"⚠️ HTTP错误 {e.response.status_code}，尝试重试 {attempt + 1}/{max_retries}")
        except Exception as e:
            print(f"⚠️ 下载失败，尝试重试 {attempt + 1}/{max_retries}: {e}")
        
        if attempt < max_retries - 1:
            time.sleep(2)  # 重试前等待2秒
    
    print(f"❌ 文档下载失败，已尝试 {max_retries} 次")
    return None

def get_mime_type(doc_type, filename):
    """根据文档类型和文件名获取MIME类型"""
    filename_lower = filename.lower()
    
    if doc_type == 'pdf':
        return 'application/pdf'
    elif doc_type == 'image':
        if filename_lower.endswith(('.jpg', '.jpeg')):
            return 'image/jpeg'
        elif filename_lower.endswith('.png'):
            return 'image/png'
        elif filename_lower.endswith('.bmp'):
            return 'image/bmp'
        elif filename_lower.endswith('.gif'):
            return 'image/gif'
        else:
            return 'image/jpeg'  # 默认
    elif doc_type == 'docx':
        if filename_lower.endswith('.docx'):
            return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        else:
            return 'application/msword'
    
    return 'application/octet-stream'

def analyze_document_with_gemini(client, doc_content, document_info, component_info, doc_type):
    """使用Gemini分析文档内容"""
    try:
        print(f"🤖 正在使用Gemini分析{doc_type.upper()}文档...")
        
        # 构建详细的分析提示
        prompt = f"""
你是一个专业的工程技术分析师，请按照以下结构化框架详细分析这个技术文档：

**文档信息：**
- 文档名称：{document_info['document_name']}
- 文档类型：{doc_type.upper()}
- 组件名称：{component_info.get('name', '未知')}
- 组件编码：{component_info.get('component_code', '未知')}
- 组件规格：{component_info.get('spec', '未知')}
- 组件价格：{component_info.get('price', '未知')}元

**分析要求：**
请严格按照以下格式进行分析，如果某些信息在文档中没有明确提及，请注明"文档中未明确说明"：

【组件概述】：
- 用途: [描述组件在设备中的用途和功能]
- 关键特性: [列出组件的2-3个关键特性]

【技术参数】：
- 材料: [明确标注材料种类和等级，如6061-T4铝合金、ABS塑料等]
- 尺寸: [列出关键尺寸，包括精确的公差要求，如±0.05mm]
- 表面处理: [描述需要的表面处理方法，如阳极氧化、喷砂等]
- 公差要求: [引用相关标准，如GB/T1804-2000等]
- 电气参数: [如适用，包括电压、电流、功率等]
- 性能参数: [工作温度、压力等级、机械强度等]
- 其他特殊要求: [如有特殊工艺或环境要求]

【制造工艺】：
- 主要加工方法: [如CNC加工、注塑成型、PCB制造等]
- 关键工艺步骤: [列出2-3个关键步骤]
- 难点和注意事项: [指出制造过程中可能遇到的挑战]

【关键词建议】：
[提供5-7个搜索关键词组合，包含材料、尺寸、加工方式和用途，例如：
1. "6061-T4铝合金支架135×55mm CNC加工"
2. "黑色阳极氧化精密机械件±0.05mm"]

【采购建议】：
[针对此类组件，提供2-3点采购时需要注意的要点，如供应商能力要求、质量控制要点等]

【分类标签】：
[提供3-7个适合该组件的分类标签，如：精密铝合金加工件、光学设备支架组件、CNC高精度机械件等]

**分析可行性自检：**
请在分析完成后进行自检：
1. 是否能够清晰识别图纸/文档内容？
2. 是否能识别出基本组件信息（名称、尺寸、材料）？
3. 是否有足够信息生成有效的关键词和采购建议？

**重要提醒：**
- 只分析文档中明确可见的信息，不要猜测或假设
- 对于机械图纸，重点关注尺寸公差、材料等级、表面处理要求
- 对于电气图纸，详细提取电气参数和接口信息
- 对于塑料件，注意材料特性、壁厚、成型要求
- 对于标准件，明确型号规格和性能参数

**最终标注：**
请在分析结束时标注分析结果：
- 如果能够提供完整可靠的分析：analysis_result: success
- 如果图纸不清晰或信息不足：analysis_result: fail，并说明原因

请用中文详细分析，严格按照上述格式输出。
"""

        # 获取正确的MIME类型
        mime_type = get_mime_type(doc_type, document_info['document_name'])
        
        # 调用Gemini API
        response = client.models.generate_content(
            model="gemini-2.0-flash",
            contents=[
                types.Part.from_bytes(
                    data=doc_content,
                    mime_type=mime_type,
                ),
                prompt
            ]
        )
        
        print("✅ Gemini分析完成")
        return response.text
        
    except Exception as e:
        print(f"❌ Gemini分析失败: {e}")
        return None

def save_analysis_to_component_analysis(connection, component_id, analysis_result):
    """保存分析结果到component_analysis表"""
    cursor = None
    try:
        # 确保数据库连接有效
        connection = ensure_db_connection(connection)
        if not connection:
            print("❌ 无法建立数据库连接")
            return connection
        
        cursor = connection.cursor()
        
        # 检查是否已存在该组件的分析记录
        check_query = "SELECT id FROM component_analysis WHERE component_id = %s"
        cursor.execute(check_query, (component_id,))
        existing = cursor.fetchone()
        
        if existing:
            # 更新现有记录
            update_query = """
            UPDATE component_analysis 
            SET analysis = %s, is_success = 1, created_at = CURRENT_TIMESTAMP
            WHERE component_id = %s
            """
            cursor.execute(update_query, (analysis_result, component_id))
            print(f"✅ 更新了物料ID {component_id} 的分析结果")
        else:
            # 插入新记录
            insert_query = """
            INSERT INTO component_analysis (component_id, analysis, is_success)
            VALUES (%s, %s, 1)
            """
            cursor.execute(insert_query, (component_id, analysis_result))
            print(f"✅ 插入了物料ID {component_id} 的分析结果")
        
        connection.commit()
        return connection
        
    except Error as e:
        print(f"❌ 保存分析结果失败: {e}")
        try:
            if connection and connection.is_connected():
                connection.rollback()
        except:
            pass
        return connection
    finally:
        if cursor:
            cursor.close()

def mark_analysis_failed(connection, component_id, error_msg):
    """标记分析失败的记录"""
    cursor = None
    try:
        # 确保数据库连接有效
        connection = ensure_db_connection(connection)
        if not connection:
            print("❌ 无法建立数据库连接")
            return connection
        
        cursor = connection.cursor()
        
        # 检查是否已存在该组件的分析记录
        check_query = "SELECT id FROM component_analysis WHERE component_id = %s"
        cursor.execute(check_query, (component_id,))
        existing = cursor.fetchone()
        
        error_analysis = f"分析失败: {error_msg}"
        
        if existing:
            # 更新现有记录
            update_query = """
            UPDATE component_analysis 
            SET analysis = %s, is_success = 0, created_at = CURRENT_TIMESTAMP
            WHERE component_id = %s
            """
            cursor.execute(update_query, (error_analysis, component_id))
        else:
            # 插入新记录
            insert_query = """
            INSERT INTO component_analysis (component_id, analysis, is_success)
            VALUES (%s, %s, 0)
            """
            cursor.execute(insert_query, (component_id, error_analysis))
        
        connection.commit()
        return connection
        
    except Error as e:
        print(f"❌ 标记失败记录时出错: {e}")
        try:
            if connection and connection.is_connected():
                connection.rollback()
        except:
            pass
        return connection
    finally:
        if cursor:
            cursor.close()

def analyze_top_components():
    """分析前300个最贵的物料文档"""
    print("🚀 开始批量文档分析流程...")
    print("="*80)
    print("🎯 目标：分析product_id=2的前300个最贵物料的技术文档")
    print("📋 文档优先级：PDF > 图片 > Word文档")
    
    # 1. 初始化Gemini客户端
    client = setup_gemini_client()
    if not client:
        print("❌ 无法初始化Gemini客户端，程序退出")
        return
    
    # 2. 连接数据库
    connection = get_database_connection()
    if not connection:
        print("❌ 无法连接数据库，程序退出")
        return
    
    try:
        # 3. 获取前300个最贵的有文档的物料
        components = get_top_expensive_components_with_docs(connection, 300)
        if not components:
            print("❌ 没有找到符合条件的物料，程序退出")
            return
        
        print(f"\n📊 准备分析 {len(components)} 个物料的技术文档")
        print("="*80)
        
        success_count = 0
        failed_count = 0
        
        # 4. 逐个分析物料文档
        for i, component in enumerate(components, 1):
            component_id = component['id']
            component_name = component['name']
            component_price = component['price']
            
            print(f"\n[{i}/{len(components)}] 🔍 分析物料: {component_name} (ID: {component_id}, 价格: ¥{component_price})")
            
            try:
                # 获取该物料的最优先文档
                document_info, doc_type = get_component_document_by_priority(connection, component_id)
                
                if not document_info:
                    print(f"⚠️ 物料 {component_name} 没有找到可分析的文档")
                    connection = mark_analysis_failed(connection, component_id, "没有找到可分析的文档")
                    failed_count += 1
                    continue
                
                print(f"📄 选择文档: {document_info['document_name']} (类型: {doc_type.upper()})")
                
                # 下载文档内容
                doc_content = download_document_content(document_info['oss_path'])
                if not doc_content:
                    print(f"❌ 文档下载失败: {document_info['document_name']}")
                    connection = mark_analysis_failed(connection, component_id, "文档下载失败")
                    failed_count += 1
                    continue
                
                # 使用Gemini分析文档
                analysis_result = analyze_document_with_gemini(
                    client, doc_content, document_info, component, doc_type
                )
                
                if analysis_result:
                    # 保存分析结果到component_analysis表
                    connection = save_analysis_to_component_analysis(connection, component_id, analysis_result)
                    if connection:
                        success_count += 1
                        print(f"✅ 物料 {component_name} 分析完成")
                    else:
                        print(f"❌ 物料 {component_name} 保存失败")
                        failed_count += 1
                else:
                    print(f"❌ 物料 {component_name} 分析失败")
                    connection = mark_analysis_failed(connection, component_id, "AI分析失败")
                    failed_count += 1
                
                # 添加延迟避免API限制
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ 处理物料 {component_name} 时出错: {e}")
                connection = mark_analysis_failed(connection, component_id, str(e))
                failed_count += 1
                continue
        
        # 5. 输出统计结果
        print("\n" + "="*80)
        print("📊 批量分析完成！")
        print("="*80)
        print(f"✅ 成功分析: {success_count} 个物料")
        print(f"❌ 分析失败: {failed_count} 个物料")
        print(f"📈 成功率: {success_count/(success_count+failed_count)*100:.1f}%")
        print("="*80)
        
        return {
            'total': len(components),
            'success': success_count,
            'failed': failed_count
        }
        
    finally:
        # 关闭数据库连接
        if connection.is_connected():
            connection.close()
            print("🔒 数据库连接已关闭")

def analyze_component_document(component_id=None):
    """分析指定物料的PDF文档"""
    print("🚀 开始PDF文档分析流程...")
    print("="*60)
    
    if component_id:
        print(f"🎯 目标物料ID: {component_id}")
    else:
        print("🔍 查找数据库中的第一个PDF文档")
    
    # 1. 初始化Gemini客户端
    client = setup_gemini_client()
    if not client:
        print("❌ 无法初始化Gemini客户端，程序退出")
        return
    
    # 2. 连接数据库
    connection = get_database_connection()
    if not connection:
        print("❌ 无法连接数据库，程序退出")
        return
    
    try:
        # 3. 获取PDF文档信息
        document_info = get_first_pdf_document(connection, component_id)
        if not document_info:
            print("❌ 没有找到PDF文档，程序退出")
            return
        
        print(f"\n📄 准备分析文档:")
        print(f"   文档ID: {document_info['id']}")
        print(f"   文档名称: {document_info['document_name']}")
        print(f"   物料ID: {document_info['component_id']}")
        print(f"   物料名称: {document_info.get('component_name', '未知')}")
        print(f"   物料规格: {document_info.get('component_spec', '未知')}")
        print(f"   物料分类: {document_info.get('category', '未知')}")
        print(f"   物料材质: {document_info.get('material', '未知')}")
        print(f"   PDF路径: {document_info['oss_path']}")
        
        # 4. 下载PDF内容
        pdf_content = download_pdf_content(document_info['oss_path'])
        if not pdf_content:
            print("❌ PDF下载失败，程序退出")
            return
        
        # 5. 使用Gemini分析PDF
        analysis_result = analyze_pdf_with_gemini(client, pdf_content, document_info)
        if not analysis_result:
            print("❌ PDF分析失败，程序退出")
            return
        
        # 6. 输出分析结果
        print("\n" + "="*60)
        print("📊 分析结果:")
        print("="*60)
        print(analysis_result)
        print("="*60)
        
        # 7. 保存结果到文件
        output_file = save_to_file(document_info, analysis_result)
        
        # 8. 保存结果到component_analysis表
        try:
            component_id = document_info['component_id']
            connection = save_analysis_to_component_analysis(connection, component_id, analysis_result)
            if connection:
                print("✅ 分析结果已保存到component_analysis表")
            else:
                print(f"❌ 物料 {document_info['component_name']} 保存失败")
        except Exception as e:
            print(f"⚠️ 保存到component_analysis表失败: {e}")
        
        print(f"\n✅ 文档分析完成！")
        if output_file:
            print(f"📁 详细报告已保存到: {output_file}")
        
        return {
            'document_info': document_info,
            'analysis_result': analysis_result,
            'output_file': output_file
        }
        
    finally:
        # 关闭数据库连接
        if connection.is_connected():
            connection.close()
            print("🔒 数据库连接已关闭")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量分析物料技术文档工具')
    parser.add_argument('--batch', '-b', action='store_true',
                       help='批量分析product_id=2的前300个最贵物料')
    parser.add_argument('--component-id', '-c', type=int, 
                       help='指定要分析的物料ID（单个分析模式）')
    
    args = parser.parse_args()
    
    if args.batch:
        # 批量分析模式
        analyze_top_components()
    elif args.component_id:
        # 单个分析模式（保留原有功能）
        analyze_component_document(args.component_id)
    else:
        # 默认批量分析模式
        analyze_top_components()

def get_first_pdf_document(connection, component_id=None):
    """从数据库获取PDF文档信息（保留原功能）"""
    try:
        cursor = connection.cursor(dictionary=True)
        
        if component_id:
            # 查询指定物料ID的第一个PDF文档
            query = """
            SELECT cd.*, c.name as component_name, c.spec as component_spec, 
                   c.category, c.material, c.component_code
            FROM component_documents cd
            LEFT JOIN component c ON cd.component_id = c.id
            WHERE cd.component_id = %s 
            AND (cd.document_name LIKE '%.pdf' OR cd.document_name LIKE '%.PDF')
            ORDER BY cd.id ASC
            LIMIT 1
            """
            cursor.execute(query, (component_id,))
        else:
            # 查询第一个PDF文档
            query = """
            SELECT cd.*, c.name as component_name, c.spec as component_spec, 
                   c.category, c.material, c.component_code
            FROM component_documents cd
            LEFT JOIN component c ON cd.component_id = c.id
            WHERE cd.document_name LIKE '%.pdf' OR cd.document_name LIKE '%.PDF'
            ORDER BY cd.id ASC
            LIMIT 1
            """
            cursor.execute(query)
        
        result = cursor.fetchone()
        
        if result:
            print(f"✅ 找到PDF文档: {result['document_name']}")
            return result
        else:
            if component_id:
                print(f"❌ 没有找到物料ID {component_id} 对应的PDF文档")
            else:
                print("❌ 没有找到PDF文档")
            return None
            
    except Error as e:
        print(f"❌ 查询数据库失败: {e}")
        return None
    finally:
        if cursor:
            cursor.close()

def download_pdf_content(pdf_url):
    """下载PDF文件内容（保留原功能）"""
    return download_document_content(pdf_url)

def analyze_pdf_with_gemini(client, pdf_content, document_info):
    """使用Gemini分析PDF内容（保留原功能）"""
    component_info = {
        'name': document_info.get('component_name', '未知'),
        'component_code': document_info.get('component_code', '未知'),
        'spec': document_info.get('component_spec', '未知'),
        'price': '未知'
    }
    return analyze_document_with_gemini(client, pdf_content, document_info, component_info, 'pdf')

def save_analysis_result(connection, document_id, analysis_result):
    """保存分析结果到数据库（保留原功能，但简化实现）"""
    print("⚠️ document_analysis表已废弃，建议使用component_analysis表存储分析结果")
    # 此函数保留但不实际操作，避免破坏向后兼容性
    pass

def save_to_file(document_info, analysis_result):
    """保存分析结果到文件（保留原功能）"""
    try:
        # 创建输出目录
        output_dir = "analysis_results"
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/analysis_{document_info['id']}_{timestamp}.txt"
        
        # 写入分析结果
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("PDF文档技术分析报告\n")
            f.write("="*80 + "\n\n")
            f.write(f"文档ID: {document_info['id']}\n")
            f.write(f"文档名称: {document_info['document_name']}\n")
            f.write(f"组件名称: {document_info.get('component_name', '未知')}\n")
            f.write(f"OSS路径: {document_info['oss_path']}\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("\n" + "="*80 + "\n")
            f.write("技术分析结果\n")
            f.write("="*80 + "\n\n")
            f.write(analysis_result)
            f.write("\n\n" + "="*80 + "\n")
            f.write("报告结束\n")
            f.write("="*80 + "\n")
        
        print(f"✅ 分析结果已保存到文件: {filename}")
        return filename
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return None

if __name__ == "__main__":
    main() 