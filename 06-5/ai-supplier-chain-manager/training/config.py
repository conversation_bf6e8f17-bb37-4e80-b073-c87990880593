#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
管理数据库连接、API密钥等配置信息
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# Gemini API配置
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyD8S7u1BWo9VuAjc5nr8TRF6OzO2bYVl5Q")

# 数据库配置 - 使用阿里云RDS
DB_CONFIG = {
    'host': os.getenv("DB_HOST", "rm-uf6460x8sj8242fn64o.mysql.rds.aliyuncs.com"),
    'port': int(os.getenv("DB_PORT", "3306")),
    'database': os.getenv("DB_NAME", "procurement_system"),
    'user': os.getenv("DB_USER", "yj_app"),
    'password': os.getenv("DB_PASSWORD", "4iLe5fifhMqOo9Ne"),
    'charset': 'utf8mb4'
}

# 输出配置
OUTPUT_DIR = os.getenv("OUTPUT_DIR", "analysis_results")

# HTTP请求配置
REQUEST_TIMEOUT = 30.0
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB 