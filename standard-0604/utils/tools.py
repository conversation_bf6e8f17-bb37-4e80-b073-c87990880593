import json
import re


def extract_json(text):
    """
    从文本中提取最大的JSON字典
    
    Args:
        text (str): 包含JSON字典的文本
        
    Returns:
        dict: 提取的JSON字典，如果提取失败则返回None
    """
    try:
        # 找到所有可能的JSON字典
        json_matches = []
        # 跟踪大括号的嵌套
        open_braces = 0
        start_index = -1
        
        for i, char in enumerate(text):
            if char == '{':
                if open_braces == 0:
                    start_index = i
                open_braces += 1
            elif char == '}':
                open_braces -= 1
                if open_braces == 0 and start_index != -1:
                    json_str = text[start_index:i+1]
                    try:
                        # 尝试解析JSON
                        json_obj = json.loads(json_str)
                        json_matches.append((json_str, json_obj))
                    except:
                        pass  # 不是有效的JSON，继续搜索
        
        # 如果找到了JSON字典，返回最大的一个
        if json_matches:
            # 按字符串长度排序，返回最长的
            json_matches.sort(key=lambda x: len(x[0]), reverse=True)
            return json_matches[0][1]
        
        return None
    except Exception as e:
        print(f"JSON提取失败: {e}")
        return None