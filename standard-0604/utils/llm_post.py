from openai import OpenAI
import time

OPENAI_API_KEY = "sk-cea8ff01b7dc47caa89fa2943728138e"
OPENAI_BASE_URL = "https://xiaohumini.site/v1"


def post_openai_web_search(model: str="gpt-4.1-2025-04-14", question: str):
    client = OpenAI(api_key="********************************************************************************************************************************************************************", base_url="http://43.130.31.174:8003/v1")

    response = client.responses.create(
        model=model,
        tools=[{
            "type": "web_search_preview",
            "search_context_size": "high",
            "user_location": {
                "type": "approximate",
                "country": "CN",
                # "city": "Shanghai",
                # "region": "Shanghai",
            }
        }],
        input=question
    )

    print(response.output_text)
    return response.output_text


def post_openai_chat(model: str="gpt-4.1-2025-04-14", question: str):
    client = OpenAI(api_key=OPENAI_API_KEY, base_url=OPENAI_BASE_URL)
    response = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": question}
        ]
    )
    print(response.choices[0].message.content)
    return response.choices[0].message.content