from utils.llm_post import post_openai_chat, post_openai_web_search
from utils.biz_prompt import CHECK_COMPANY_TYPE_PROMPT
from utils.tools import extract_json


def check_company_type(company_name: str):
    prompt = CHECK_COMPANY_TYPE_PROMPT.format(company_name=company_name)
    response = post_openai_web_search(question=prompt)
    response = extract_json(response)
    return response


def test():
    company_name = "滨松光子学商贸（中国）有限公司"
    response = check_company_type(company_name)
    print(response)

if __name__ == '__main__':
    test()