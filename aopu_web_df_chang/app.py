import os
import pandas as pd
import json
from flask import Flask, render_template, request, send_file, redirect, url_for, flash
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.secret_key = 'aopu_web_df_chang_secret_key'

# 配置上传文件夹
UPLOAD_FOLDER = 'uploads'
DOWNLOAD_FOLDER = 'downloads'
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保上传和下载文件夹存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(DOWNLOAD_FOLDER, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['DOWNLOAD_FOLDER'] = DOWNLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def change_data(file_path, save_path):
    """
    转换Excel数据的函数
    """
    df = pd.read_excel(file_path)

    supplier_data = dict()
    old_material_id = 0
    for index, row in df.iterrows():
        if pd.isna(row['商家名称']):
            continue
        if pd.isna(row['物料编码']):
            material_id = old_material_id
        else:
            material_id = index
            old_material_id = material_id
        
        new_supplier_name = row['商家名称']
        new_supplier_info = {
            'new_supplier_name': new_supplier_name,
            'registered_capital': row['注册资金'],
            'company_website': row['公司网址'],
            'contact_email': row['联系邮箱'],
            'establishment_date': row['成立日期'],
            'contact_phone': row['联系电话'],
            'staff_size': row['人员规模'],
            'business_scope': row['经营范围']
        }
        material_info = {
            'material_id': str(int(df.iloc[material_id]['物料编码'])),
            'material_name': df.iloc[material_id]['物料名称'],
            'specification': df.iloc[material_id]['规格/型号'] if pd.notna(df.iloc[material_id]['规格/型号']) else '',
            'unit_consumption': df.iloc[material_id]['单耗'] if pd.notna(df.iloc[material_id]['单耗']) else '',
            'unit': df.iloc[material_id]['单位'] if pd.notna(df.iloc[material_id]['单位']) else '',
            'classes': df.iloc[material_id]['类别'] if pd.notna(df.iloc[material_id]['类别']) else '',
            'productor': df.iloc[material_id]['制造商'] if pd.notna(df.iloc[material_id]['制造商']) else '',
            'old_supplier_name': df.iloc[material_id]['当前供货商'] if pd.notna(df.iloc[material_id]['当前供货商']) else '',
            'old_price': df.iloc[material_id]['当前价格'] if pd.notna(df.iloc[material_id]['当前价格']) else '',
            'new_good_url': row['新供货链接'],
            'new_price': row['新价格'],
            'name_matching_result': row['物料名匹配结果'],
            'spec_matching_result': row['规格匹配结果'],
            'is_found': row['是否找到'],
            'human_check': row['人工确认（符合/不符合）'],
            'qualification': row['merchants_quality'],
            'new_sum_price': row['new_sum_price'],
            'is_lower': row['is_lower']
        }

        if new_supplier_name not in supplier_data:
            supplier_data[new_supplier_name] = {}
            supplier_data[new_supplier_name]['supplier_info'] = new_supplier_info
            supplier_data[new_supplier_name]['material_info'] = [material_info]
        else:
            supplier_data[new_supplier_name]['material_info'].append(material_info)
    
    df_new = pd.DataFrame(columns=[
        '新供应商名称',
        '注册资金',
        '公司网址',
        '联系邮箱',
        '成立日期',
        '联系电话',
        '人员规模',
        '经营范围',
        '物料编码',
        '物料名称',
        '规格/型号',
        '单耗',
        '单位',
        '类别',
        '制造商',
        '当前供货商',
        '当前价格',
        '新供货链接',
        '新价格',
        '物料名匹配结果',
        '规格匹配结果',
        '是否找到',
        '人工确认（符合/不符合）',
        'merchants_quality',
        'new_sum_price',
        'is_lower'
    ])
    already_supplier = list()
    for supplier_name, supplier_info in supplier_data.items():
        for material_info in supplier_info['material_info']:
            if supplier_name not in already_supplier:
                tmp_data = {
                    '新供应商名称': supplier_name,
                    '注册资金': supplier_info['supplier_info']['registered_capital'],
                    '公司网址': supplier_info['supplier_info']['company_website'],
                    '联系邮箱': supplier_info['supplier_info']['contact_email'],
                    '成立日期': supplier_info['supplier_info']['establishment_date'],
                    '联系电话': supplier_info['supplier_info']['contact_phone'],
                    '人员规模': supplier_info['supplier_info']['staff_size'],
                    '经营范围': supplier_info['supplier_info']['business_scope'],
                    '物料编码': material_info['material_id'],
                    '物料名称': material_info['material_name'],
                    '规格/型号': material_info['specification'],
                    '单耗': material_info['unit_consumption'],
                    '单位': material_info['unit'],
                    '类别': material_info['classes'],
                    '制造商': material_info['productor'],
                    '当前供货商': material_info['old_supplier_name'],
                    '当前价格': material_info['old_price'],
                    '新供货链接': material_info['new_good_url'],
                    '新价格': material_info['new_price'],
                    '物料名匹配结果': material_info['name_matching_result'],
                    '规格匹配结果': material_info['spec_matching_result'],
                    '是否找到': material_info['is_found'],
                    '人工确认（符合/不符合）': material_info['human_check'],
                    'merchants_quality': material_info['qualification'],
                    'new_sum_price': material_info['new_sum_price'],
                    'is_lower': material_info['is_lower']
                }
            else:
                tmp_data = {
                    '新供应商名称': "",
                    '注册资金': "",
                    '公司网址': "",
                    '联系邮箱': "",
                    '成立日期': "",
                    '联系电话': "",
                    '人员规模': "",
                    '经营范围': "",
                    '物料编码': material_info['material_id'],
                    '物料名称': material_info['material_name'],
                    '规格/型号': material_info['specification'],
                    '单耗': material_info['unit_consumption'],
                    '单位': material_info['unit'],
                    '类别': material_info['classes'],
                    '制造商': material_info['productor'],
                    '当前供货商': material_info['old_supplier_name'],
                    '当前价格': material_info['old_price'],
                    '新供货链接': material_info['new_good_url'],
                    '新价格': material_info['new_price'],
                    '物料名匹配结果': material_info['name_matching_result'],
                    '规格匹配结果': material_info['spec_matching_result'],
                    '是否找到': material_info['is_found'],
                    '人工确认（符合/不符合）': material_info['human_check'],
                    'merchants_quality': material_info['qualification'],
                    'new_sum_price': material_info['new_sum_price'],
                    'is_lower': material_info['is_lower']
                }
            df_new = pd.concat([df_new, pd.DataFrame([tmp_data])], ignore_index=True)
            already_supplier.append(supplier_name)
    
    df_new.to_excel(save_path, index=False)
    return save_path

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        flash('没有选择文件')
        return redirect(request.url)
    
    file = request.files['file']
    
    if file.filename == '':
        flash('没有选择文件')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # 生成转换后的文件名
        output_filename = f"converted_{filename}"
        save_path = os.path.join(app.config['DOWNLOAD_FOLDER'], output_filename)
        
        try:
            # 处理Excel文件
            change_data(file_path, save_path)
            
            # 返回下载链接
            return render_template('download.html', filename=output_filename)
        except Exception as e:
            flash(f'处理文件时出错: {str(e)}')
            return redirect(url_for('index'))
    else:
        flash('只允许上传Excel文件 (.xlsx, .xls)')
        return redirect(url_for('index'))

@app.route('/download/<filename>')
def download_file(filename):
    return send_file(os.path.join(app.config['DOWNLOAD_FOLDER'], filename),
                     as_attachment=True,
                     download_name=filename)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8610, debug=False)
