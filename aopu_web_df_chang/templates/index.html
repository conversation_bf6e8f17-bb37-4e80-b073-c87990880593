<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据转换工具</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #343a40;
            font-weight: 600;
        }
        .upload-form {
            text-align: center;
        }
        .file-input-wrapper {
            position: relative;
            margin: 30px auto;
            width: 100%;
            max-width: 500px;
        }
        .file-input-wrapper input[type="file"] {
            width: 100%;
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 10px 30px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            transform: translateY(-2px);
        }
        .alert {
            margin-top: 20px;
        }
        .instructions {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Excel数据转换工具</h1>
            <p class="text-muted">上传Excel文件，自动处理并生成新的Excel文件</p>
        </div>

        {% with messages = get_flashed_messages() %}
        {% if messages %}
        <div class="alert alert-danger" role="alert">
            {% for message in messages %}
            {{ message }}
            {% endfor %}
        </div>
        {% endif %}
        {% endwith %}

        <div class="instructions">
            <h5>使用说明：</h5>
            <ol class="text-start">
                <li>请确保上传的Excel文件包含所需的所有列（如：商家名称、物料编码等）</li>
                <li>文件格式必须为.xlsx或.xls</li>
                <li>上传后系统将自动处理数据并提供下载链接</li>
            </ol>
        </div>

        <div class="upload-form">
            <form action="/upload" method="post" enctype="multipart/form-data">
                <div class="file-input-wrapper">
                    <div class="mb-3">
                        <label for="formFile" class="form-label">选择Excel文件</label>
                        <input class="form-control" type="file" id="formFile" name="file" accept=".xlsx, .xls">
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">上传并处理</button>
            </form>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>
</body>
</html>
