# Excel数据转换工具

这是一个基于Flask的Web应用程序，用于处理Excel文件数据转换。用户可以上传Excel文件，系统会按照指定的逻辑进行处理，并生成新的Excel文件供用户下载。

## 功能特点

- 简单易用的Web界面
- 支持上传Excel文件（.xlsx和.xls格式）
- 自动处理数据并重组Excel内容
- 提供处理后文件的下载链接

## 安装与运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
python app.py
```

应用将在本地启动，访问 http://127.0.0.1:5000 即可使用。

## 使用说明

1. 在首页点击"选择Excel文件"按钮，选择要处理的Excel文件
2. 点击"上传并处理"按钮
3. 等待系统处理完成
4. 在结果页面点击"下载文件"按钮获取处理后的Excel文件

## 注意事项

- 上传的Excel文件必须包含所需的所有列（如：商家名称、物料编码等）
- 文件格式必须为.xlsx或.xls
- 处理后的文件将保存在服务器的downloads目录中
