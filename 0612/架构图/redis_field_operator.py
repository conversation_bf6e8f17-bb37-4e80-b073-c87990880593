#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis字段操作脚本
支持对Redis中特定key的字段进行各种操作，包括覆盖、读取、删除等
"""

import redis
import json
import argparse
import sys
from typing import Dict, Any, Optional, Union


class RedisFieldOperator:
    """Redis字段操作类"""
    
    def __init__(self, host='localhost', port=6379, db=0, password=None, decode_responses=True):
        """
        初始化Redis连接
        
        Args:
            host: Redis服务器地址
            port: Redis端口
            db: 数据库编号
            password: 密码
            decode_responses: 是否解码响应
        """
        try:
            self.redis_client = redis.Redis(
                host=host,
                port=port,
                db=db,
                password=password,
                decode_responses=decode_responses
            )
            # 测试连接
            self.redis_client.ping()
            print(f"✅ 成功连接到Redis: {host}:{port}/{db}")
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            sys.exit(1)
    
    def set_field(self, key: str, field: str, value: Union[str, int, float, dict, list]) -> bool:
        """
        设置Hash字段值（覆盖模式）
        
        Args:
            key: Redis key
            field: 字段名
            value: 字段值
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 如果值是字典或列表，转换为JSON字符串
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            result = self.redis_client.hset(key, field, value)
            print(f"✅ 设置字段成功: {key}.{field} = {value}")
            return True
        except Exception as e:
            print(f"❌ 设置字段失败: {e}")
            return False
    
    def get_field(self, key: str, field: str) -> Optional[str]:
        """
        获取Hash字段值
        
        Args:
            key: Redis key
            field: 字段名
            
        Returns:
            字段值或None
        """
        try:
            value = self.redis_client.hget(key, field)
            if value is None:
                print(f"⚠️  字段不存在: {key}.{field}")
                return None
            
            # 尝试解析JSON
            try:
                parsed_value = json.loads(value)
                print(f"📄 获取字段: {key}.{field} = {parsed_value}")
                return parsed_value
            except json.JSONDecodeError:
                print(f"📄 获取字段: {key}.{field} = {value}")
                return value
        except Exception as e:
            print(f"❌ 获取字段失败: {e}")
            return None
    
    def get_all_fields(self, key: str) -> Dict[str, Any]:
        """
        获取Hash的所有字段
        
        Args:
            key: Redis key
            
        Returns:
            所有字段的字典
        """
        try:
            fields = self.redis_client.hgetall(key)
            if not fields:
                print(f"⚠️  Key不存在或为空: {key}")
                return {}
            
            # 尝试解析JSON值
            parsed_fields = {}
            for field, value in fields.items():
                try:
                    parsed_fields[field] = json.loads(value)
                except json.JSONDecodeError:
                    parsed_fields[field] = value
            
            print(f"📋 获取所有字段: {key}")
            for field, value in parsed_fields.items():
                print(f"  {field}: {value}")
            
            return parsed_fields
        except Exception as e:
            print(f"❌ 获取所有字段失败: {e}")
            return {}
    
    def delete_field(self, key: str, field: str) -> bool:
        """
        删除Hash字段
        
        Args:
            key: Redis key
            field: 字段名
            
        Returns:
            bool: 操作是否成功
        """
        try:
            result = self.redis_client.hdel(key, field)
            if result:
                print(f"✅ 删除字段成功: {key}.{field}")
                return True
            else:
                print(f"⚠️  字段不存在: {key}.{field}")
                return False
        except Exception as e:
            print(f"❌ 删除字段失败: {e}")
            return False
    
    def field_exists(self, key: str, field: str) -> bool:
        """
        检查字段是否存在
        
        Args:
            key: Redis key
            field: 字段名
            
        Returns:
            bool: 字段是否存在
        """
        try:
            exists = self.redis_client.hexists(key, field)
            print(f"🔍 字段存在性检查: {key}.{field} = {exists}")
            return exists
        except Exception as e:
            print(f"❌ 检查字段存在性失败: {e}")
            return False
    
    def update_fields(self, key: str, field_dict: Dict[str, Any]) -> bool:
        """
        批量更新多个字段（覆盖模式）
        
        Args:
            key: Redis key
            field_dict: 字段字典
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 处理字典和列表值
            processed_dict = {}
            for field, value in field_dict.items():
                if isinstance(value, (dict, list)):
                    processed_dict[field] = json.dumps(value, ensure_ascii=False)
                else:
                    processed_dict[field] = value
            
            self.redis_client.hmset(key, processed_dict)
            print(f"✅ 批量更新字段成功: {key}")
            for field, value in field_dict.items():
                print(f"  {field}: {value}")
            return True
        except Exception as e:
            print(f"❌ 批量更新字段失败: {e}")
            return False
    
    def increment_field(self, key: str, field: str, increment: Union[int, float] = 1) -> Optional[Union[int, float]]:
        """
        对数值字段进行增量操作
        
        Args:
            key: Redis key
            field: 字段名
            increment: 增量值
            
        Returns:
            增量后的值
        """
        try:
            if isinstance(increment, float):
                result = self.redis_client.hincrbyfloat(key, field, increment)
            else:
                result = self.redis_client.hincrby(key, field, increment)
            
            print(f"✅ 字段增量操作成功: {key}.{field} += {increment} = {result}")
            return result
        except Exception as e:
            print(f"❌ 字段增量操作失败: {e}")
            return None


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='Redis字段操作工具')
    parser.add_argument('--host', default='localhost', help='Redis主机地址')
    parser.add_argument('--port', type=int, default=6379, help='Redis端口')
    parser.add_argument('--db', type=int, default=0, help='数据库编号')
    parser.add_argument('--password', help='Redis密码')
    
    subparsers = parser.add_subparsers(dest='command', help='操作命令')
    
    # 设置字段命令
    set_parser = subparsers.add_parser('set', help='设置字段值')
    set_parser.add_argument('key', help='Redis key')
    set_parser.add_argument('field', help='字段名')
    set_parser.add_argument('value', help='字段值')
    
    # 获取字段命令
    get_parser = subparsers.add_parser('get', help='获取字段值')
    get_parser.add_argument('key', help='Redis key')
    get_parser.add_argument('field', help='字段名')
    
    # 获取所有字段命令
    getall_parser = subparsers.add_parser('getall', help='获取所有字段')
    getall_parser.add_argument('key', help='Redis key')
    
    # 删除字段命令
    del_parser = subparsers.add_parser('delete', help='删除字段')
    del_parser.add_argument('key', help='Redis key')
    del_parser.add_argument('field', help='字段名')
    
    # 检查字段存在性命令
    exists_parser = subparsers.add_parser('exists', help='检查字段是否存在')
    exists_parser.add_argument('key', help='Redis key')
    exists_parser.add_argument('field', help='字段名')
    
    # 增量操作命令
    incr_parser = subparsers.add_parser('incr', help='字段增量操作')
    incr_parser.add_argument('key', help='Redis key')
    incr_parser.add_argument('field', help='字段名')
    incr_parser.add_argument('increment', type=float, default=1, help='增量值')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建Redis操作实例
    operator = RedisFieldOperator(
        host=args.host,
        port=args.port,
        db=args.db,
        password=args.password
    )
    
    # 执行相应命令
    if args.command == 'set':
        # 尝试解析JSON值
        try:
            value = json.loads(args.value)
        except json.JSONDecodeError:
            value = args.value
        operator.set_field(args.key, args.field, value)
    
    elif args.command == 'get':
        operator.get_field(args.key, args.field)
    
    elif args.command == 'getall':
        operator.get_all_fields(args.key)
    
    elif args.command == 'delete':
        operator.delete_field(args.key, args.field)
    
    elif args.command == 'exists':
        operator.field_exists(args.key, args.field)
    
    elif args.command == 'incr':
        operator.increment_field(args.key, args.field, args.increment)


if __name__ == '__main__':
    # 示例用法
    print("=== Redis字段操作脚本示例 ===")
    print()
    
    # 如果直接运行脚本，显示示例用法
    if len(sys.argv) == 1:
        print("📖 使用示例:")
        print()
        print("# 基本用法")
        print("python redis_field_operator.py set user:1001 name '张三'")
        print("python redis_field_operator.py set user:1001 age 25")
        print("python redis_field_operator.py set user:1001 profile '{\"city\": \"北京\", \"hobby\": [\"读书\", \"游泳\"]}'")
        print()
        print("# 获取字段")
        print("python redis_field_operator.py get user:1001 name")
        print("python redis_field_operator.py getall user:1001")
        print()
        print("# 其他操作")
        print("python redis_field_operator.py delete user:1001 age")
        print("python redis_field_operator.py exists user:1001 name")
        print("python redis_field_operator.py incr user:1001 login_count 1")
        print()
        print("# 连接参数")
        print("python redis_field_operator.py --host ************* --port 6379 --db 1 --password mypass set mykey field value")
        print()
        print("💡 编程接口示例:")
        print()
        print("```python")
        print("# 创建操作实例")
        print("operator = RedisFieldOperator(host='localhost', port=6379, db=0)")
        print()
        print("# 设置字段")
        print("operator.set_field('user:1001', 'name', '张三')")
        print("operator.set_field('user:1001', 'profile', {'city': '北京', 'age': 25})")
        print()
        print("# 批量更新")
        print("operator.update_fields('user:1001', {")
        print("    'name': '李四',")
        print("    'age': 30,")
        print("    'tags': ['VIP', '活跃用户']")
        print("})")
        print()
        print("# 获取字段")
        print("name = operator.get_field('user:1001', 'name')")
        print("all_fields = operator.get_all_fields('user:1001')")
        print("```")
    else:
        main()