<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能化医疗器械设计与供应链一体化平台 - 技术架构图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 50px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section h2::before {
            content: "🔧";
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .phase-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .diagram-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .mermaid {
            text-align: center;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .feature-card h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .feature-card ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-card li {
            margin-bottom: 5px;
            color: #666;
        }
        
        .nav-tabs {
            display: flex;
            background: #f1f3f4;
            border-radius: 8px;
            padding: 5px;
            margin-bottom: 20px;
        }
        
        .nav-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智能化医疗器械设计与供应链一体化平台</h1>
            <p>技术架构图与实现流程图</p>
        </div>
        
        <div class="content">
            <!-- 整体架构 -->
            <div class="section">
                <h2>整体系统架构</h2>
                <div class="diagram-container">
                    <div class="mermaid">
                        graph TB
                            subgraph "交互层"
                                A1[Web端界面]
                                A2[移动端APP]
                                A3[API网关]
                                A4[管理后台]
                            end
                            
                            subgraph "AI Agent层"
                                B1[设计协同Agent]
                                B2[采购Agent]
                                B3[联系Agent]
                            end
                            
                            subgraph "核心引擎层"
                                C1[MCP服务引擎]
                                C2[Blender集成引擎]
                                C3[智能检索引擎]
                                C4[工作流编排引擎]
                            end
                            
                            subgraph "算法层"
                                D1[大模型推理]
                                D2[推荐算法]
                                D3[优化算法]
                                D4[多模态AI]
                            end
                            
                            subgraph "数据层"
                                E1[设计数据湖]
                                E2[供应商知识图谱]
                                E3[物料库]
                                E4[版本管理系统]
                            end
                            
                            subgraph "基础设施层"
                                F1[GPU集群]
                                F2[存储系统]
                                F3[安全网关]
                                F4[监控系统]
                            end
                            
                            A2 -.-> B2
                            B2 -.-> C2
                            C2 -.-> D2
                            D2 -.-> E2
                            E2 -.-> F2
                            
                            F2 -.-> E2
                            E2 -.-> D2
                            D2 -.-> C2
                            C2 -.-> B2
                            B2 -.-> A2
                    </div>
                </div>
            </div>

            <!-- 分阶段架构 -->
            <div class="section">
                <h2>分阶段实施架构</h2>
                
                <div class="nav-tabs">
                    <button class="nav-tab active" onclick="showTab('phase1')">第一阶段</button>
                    <button class="nav-tab" onclick="showTab('phase2')">第二阶段</button>
                    <button class="nav-tab" onclick="showTab('phase3')">第三阶段</button>
                    <button class="nav-tab" onclick="showTab('phase4')">第四阶段</button>
                </div>

                <!-- 第一阶段 -->
                <div id="phase1" class="tab-content active">
                    <div class="phase-header">
                        第一阶段：AI Agent与3D设计软件协同工作流开发
                    </div>
                    <div class="diagram-container">
                        <div class="mermaid">
                            graph LR
                                subgraph "设计师工作台"
                                    A[设计师]
                                    B[自然语言输入]
                                end
                                
                                subgraph "AI Agent核心"
                                    C[意图理解]
                                    D[设计知识引擎]
                                    E[参数优化算法]
                                end
                                
                                subgraph "MCP服务层"
                                    F[MCP协议处理]
                                    G[指令转换]
                                    H[实时通信]
                                end
                                
                                subgraph "3D设计软件"
                                    I[Blender]
                                    J[建模操作]
                                    K[渲染输出]
                                end
                                
                                A --> B
                                B --> C
                                C --> D
                                D --> E
                                E --> F
                                F --> G
                                G --> H
                                H --> I
                                I --> J
                                J --> K
                                K --> A
                        </div>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4>🔧 MCP服务架构</h4>
                            <ul>
                                <li>AI Agent与Blender通信桥梁</li>
                                <li>实时双向数据交换</li>
                                <li>标准化接口适配</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>🤖 AI智能建模引擎</h4>
                            <ul>
                                <li>知识驱动设计</li>
                                <li>参数化建模助手</li>
                                <li>自动化建模流程</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>👥 人机协同界面</h4>
                            <ul>
                                <li>自然语言交互</li>
                                <li>实时反馈系统</li>
                                <li>版本控制集成</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 第二阶段 -->
                <div id="phase2" class="tab-content">
                    <div class="phase-header">
                        第二阶段：智能供应商发现与匹配系统
                    </div>
                    <div class="diagram-container">
                        <div class="mermaid">
                            graph TD
                                A[产品图纸] --> B[物料智能分析]
                                B --> C[物料聚类]
                                C --> D[需求画像生成]
                                D --> E[智能检索策略]
                                E --> F[多源数据检索]
                                F --> G[供应商数据整合]
                                G --> H[智能评估系统]
                                H --> I[匹配度评分]
                                I --> J[风险评估]
                                J --> K[自动化联系Agent]
                                K --> L[需求确认]
                                L --> M[报价询问]
                                M --> N[过程记录]
                                
                                subgraph "数据源"
                                    O[B2B平台]
                                    P[企业官网]
                                    Q[行业数据库]
                                end
                                
                                F --> O
                                F --> P
                                F --> Q
                        </div>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4>📊 物料智能分析</h4>
                            <ul>
                                <li>图纸自动解析</li>
                                <li>物料智能聚类</li>
                                <li>需求画像生成</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>🔍 智能检索策略</h4>
                            <ul>
                                <li>多维度检索</li>
                                <li>动态策略调整</li>
                                <li>多源数据整合</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>⚖️ 供应商评估</h4>
                            <ul>
                                <li>资质自动验证</li>
                                <li>匹配度评分</li>
                                <li>风险评估分析</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>🤝 自动化联系</h4>
                            <ul>
                                <li>需求确认</li>
                                <li>报价询问</li>
                                <li>全程记录追踪</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 第三阶段 -->
                <div id="phase3" class="tab-content">
                    <div class="phase-header">
                        第三阶段：私有化硬件部署与算力支撑
                    </div>
                    <div class="diagram-container">
                        <div class="mermaid">
                            graph TB
                                subgraph "国内硬件合作"
                                    A1[摩尔线程]
                                    A2[MTT S4000系列GPU]
                                    A3[本土化服务]
                                end
                                
                                subgraph "国际硬件合作"
                                    B1[NVIDIA]
                                    B2[A100/H100 GPU]
                                    B3[RTX 4090/3090]
                                end
                                
                                subgraph "标准配置"
                                    C1[AI训练集群<br/>8×A100 80GB]
                                    C2[渲染集群<br/>20×RTX 4090]
                                    C3[存储系统<br/>NVMe SSD阵列]
                                end
                                
                                subgraph "定制化服务"
                                    D1[企业规模评估]
                                    D2[硬件配置定制]
                                    D3[现场安装调试]
                                    D4[7×24小时支持]
                                end
                                
                                A1 --> C1
                                B1 --> C1
                                C1 --> D1
                                C2 --> D2
                                C3 --> D3
                                D1 --> D4
                        </div>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4>🇨🇳 国内合作伙伴</h4>
                            <ul>
                                <li>摩尔线程 MTT S4000系列</li>
                                <li>本土化服务支持</li>
                                <li>符合国产化要求</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>🌍 国际合作伙伴</h4>
                            <ul>
                                <li>NVIDIA A100/H100</li>
                                <li>RTX 4090/3090</li>
                                <li>完整CUDA生态</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>⚙️ 标准配置</h4>
                            <ul>
                                <li>AI训练集群</li>
                                <li>渲染集群</li>
                                <li>高性能存储</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>🔧 定制化服务</h4>
                            <ul>
                                <li>企业需求定制</li>
                                <li>现场安装调试</li>
                                <li>7×24小时支持</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 第四阶段 -->
                <div id="phase4" class="tab-content">
                    <div class="phase-header">
                        第四阶段：企业级安全与合规部署
                    </div>
                    <div class="diagram-container">
                        <div class="mermaid">
                            graph TB
                                subgraph "零信任安全模型"
                                    A1[最小权限访问]
                                    A2[多因子认证]
                                    A3[实时行为监控]
                                end
                                
                                subgraph "数据保护"
                                    B1[端到端加密]
                                    B2[敏感数据脱敏]
                                    B3[审计日志追踪]
                                end
                                
                                subgraph "合规标准"
                                    C1[ISO 13485]
                                    C2[GMP规范]
                                    C3[数据本地化]
                                end
                                
                                subgraph "安全防护"
                                    D1[防火墙]
                                    D2[入侵检测]
                                    D3[安全审计]
                                end
                                
                                A1 --> D1
                                A2 --> D2
                                A3 --> D3
                                B1 --> C1
                                B2 --> C2
                                B3 --> C3
                        </div>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4>🔒 零信任安全</h4>
                            <ul>
                                <li>最小权限访问控制</li>
                                <li>多因子身份认证</li>
                                <li>实时行为监控</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>🛡️ 数据保护</h4>
                            <ul>
                                <li>端到端加密</li>
                                <li>敏感数据脱敏</li>
                                <li>完整审计追踪</li>
                            </ul>
                        </div>
                        <div class="feature-card">
                            <h4>📋 合规标准</h4>
                            <ul>
                                <li>ISO 13485质量体系</li>
                                <li>GMP生产规范</li>
                                <li>数据本地化存储</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关键流程图 -->
            <div class="section">
                <h2>关键实现流程</h2>
                
                <div class="phase-header">
                    MCP驱动的人机协同设计流程
                </div>
                <div class="diagram-container">
                    <div class="mermaid">
                        sequenceDiagram
                            participant Designer as 设计师
                            participant UI as 用户界面
                            participant Agent as AI Agent
                            participant MCP as MCP服务
                            participant Blender as Blender
                            
                            Designer->>UI: 输入设计需求
                            UI->>Agent: 传递需求信息
                            Agent->>Agent: 意图理解与分析
                            Agent->>MCP: 发送建模指令
                            MCP->>Blender: 执行3D操作
                            Blender->>MCP: 返回操作结果
                            MCP->>Agent: 反馈执行状态
                            Agent->>UI: 提供优化建议
                            UI->>Designer: 显示结果与建议
                            Designer->>UI: 确认或调整
                            UI->>Agent: 传递调整指令
                            Agent->>MCP: 发送修改指令
                            MCP->>Blender: 执行修改操作
                    </div>
                </div>
                
                <div class="phase-header">
                    智能供应商发现与匹配流程
                </div>
                <div class="diagram-container">
                    <div class="mermaid">
                        flowchart TD
                            A[上传产品图纸] --> B[AI图纸解析]
                            B --> C[提取物料信息]
                            C --> D[物料分类聚类]
                            D --> E[生成采购需求]
                            E --> F[制定检索策略]
                            F --> G[多源数据检索]
                            G --> H{检索结果质量}
                            H -->|质量不佳| I[优化检索策略]
                            I --> G
                            H -->|质量良好| J[供应商数据整合]
                            J --> K[资质验证]
                            K --> L[匹配度评分]
                            L --> M[风险评估]
                            M --> N[生成推荐列表]
                            N --> O[自动化联系]
                            O --> P[需求确认]
                            P --> Q[报价收集]
                            Q --> R[结果汇总报告]
                    </div>
                </div>
            </div>

            <!-- 预期成果 -->
            <div class="section">
                <h2>项目预期成果</h2>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">40%+</div>
                        <div class="metric-label">设计效率提升</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">60%</div>
                        <div class="metric-label">供应商发现时间缩短</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">≥85%</div>
                        <div class="metric-label">供应商匹配准确率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">15-20%</div>
                        <div class="metric-label">采购成本降低</div>
                    </div>
                </div>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🚀 技术创新点</h4>
                        <ul>
                            <li>首创MCP驱动的3D设计协同工作流</li>
                            <li>多模态AI驱动的供应商智能发现系统</li>
                            <li>端到端可溯源的智能采购决策链</li>
                            <li>医疗器械行业定制化私有部署方案</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>💼 商业价值</h4>
                        <ul>
                            <li>大幅提升设计和采购效率</li>
                            <li>降低人工成本和采购成本</li>
                            <li>提高决策准确性和可靠性</li>
                            <li>增强企业竞争力</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                wrap: true
            }
        });

        // 标签页切换功能
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabId).classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }

        // 页面加载完成后重新渲染图表
        window.addEventListener('load', function() {
            setTimeout(() => {
                mermaid.init();
            }, 1000);
        });
    </script>
</body>
</html>
