<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目立项技术架构图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .diagram-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }

        .mermaid {
            text-align: center;
        }

        .phase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .phase-card {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .phase-card h3 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .phase-card ul {
            list-style: none;
        }

        .phase-card li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .phase-card li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #00b894;
            font-weight: bold;
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .tech-item {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .nav-item {
            display: block;
            padding: 8px 15px;
            text-decoration: none;
            color: #2c3e50;
            border-radius: 5px;
            margin-bottom: 5px;
            transition: background 0.3s ease;
        }

        .nav-item:hover {
            background: #3498db;
            color: white;
        }

        /* 架构图样式 */
        .architecture-diagram {
            background: #f0f8f0;
            border: 3px solid #4a90e2;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .arch-layer {
            display: flex;
            margin-bottom: 15px;
            min-height: 120px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .layer-title {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 15px;
            font-weight: bold;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 80px;
            border-radius: 10px 0 0 10px;
        }

        .layer-content {
            flex: 1;
            padding: 15px;
            gap: 15px;
        }

        /* 展现层样式 */
        .presentation-layer {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .presentation-layer .layer-content {
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            flex-wrap: wrap;
        }

        .presentation-layer .arch-component {
            flex: 1;
            min-width: 180px;
            max-width: 250px;
        }

        /* 通讯层样式 */
        .communication-layer {
            background: linear-gradient(135deg, #00cec9 0%, #00b894 100%);
        }

        .communication-layer .layer-content {
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            flex-wrap: wrap;
        }

        .communication-layer .arch-component {
            flex: 1;
            min-width: 140px;
            max-width: 180px;
        }

        /* 服务层样式 */
        .service-layer {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            flex-direction: column;
        }

        .service-layer .layer-title {
            writing-mode: horizontal-tb;
            min-width: auto;
            width: 100%;
            border-radius: 10px 10px 0 0;
            text-align: center;
            min-height: 50px;
        }

        .service-layer .layer-content {
            background: rgba(255,255,255,0.9);
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            padding: 20px;
            justify-content: stretch;
        }

        /* 数据层样式 */
        .data-layer {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        }

        .data-layer .layer-content {
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            flex-wrap: wrap;
        }

        .data-layer .arch-component {
            flex: 1;
            min-width: 160px;
            max-width: 200px;
        }

        /* AI/算法层样式 */
        .ai-layer {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
            flex-direction: column;
        }

        .ai-layer .layer-title {
            writing-mode: horizontal-tb;
            min-width: auto;
            width: 100%;
            border-radius: 10px 10px 0 0;
            text-align: center;
            min-height: 50px;
        }

        .ai-layer .layer-content {
            background: rgba(255,255,255,0.9);
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            padding: 20px;
            justify-content: stretch;
        }

        /* AI特殊组件样式 */
        .ai-agent-group {
            background: #fff5f5;
            border-color: #e53e3e;
        }

        .ai-agent-group .group-title {
            color: #c53030;
        }

        .algorithm-group {
            background: #f0fff4;
            border-color: #38a169;
        }

        .algorithm-group .group-title {
            color: #2f855a;
        }

        .analysis-group {
            background: #ebf8ff;
            border-color: #3182ce;
        }

        .analysis-group .group-title {
            color: #2c5282;
        }

        .knowledge-group {
            background: #faf5ff;
            border-color: #805ad5;
        }

        .knowledge-group .group-title {
            color: #553c9a;
        }

        /* MCP和集成引擎特殊样式 */
        .mcp-group {
            background: #fffbeb;
            border-color: #d69e2e;
        }

        .mcp-group .group-title {
            color: #b7791f;
        }

        .integration-group {
            background: #f7fafc;
            border-color: #4a5568;
        }

        .integration-group .group-title {
            color: #2d3748;
        }

        /* 基础设施层样式 */
        .infrastructure-layer {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
        }

        .infrastructure-layer .layer-content {
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            flex-wrap: wrap;
        }

        .infrastructure-layer .arch-component {
            flex: 1;
            min-width: 180px;
            max-width: 250px;
        }

        .infrastructure {
            background: #f8f9fa !important;
            border-color: #6c757d !important;
            color: #495057 !important;
        }

        .arch-component {
            background: #ffffff;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex: 1;
            min-width: 150px;
        }

        .arch-component:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .component-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .component-desc {
            font-size: 0.85rem;
            color: #7f8c8d;
        }

        /* 服务层特殊组件 */
        .service-group {
            background: #f8f9fa;
            border: 2px dashed #6c5ce7;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            height: 100%;
        }

        .group-title {
            font-weight: bold;
            color: #6c5ce7;
            text-align: center;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .gateway-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            background: #fff3cd;
            border-color: #ffc107;
        }

        .gateway-group .group-title {
            color: #856404;
        }

        .gateway {
            background: #fff3cd !important;
            border-color: #ffc107 !important;
            color: #856404 !important;
            font-weight: bold;
        }

        .business-group {
            background: #d1ecf1;
            border-color: #17a2b8;
        }

        .business-group .group-title {
            color: #0c5460;
        }

        .business-cluster {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }

        .business-cluster .arch-component {
            flex: 1;
        }

        .spring-cloud-components {
            grid-column: 1 / -1;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 15px;
            justify-content: space-between;
        }

        .spring-cloud-components .arch-component {
            flex: 1;
            min-width: 200px;
            background: #e8f5e8;
            border-color: #28a745;
            color: #155724;
        }

        .framework-list {
            grid-column: 1 / -1;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            font-style: italic;
            color: #6c757d;
            margin-top: 10px;
        }

        .database {
            background: #fff5f5 !important;
            border-color: #e53e3e !important;
        }

        .db-icon {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .navigation {
                position: static;
                margin-bottom: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 20px;
            }

            .service-layer .layer-content {
                grid-template-columns: 1fr 1fr;
            }

            .ai-layer .layer-content {
                grid-template-columns: 1fr 1fr;
            }

            .layer-content {
                flex-direction: column;
            }

            .spring-cloud-components {
                flex-direction: column;
            }

            .business-cluster {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#overall" class="nav-item">整体架构</a>
        <a href="#phases" class="nav-item">分阶段实施</a>
        <a href="#flows" class="nav-item">关键流程</a>
        <a href="#tech" class="nav-item">技术栈</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>项目立项技术架构图</h1>
            <p>清晰展示项目的技术架构、实施阶段和关键流程</p>
        </div>

        <!-- 整体技术架构 -->
        <section id="overall" class="section">
            <h2 class="section-title">整体技术架构</h2>
            
            <!-- 分层架构图 -->
            <div class="architecture-diagram">
                <!-- 展现层 -->
                <div class="arch-layer presentation-layer">
                    <div class="layer-title">展现层</div>
                                         <div class="layer-content">
                         <div class="arch-component web">
                             <div class="component-title">3D设计协同界面</div>
                             <div class="component-desc">(Blender集成, MCP协议)</div>
                         </div>
                         <div class="arch-component app">
                             <div class="component-title">供应商管理看板</div>
                             <div class="component-desc">(移动端, Web端)</div>
                         </div>
                         <div class="arch-component wechat">
                             <div class="component-title">AI协同界面</div>
                             <div class="component-desc">自然语言交互</div>
                         </div>
                         <div class="arch-component restful">
                             <div class="component-title">API网关</div>
                             <div class="component-desc">统一接口管理</div>
                         </div>
                     </div>
                </div>

                <!-- 通讯层 -->
                <div class="arch-layer communication-layer">
                    <div class="layer-title">通讯层</div>
                    <div class="layer-content">
                        <div class="arch-component">CDN</div>
                        <div class="arch-component">SLB</div>
                        <div class="arch-component">Netty</div>
                        <div class="arch-component">Socket</div>
                        <div class="arch-component">HTTP/HTTPS</div>
                    </div>
                </div>

                <!-- AI/算法层 -->
                <div class="arch-layer ai-layer">
                    <div class="layer-title">AI/算法层</div>
                    <div class="layer-content">
                        <!-- AI Agent -->
                        <div class="service-group ai-agent-group">
                            <div class="group-title">AI Agent</div>
                            <div class="arch-component">设计协同Agent<br/>MCP驱动建模</div>
                            <div class="arch-component">采购Agent<br/>智能决策</div>
                            <div class="arch-component">联系Agent<br/>自动化沟通</div>
                        </div>

                        <!-- 核心算法引擎 -->
                        <div class="service-group algorithm-group">
                            <div class="group-title">核心算法引擎</div>
                            <div class="arch-component">大模型推理<br/>LLM/多模态AI</div>
                            <div class="arch-component">推荐算法<br/>供应商匹配</div>
                            <div class="arch-component">优化算法<br/>参数优化</div>
                        </div>

                        <!-- 智能分析 -->
                        <div class="service-group analysis-group">
                            <div class="group-title">智能分析</div>
                            <div class="arch-component">物料分析<br/>图纸解析</div>
                            <div class="arch-component">需求画像<br/>智能聚类</div>
                            <div class="arch-component">风险评估<br/>供应商评分</div>
                        </div>

                        <!-- 知识引擎 -->
                        <div class="service-group knowledge-group">
                            <div class="group-title">知识引擎</div>
                            <div class="arch-component">知识图谱<br/>关系推理</div>
                            <div class="arch-component">向量检索<br/>语义匹配</div>
                            <div class="arch-component">设计知识库<br/>经验积累</div>
                        </div>
                    </div>
                </div>

                <!-- 服务层 -->
                <div class="arch-layer service-layer">
                    <div class="layer-title">服务层</div>
                                         <div class="layer-content">
                         <!-- MCP服务引擎 -->
                         <div class="service-group mcp-group">
                             <div class="group-title">MCP服务引擎</div>
                             <div class="arch-component">MCP协议服务<br/>AI-软件桥梁</div>
                             <div class="arch-component">工作流编排<br/>任务调度</div>
                             <div class="arch-component">实时通信<br/>双向数据交换</div>
                         </div>

                         <!-- 集成引擎 -->
                         <div class="service-group integration-group">
                             <div class="group-title">集成引擎</div>
                             <div class="arch-component">Blender集成<br/>Python API</div>
                             <div class="arch-component">多源检索<br/>数据整合</div>
                         </div>

                         <!-- 业务服务 -->
                         <div class="service-group business-group">
                             <div class="group-title">业务服务</div>
                             <div class="business-cluster">
                                 <div class="arch-component">设计服务</div>
                                 <div class="arch-component">采购服务</div>
                             </div>
                             <div class="business-cluster">
                                 <div class="arch-component">供应商服务</div>
                                 <div class="arch-component">仿真服务</div>
                             </div>
                         </div>

                         <!-- 系统管理 -->
                         <div class="service-group config-group">
                             <div class="group-title">系统管理</div>
                             <div class="arch-component">权限管理<br/>零信任架构</div>
                             <div class="arch-component">监控告警<br/>性能追踪</div>
                             <div class="arch-component">配置中心<br/>参数管理</div>
                         </div>
                     </div>

                    <!-- Spring Cloud 组件 -->
                    <div class="spring-cloud-components">
                        <div class="arch-component">Spring Cloud Bus<br/>消息总线</div>
                        <div class="arch-component">Cloud Data Flow<br/>大数据操作</div>
                        <div class="arch-component">Cloud Task<br/>任务调度</div>
                        <div class="arch-component">Cloud Data Stream<br/>数据流操作</div>
                    </div>

                    <div class="framework-list">
                        Netflix Eureka、Netflix Zuul、Spring Cloud Config、Spring Cloud Bus......
                    </div>
                </div>

                <!-- 数据层 -->
                <div class="arch-layer data-layer">
                    <div class="layer-title">数据层</div>
                                         <div class="layer-content">
                         <div class="arch-component database">
                             <div class="db-icon">🧠</div>
                             <div>设计数据湖<br/>3D模型/版本管理</div>
                         </div>
                         <div class="arch-component database">
                             <div class="db-icon">🕸️</div>
                             <div>供应商知识图谱<br/>关系映射</div>
                         </div>
                         <div class="arch-component database">
                             <div class="db-icon">📦</div>
                             <div>物料库<br/>规格标准化</div>
                         </div>
                         <div class="arch-component database">
                             <div class="db-icon">🔍</div>
                             <div>向量数据库<br/>语义检索</div>
                         </div>
                                          </div>
                 </div>

                 <!-- 基础设施层 -->
                 <div class="arch-layer infrastructure-layer">
                     <div class="layer-title">基础设施层</div>
                     <div class="layer-content">
                         <div class="arch-component infrastructure">
                             <div class="component-title">GPU集群</div>
                             <div class="component-desc">A100/H100 AI训练<br/>RTX 4090 渲染加速</div>
                         </div>
                         <div class="arch-component infrastructure">
                             <div class="component-title">存储系统</div>
                             <div class="component-desc">NVMe SSD阵列<br/>高速数据访问</div>
                         </div>
                         <div class="arch-component infrastructure">
                             <div class="component-title">安全网关</div>
                             <div class="component-desc">零信任架构<br/>端到端加密</div>
                         </div>
                         <div class="arch-component infrastructure">
                             <div class="component-title">私有化部署</div>
                             <div class="component-desc">本地算力<br/>数据安全</div>
                         </div>
                     </div>
                 </div>
             </div>
         </section>

        <!-- 分阶段实施架构 -->
        <section id="phases" class="section">
            <h2 class="section-title">分阶段实施架构</h2>
            
            <div class="phase-grid">
                <div class="phase-card">
                    <h3>第一阶段：基础架构</h3>
                    <ul>
                        <li>搭建基础开发环境</li>
                        <li>建立CI/CD流水线</li>
                        <li>部署基础服务框架</li>
                        <li>实现用户认证系统</li>
                        <li>建立数据库架构</li>
                    </ul>
                </div>
                
                <div class="phase-card">
                    <h3>第二阶段：核心功能</h3>
                    <ul>
                        <li>开发核心业务服务</li>
                        <li>实现API网关</li>
                        <li>集成缓存系统</li>
                        <li>开发前端界面</li>
                        <li>建立监控体系</li>
                    </ul>
                </div>
                
                <div class="phase-card">
                    <h3>第三阶段：扩展功能</h3>
                    <ul>
                        <li>集成第三方服务</li>
                        <li>实现搜索功能</li>
                        <li>添加文件处理</li>
                        <li>优化性能</li>
                        <li>增强安全性</li>
                    </ul>
                </div>
                
                <div class="phase-card">
                    <h3>第四阶段：优化部署</h3>
                    <ul>
                        <li>容器化部署</li>
                        <li>自动扩缩容</li>
                        <li>灾备方案</li>
                        <li>性能调优</li>
                        <li>上线发布</li>
                    </ul>
                </div>
            </div>

            <div class="diagram-container">
                <div class="mermaid">
                    gantt
                        title 项目实施时间线
                        dateFormat  YYYY-MM-DD
                        section 第一阶段
                        基础架构搭建    :a1, 2024-01-01, 30d
                        CI/CD建立      :a2, after a1, 15d
                        section 第二阶段
                        核心功能开发    :b1, after a2, 45d
                        前端开发       :b2, after a2, 40d
                        section 第三阶段
                        扩展功能       :c1, after b1, 30d
                        性能优化       :c2, after b2, 25d
                        section 第四阶段
                        部署上线       :d1, after c1, 20d
                        运维监控       :d2, after c2, 15d
                </div>
            </div>
        </section>

        <!-- 关键节点流程图 -->
        <section id="flows" class="section">
            <h2 class="section-title">关键节点实现流程</h2>
            
            <h3 style="margin: 30px 0 15px 0; color: #2c3e50;">用户注册登录流程</h3>
            <div class="diagram-container">
                <div class="mermaid">
                    sequenceDiagram
                        participant U as 用户
                        participant F as 前端
                        participant G as API网关
                        participant A as 认证服务
                        participant D as 数据库
                        participant R as Redis缓存
                        
                        U->>F: 输入注册信息
                        F->>G: 发送注册请求
                        G->>A: 转发请求
                        A->>D: 检查用户是否存在
                        D-->>A: 返回查询结果
                        A->>D: 创建用户记录
                        A->>R: 缓存用户信息
                        A-->>G: 返回注册结果
                        G-->>F: 返回响应
                        F-->>U: 显示注册成功
                        
                        U->>F: 输入登录信息
                        F->>G: 发送登录请求
                        G->>A: 转发请求
                        A->>R: 检查缓存
                        alt 缓存命中
                            R-->>A: 返回用户信息
                        else 缓存未命中
                            A->>D: 查询数据库
                            D-->>A: 返回用户信息
                            A->>R: 更新缓存
                        end
                        A-->>G: 返回JWT Token
                        G-->>F: 返回登录结果
                        F-->>U: 登录成功
                </div>
            </div>

            <h3 style="margin: 30px 0 15px 0; color: #2c3e50;">业务处理流程</h3>
            <div class="diagram-container">
                <div class="mermaid">
                    flowchart TD
                        A[用户请求] --> B{身份验证}
                        B -->|验证失败| C[返回401错误]
                        B -->|验证成功| D[参数校验]
                        D -->|校验失败| E[返回400错误]
                        D -->|校验成功| F[业务逻辑处理]
                        F --> G{需要数据库操作?}
                        G -->|是| H[数据库事务]
                        G -->|否| I[内存处理]
                        H --> J{事务成功?}
                        J -->|失败| K[回滚事务]
                        J -->|成功| L[更新缓存]
                        I --> L
                        K --> M[返回500错误]
                        L --> N[记录日志]
                        N --> O[返回成功结果]
                        
                        style A fill:#e1f5fe
                        style O fill:#c8e6c9
                        style C fill:#ffcdd2
                        style E fill:#ffcdd2
                        style M fill:#ffcdd2
                </div>
            </div>

            <h3 style="margin: 30px 0 15px 0; color: #2c3e50;">部署发布流程</h3>
            <div class="diagram-container">
                <div class="mermaid">
                    flowchart LR
                        A[代码提交] --> B[触发CI]
                        B --> C[代码检查]
                        C --> D[单元测试]
                        D --> E[构建镜像]
                        E --> F[推送镜像仓库]
                        F --> G[部署到测试环境]
                        G --> H[集成测试]
                        H --> I{测试通过?}
                        I -->|否| J[修复问题]
                        J --> A
                        I -->|是| K[部署到预生产]
                        K --> L[性能测试]
                        L --> M{性能达标?}
                        M -->|否| N[性能优化]
                        N --> A
                        M -->|是| O[部署到生产环境]
                        O --> P[健康检查]
                        P --> Q[发布完成]
                        
                        style A fill:#e3f2fd
                        style Q fill:#e8f5e8
                        style J fill:#fff3e0
                        style N fill:#fff3e0
                </div>
            </div>
        </section>

        <!-- 技术栈选型 -->
        <section id="tech" class="section">
            <h2 class="section-title">技术栈选型</h2>
            
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>AI技术</h4>
                    <p>大语言模型 + 多模态AI</p>
                </div>
                <div class="tech-item">
                    <h4>MCP协议</h4>
                    <p>Model Context Protocol</p>
                </div>
                <div class="tech-item">
                    <h4>3D集成</h4>
                    <p>Blender Python API</p>
                </div>
                <div class="tech-item">
                    <h4>算法引擎</h4>
                    <p>推荐算法 + 优化算法</p>
                </div>
                <div class="tech-item">
                    <h4>知识图谱</h4>
                    <p>Neo4j + 向量数据库</p>
                </div>
                <div class="tech-item">
                    <h4>GPU计算</h4>
                    <p>CUDA + 分布式训练</p>
                </div>
                <div class="tech-item">
                    <h4>安全架构</h4>
                    <p>零信任 + 端到端加密</p>
                </div>
                <div class="tech-item">
                    <h4>私有部署</h4>
                    <p>本地算力 + 数据安全</p>
                </div>
            </div>

            <div class="diagram-container">
                <div class="mermaid">
                    mindmap
                        root((智能化医疗器械设计平台))
                            展现层
                                3D设计协同界面
                                供应商管理看板
                                AI协同界面
                                API网关
                            AI算法层
                                AI Agent
                                    设计协同Agent
                                    采购Agent
                                    联系Agent
                                核心算法引擎
                                    大模型推理
                                    推荐算法
                                    优化算法
                                智能分析
                                    物料分析
                                    需求画像
                                    风险评估
                                知识引擎
                                    知识图谱
                                    向量检索
                                    设计知识库
                            服务层
                                MCP服务引擎
                                    MCP协议服务
                                    工作流编排
                                    实时通信
                                集成引擎
                                    Blender集成
                                    多源检索
                                业务服务
                                    设计服务
                                    采购服务
                                    供应商服务
                                    仿真服务
                            数据层
                                设计数据湖
                                供应商知识图谱
                                物料库
                                向量数据库
                            基础设施层
                                GPU集群
                                存储系统
                                安全网关
                                私有化部署
                </div>
            </div>
        </section>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                wrap: true
            },
            gantt: {
                useMaxWidth: true
            }
        });

        // 平滑滚动
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 添加滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });
    </script>
</body>
</html>