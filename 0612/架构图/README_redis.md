# Redis字段操作脚本

这是一个功能完整的Python脚本，用于操作Redis中特定key的字段，支持覆盖、读取、删除等多种操作。

## 功能特性

- ✅ **字段覆盖**: 设置或覆盖Hash字段值
- 📖 **字段读取**: 获取单个或所有字段值
- 🗑️ **字段删除**: 删除指定字段
- 🔍 **存在性检查**: 检查字段是否存在
- 📊 **增量操作**: 对数值字段进行增减操作
- 🔄 **批量更新**: 一次性更新多个字段
- 🎯 **JSON支持**: 自动处理JSON格式的复杂数据
- 🛡️ **错误处理**: 完善的异常处理和用户友好的提示

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装Redis包：

```bash
pip install redis
```

## 使用方法

### 1. 命令行使用

#### 基本语法
```bash
python redis_field_operator.py [连接参数] <命令> <参数>
```

#### 连接参数（可选）
- `--host`: Redis服务器地址（默认: localhost）
- `--port`: Redis端口（默认: 6379）
- `--db`: 数据库编号（默认: 0）
- `--password`: Redis密码

#### 支持的命令

**设置字段值（覆盖模式）**
```bash
# 设置简单字符串
python redis_field_operator.py set user:1001 name "张三"

# 设置数字
python redis_field_operator.py set user:1001 age 25

# 设置JSON对象
python redis_field_operator.py set user:1001 profile '{"city": "北京", "hobby": ["读书", "游泳"]}'
```

**获取字段值**
```bash
# 获取单个字段
python redis_field_operator.py get user:1001 name

# 获取所有字段
python redis_field_operator.py getall user:1001
```

**删除字段**
```bash
python redis_field_operator.py delete user:1001 age
```

**检查字段存在性**
```bash
python redis_field_operator.py exists user:1001 name
```

**数值增量操作**
```bash
# 增加1（默认）
python redis_field_operator.py incr user:1001 login_count

# 增加指定值
python redis_field_operator.py incr user:1001 score 10.5

# 减少值（使用负数）
python redis_field_operator.py incr user:1001 balance -50
```

**使用自定义连接参数**
```bash
python redis_field_operator.py --host ************* --port 6379 --db 1 --password mypass set mykey field value
```

### 2. 编程接口使用

```python
from redis_field_operator import RedisFieldOperator

# 创建操作实例
operator = RedisFieldOperator(
    host='localhost',
    port=6379,
    db=0,
    password=None  # 如果需要密码
)

# 设置字段
operator.set_field('user:1001', 'name', '张三')
operator.set_field('user:1001', 'age', 25)
operator.set_field('user:1001', 'profile', {
    'city': '北京',
    'hobby': ['读书', '游泳'],
    'vip': True
})

# 批量更新字段
operator.update_fields('user:1001', {
    'name': '李四',
    'age': 30,
    'tags': ['VIP', '活跃用户'],
    'last_login': '2024-06-13 11:51:00'
})

# 获取字段
name = operator.get_field('user:1001', 'name')
print(f"用户名: {name}")

# 获取所有字段
all_fields = operator.get_all_fields('user:1001')
print(f"所有字段: {all_fields}")

# 检查字段存在性
if operator.field_exists('user:1001', 'email'):
    print("邮箱字段存在")
else:
    print("邮箱字段不存在")

# 数值增量操作
new_count = operator.increment_field('user:1001', 'login_count', 1)
print(f"新的登录次数: {new_count}")

# 删除字段
operator.delete_field('user:1001', 'temp_field')
```

## 数据类型支持

脚本自动处理以下数据类型：

- **字符串**: 直接存储
- **数字**: 整数和浮点数
- **字典**: 自动转换为JSON字符串存储
- **列表**: 自动转换为JSON字符串存储
- **布尔值**: 作为JSON的一部分存储

读取时会自动尝试解析JSON格式的数据。

## 输出说明

脚本使用emoji和颜色标识不同的操作结果：

- ✅ 操作成功
- ❌ 操作失败
- ⚠️ 警告信息（如字段不存在）
- 📄 数据读取
- 📋 列表显示
- 🔍 查询操作
- 💡 提示信息

## 错误处理

脚本包含完善的错误处理机制：

1. **连接错误**: 自动检测Redis连接状态
2. **数据格式错误**: 智能处理JSON解析失败
3. **操作错误**: 捕获并显示具体的Redis操作错误
4. **参数错误**: 命令行参数验证

## 使用场景

### 1. 用户信息管理
```python
# 用户注册
operator.update_fields('user:1001', {
    'name': '张三',
    'email': '<EMAIL>',
    'age': 25,
    'created_at': '2024-06-13',
    'profile': {
        'city': '北京',
        'interests': ['技术', '旅游']
    }
})

# 更新用户信息（覆盖模式）
operator.set_field('user:1001', 'last_login', '2024-06-13 11:51:00')
operator.increment_field('user:1001', 'login_count', 1)
```

### 2. 配置管理
```python
# 应用配置
operator.update_fields('app:config', {
    'debug': True,
    'max_connections': 100,
    'features': ['feature_a', 'feature_b'],
    'database': {
        'host': 'localhost',
        'port': 5432
    }
})
```

### 3. 缓存管理
```python
# 缓存用户会话
operator.set_field('session:abc123', 'user_id', 1001)
operator.set_field('session:abc123', 'expires_at', '2024-06-14 11:51:00')

# 更新缓存
operator.update_fields('cache:product:123', {
    'name': '商品名称',
    'price': 99.99,
    'stock': 50,
    'updated_at': '2024-06-13 11:51:00'
})
```

## 注意事项

1. **覆盖模式**: `set_field` 和 `update_fields` 都是覆盖模式，会替换现有值
2. **JSON处理**: 复杂数据类型（字典、列表）会自动转换为JSON存储
3. **数值操作**: `increment_field` 只能用于数值字段
4. **连接安全**: 生产环境建议使用密码和SSL连接
5. **性能考虑**: 批量操作时优先使用 `update_fields` 而不是多次调用 `set_field`

## 故障排除

### 常见问题

**1. 连接失败**
```
❌ Redis连接失败: [Errno 61] Connection refused
```
解决方案：检查Redis服务是否启动，确认主机地址和端口正确。

**2. 认证失败**
```
❌ Redis连接失败: NOAUTH Authentication required
```
解决方案：使用 `--password` 参数提供正确的密码。

**3. JSON格式错误**
```
❌ 设置字段失败: ...
```
解决方案：检查JSON格式是否正确，确保引号和括号匹配。

### 调试技巧

1. 使用 `getall` 命令查看key的所有字段
2. 使用 `exists` 命令确认字段是否存在
3. 检查Redis日志获取详细错误信息

## 扩展功能

可以根据需要扩展以下功能：

- 字段过期时间设置
- 条件更新（仅当字段不存在时设置）
- 字段重命名
- 模式匹配字段操作
- 事务支持
- 管道操作优化

---

**作者**: AI Assistant  
**版本**: 1.0  
**更新时间**: 2024-06-13