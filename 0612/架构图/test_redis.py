from redis_field_operator import RedisFieldOperator
import json
import redis
from datetime import datetime

# 创建操作实例
operator = RedisFieldOperator(
    host='r-uf6805sowwjiimkgospd.redis.rds.aliyuncs.com', 
    port=6379, 
    db=8, 
    password='video_app_prod:video_app@2023'
)

# 直接使用Redis客户端处理JSON列表数据
redis_client = redis.Redis(
    host='r-uf6805sowwjiimkgospd.redis.rds.aliyuncs.com',
    port=6379,
    db=8,
    password='video_app_prod:video_app@2023',
    decode_responses=True
)

def backup_messages(session_id):
    """备份消息数据"""
    key = f"supplier_messages:{session_id}"
    data = redis_client.get(key)
    if data:
        with open(f"backup_{session_id}.json", 'w', encoding='utf-8') as f:
            f.write(data)
        print(f"✅ 已备份消息数据到 backup_{session_id}.json")
        return data
    else:
        print(f"⚠️  未找到消息数据: {key}")
        return None

def set_messages(session_id, messages_list):
    """设置消息列表（覆盖模式）"""
    key = f"supplier_messages:{session_id}"
    json_data = json.dumps(messages_list, ensure_ascii=False, indent=2)
    redis_client.set(key, json_data)
    print(f"✅ 已更新消息数据: {key}")
    print(f"📄 消息数量: {len(messages_list)}")

def get_messages(session_id):
    """获取消息列表"""
    key = f"supplier_messages:{session_id}"
    data = redis_client.get(key)
    if data:
        try:
            messages = json.loads(data)
            print(f"📋 获取消息数据: {key}")
            print(f"📄 消息数量: {len(messages)}")
            for i, msg in enumerate(messages):
                print(f"  [{i}] {msg.get('role', 'unknown')}: {msg.get('content', '')[:50]}...")
            return messages
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return None
    else:
        print(f"⚠️  未找到消息数据: {key}")
        return None

def add_message(session_id, message):
    """添加单条消息到列表"""
    messages = get_messages(session_id) or []
    messages.append(message)
    set_messages(session_id, messages)
    print(f"✅ 已添加消息到 {session_id}")

def update_message_by_index(session_id, index, new_message):
    """根据索引更新消息"""
    messages = get_messages(session_id)
    if messages and 0 <= index < len(messages):
        messages[index] = new_message
        set_messages(session_id, messages)
        print(f"✅ 已更新索引 {index} 的消息")
    else:
        print(f"❌ 索引 {index} 超出范围或消息不存在")

# 示例使用
if __name__ == "__main__":
    # 示例session_id
    session_id = "AOPU_1_user001"
    
    print("=== Redis消息列表操作示例 ===")
    print()
    
    # 1. 备份原数据
    print("1. 备份原数据...")
    # backup_messages(session_id)
    print()
    
    # 2. 创建新的消息列表
    print("2. 设置新的消息列表...")
    new_messages = [
        {
            "id": "msg_001",
            "role": "user",
            "content": "修改后的用户消息",
            "timestamp": "2025-06-13T11:30:00",
            "metadata": {}
        },
        {
            "id": "msg_002",
            "role": "assistant",
            "content": "修改后的AI回复",
            "timestamp": "2025-06-13T11:30:01",
            "metadata": {
                "agent": "supplier_capability_agent",
                "processing_time": 2.163799
            }
        }
    ]
    
    set_messages(session_id, new_messages)
    print()
    
    # 3. 查询消息
    print("3. 查询消息列表...")
    messages = get_messages(session_id)
    print()
    
    # 4. 添加新消息
    # print("4. 添加新消息...")
    # new_msg = {
    #     "id": "msg_003",
    #     "role": "user",
    #     "content": "这是新添加的消息",
    #     "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
    #     "metadata": {}
    # }
    # add_message(session_id, new_msg)
    # print()
    
    # 5. 更新指定索引的消息
    # print("5. 更新索引1的消息...")
    # updated_msg = {
    #     "id": "msg_002_updated",
    #     "role": "assistant",
    #     "content": "这是更新后的AI回复",
    #     "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
    #     "metadata": {
    #         "agent": "supplier_capability_agent",
    #         "processing_time": 1.875518,
    #         "updated": True
    #     }
    # }
    # update_message_by_index(session_id, 1, updated_msg)
    # print()
    
    # 6. 最终查询结果
    # print("6. 最终消息列表...")
    # final_messages = get_messages(session_id)
    
    print()
    print("=== 操作完成 ===")