<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能化医疗器械设计平台 - 技术架构图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
            padding: 20px;
            line-height: 1.4;
        }

        .architecture-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border: 2px solid #4a90e2;
            border-radius: 10px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 24px;
            font-weight: bold;
        }

        .arch-layer {
            display: flex;
            border-bottom: 1px solid #ddd;
            min-height: 120px;
        }

        .arch-layer:last-child {
            border-bottom: none;
        }

        .layer-title {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 10px;
            font-weight: bold;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 70px;
            text-align: center;
        }

        .layer-content {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-evenly;
            gap: 12px;
        }

        .arch-component {
            background: #ffffff;
            border: 2px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            font-weight: 500;
            font-size: 13px;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex: 1;
            min-width: 140px;
            max-width: 200px;
        }

        .component-title {
            font-weight: bold;
            margin-bottom: 3px;
            color: #2c3e50;
            font-size: 14px;
        }

        .component-desc {
            font-size: 11px;
            color: #7f8c8d;
            line-height: 1.2;
        }

        /* 展现层样式 */
        .presentation-layer {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .presentation-layer .layer-content {
            background: rgba(255,255,255,0.95);
        }

        /* 通讯层样式 */
        .communication-layer {
            background: linear-gradient(135deg, #00cec9 0%, #00b894 100%);
        }

        .communication-layer .layer-content {
            background: rgba(255,255,255,0.95);
        }

        /* AI/算法层样式 */
        .ai-layer {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
            flex-direction: column;
        }

        .ai-layer .layer-title {
            writing-mode: horizontal-tb;
            min-width: auto;
            width: 100%;
            text-align: center;
            min-height: 40px;
            padding: 10px;
        }

        .ai-layer .layer-content {
            background: rgba(255,255,255,0.95);
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 12px;
            padding: 15px;
        }

        /* 服务层样式 */
        .service-layer {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            flex-direction: column;
        }

        .service-layer .layer-title {
            writing-mode: horizontal-tb;
            min-width: auto;
            width: 100%;
            text-align: center;
            min-height: 40px;
            padding: 10px;
        }

        .service-layer .layer-content {
            background: rgba(255,255,255,0.95);
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 12px;
            padding: 15px;
        }

        /* 数据层样式 */
        .data-layer {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        }

        .data-layer .layer-content {
            background: rgba(255,255,255,0.95);
        }

        /* 基础设施层样式 */
        .infrastructure-layer {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
        }

        .infrastructure-layer .layer-content {
            background: rgba(255,255,255,0.95);
        }

        /* 服务组样式 */
        .service-group {
            background: #f8f9fa;
            border: 2px dashed #6c5ce7;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }

        .group-title {
            font-weight: bold;
            color: #6c5ce7;
            text-align: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        /* AI特殊组件样式 */
        .ai-agent-group {
            background: #fff5f5;
            border-color: #e53e3e;
        }

        .ai-agent-group .group-title {
            color: #c53030;
        }

        .algorithm-group {
            background: #f0fff4;
            border-color: #38a169;
        }

        .algorithm-group .group-title {
            color: #2f855a;
        }

        .analysis-group {
            background: #ebf8ff;
            border-color: #3182ce;
        }

        .analysis-group .group-title {
            color: #2c5282;
        }

        .knowledge-group {
            background: #faf5ff;
            border-color: #805ad5;
        }

        .knowledge-group .group-title {
            color: #553c9a;
        }

        /* MCP和集成引擎特殊样式 */
        .mcp-group {
            background: #fffbeb;
            border-color: #d69e2e;
        }

        .mcp-group .group-title {
            color: #b7791f;
        }

        .integration-group {
            background: #f7fafc;
            border-color: #4a5568;
        }

        .integration-group .group-title {
            color: #2d3748;
        }

        .business-group {
            background: #d1ecf1;
            border-color: #17a2b8;
        }

        .business-group .group-title {
            color: #0c5460;
        }

        .business-cluster {
            display: flex;
            gap: 8px;
            justify-content: space-between;
        }

        .business-cluster .arch-component {
            flex: 1;
            min-width: auto;
        }

        .database {
            background: #fff5f5 !important;
            border-color: #e53e3e !important;
        }

        .db-icon {
            font-size: 18px;
            margin-bottom: 3px;
        }

        .infrastructure {
            background: #f8f9fa !important;
            border-color: #6c757d !important;
            color: #495057 !important;
        }

        /* 打印样式 */
        @media print {
            body {
                padding: 0;
            }
            
            .architecture-container {
                max-width: none;
                border: none;
                border-radius: 0;
            }
        }

        /* 导出提示 */
        .export-tips {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <!-- <div class="header">
            智能化医疗器械设计与供应链一体化平台 - 技术架构图
        </div> -->

        <!-- 展现层 -->
        <div class="arch-layer presentation-layer">
            <div class="layer-title">展现层</div>
            <div class="layer-content">
                <div class="arch-component">
                    <div class="component-title">3D设计协同界面</div>
                    <div class="component-desc">(Blender集成, MCP协议)</div>
                </div>
                <div class="arch-component">
                    <div class="component-title">供应商管理看板</div>
                    <div class="component-desc">(移动端, Web端)</div>
                </div>
                <div class="arch-component">
                    <div class="component-title">AI协同界面</div>
                    <div class="component-desc">自然语言交互</div>
                </div>
                <div class="arch-component">
                    <div class="component-title">API网关</div>
                    <div class="component-desc">统一接口管理</div>
                </div>
            </div>
        </div>

        <!-- 通讯层 -->
        <div class="arch-layer communication-layer">
            <div class="layer-title">通讯层</div>
            <div class="layer-content">
                <div class="arch-component">CDN</div>
                <div class="arch-component">SLB</div>
                <div class="arch-component">Netty</div>
                <div class="arch-component">Socket</div>
                <div class="arch-component">HTTP/HTTPS</div>
            </div>
        </div>

        <!-- AI/算法层 -->
        <div class="arch-layer ai-layer">
            <div class="layer-title">AI/算法层</div>
            <div class="layer-content">
                <!-- AI Agent -->
                <div class="service-group ai-agent-group">
                    <div class="group-title">AI Agent</div>
                    <div class="arch-component">设计协同Agent<br/>MCP驱动建模</div>
                    <div class="arch-component">采购Agent<br/>智能决策</div>
                    <div class="arch-component">联系Agent<br/>自动化沟通</div>
                </div>

                <!-- 核心算法引擎 -->
                <div class="service-group algorithm-group">
                    <div class="group-title">核心算法引擎</div>
                    <div class="arch-component">大模型推理<br/>LLM/多模态AI</div>
                    <div class="arch-component">推荐算法<br/>供应商匹配</div>
                    <div class="arch-component">优化算法<br/>参数优化</div>
                </div>

                <!-- 智能分析 -->
                <div class="service-group analysis-group">
                    <div class="group-title">智能分析</div>
                    <div class="arch-component">物料分析<br/>图纸解析</div>
                    <div class="arch-component">需求画像<br/>智能聚类</div>
                    <div class="arch-component">风险评估<br/>供应商评分</div>
                </div>

                <!-- 知识引擎 -->
                <div class="service-group knowledge-group">
                    <div class="group-title">知识引擎</div>
                    <div class="arch-component">知识图谱<br/>关系推理</div>
                    <div class="arch-component">向量检索<br/>语义匹配</div>
                    <div class="arch-component">设计知识库<br/>经验积累</div>
                </div>
            </div>
        </div>

        <!-- 服务层 -->
        <div class="arch-layer service-layer">
            <div class="layer-title">服务层</div>
            <div class="layer-content">
                <!-- MCP服务引擎 -->
                <div class="service-group mcp-group">
                    <div class="group-title">MCP服务引擎</div>
                    <div class="arch-component">MCP协议服务<br/>AI-软件桥梁</div>
                    <div class="arch-component">工作流编排<br/>任务调度</div>
                    <div class="arch-component">实时通信<br/>双向数据交换</div>
                </div>

                <!-- 集成引擎 -->
                <div class="service-group integration-group">
                    <div class="group-title">集成引擎</div>
                    <div class="arch-component">Blender集成<br/>Python API</div>
                    <div class="arch-component">多源检索<br/>数据整合</div>
                </div>

                <!-- 业务服务 -->
                <div class="service-group business-group">
                    <div class="group-title">业务服务</div>
                    <div class="business-cluster">
                        <div class="arch-component">设计服务</div>
                        <div class="arch-component">采购服务</div>
                    </div>
                    <div class="business-cluster">
                        <div class="arch-component">供应商服务</div>
                        <div class="arch-component">仿真服务</div>
                    </div>
                </div>

                <!-- 系统管理 -->
                <div class="service-group config-group">
                    <div class="group-title">系统管理</div>
                    <div class="arch-component">权限管理<br/>零信任架构</div>
                    <div class="arch-component">监控告警<br/>性能追踪</div>
                    <div class="arch-component">配置中心<br/>参数管理</div>
                </div>
            </div>
        </div>

        <!-- 数据层 -->
        <div class="arch-layer data-layer">
            <div class="layer-title">数据层</div>
            <div class="layer-content">
                <div class="arch-component database">
                    <div class="db-icon">🧠</div>
                    <div>设计数据湖<br/>3D模型/版本管理</div>
                </div>
                <div class="arch-component database">
                    <div class="db-icon">🕸️</div>
                    <div>供应商知识图谱<br/>关系映射</div>
                </div>
                <div class="arch-component database">
                    <div class="db-icon">📦</div>
                    <div>物料库<br/>规格标准化</div>
                </div>
                <div class="arch-component database">
                    <div class="db-icon">🔍</div>
                    <div>向量数据库<br/>语义检索</div>
                </div>
            </div>
        </div>

        <!-- 基础设施层 -->
        <div class="arch-layer infrastructure-layer">
            <div class="layer-title">基础设施层</div>
            <div class="layer-content">
                <div class="arch-component infrastructure">
                    <div class="component-title">GPU集群</div>
                    <div class="component-desc">A100/H100 AI训练<br/>RTX 4090 渲染加速</div>
                </div>
                <div class="arch-component infrastructure">
                    <div class="component-title">存储系统</div>
                    <div class="component-desc">NVMe SSD阵列<br/>高速数据访问</div>
                </div>
                <div class="arch-component infrastructure">
                    <div class="component-title">安全网关</div>
                    <div class="component-desc">零信任架构<br/>端到端加密</div>
                </div>
                <div class="arch-component infrastructure">
                    <div class="component-title">私有化部署</div>
                    <div class="component-desc">本地算力<br/>数据安全</div>
                </div>
            </div>
        </div>

        <!-- <div class="export-tips">
            💡 导出提示：在浏览器中按 Ctrl+P (Windows) 或 Cmd+P (Mac) 可以打印或保存为PDF。也可以使用浏览器的截图功能或第三方截图工具获取高清图片。
        </div> -->
    </div>

    <script>
        // 页面加载完成后自动调整布局
        window.addEventListener('load', function() {
            console.log('架构图已加载完成，可以进行截图或打印导出');
        });
    </script>
</body>
</html> 