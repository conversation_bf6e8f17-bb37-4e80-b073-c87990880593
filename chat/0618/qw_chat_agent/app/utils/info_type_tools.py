import pandas as pd
from typing import Dict, Any, List, Union
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(current_dir, "../..", "data", "cy3000")

def get_material_spec_info(supplier_name: str, component_codes: List[str] = None, need_num: int = 10) -> str:
    """根据供应商名称和物料编码获取物料规格信息"""
    supplier_info_df = pd.read_excel(os.path.join(data_dir, "supplier.xlsx"), sheet_name="供应链数据总览")
    supplier_info_spec = supplier_info_df[supplier_info_df["推荐供应商"] == supplier_name]
    if supplier_info_spec.empty:
        return {"material_need": []}
    
    material_types = list()
    for row in supplier_info_spec.itertuples():
        # print(row)
        if not hasattr(row, '一级分类') or not hasattr(row, '二级分类'):
            continue
        first_type = row.一级分类
        second_type = row.二级分类
        # print(first_type, second_type)
        if first_type and second_type and (first_type, second_type) not in material_types:
            material_types.append((first_type, second_type))
    print(material_types)

    material_df = pd.read_excel(os.path.join(data_dir, "oss_material.xlsx"))
    material_need = list()
    for material_type in material_types:
        first_type, second_type = material_type
        material_need_df = material_df[(material_df["一级分类"] == first_type) & (material_df["二级分类"] == second_type)]
        if not material_need_df.empty:
            for row in material_need_df.itertuples():
                if pd.isna(row.文档名称) or "未找到匹配文件" in row.文档名称:
                    continue

                tmp = dict()
                tmp['component_name'] = row.物料名称
                tmp['component_spec'] = row.物料规格
                tmp['component_code'] = row.物料编码
                tmp['component_type'] = row.材料
                tmp['component_num'] = int(row.数量) * need_num
                tmp['file_name'] = row.文档名称
                tmp['oss_path'] = row.OSS路径[1:-1].split(",") if row.OSS路径 != '[]' else []
                # print(type(tmp['OSS路径']), tmp['OSS路径']) # [path1, path2]
                if tmp not in material_need:
                    material_need.append(tmp)
    # print(material_need)
    print(len(material_need))

    return {"material_need": material_need, "material_types": material_types}

if __name__ == "__main__":
    print(get_material_spec_info("大连鸿升机械有限公司"))


