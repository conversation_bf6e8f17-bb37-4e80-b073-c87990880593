"""
后台任务管理器
用于处理需要延迟执行的任务，如文件清理
"""

import asyncio
import threading
import time
from typing import Dict, Any, Callable, Optional
from datetime import datetime, timedelta
import logging
from concurrent.futures import ThreadPoolExecutor


class BackgroundTaskManager:
    """后台任务管理器"""
    
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.scheduled_tasks: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(__name__)
        self._running = True
        
        # 启动调度器线程
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
    
    def schedule_cleanup(self, file_path: str, delay_seconds: int = 300) -> str:
        """
        调度文件清理任务
        
        Args:
            file_path: 要清理的文件路径
            delay_seconds: 延迟秒数，默认5分钟
            
        Returns:
            str: 任务ID
        """
        task_id = f"cleanup_{int(time.time())}_{hash(file_path)}"
        execute_time = datetime.now() + timedelta(seconds=delay_seconds)
        
        self.scheduled_tasks[task_id] = {
            "type": "cleanup",
            "file_path": file_path,
            "execute_time": execute_time,
            "created_time": datetime.now()
        }
        
        self.logger.info(f"调度清理任务: {file_path}, 执行时间: {execute_time}")
        return task_id
    
    def schedule_custom_task(self, task_func: Callable, delay_seconds: int, *args, **kwargs) -> str:
        """
        调度自定义任务
        
        Args:
            task_func: 任务函数
            delay_seconds: 延迟秒数
            *args: 任务函数参数
            **kwargs: 任务函数关键字参数
            
        Returns:
            str: 任务ID
        """
        task_id = f"custom_{int(time.time())}_{hash(str(task_func))}"
        execute_time = datetime.now() + timedelta(seconds=delay_seconds)
        
        self.scheduled_tasks[task_id] = {
            "type": "custom",
            "task_func": task_func,
            "args": args,
            "kwargs": kwargs,
            "execute_time": execute_time,
            "created_time": datetime.now()
        }
        
        self.logger.info(f"调度自定义任务: {task_func.__name__}, 执行时间: {execute_time}")
        return task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        if task_id in self.scheduled_tasks:
            del self.scheduled_tasks[task_id]
            self.logger.info(f"取消任务: {task_id}")
            return True
        return False
    
    def _scheduler_loop(self):
        """调度器循环"""
        while self._running:
            try:
                current_time = datetime.now()
                tasks_to_execute = []
                
                # 查找需要执行的任务
                for task_id, task_info in list(self.scheduled_tasks.items()):
                    if current_time >= task_info["execute_time"]:
                        tasks_to_execute.append((task_id, task_info))
                
                # 执行任务
                for task_id, task_info in tasks_to_execute:
                    self.executor.submit(self._execute_task, task_id, task_info)
                    # 从调度列表中移除
                    self.scheduled_tasks.pop(task_id, None)
                
                # 休眠30秒后再次检查
                time.sleep(30)
                
            except Exception as e:
                self.logger.error(f"调度器循环错误: {str(e)}")
                time.sleep(60)  # 出错时休眠更长时间
    
    def _execute_task(self, task_id: str, task_info: Dict[str, Any]):
        """执行任务"""
        try:
            task_type = task_info["type"]
            
            if task_type == "cleanup":
                self._execute_cleanup_task(task_info)
            elif task_type == "custom":
                self._execute_custom_task(task_info)
            
            self.logger.info(f"任务执行完成: {task_id}")
            
        except Exception as e:
            self.logger.error(f"任务执行失败 {task_id}: {str(e)}")
    
    def _execute_cleanup_task(self, task_info: Dict[str, Any]):
        """执行清理任务"""
        import os
        import shutil
        
        file_path = task_info["file_path"]
        
        try:
            if os.path.exists(file_path):
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path, ignore_errors=True)
                    self.logger.info(f"清理目录: {file_path}")
                else:
                    os.remove(file_path)
                    self.logger.info(f"清理文件: {file_path}")
            else:
                self.logger.debug(f"文件不存在，无需清理: {file_path}")
                
        except Exception as e:
            self.logger.error(f"清理文件失败 {file_path}: {str(e)}")
    
    def _execute_custom_task(self, task_info: Dict[str, Any]):
        """执行自定义任务"""
        task_func = task_info["task_func"]
        args = task_info.get("args", ())
        kwargs = task_info.get("kwargs", {})
        
        task_func(*args, **kwargs)
    
    def get_task_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        return {
            "total_scheduled_tasks": len(self.scheduled_tasks),
            "scheduled_tasks": {
                task_id: {
                    "type": task_info["type"],
                    "execute_time": task_info["execute_time"].strftime("%Y-%m-%d %H:%M:%S"),
                    "created_time": task_info["created_time"].strftime("%Y-%m-%d %H:%M:%S")
                }
                for task_id, task_info in self.scheduled_tasks.items()
            },
            "executor_status": {
                "max_workers": self.max_workers,
                "active_threads": threading.active_count()
            }
        }
    
    def shutdown(self):
        """关闭任务管理器"""
        self._running = False
        self.executor.shutdown(wait=True)
        self.logger.info("后台任务管理器已关闭")


# 全局后台任务管理器实例
background_task_manager = BackgroundTaskManager() 