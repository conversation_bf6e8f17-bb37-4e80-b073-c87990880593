from typing import Dict, Any, List
from app.services.product_category_service import ProductCategoryService
import os
import pandas as pd

current_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(current_dir, "../..", "data", "cy3000")

def get_supplier_material_info(supplier_name: str, component_code: List[str] = None) -> Dict[str, Any]:
    """获取供应商对应的物料信息"""
    supplier_info_df = pd.read_excel(os.path.join(data_dir, "supplier.xlsx"), sheet_name="供应链数据总览")
    supplier_info_spec = supplier_info_df[supplier_info_df["推荐供应商"] == supplier_name]
    if supplier_info_spec.empty:
        return {
            "material_type": "",
            "feature": ""
        }
    
    material_types = list()
    for row in supplier_info_spec.itertuples():
        # print(row)
        if not hasattr(row, '一级分类') or not hasattr(row, '二级分类'):
            continue
        first_type = row.一级分类
        second_type = row.二级分类
        # print(first_type, second_type)
        if first_type and second_type and (first_type, second_type) not in material_types:
            material_types.append((first_type, second_type))
    print(material_types)

    # 检查material_types是否为空
    if not material_types:
        return {
            "material_type": "",
            "feature": ""
        }

    product_category_service = ProductCategoryService()
    # 安全地访问第一个元素
    search_category = material_types[0][1] if material_types[0][1] else material_types[0][0]
    categories = product_category_service.get_categories_by_category(search_category)
    print(categories)

    # 检查categories是否为空
    if not categories:
        return {
            "material_type": search_category,  # 使用搜索的分类名作为备选
            "feature": ""
        }

    return {
        "material_type": categories[0].get("category", ""),
        "feature": categories[0].get("feature", "")
    }


if __name__ == "__main__":
    print(get_supplier_material_info("大连鸿升机械有限公司"))
