"""信息回复Agent

负责回答供应商的各种提问，包括：
- 公司基本信息
- 物料技术规格和图纸
- 采购需求详情
- 商务条件说明
- 其他相关信息
"""

import json
from typing import Dict, Any, List, Union
from datetime import datetime

from app.core.base_agent import (
    BaseAgent, AgentResult, AgentMessage, MessageRole, AgentConfig,
    create_success_result, create_error_result
)
from app.core.llm_client import LLMClientFactory
from app.utils.get_material_info import get_supplier_material_info
from app.utils.tool_function import extract_largest_json
from app.utils.info_type_tools import get_material_spec_info
from config.llm_config import DEFAULT_MODEL


class InfoResponseAgent(BaseAgent):
    """信息回复Agent"""
    
    def __init__(self, config: AgentConfig = None, llm_client=None):
        if config is None:
            config = AgentConfig(
                name="info_response_agent",
                description="回答供应商提问的Agent，提供公司信息、物料规格等",
                system_prompt=self._get_system_prompt(),
                # model="deepseek/deepseek-chat-v3-0324",
                model=DEFAULT_MODEL,
            )
        super().__init__(config)

        # 注入LLM客户端
        self.llm_client = llm_client
        
        # 初始化LLM客户端
        if not self.llm_client:
            try:
                from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
                self.llm_client = LLMClientFactory.create_openai_client(
                    base_url=OPENAI_BASE_URL,
                    api_key=OPENAI_API_KEY,
                    default_model=self.config.model
                )
            except ImportError:
                # 如果没有配置文件，使用环境变量
                self.llm_client = LLMClientFactory.create_from_env()
        
        self.user_prompt = self.get_user_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一名专业的采购人员，代表采购方企业与供应商进行商务沟通。你的主要任务是回答供应商关于采购需求、项目信息等方面的提问。

角色定位：
- 你代表的是采购方（买方），不是供应商（卖方）
- 供应商是向你询问采购需求的一方
- 你需要根据采购需求为供应商提供必要的项目信息

工作要求：
1. 根据供应商的问题，提供准确、专业、详细的回答
2. 保持商务沟通的礼貌和专业性
3. 只有对方明确询问时才提供相应信息，不要主动透露敏感信息
4. 如果当前信息不足以回答问题，应设置need_human为true
5. 回答要具体实用，避免模糊不清的表述
6. 记住你是采购方，不要使用供应商的语气和话术

信息类型分类：
- company_info: 采购方公司基本信息（规模、地址、资质等）
- project_info: 采购项目相关信息（项目背景、用途等）
- material_spec: 物料规格信息（图纸、技术要求、需求量等）
- personal_contact: 个人联系信息（手机号、姓名等）
- other: 其他类型信息

保密原则：
- 只在对方明确询问时才提供相应信息
- 敏感信息需要谨慎处理
- 个人联系信息只有在建立信任后才提供

沟通注意事项：
- 不要说"感谢您的关注"等供应商常用语
- 不要主动推销或介绍业务能力
- 重点是回答供应商关于采购需求的疑问
- 可以询问供应商的产品规格、价格、交期等信息

输出格式：
必须返回JSON格式：
{
    "message": "回复内容",
    "reason": "选择这种回复方式的理由",
    "info_type": "信息类型（多个用逗号分隔）",
    "need_human": false
}

示例输出：
{
    "message": "我们公司位于上海市浦东新区，是一家制造企业。目前我们有304不锈钢板材的采购需求，具体规格要求：厚度2.0mm±0.1mm，表面要求2B面，需要提供材质证明书。请问贵司能否供应此类产品？",
    "reason": "回答了公司信息询问，并主动说明采购需求以推进商务洽谈",
    "info_type": "company_info,material_spec",
    "need_human": false
}

{
    "message": "关于具体的付款条件和验收标准，我需要和公司相关部门确认后才能给您准确信息。",
    "reason": "涉及重要商务条款，当前信息不足以准确回答",
    "info_type": "other",
    "need_human": true
}        
"""

    def get_user_prompt(self) -> str:
        """获取用户提示词"""
        return """
供应商的问题：
{user_message}

回复要求：
1. 你是采购方代表，供应商是向你询问采购需求的一方
2. 根据供应商的问题，提供准确、专业、详细的回答
3. 结合对话历史，给出有针对性的回复
4. 保持商务沟通的礼貌和专业性
5. 只有对方明确询问时才提供相应信息，不要主动透露敏感信息
6. 如果当前信息不足以回答问题，应设置need_human为true
7. 如果涉及物料规格信息，只需回复"我们有相关的图纸信息，等会我发给您"类似语句即可
8. 如果涉及商务条款，需谨慎回复，设置need_human为true
9. 如果对方明确表示需要询问我们的合作方信息，需谨慎回复，设置need_human为true
10. 记住你是采购方，不要使用供应商的语气（如"感谢您的关注"、"欢迎合作"等）

信息类型说明：
- company_info: 采购方公司基本信息（规模、地址、资质等）
- project_info: 采购项目相关信息（项目背景、用途等）
- material_spec: 物料规格信息（图纸、技术要求、需求量等）
- personal_contact: 个人联系信息（手机号、姓名等）
- other: 其他类型信息

输出格式：
必须返回JSON格式：
{{
    "message": "回复内容",
    "reason": "选择这种回复方式的理由",
    "info_type": "信息类型（多个用逗号分隔）",
    "need_human": false
}}

示例输出：
{{
    "message": "我们公司位于上海市浦东新区，是一家制造企业。关于具体的技术规格，我们有详细的图纸信息，等会我发给您。请问贵司能否提供此类产品的报价和交期？",
    "reason": "回答了公司信息询问，告知有图纸资料，并反问供应商产品信息以推进洽谈",
    "info_type": "company_info,material_spec",
    "need_human": false
}}

{{
    "message": "关于具体的付款条件和商务条款，我需要和公司财务部门确认后才能给您准确信息。",
    "reason": "涉及重要商务条款，当前信息不足以准确回答",
    "info_type": "other",
    "need_human": true
}}

请根据以上要求生成专业的回复。        
"""
    
    def get_supported_types(self) -> List[str]:
        """返回支持的请求类型"""
        return [
            "info_response",
            "company_info_request",
            "material_spec_inquiry",
            "quality_requirement_question",
            "business_terms_inquiry",
            "technical_drawing_request"
        ]
    
    async def process(self, session_id: str, data: Dict[str, Any]) -> AgentResult:
        """处理信息回复请求"""
        start_time = datetime.now()
        
        try:
            # 验证输入
            if not await self.validate_input(data):
                return create_error_result("输入数据验证失败")
            
            # 预处理
            processed_data = await self.pre_process(session_id, data)
            
            # 获取用户消息和历史上下文
            user_message = processed_data.get("message", "")
            message_history = processed_data.get("message_history", [])
            supplier_info = processed_data.get("supplier_info", {})

            supplier_name = supplier_info.get("company_name", "")
            component_code = processed_data.get("component_code", [])

            material_info = get_supplier_material_info(supplier_name, component_code)
            
            # 构建对话上下文
            messages = await self._build_conversation_context(
                user_message, message_history, supplier_info, material_info
            )
            
            # 调用LLM生成回复
            response_message = await self.call_llm(messages)

            response_json = extract_largest_json(response_message.content)
            
            # 构建结果
            result = create_success_result(
                data={
                    "response": response_json.get("message", "提取大模型回复失败"),
                    "agent_type": "info_response_agent",
                    "session_id": session_id,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "info_type": response_json.get("info_type", ""),
                    "need_human": response_json.get("need_human", False),
                    "reason": response_json.get("reason", ""),
                    "supplier_name": supplier_info.get("company_name", ""),
                },
                message="信息回复处理完成"
            )
            
            # 添加响应消息到结果
            result.add_message(
                role=MessageRole.ASSISTANT,
                content=response_json.get("message", "提取大模型回复失败"),
                metadata={
                    "agent": self.name,
                    "processing_time": (datetime.now() - start_time).total_seconds(),
                    "info_type": response_json.get("info_type", "general")
                }
            )
            
            # 后处理
            result = await self.post_process(session_id, result)
            
            return result
            
        except Exception as e:
            self.log_error(f"处理信息回复失败: {str(e)}")
            return create_error_result(
                error=f"处理失败: {str(e)}",
                data={"session_id": session_id}
            )
    
    async def _build_conversation_context(
        self, 
        user_message: str, 
        message_history: Union[List[Dict[str, Any]], List[AgentMessage]], 
        supplier_info: Dict[str, Any] = None,
        material_info: Dict[str, Any] = None
    ) -> List[AgentMessage]:
        """构建对话上下文"""
        messages = []
        
        # 系统提示
        system_prompt = self.config.system_prompt
        
        # 如果有供应商信息，添加到系统提示中
        if supplier_info:
            system_prompt += f"\n\n当前供应商信息：\n{json.dumps(supplier_info, ensure_ascii=False, indent=2)}"
        
        # 添加公司信息模板
        company_info_template = self._get_company_info_template()
        system_prompt += f"\n\n公司信息参考：\n{company_info_template}"
        
        messages.append(AgentMessage(
            role=MessageRole.SYSTEM,
            content=system_prompt
        ))
        
        # 添加历史消息（最近20条）
        recent_history = message_history[-20:] if len(message_history) > 20 else message_history
        for msg in recent_history:
            if isinstance(msg, dict):
                messages.append(AgentMessage(
                    role=MessageRole(msg.get("role", "user")),
                    content=msg.get("content", "")
                ))
            elif isinstance(msg, AgentMessage):
                messages.append(msg)
        
        # 添加当前用户消息
        if user_message:
            # 格式化消息历史为字符串
            history_str = ""
            for msg in recent_history[-20:]:  # 只显示最近20条
                if isinstance(msg, dict):
                    role = msg.get("role", "user")
                    content = msg.get("content", "")
                elif isinstance(msg, AgentMessage):
                    role = msg.role
                    content = msg.content
                else:
                    continue
                history_str += f"{role}: {content}\n"
            
            user_question = self.user_prompt.format(
                # material_info=json.dumps(material_info, ensure_ascii=False, indent=2),
                user_message=user_message,
                # message_history=history_str
            )
            messages.append(AgentMessage(
                role=MessageRole.USER,
                content=user_question
            ))
        
        return messages
    
    def _get_company_info_template(self) -> str:
        """获取公司信息模板"""
        return """
公司基本信息：
- 公司名称：上海优谦智能科技有限公司
- 公司地址：中国上海市浦东新区
- 主营业务：制造业智能采购，企业智能化转型等
- 联系方式：通过平台信息联系

当前的项目信息：
- 项目名称：合作方的一个小型的医疗检测器械
- 目前处于量产前期
- 项目目标：合作方在市场上推出一款功能完善、性能优越的医疗检测设备；我们在为合作方提供专业的采购支持和解决方案
- 不能透露具体的合作方信息，如果对方执意要问，这个时候需要人工介入

个人信息
- 姓卢
- 采购经理

采购相关信息：
- 采购流程：询价 → 技术确认 → 报价单询问 → 后续商务洽谈
- 质量要求：严格按照国家标准和行业标准执行
- 付款方式：根据合作情况协商确定
- 交期要求：根据具体项目需求确定

注意：具体的敏感信息（如详细地址、联系电话等）请引导供应商通过正式渠道获取。
"""
    
    async def validate_input(self, data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        if not isinstance(data, dict):
            return False
        
        # 检查必要字段
        if "message" not in data and "message_history" not in data:
            return False
        
        return True
    
    async def pre_process(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理数据"""
        # 获取会话记忆上下文
        memory_context = await self.get_memory_context(session_id)
        
        # 合并上下文信息
        processed_data = data.copy()
        if memory_context:
            processed_data["memory_context"] = memory_context
        
        return processed_data
    
    async def post_process(self, session_id: str, result: AgentResult) -> AgentResult:
        """后处理结果"""
        
        # 开始处理不同的info_type
        if "material_spec" in result.data.get("info_type", ""):
            # 从供应商信息中获取物料信息
            supplier_name = result.data.get("supplier_name", "")
            if supplier_name:
                material_spec_info = get_material_spec_info(supplier_name)
                result.data["material_spec"] = material_spec_info
        
        # 保存会话上下文到记忆
        if result.success and result.messages:
            context_to_save = {
                "last_interaction": datetime.now().isoformat(),
                "agent_type": "info_response_agent",
                "last_response": result.messages[-1].content if result.messages else None,
                "info_type": result.data.get("info_type") if result.data else None,
                "need_human": result.data.get("need_human", False) if result.data else False
            }
            await self.save_memory_context(session_id, context_to_save)
        
        return result
    
    def get_common_questions(self) -> Dict[str, List[str]]:
        """获取常见问题分类"""
        return {
            "company_info": [
                "贵公司在哪里？",
                "公司规模多大？",
                "主要做什么业务？",
                "联系方式是什么？"
            ],
            "material_spec": [
                "具体规格要求是什么？",
                "有技术图纸吗？",
                "材质有什么要求？",
                "尺寸公差是多少？"
            ],
            "quality_req": [
                "质量标准是什么？",
                "需要什么认证？",
                "检验要求有哪些？",
                "需要提供质保书吗？"
            ],
            "business_terms": [
                "付款方式是什么？",
                "交期要求多久？",
                "包装有什么要求？",
                "运输方式如何？"
            ],
            "quantity_delivery": [
                "采购数量是多少？",
                "什么时候要货？",
                "可以分批交货吗？",
                "有长期合作计划吗？"
            ]
        }
    
    def get_response_templates(self) -> Dict[str, str]:
        """获取回复模板"""
        return {
            "company_info": "我们是一家位于{location}的{business_type}企业，主要从事{main_business}。具体的联系方式建议通过平台正式渠道获取。",
            "material_spec": "关于{material_name}的技术规格要求：{specifications}。如需详细图纸，我们可以在后续正式合作中提供。",
            "quality_req": "我们对产品质量要求严格，需要符合{standards}标准，并提供{certifications}。",
            "business_terms": "关于商务条件：付款方式为{payment_terms}，交期要求{delivery_time}，具体条款可进一步协商。",
            "quantity_delivery": "本次采购数量为{quantity}，期望交期{delivery_date}，后续可能有持续合作机会。"
        }
    
    def __str__(self) -> str:
        return f"InfoResponseAgent({self.name})"
    
    def __repr__(self) -> str:
        return f"InfoResponseAgent(name='{self.name}', supported_types={self.get_supported_types()})"


# 使用示例
if __name__ == "__main__":
    import asyncio
    from app.core.llm_client import LLMClientFactory
    
    async def test_agent():
        # 创建Agent（使用自带的初始化配置）
        agent = InfoResponseAgent()
        
        # 模拟处理请求
        test_data = {
            # "message": "请问贵公司在哪里？主要做什么业务的？",
            "message": "想问下你们这个是要用在哪里的？",
            # "message": "有具体合作方的信息吗？报价需要登记的",
            # "message": "您好，请问您贵姓？",
            "message_history": [
                {"role": "user", "content": "你好"},
                {"role": "assistant", "content": "您好，我们需要采购304不锈钢板材，想了解贵司是否有相应的供货能力？"}
            ],
            "supplier_info": {
                "company_name": "大连鸿升机械有限公司1",
            },
            "component_code": ["SS304-PLATE-001"]
        }
        
        result = await agent.process("test_session_001", test_data)
        print(f"处理结果: {result.success}")
        print(f"响应消息: {result.data.get('response') if result.data else 'None'}")
        print(f"信息类型: {result.data.get('info_type') if result.data else 'None'}")
        print(json.dumps(result.to_dict(), ensure_ascii=False, indent=2))
        print(f"错误信息: {result.error}")
    
    # 运行测试
    asyncio.run(test_agent())