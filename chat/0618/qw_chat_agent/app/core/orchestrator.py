"""
供应商询价系统核心编排器 - 简化版

核心功能：
- Agent注册和管理
- 智能路由决策
- 会话状态管理
- 人工接管支持
- LLM客户端集成
- 可选的Redis缓存支持
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from enum import Enum
import uuid

from pydantic import BaseModel, Field

from app.core.base_agent import (
    BaseAgent, AgentResult, AgentMessage, MessageRole, AgentConfig, 
    AgentStatus, AgentMetrics, create_success_result, create_error_result
)
from app.core.llm_client import LLMClientFactory, OpenAIClient
from app.utils.tool_function import extract_largest_json
from app.utils.get_material_info import get_supplier_material_info
from app.core.redis_session_manager import SessionStorageInterface, create_session_manager

# 可选的Redis支持
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class SessionStatus(str, Enum):
    """会话状态"""
    ACTIVE = "active"           # 活跃中
    HUMAN_TAKEN = "human_taken" # 已人工接管
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失败


class SessionContext(BaseModel):
    """会话上下文"""
    session_id: str
    status: SessionStatus = SessionStatus.ACTIVE
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    # 业务数据
    inquiry_data: Dict[str, Any] = Field(default_factory=dict)
    current_agent: Optional[str] = None
    
    # 消息历史 - 可能存储在Redis中
    message_history: List[AgentMessage] = Field(default_factory=list)
    
    # 人工接管相关
    human_takeover_reason: Optional[str] = None
    human_operator_id: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def add_message(self, message: AgentMessage):
        """添加消息到历史"""
        self.message_history.append(message)
        self.updated_at = datetime.now()
    
    def update_status(self, status: SessionStatus, reason: str = None):
        """更新状态"""
        self.status = status
        self.updated_at = datetime.now()  # 保持datetime类型
        if status == SessionStatus.HUMAN_TAKEN and reason:
            self.human_takeover_reason = reason
    
    def set_current_agent(self, agent_name: str):
        """设置当前处理的Agent"""
        self.current_agent = agent_name
        self.updated_at = datetime.now()  # 保持datetime类型

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "status": self.status.value,  # 确保枚举值正确序列化
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if isinstance(self.created_at, datetime) else str(self.created_at),
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S") if isinstance(self.updated_at, datetime) else str(self.updated_at),
            "inquiry_data": self.inquiry_data,
            "current_agent": self.current_agent,
            "message_history": [msg.to_dict() for msg in self.message_history],
            "human_takeover_reason": self.human_takeover_reason,
            "human_operator_id": self.human_operator_id,
            "metadata": self.metadata
        }
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S")
        }


class RouteDecision(BaseModel):
    """路由决策"""
    target_agent: str
    confidence: float = 1.0
    reason: str = ""
    need_human: bool = False  # 是否需要人工接管
    human_reason: Optional[str] = None  # 人工接管原因

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "target_agent": self.target_agent,
            "confidence": self.confidence,
            "reason": self.reason,
            "need_human": self.need_human,
            "human_reason": self.human_reason
        }


class SupplierInquiryOrchestrator:
    """供应商询价编排器 - 简化版"""
    
    def __init__(self, config: Dict[str, Any] = None, logger=None, session_manager: SessionStorageInterface = None):
        self.config = config or {}
        self.logger = logger  # 使用注入的logger
        
        # Agent管理
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_metrics: Dict[str, AgentMetrics] = {}
        
        # 会话管理 - 使用注入的会话管理器或创建默认的
        self.sessions: Dict[str, SessionContext] = {}  # 内存中的会话缓存
        self.session_manager = session_manager or create_session_manager(self.config, self.logger)
        
        # LLM客户端
        self.llm_client: Optional[OpenAIClient] = None
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化编排器"""
        # 初始化LLM客户端
        self._init_llm_client()
        
        self.log_info(f"供应商询价编排器初始化完成 (会话管理器: {type(self.session_manager).__name__})")
    
    def _init_llm_client(self):
        """初始化LLM客户端"""
        try:
            llm_config = self.config.get("llm", {})
            if llm_config:
                self.llm_client = LLMClientFactory.create_from_config(llm_config, self.logger)
            else:
                self.llm_client = LLMClientFactory.create_from_env(self.logger)
            
            self.log_info("LLM客户端初始化成功")
        except Exception as e:
            self.log_error(f"LLM客户端初始化失败: {str(e)}")
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        if self.logger:
            self.logger.info(f"[Orchestrator] {message}", **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """记录错误日志"""
        if self.logger:
            self.logger.error(f"[Orchestrator] {message}", **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """记录调试日志"""
        if self.logger:
            self.logger.debug(f"[Orchestrator] {message}", **kwargs)
    
    # Agent管理
    def register_agent(self, agent: BaseAgent):
        """注册Agent"""
        # 注入依赖
        agent.llm_client = self.llm_client
        agent.logger = self.logger
        
        # 注册到管理器
        self.agents[agent.name] = agent
        self.agent_metrics[agent.name] = AgentMetrics()
        
        self.log_info(f"Agent注册成功: {agent.name}")
    
    def unregister_agent(self, agent_name: str):
        """注销Agent"""
        if agent_name in self.agents:
            del self.agents[agent_name]
            del self.agent_metrics[agent_name]
            self.log_info(f"Agent注销成功: {agent_name}")
    
    def get_agent(self, agent_name: str) -> Optional[BaseAgent]:
        """获取Agent"""
        return self.agents.get(agent_name)
    
    def list_agents(self) -> List[str]:
        """列出所有Agent"""
        return list(self.agents.keys())
    
    # 智能路由 - 简化版（预留缓存接口）
    async def route_request(self, session_id: str, user_message: str, context: List[Dict[str, Any]] = None) -> RouteDecision:
        """智能路由决策"""
        try:
            # TODO: 这里可以添加缓存逻辑
            # cache_key = self._generate_cache_key(user_message, context)
            # cached_decision = self._get_cached_decision(cache_key)
            # if cached_decision:
            #     return cached_decision
            
            # 使用LLM进行路由决策
            decision = await self._llm_route_decision(user_message, context or [])
            
            # TODO: 缓存决策结果
            # self._cache_decision(cache_key, decision)
            
            return decision
        
        except Exception as e:
            self.log_error(f"路由决策失败: {str(e)}")
            # 返回默认路由 - 直接转人工
            return RouteDecision(
                target_agent="",
                confidence=0.5,
                reason=f"路由失败，转人工处理: {str(e)}",
                need_human=True,
                human_reason=f"系统路由失败: {str(e)}"
            )
    
    async def _llm_route_decision(self, user_message: str, context: list[Dict[str, Any]]) -> RouteDecision:
        """使用LLM进行路由决策"""
        if not self.llm_client:
            return RouteDecision(
                target_agent="",
                confidence=0.5,
                reason="LLM客户端未初始化",
                need_human=True,
                human_reason="LLM客户端未初始化，需要人工处理"
            )
        
        # 判断是否为会话开始（没有历史消息或历史消息很少）
        is_conversation_start = len(context) <= 1
        
        # 构建路由提示 - 去掉general_chat_agent
        available_agents = {
            "supplier_capability_agent": "询问供应商是否能提供某类商品的能力，以及处理会话开始时的打招呼等初始交流",
            "info_response_agent": "回答供应商的提问，如公司信息、物料图纸等",
            "quote_request_agent": "在供应商确认能力后，发送报价请求"
        }

        routing_prompt = f"""
你是供应商询价系统的智能路由器。你模拟公司采购人员与供应商进行商务沟通。根据用户消息选择最合适的Agent处理。

用户消息: {user_message}
会话上下文: {json.dumps(context, ensure_ascii=False)}
是否为会话开始: {is_conversation_start}

可用的Agent:
{json.dumps(available_agents, ensure_ascii=False, indent=2)}

系统定位：模拟公司采购人员与供应商的商务询价对话
- 核心场景：询价、报价、供应商能力了解、技术规格确认、商务条件洽谈等

判断规则:
**首先判断是否属于采购供应商主题范围：**
- 如果与采购、供应商、产品询价、商务沟通无关 -> 直接转人工 (need_human=true)
- 例：天气、娱乐、个人生活、新闻、技术无关话题等

**如果属于采购供应商主题，再选择对应Agent：**
1. **会话开始或主动询问供应商能力** -> supplier_capability_agent
   - 会话刚开始时的打招呼、问候等初始交流
   - 采购人员主动了解供应商基本情况和能力
   - 例："您好"、"你好"、"请问你们公司主要生产什么产品？"、"有ISO认证吗？"

2. **回应供应商询问** -> info_response_agent  
   - 回答供应商的反问（提供公司信息、需求规格等）
   - 例：回答"你们需要什么规格？"、"贵公司在哪里？"

3. **询问报价信息** -> quote_request_agent
   - 采购人员主动询问价格、交期、付款条件等
   - 例："这个产品什么价格？"、"交期需要多久？"

**必须转人工的情况 (need_human=true)：**
- 与采购供应商无关的任何话题（天气、娱乐、新闻等）
- 复杂专业技术问题、法律问题
- 敏感信息、争议处理
- 系统故障或无法理解的指令
- 无法明确分类到上述3个Agent的情况

**特别注意：**
- 如果是会话开始且为简单问候语，优先选择supplier_capability_agent
- 如果无法确定选择哪个Agent，直接转人工处理

请返回JSON格式:
{{
    "target_agent": "选择的Agent名称或空字符串(转人工时为空字符串)",
    "confidence": 0.95,
    "reason": "选择理由",
    "need_human": true/false,
    "human_reason": "如果需要人工，说明原因"
}}
"""

        try:
            messages = [
                AgentMessage(role=MessageRole.SYSTEM, content="你是专业的智能路由器"),
                AgentMessage(role=MessageRole.USER, content=routing_prompt)
            ]
            
            response = await self.llm_client.chat_completion(messages, temperature=0.1)
            
            # 解析响应
            decision_data = extract_largest_json(response.content)
            
            if not decision_data:
                # 如果解析失败，默认转人工
                return RouteDecision(
                    target_agent="",
                    confidence=0.5,
                    reason="JSON解析失败，转人工处理",
                    need_human=True,
                    human_reason="路由决策JSON解析失败"
                )
            
            # 检查是否需要人工处理
            need_human = decision_data.get("need_human", False)
            target_agent = decision_data.get("target_agent")
            
            if need_human or not target_agent:
                return RouteDecision(
                    target_agent="",
                    confidence=decision_data.get("confidence", 0.8),
                    reason=decision_data.get("reason", "LLM路由决策"),
                    need_human=True,
                    human_reason=decision_data.get("human_reason", "需要人工处理")
                )
            
            # 验证Agent存在
            if target_agent not in available_agents:
                return RouteDecision(
                    target_agent="",
                    confidence=0.5,
                    reason=f"Agent不存在: {target_agent}，转人工处理",
                    need_human=True,
                    human_reason=f"指定的Agent不存在: {target_agent}"
                )
            
            return RouteDecision(
                target_agent=target_agent,
                confidence=decision_data.get("confidence", 0.8),
                reason=decision_data.get("reason", "LLM路由决策"),
                need_human=False,
                human_reason=None
            )
        
        except Exception as e:
            self.log_error(f"LLM路由决策失败: {str(e)}")
            return RouteDecision(
                target_agent="",
                confidence=0.5,
                reason=f"LLM路由失败，转人工处理: {str(e)}",
                need_human=True,
                human_reason=f"LLM路由失败: {str(e)}"
            )
    
    # 会话管理
    async def create_session(self, session_id: Optional[str] = None, inquiry_data: Dict[str, Any] = None) -> str:
        """创建会话"""
        if not session_id:
            if inquiry_data and "uid" in inquiry_data and 'sid' in inquiry_data:
                session_id = inquiry_data['sid'] + "_" + inquiry_data['uid']
            else:
                session_id = str(uuid.uuid4())
        
        context = SessionContext(
            session_id=session_id,
            inquiry_data=inquiry_data or {}
        )
        
        # 保存到内存
        self.sessions[session_id] = context
        
        # 保存到会话管理器
        await self._save_session(context)
        
        self.log_info(f"会话创建成功: {session_id}")
        
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[SessionContext]:
        """获取会话"""
        # 先从内存查找
        if session_id in self.sessions:
            return self.sessions[session_id]
        
        # 从会话管理器查找
        context = await self.session_manager.load_session(session_id)
        if context:
            # 加载到内存中
            self.sessions[session_id] = context
            return context
        
        return None
    
    async def get_all_session_ids(self) -> List[str]:
        """获取所有会话ID"""
        # 获取内存中的会话ID
        memory_session_ids = set(self.sessions.keys())
        
        # 获取会话管理器中的会话ID  
        manager_session_ids = set(await self.session_manager.list_sessions())
        
        # 合并并去重
        all_session_ids = memory_session_ids.union(manager_session_ids)
        
        return list(all_session_ids)
    
    async def delete_session(self, session_id: str):
        """删除会话"""
        # 从内存删除
        if session_id in self.sessions:
            del self.sessions[session_id]
        
        # 从会话管理器删除
        await self.session_manager.delete_session(session_id)
        
        self.log_info(f"会话删除成功: {session_id}")
    
    # 在需要保存会话的地方调用会话管理器
    async def _save_session(self, session_context: SessionContext):
        """保存会话"""
        await self.session_manager.save_session(session_context)
    
    # 核心处理方法
    async def handle_message(self, session_id: str, user_message: str, request_context: Dict[str, Any] = None) -> AgentResult:
        """
        处理用户消息 - 简化版核心方法
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            request_context: 当前请求的上下文信息（如supplier_id、request_type等）
        """
        try:
            # 确保会话存在
            session_context = await self.get_session(session_id)
            if not session_context:
                session_id = await self.create_session(session_id, request_context or {})
                session_context = await self.get_session(session_id)
            
            # 检查会话状态 - 如果已被人工接管，直接返回
            if session_context.status == SessionStatus.HUMAN_TAKEN:
                return create_success_result(
                    data={
                        "human_taken": True, 
                        "operator_id": session_context.human_operator_id,
                        "takeover_reason": session_context.human_takeover_reason
                    },
                    message="会话已被人工接管"
                )
            
            # 先添加用户消息到历史（在路由决策之前）
            session_context.add_message(AgentMessage(
                role=MessageRole.USER,
                content=user_message,
                metadata=request_context  # 将请求上下文作为消息的元数据
            ))
            
            # 构建路由决策的上下文 - 使用包含当前消息的会话历史信息
            route_messages = [msg.to_dict() for msg in session_context.message_history]
            # print(f"route_messages: {route_messages}")
            
            # 路由决策 - 使用会话上下文
            decision = await self.route_request(session_id, user_message, route_messages)
            print(f"decision: {decision}")
            
            # 检查是否需要人工接管 - 直接接管，不需要等待状态
            if decision.need_human:
                session_context.update_status(SessionStatus.HUMAN_TAKEN, decision.human_reason)
                
                # 保存到会话管理器
                await self._save_session(session_context)
                
                self.log_info(f"会话转人工接管: {session_id} - 原因: {decision.human_reason}")
                
                return create_success_result(
                    data={
                        "need_human": True, 
                        "reason": decision.human_reason,
                        "human_taken": True
                    },
                    message=f"需要人工处理: {decision.human_reason}"
                )
            
            # 执行Agent处理 - 传入完整的上下文信息
            # agent_data = {
            #     "user_message": user_message,
            #     "session_context": {
            #         "session_id": session_id,
            #         "inquiry_data": session_context.inquiry_data,
            #         "message_history": [msg.dict() for msg in session_context.message_history],
            #         "metadata": session_context.metadata
            #     },
            #     "request_context": request_context or {},
            #     "decision": decision.to_dict()
            # }

            agent_data = {
                "message": user_message,
                "message_history": [msg for msg in session_context.message_history],
                "supplier_info": request_context.get("supplier_info", {}),
                "component_code": request_context.get("component_code", [])
            }
            
            result = await self._execute_agent(session_id, decision.target_agent, agent_data)
            
            # 添加AI回复消息到历史
            if result.messages:
                for msg in result.messages:
                    session_context.add_message(msg)
            
            # 检查Agent结果中是否有人工接管请求
            if result.data and result.data.get("need_human"):
                session_context.update_status(SessionStatus.HUMAN_TAKEN, result.data.get("human_reason"))
                self.log_info(f"Agent请求人工接管: {session_id} - 原因: {result.data.get('human_reason')}")
                
                # 更新结果，标记已转人工
                result.data["human_taken"] = True
                result.message = f"已转人工处理: {result.data.get('human_reason')}"
                
                # 保存会话状态（人工接管情况）
                await self._save_session(session_context)
                
                return result
            
            # 正常处理完成，保存会话状态
            await self._save_session(session_context)
            
            return result
        
        except Exception as e:
            self.log_error(f"消息处理失败: {str(e)}")
            return create_error_result(f"消息处理失败: {str(e)}")
    
    async def _execute_agent(self, session_id: str, agent_name: str, data: Dict[str, Any]) -> AgentResult:
        """执行Agent处理"""
        if agent_name not in self.agents:
            return create_error_result(f"Agent不存在: {agent_name}")
        
        agent = self.agents[agent_name]
        metrics = self.agent_metrics[agent_name]
        
        start_time = datetime.now()
        
        try:
            # 更新Agent状态
            metrics.status = AgentStatus.PROCESSING
            session_context = await self.get_session(session_id)
            if session_context:
                session_context.set_current_agent(agent_name)
            
            # 执行Agent处理
            result = await agent.process(session_id, data)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            result.processing_time = processing_time
            
            # 更新指标
            metrics.update_metrics(result.success, processing_time)
            metrics.status = AgentStatus.IDLE
            
            if session_context:
                session_context.current_agent = None
            
            self.log_info(f"Agent执行完成: {agent_name} - 成功: {result.success}")
            
            return result
        
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            metrics.update_metrics(False, processing_time)
            metrics.status = AgentStatus.ERROR
            
            self.log_error(f"Agent执行失败: {agent_name} - {str(e)}")
            return create_error_result(f"Agent执行失败: {str(e)}")
    
    # 人工接管相关 - 保留一个释放方法，用于人工主动释放
    async def release_human_takeover(self, session_id: str) -> bool:
        """释放人工接管，回到AI处理（人工主动释放时使用）"""
        session_context = await self.get_session(session_id)
        if not session_context:
            return False
        
        session_context.status = SessionStatus.ACTIVE
        session_context.human_operator_id = None
        session_context.human_takeover_reason = None
        session_context.updated_at = datetime.now()
        
        # 保存到会话管理器
        await self._save_session(session_context)
        
        self.log_info(f"会话已释放人工接管: {session_id}")
        return True
    
    async def update_session_status(self, session_id: str) -> Dict[str, Any]:
        """检验并更新指定session状态
        
        检验指定session是否处于人工接管状态，如果是则更新为激活状态。
        返回当前session的状态信息。
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含session状态信息的字典
                - session_id: 会话ID
                - status: 当前状态
                - updated: 是否进行了更新
                - previous_status: 更新前的状态（如果有更新）
                - error: 错误信息（如果有）
        """
        try:
            # 获取会话上下文
            session_context = await self.get_session(session_id)
            if not session_context:
                return {
                    "session_id": session_id,
                    "error": "会话不存在",
                    "status": None,
                    "updated": False
                }
            
            # 记录原始状态
            previous_status = session_context.status
            updated = False
            
            # 检查是否处于人工接管状态，如果是则更新为激活状态
            if session_context.status == SessionStatus.HUMAN_TAKEN:
                session_context.status = SessionStatus.ACTIVE
                session_context.human_operator_id = None
                session_context.human_takeover_reason = None
                session_context.updated_at = datetime.now()
                updated = True
                
                # 保存到会话管理器
                await self._save_session(session_context)
                
                self.log_info(f"会话状态已从人工接管更新为激活: {session_id}")
            
            # 返回状态信息
            result = {
                "session_id": session_id,
                "status": session_context.status.value,
                "updated": updated,
                "created_at": session_context.created_at.isoformat(),
                "updated_at": session_context.updated_at.isoformat()
            }
            
            if updated:
                result["previous_status"] = previous_status.value
            
            return result
            
        except Exception as e:
            error_msg = f"更新会话状态失败: {str(e)}"
            self.log_error(error_msg)
            return {
                "session_id": session_id,
                "error": error_msg,
                "status": None,
                "updated": False
            }

    async def get_human_taken_sessions(self) -> List[Dict[str, Any]]:
        """获取已被人工接管的会话列表"""
        human_sessions = []
        
        # 从内存中查找
        for session_id, context in self.sessions.items():
            if context.status == SessionStatus.HUMAN_TAKEN:
                human_sessions.append({
                    "session_id": session_id,
                    "operator_id": context.human_operator_id,
                    "takeover_reason": context.human_takeover_reason,
                    "created_at": context.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "updated_at": context.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "message_count": len(context.message_history)
                })
        
        # 从会话管理器中查找
        manager_sessions = await self.session_manager.list_sessions()
        for session_id in manager_sessions:
            if session_id not in self.sessions:  # 避免重复
                context = await self.session_manager.load_session(session_id)
                if context and context.status == SessionStatus.HUMAN_TAKEN:
                    human_sessions.append({
                        "session_id": session_id,
                        "operator_id": context.human_operator_id,
                        "takeover_reason": context.human_takeover_reason,
                        "created_at": context.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                        "updated_at": context.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                        "message_count": len(context.message_history)
                    })
        
        return human_sessions
    
    # 缓存清理接口
    async def clear_redis_cache(self, save_to_database: bool = True) -> Dict[str, Any]:
        """清理会话缓存"""
        return await self.session_manager.clear_all_sessions(save_to_database)
    
    async def clear_expired_sessions(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """清理过期会话"""
        # 先清理内存中的过期会话
        from datetime import timedelta
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        expired_memory_sessions = []
        for session_id, context in list(self.sessions.items()):
            if context.updated_at < cutoff_time and context.status != SessionStatus.HUMAN_TAKEN:
                expired_memory_sessions.append(session_id)
        
        for session_id in expired_memory_sessions:
            await self.delete_session(session_id)
        
        # 清理会话管理器中的过期会话
        # 使用clear_sessions_by_age方法替代clear_expired_sessions
        manager_result = await self.session_manager.clear_sessions_by_age(
            max_age_hours=max_age_hours, 
            exclude_statuses=["human_taken"]
        )
        
        # 合并结果
        total_cleared = len(expired_memory_sessions) + manager_result.get("cleared_sessions", 0)
        
        result = {
            "success": True,
            "cleared_sessions": total_cleared,
            "memory_cleared": len(expired_memory_sessions),
            "storage_cleared": manager_result.get("cleared_sessions", 0),
            "cutoff_time": cutoff_time.isoformat(),
            "errors": manager_result.get("errors", [])
        }
        
        self.log_info(f"过期会话清理完成: {result}")
        return result
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "agents": {
                name: {
                    "status": metrics.status,
                    "total_requests": metrics.total_requests,
                    "success_rate": metrics.success_rate,
                    "avg_processing_time": metrics.average_processing_time
                }
                for name, metrics in self.agent_metrics.items()
            },
            "sessions": {
                "memory_total": len(self.sessions),
                "by_status": {
                    status.value: len([s for s in self.sessions.values() if s.status == status])
                    for status in SessionStatus
                },
                "session_manager": type(self.session_manager).__name__
            }
        }
    
    # 预留的缓存接口
    def _generate_cache_key(self, user_message: str, context: Dict[str, Any]) -> str:
        """生成缓存键 - 预留接口"""
        # TODO: 实现缓存键生成逻辑
        pass
    
    def _get_cached_decision(self, cache_key: str) -> Optional[RouteDecision]:
        """获取缓存的路由决策 - 预留接口"""
        # TODO: 实现缓存获取逻辑
        pass
    
    def _cache_decision(self, cache_key: str, decision: RouteDecision):
        """缓存路由决策 - 预留接口"""
        # TODO: 实现缓存存储逻辑
        pass
    
    def __str__(self) -> str:
        storage_type = "会话管理器"
        return f"SupplierInquiryOrchestrator(agents={len(self.agents)}, sessions={len(self.sessions)}, storage={storage_type})"


# 修改便捷函数
def create_orchestrator(config: Dict[str, Any] = None, logger=None, session_manager: SessionStorageInterface = None) -> SupplierInquiryOrchestrator:
    """创建编排器实例"""
    return SupplierInquiryOrchestrator(config, logger, session_manager)


# 使用示例
if __name__ == "__main__":
    async def test_orchestrator():
        from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
        from config.db_config import REDIS_CONFIG
        # 创建编排器配置
        config = {
            "use_redis": True,  # 启用Redis
            "redis": REDIS_CONFIG,
            "llm": {
                "base_url": OPENAI_BASE_URL,
                "api_key": OPENAI_API_KEY,
                "default_model": "deepseek/deepseek-chat-v3-0324"
            }
        }
        
        # 创建编排器
        orchestrator = create_orchestrator(config)

        # await orchestrator.delete_session("427cebe2-a02d-4968-b58e-546177cbed4a")
        
        # 创建会话
        session_id = await orchestrator.create_session()

        request_context={
            "supplier_id": "supplier_001",
            "user_id": "user_456",
            "channel": "web",
            "ip_address": "***********"
        }
        
        # 处理消息
        result = await orchestrator.handle_message(
            session_id=session_id,
            user_message="你好",
            request_context={"supplier_id": "supplier_001"}
        )
        
        print(f"处理结果: {result.success}")
        print(json.dumps(result.to_dict(), indent=2, ensure_ascii=False))
        print(f"系统状态: {orchestrator.get_system_status()}")
        print(f"agent信息: {orchestrator.list_agents()}")
        
        # 测试清理缓存
        # clear_result = await orchestrator.clear_expired_sessions(max_age_hours=1)
        # print(f"清理结果: {clear_result}")
    
    # 运行测试
    asyncio.run(test_orchestrator())