"""
物料图纸信息模型

对应数据库表：component_documents
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class ComponentDocument(Base):
    """物料图纸信息模型"""
    
    __tablename__ = "component_documents"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='图纸ID，自动递增的主键')
    
    # 关联信息
    component_id = Column(Integer, ForeignKey('component.id', ondelete='CASCADE', onupdate='CASCADE'), 
                         nullable=False, comment='物料ID，关联component表')
    
    # 基础信息
    document_name = Column(String(255), nullable=False, comment='图纸名称，图纸的文件名称')
    oss_path = Column(String(512), nullable=False, comment='OSS存储路径，文件在OSS中的完整访问路径')
    description = Column(Text, comment='描述信息，关于图纸的说明')
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp(), comment='创建时间')
    
    # 关系定义（如果需要的话）
    # component = relationship("Component", back_populates="documents")
    
    # 索引定义
    __table_args__ = (
        Index('idx_component', 'component_id'),
        {
            'mysql_engine': 'InnoDB',
            'mysql_charset': 'utf8mb4',
            'mysql_collate': 'utf8mb4_unicode_ci'
        }
    )
    
    def __repr__(self):
        return f"<ComponentDocument(id={self.id}, component_id={self.component_id}, document_name='{self.document_name}', oss_path='{self.oss_path}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'component_id': self.component_id,
            'document_name': self.document_name,
            'oss_path': self.oss_path,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        """从字典创建实例"""
        return cls(
            component_id=data.get('component_id'),
            document_name=data.get('document_name'),
            oss_path=data.get('oss_path'),
            description=data.get('description')
        )
    
    def update_from_dict(self, data: dict):
        """从字典更新实例"""
        for key, value in data.items():
            if hasattr(self, key) and key != 'id':  # 不允许更新ID
                setattr(self, key, value) 