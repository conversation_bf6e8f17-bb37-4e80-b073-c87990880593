"""
API依赖项
"""
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.core.auth import auth_manager
from app.services.user_service import UserService
from app.models.user import UserResponse, SysUser
from app.database import get_material_db as get_db

# HTTP Bearer认证
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> SysUser:
    """获取当前用户"""
    token = credentials.credentials
    token_data = auth_manager.verify_token(token, "access")
    
    user_service = UserService(db)
    user = user_service.get_user_by_username(token_data.user_name)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_active_user(current_user: SysUser = Depends(get_current_user)) -> SysUser:
    """获取当前活跃用户"""
    if current_user.status_ != 1:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


def get_current_user_info(current_user: SysUser = Depends(get_current_active_user)) -> UserResponse:
    """获取当前用户信息（不包含敏感信息）"""
    return UserResponse(
        id=current_user.id,
        user_name=current_user.user_name,
        nick_name=current_user.nick_name,
        email_=current_user.email_,
        phone_=current_user.phone_,
        avatar_=current_user.avatar_,
        status_=current_user.status_,
        LIMIT_=current_user.LIMIT_,
        TENANT_ID=current_user.TENANT_ID,
        create_time=current_user.create_time,
        update_time=current_user.update_time,
        lastLogin_time=current_user.lastLogin_time,
        is_delete=current_user.is_delete
    )


def get_user_service(db: Session = Depends(get_db)) -> UserService:
    """获取用户服务"""
    return UserService(db) 