#!/usr/bin/env python3
"""
API测试脚本
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8650"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health(self) -> Dict[str, Any]:
        """测试健康检查"""
        url = f"{self.base_url}/health"
        
        try:
            async with self.session.get(url) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 健康检查成功: {result['status']}")
                else:
                    print(f"❌ 健康检查失败: {result}")
                return result
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return {"error": str(e)}
    
    async def debug_routes(self) -> Dict[str, Any]:
        """调试：查看所有路由"""
        url = f"{self.base_url}/debug/routes"
        
        try:
            async with self.session.get(url) as response:
                result = await response.json()
                if response.status == 200:
                    print("✅ 路由调试信息:")
                    for route in result.get("routes", []):
                        print(f"   {route['methods']} {route['path']}")
                else:
                    print(f"❌ 获取路由信息失败: {result}")
                return result
        except Exception as e:
            print(f"❌ 路由调试异常: {str(e)}")
            return {"error": str(e)}
    
    async def login(self, user_name: str, password_: str) -> Dict[str, Any]:
        """登录获取token"""
        url = f"{self.base_url}/api/v1/auth/login"
        data = {
            "user_name": user_name,
            "password_": password_
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                result = await response.json()
                if response.status == 200:
                    self.access_token = result["access_token"]
                    print(f"✅ 登录成功，获取到token: {self.access_token[:20]}...")
                else:
                    print(f"❌ 登录失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return {"error": str(e)}
    
    def get_headers(self) -> Dict[str, str]:
        """获取带认证的请求头"""
        if not self.access_token:
            raise ValueError("请先登录获取token")
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
    
    async def get_user_info(self) -> Dict[str, Any]:
        """获取当前用户信息"""
        url = f"{self.base_url}/api/v1/auth/me"
        
        try:
            async with self.session.get(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取用户信息成功: {result['user_name']}")
                else:
                    print(f"❌ 获取用户信息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取用户信息异常: {str(e)}")
            return {"error": str(e)}
    
    async def send_inquiry(self, message: str, session_id: str = None) -> Dict[str, Any]:
        """发送询价消息"""
        url = f"{self.base_url}/api/v1/inquiry/chat"
        data = {
            "message": message,
            "session_id": session_id,
            "sid": "AOPU_lhh",
            "uid": "23467",
            "supplier_info": {
                "company_name": "大连鸿升机械有限公司",
                # "contact_person": "张经理"
            },
            # "component_code": ["TEST_COMPONENT_001"]
        }
        
        try:
            async with self.session.post(url, json=data, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 发送询价消息成功")
                    print(f"   消息: {message}")
                    print(f"   会话ID: {result['session_id']}")
                    print(f"   响应: {result['response']}")
                    print(f"   Agent类型: {result.get('agent_type')}")
                    print(f"   需要人工: {result.get('need_human')}")
                    print(f"   响应类型: {result.get('response_type')}")
                    print(f"   物料规格: {result.get('material_spec')}")
                else:
                    print(f"❌ 发送询价消息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 发送询价消息异常: {str(e)}")
            return {"error": str(e)}
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        url = f"{self.base_url}/api/v1/inquiry/session/{session_id}/status"
        
        try:
            async with self.session.get(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取会话状态成功")
                    print(f"   会话ID: {result['session_id']}")
                    print(f"   状态: {result['status']}")
                    print(f"   创建时间: {result['created_at']}")
                    print(f"   更新时间: {result['updated_at']}")
                    print(f"   消息数量: {result['message_count']}")
                    print(f"   当前Agent: {result.get('current_agent')}")
                else:
                    print(f"❌ 获取会话状态失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取会话状态异常: {str(e)}")
            return {"error": str(e)}


async def test_api():
    """测试API功能"""
    # async with APITester(base_url="http://*************:8650") as tester:
    async with APITester(base_url="http://localhost:8650") as tester:
        print("=== 供应商询价系统API测试 ===\n")
        
        # 0. 测试健康检查
        print("0. 测试健康检查...")
        await tester.test_health()
        print()
        
        # 0.5. 调试路由信息
        print("0.5. 调试路由信息...")
        await tester.debug_routes()
        print()
        
        # 1. 测试登录
        print("1. 测试登录...")
        print("尝试使用默认测试账户...")
        
        # 尝试多个可能的账户
        test_accounts = [
            # ("admin", "admin123"),
            ("lhh-aopu", "bluemen.123")
        ]
        
        login_success = False
        for username, password in test_accounts:
            print(f"   尝试登录: {username}")
            result = await tester.login(username, password)
            if "access_token" in result:
                login_success = True
                break
            await asyncio.sleep(0.5)
        
        if not login_success:
            print("❌ 所有测试账户登录失败")
            print("请先运行 'python init_db.py' 创建测试用户")
            return
        
        print()
        
        # 2. 测试获取用户信息
        print("2. 测试获取用户信息...")
        await tester.get_user_info()
        print()
        
        # 3. 测试发送询价消息
        print("3. 测试发送询价消息...")

        # # 第一条消息 - 创建新会话
        result1 = await tester.send_inquiry("你好")
        session_id = result1["session_id"]
        if "session_id" in result1:
            session_id = result1["session_id"]
            print(f"   获取到会话ID: {session_id}")

            # 第二条消息 - 使用相同会话ID
            print("\n4. 测试会话连续性（使用相同session_id）...")
            result2 = await tester.send_inquiry("请问贵公司在哪里？主要做什么业务的？", session_id)

            # # 第三条消息 - 继续对话
            print("\n5. 测试更多对话内容...")
            result3 = await tester.send_inquiry("有相关图纸吗？", session_id)
        #
        #     # 验证会话状态和消息历史
        #     print("\n6. 验证Redis中的会话数据...")
        #     await tester.get_session_status(session_id)
        #
        #     print(f"\n✅ 会话测试完成，共使用会话ID: {session_id}")
        #     print("   如果Redis配置正确，这些消息应该已保存到Redis中")
        #     print("   从会话状态可以看到消息数量应该为6条（3条用户消息 + 3条AI回复）")
        # else:
        #     print("❌ 未能获取到session_id，无法测试会话连续性")
        
        print()
        print("🎉 API测试完成！")
        print("\n💡 提示：")
        print("   - 如果你想验证Redis中的session数据，可以使用Redis客户端查看")
        print("   - 键格式: session:<session_id> 和 messages:<session_id>")

        # 验证会话状态和消息历史
        print("\n6. 验证Redis中的会话数据...")
        # await tester.get_session_status("AOPU_lhh_234")
        await tester.get_session_status(session_id)

        # print(f"\n✅ 会话测试完成，共使用会话ID: {'AOPU_lhh_234'}")
        print(f"\n✅ 会话测试完成，共使用会话ID: {session_id}")
        print("   如果Redis配置正确，这些消息应该已保存到Redis中")
        print("   从会话状态可以看到消息数量应该为6条（3条用户消息 + 3条AI回复）")


if __name__ == "__main__":
    asyncio.run(test_api())