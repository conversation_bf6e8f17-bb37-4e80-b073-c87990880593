#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OSS API测试脚本
"""
import requests
import json
import os
from typing import Dict, Any

# API基础配置
BASE_URL = "http://localhost:8000/api/v1"
TEST_SUPPLIER = "深圳市亚美三兄吸塑有限公司"

def get_auth_token() -> str:
    """获取认证token（需要先登录）"""
    # 这里需要根据你的认证系统来获取token
    # 示例：
    login_data = {
        "user_name": "lhh-aopu",
        "password_": "bluemen.123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            return response.json().get("access_token", "")
        else:
            print(f"登录失败: {response.text}")
            return ""
    except Exception as e:
        print(f"登录请求失败: {str(e)}")
        return ""

def test_list_materials(token: str):
    """测试获取物料清单"""
    print("=== 测试获取物料清单 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    url = f"{BASE_URL}/oss/list-supplier-materials/{TEST_SUPPLIER}"
    
    try:
        response = requests.get(url, headers=headers, params={"need_num": 5})
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data['message']}")
            print(f"物料数量: {data['total_materials']}")
            print(f"文件数量: {data['total_files']}")
        else:
            print(f"失败: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")

def test_download_excel(token: str):
    """测试下载Excel清单"""
    print("\n=== 测试下载Excel清单 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    url = f"{BASE_URL}/oss/download-excel-only"
    
    data = {
        "supplier_name": TEST_SUPPLIER,
        "need_num": 5
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 保存Excel文件
            filename = f"{TEST_SUPPLIER}_物料清单.xlsx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"Excel文件已保存: {filename}")
            print(f"文件大小: {len(response.content)} bytes")
        else:
            print(f"失败: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")

def test_download_materials(token: str):
    """测试下载物料文件（压缩包）"""
    print("\n=== 测试下载物料文件 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    url = f"{BASE_URL}/oss/download-supplier-materials"
    
    data = {
        "supplier_name": TEST_SUPPLIER,
        "need_num": 10,  # 减少数量以便测试
        "compress_after_download": True,
        "delete_original_after_compress": True
    }
    
    try:
        print("开始下载（这可能需要一些时间）...")
        response = requests.post(url, headers=headers, json=data, timeout=300)  # 5分钟超时
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 检查是否是文件下载响应
            content_type = response.headers.get('content-type', '')
            if 'application/zip' in content_type:
                # 保存压缩文件
                filename = f"{TEST_SUPPLIER}_materials.zip"
                with open(filename, 'wb') as f:
                    f.write(response.content)
                print(f"压缩文件已保存: {filename}")
                print(f"文件大小: {len(response.content)} bytes")
                
                # 打印下载信息（如果在headers中）
                download_info = response.headers.get('X-Download-Info')
                if download_info:
                    try:
                        info = json.loads(download_info)
                        print(f"下载统计: {info}")
                    except:
                        pass
            else:
                # JSON响应
                data = response.json()
                print(f"响应: {data}")
        else:
            print(f"失败: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")

def main():
    """主测试函数"""
    print("OSS API测试开始")
    
    # 获取认证token
    print("获取认证token...")
    token = get_auth_token()
    
    if not token:
        print("无法获取认证token，跳过需要认证的测试")
        print("请确保:")
        print("1. 服务器正在运行")
        print("2. 认证信息正确")
        print("3. 数据库连接正常")
        return
    
    print(f"认证token获取成功: {token[:20]}...")
    
    # 运行测试
    test_list_materials(token)
    test_download_excel(token)
    test_download_materials(token)
    
    print("\n测试完成！")

def test_without_auth():
    """不需要认证的测试（用于调试API结构）"""
    print("=== 测试API健康检查 ===")
    
    try:
        response = requests.get(f"http://localhost:8000/health")
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"健康检查结果: {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {str(e)}")
    
    print("\n=== 测试API路由信息 ===")
    try:
        response = requests.get(f"http://localhost:8000/debug/routes")
        print(f"路由信息状态码: {response.status_code}")
        if response.status_code == 200:
            routes = response.json()["routes"]
            oss_routes = [r for r in routes if "/oss" in r["path"]]
            print("OSS相关路由:")
            for route in oss_routes:
                print(f"  {route['methods']} {route['path']}")
    except Exception as e:
        print(f"获取路由信息失败: {str(e)}")

if __name__ == "__main__":
    # 首先测试不需要认证的接口
    test_without_auth()
    
    print("\n" + "="*50)
    
    # 然后测试需要认证的接口
    main() 