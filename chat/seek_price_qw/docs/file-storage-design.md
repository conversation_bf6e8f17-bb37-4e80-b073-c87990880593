# 文件存储设计方案

## 1. 存储架构

### 1.1 整体设计思路

```mermaid
graph TB
    A[用户上传文件] --> B[本地临时存储]
    B --> C[上传到阿里云OSS]
    C --> D[返回OSS链接]
    
    E[Agent需要发送文件] --> F{文件来源}
    F -->|OSS链接| G[下载到本地]
    F -->|本地文件| H[直接使用]
    G --> I[发送给供应商]
    H --> I
    
    J[返回结果] --> K{消息类型}
    K -->|chat| L[普通文本消息]
    K -->|file| M[OSS链接]
```

### 1.2 消息类型设计

```python
from enum import Enum
from pydantic import BaseModel
from typing import Optional, Union

class MessageType(str, Enum):
    CHAT = "chat"
    FILE = "file"

class ChatContent(BaseModel):
    text: str

class FileContent(BaseModel):
    filename: str
    oss_url: str
    file_type: str
    file_size: int

class AgentResponse(BaseModel):
    message_type: MessageType
    content: Union[ChatContent, FileContent]
    metadata: Optional[dict] = None

# 使用示例
chat_response = AgentResponse(
    message_type=MessageType.CHAT,
    content=ChatContent(text="您好，我需要了解更多技术规格信息")
)

file_response = AgentResponse(
    message_type=MessageType.FILE,
    content=FileContent(
        filename="技术规格要求.pdf",
        oss_url="https://your-bucket.oss-cn-hangzhou.aliyuncs.com/files/xxx.pdf",
        file_type="application/pdf",
        file_size=1024000
    )
)
```

## 2. 阿里云OSS集成

### 2.1 OSS客户端封装

```python
# app/services/oss_service.py
import oss2
from typing import Optional
import os
from pathlib import Path
import uuid

class OSSService:
    def __init__(self, access_key_id: str, access_key_secret: str, 
                 endpoint: str, bucket_name: str):
        auth = oss2.Auth(access_key_id, access_key_secret)
        self.bucket = oss2.Bucket(auth, endpoint, bucket_name)
        self.bucket_name = bucket_name
        self.endpoint = endpoint
    
    async def upload_file(self, local_file_path: str, 
                         oss_key: Optional[str] = None) -> str:
        """上传文件到OSS"""
        if not oss_key:
            # 生成唯一的OSS key
            file_ext = Path(local_file_path).suffix
            oss_key = f"supplier-inquiry/{uuid.uuid4()}{file_ext}"
        
        # 上传文件
        result = self.bucket.put_object_from_file(oss_key, local_file_path)
        
        if result.status == 200:
            # 返回OSS URL
            return f"https://{self.bucket_name}.{self.endpoint}/{oss_key}"
        else:
            raise Exception(f"Upload failed: {result.status}")
    
    async def download_file(self, oss_url: str, local_path: str) -> str:
        """从OSS下载文件到本地"""
        # 从URL提取OSS key
        oss_key = oss_url.split(f"{self.bucket_name}.{self.endpoint}/")[1]
        
        # 确保本地目录存在
        Path(local_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 下载文件
        result = self.bucket.get_object_to_file(oss_key, local_path)
        
        if result.status == 200:
            return local_path
        else:
            raise Exception(f"Download failed: {result.status}")
    
    async def get_file_info(self, oss_url: str) -> dict:
        """获取OSS文件信息"""
        oss_key = oss_url.split(f"{self.bucket_name}.{self.endpoint}/")[1]
        
        try:
            result = self.bucket.head_object(oss_key)
            return {
                "size": result.content_length,
                "content_type": result.content_type,
                "last_modified": result.last_modified
            }
        except oss2.exceptions.NoSuchKey:
            raise Exception("File not found in OSS")
```

### 2.2 本地文件管理

```python
# app/services/file_service.py
import aiofiles
import shutil
from pathlib import Path
from typing import Optional
import uuid

class LocalFileService:
    def __init__(self, base_dir: str = "./temp_files"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
    
    async def save_uploaded_file(self, file, session_id: str) -> str:
        """保存上传的文件到本地临时目录"""
        # 创建会话目录
        session_dir = self.base_dir / session_id
        session_dir.mkdir(exist_ok=True)
        
        # 生成唯一文件名
        file_ext = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        local_path = session_dir / unique_filename
        
        # 异步保存文件
        async with aiofiles.open(local_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        return str(local_path)
    
    async def download_from_oss(self, oss_url: str, session_id: str, 
                               filename: Optional[str] = None) -> str:
        """从OSS下载文件到本地"""
        session_dir = self.base_dir / session_id / "downloads"
        session_dir.mkdir(parents=True, exist_ok=True)
        
        if not filename:
            filename = f"download_{uuid.uuid4()}"
        
        local_path = session_dir / filename
        
        # 使用OSS服务下载
        oss_service = get_oss_service()  # 从依赖注入获取
        return await oss_service.download_file(oss_url, str(local_path))
    
    def cleanup_session_files(self, session_id: str):
        """清理会话相关的临时文件"""
        session_dir = self.base_dir / session_id
        if session_dir.exists():
            shutil.rmtree(session_dir)
```

## 3. Agent文件处理

### 3.1 文件处理Agent扩展

```python
# app/agents/base.py (扩展)
from app.services.file_service import LocalFileService
from app.services.oss_service import OSSService

class BaseAgent:
    def __init__(self, config, llm_client, mcp_client, memory, 
                 file_service: LocalFileService, oss_service: OSSService):
        # ... 原有初始化代码
        self.file_service = file_service
        self.oss_service = oss_service
    
    async def send_file_to_supplier(self, oss_url: str, session_id: str, 
                                   supplier_email: str) -> AgentResponse:
        """向供应商发送文件"""
        try:
            # 1. 从OSS下载文件到本地
            local_path = await self.file_service.download_from_oss(
                oss_url, session_id
            )
            
            # 2. 发送邮件（使用MCP工具）
            result = await self.call_tool("email_sender", 
                to=supplier_email,
                subject="询价相关文档",
                content="请查看附件中的技术规格文档",
                attachments=[local_path]
            )
            
            # 3. 返回文件类型响应
            return AgentResponse(
                message_type=MessageType.FILE,
                content=FileContent(
                    filename=Path(local_path).name,
                    oss_url=oss_url,
                    file_type="application/pdf",
                    file_size=Path(local_path).stat().st_size
                ),
                metadata={"sent_to": supplier_email, "status": "sent"}
            )
            
        except Exception as e:
            # 返回错误的聊天响应
            return AgentResponse(
                message_type=MessageType.CHAT,
                content=ChatContent(text=f"文件发送失败: {str(e)}")
            )
    
    async def generate_document(self, content: str, session_id: str, 
                              filename: str) -> AgentResponse:
        """生成文档并上传到OSS"""
        try:
            # 1. 生成本地文件
            local_path = await self._create_document(content, session_id, filename)
            
            # 2. 上传到OSS
            oss_url = await self.oss_service.upload_file(local_path)
            
            # 3. 返回文件响应
            return AgentResponse(
                message_type=MessageType.FILE,
                content=FileContent(
                    filename=filename,
                    oss_url=oss_url,
                    file_type="application/pdf",
                    file_size=Path(local_path).stat().st_size
                )
            )
            
        except Exception as e:
            return AgentResponse(
                message_type=MessageType.CHAT,
                content=ChatContent(text=f"文档生成失败: {str(e)}")
            )
```

### 3.2 具体Agent实现示例

```python
# app/agents/decision_support.py
class DecisionSupportAgent(BaseAgent):
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        session_id = input_data.get("session_id")
        analysis_data = input_data.get("analysis_data")
        
        # 生成决策报告
        report_content = await self._generate_decision_report(analysis_data)
        
        # 创建PDF报告
        response = await self.generate_document(
            content=report_content,
            session_id=session_id,
            filename=f"供应商分析报告_{session_id[:8]}.pdf"
        )
        
        return AgentResult(
            success=True,
            data={"response": response},
            metadata={"agent": self.config.name}
        )
    
    async def _generate_decision_report(self, analysis_data: dict) -> str:
        """使用LLM生成决策报告内容"""
        messages = [
            Message(role="system", content=self.config.system_prompt),
            Message(role="user", content=f"请基于以下分析数据生成决策报告：{analysis_data}")
        ]
        
        response = await self.chat_with_llm(messages, use_tools=False)
        return response.content
```

## 4. API接口设计

### 4.1 文件上传接口

```python
# app/api/files.py
from fastapi import APIRouter, UploadFile, File, Depends
from app.services.file_service import LocalFileService
from app.services.oss_service import OSSService

router = APIRouter()

@router.post("/upload/{session_id}")
async def upload_file(
    session_id: str,
    file: UploadFile = File(...),
    file_service: LocalFileService = Depends(get_file_service),
    oss_service: OSSService = Depends(get_oss_service)
):
    """上传文件到OSS"""
    try:
        # 1. 保存到本地临时目录
        local_path = await file_service.save_uploaded_file(file, session_id)
        
        # 2. 上传到OSS
        oss_url = await oss_service.upload_file(local_path)
        
        # 3. 返回OSS链接
        return {
            "success": True,
            "data": {
                "filename": file.filename,
                "oss_url": oss_url,
                "file_type": file.content_type,
                "file_size": file.size
            }
        }
    
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.get("/download/{session_id}")
async def download_file(
    session_id: str,
    oss_url: str,
    file_service: LocalFileService = Depends(get_file_service)
):
    """从OSS下载文件到本地（用于Agent处理）"""
    try:
        local_path = await file_service.download_from_oss(oss_url, session_id)
        return {"success": True, "local_path": local_path}
    
    except Exception as e:
        return {"success": False, "error": str(e)}
```

### 4.2 消息接口扩展

```python
# app/api/conversation.py
@router.post("/{session_id}/message")
async def send_message(
    session_id: str,
    message: ConversationMessage,
    orchestrator: AgentOrchestrator = Depends(get_orchestrator)
):
    """发送消息给Agent"""
    try:
        # 处理消息
        result = await orchestrator.process_message(session_id, message)
        
        # 返回统一格式的响应
        return {
            "success": True,
            "data": {
                "message_type": result.response.message_type,
                "content": result.response.content,
                "metadata": result.response.metadata
            }
        }
    
    except Exception as e:
        return {"success": False, "error": str(e)}
```

## 5. 配置和部署

### 5.1 环境配置

```python
# app/config.py
class Settings(BaseSettings):
    # ... 其他配置
    
    # 阿里云OSS配置
    oss_access_key_id: str
    oss_access_key_secret: str
    oss_endpoint: str = "oss-cn-hangzhou.aliyuncs.com"
    oss_bucket_name: str
    
    # 本地文件配置
    temp_files_dir: str = "./temp_files"
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: list = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".png"]
    
    class Config:
        env_file = ".env"
```

### 5.2 依赖注入

```python
# app/dependencies.py
from functools import lru_cache

@lru_cache()
def get_oss_service() -> OSSService:
    settings = get_settings()
    return OSSService(
        access_key_id=settings.oss_access_key_id,
        access_key_secret=settings.oss_access_key_secret,
        endpoint=settings.oss_endpoint,
        bucket_name=settings.oss_bucket_name
    )

@lru_cache()
def get_file_service() -> LocalFileService:
    settings = get_settings()
    return LocalFileService(base_dir=settings.temp_files_dir)
```

## 6. 使用示例

### 6.1 前端调用示例

```javascript
// 上传文件
const uploadFile = async (sessionId, file) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`/api/files/upload/${sessionId}`, {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
};

// 处理Agent响应
const handleAgentResponse = (response) => {
    if (response.message_type === 'chat') {
        // 显示文本消息
        displayChatMessage(response.content.text);
    } else if (response.message_type === 'file') {
        // 显示文件链接
        displayFileLink(response.content.filename, response.content.oss_url);
    }
};
```

这个设计方案既满足了您的OSS存储需求，又保持了系统的灵活性。Agent可以根据需要选择返回文本消息或文件链接，用户也可以直接访问OSS链接获取文件。 