# 供应商询价多Agent系统设计方案

## 1. 场景概述

本系统专门针对向产品物料供应商进行询价的业务场景，通过多Agent协作来自动化处理供应商沟通、信息收集和报价分析等流程。

### 1.1 业务流程特点

- **双向信息交换**：供应商会询问项目信息、公司信息、物料规格等
- **多轮对话**：需要持续的信息确认和澄清
- **结构化数据收集**：需要收集和整理报价、交期、技术规格等信息
- **决策支持**：需要对比分析多个供应商的报价和能力

### 1.2 系统架构图

```mermaid
graph TB
    A[询价请求] --> B[任务分析Agent]
    B --> C{对话路由}
    
    subgraph "核心Agent群"
        D[信息收集Agent]
        E[供应商沟通Agent]
        F[报价分析Agent]
        G[决策支持Agent]
    end
    
    subgraph "支持模块"
        H[公司信息库]
        I[物料规格库]
        J[供应商档案]
        K[对话记忆]
    end
    
    C --> D
    C --> E
    C --> F
    C --> G
    
    D --> H
    D --> I
    E --> J
    E --> K
    F --> K
    G --> K
```

## 2. Agent设计

### 2.1 任务分析Agent (Task Analyzer)

**职责**：分析用户的询价需求，识别所需物料类型和关键信息

```typescript
interface TaskAnalyzerAgent {
    // 需求分析
    analyzeInquiryRequest(request: string): InquiryTask;
    
    // 信息缺失检测
    identifyMissingInfo(task: InquiryTask): string[];
    
    // 优先级排序
    prioritizeRequirements(requirements: string[]): PriorityList;
}

interface InquiryTask {
    materialType: string;          // 物料类型
    specifications: object;        // 技术规格
    quantity: number;             // 数量需求
    timeline: string;             // 时间要求
    budgetRange?: string;         // 预算范围
    qualityRequirements: string[]; // 质量要求
}
```

### 2.2 信息收集Agent (Information Collector)

**职责**：准备和管理与供应商沟通所需的各类信息

```typescript
interface InfoCollectorAgent {
    // 公司信息准备
    prepareCompanyInfo(level: 'basic' | 'detailed'): CompanyInfo;
    
    // 项目信息整理
    organizeProjectInfo(projectId: string): ProjectInfo;
    
    // 物料规格文档
    prepareMaterialSpecs(materialType: string): MaterialSpecs;
    
    // 信息脱敏处理
    sanitizeInfo(info: any, level: 'public' | 'confidential'): any;
}

interface CompanyInfo {
    name: string;
    industry: string;
    scale: string;
    certifications: string[];
    contactInfo: ContactInfo;
}

interface ProjectInfo {
    projectName?: string;
    applicationArea: string;
    volumeExpectation: string;
    timeline: Timeline;
    qualityStandards: string[];
}
```

### 2.3 供应商沟通Agent (Supplier Communicator)

**职责**：与供应商进行多轮对话，回答问题并收集报价信息

```typescript
interface SupplierCommunicatorAgent {
    // 初始询价
    sendInitialInquiry(supplier: Supplier, inquiry: InquiryTask): Promise<Response>;
    
    // 回答供应商问题
    respondToQuestions(questions: Question[]): Promise<Response[]>;
    
    // 跟进沟通
    followUp(supplierId: string, context: ConversationContext): Promise<Response>;
    
    // 信息确认
    confirmDetails(details: any): Promise<boolean>;
}

interface Question {
    type: 'company' | 'project' | 'material' | 'commercial';
    content: string;
    urgency: 'high' | 'medium' | 'low';
}

interface Response {
    content: string;
    attachments?: Attachment[];
    nextAction?: string;
}
```

### 2.4 报价分析Agent (Quote Analyzer)

**职责**：分析和比较供应商的报价和技术方案

```typescript
interface QuoteAnalyzerAgent {
    // 报价解析
    parseQuote(quoteDocument: any): ParsedQuote;
    
    // 多供应商比较
    compareQuotes(quotes: ParsedQuote[]): ComparisonResult;
    
    // 风险评估
    assessRisks(quote: ParsedQuote, supplier: Supplier): RiskAssessment;
    
    // 推荐排序
    rankSuppliers(comparisons: ComparisonResult[]): SupplierRanking;
}

interface ParsedQuote {
    supplierId: string;
    pricing: PricingInfo;
    technicalSpecs: TechnicalSpecs;
    deliveryTerms: DeliveryTerms;
    qualityAssurance: QualityInfo;
    commercialTerms: CommercialTerms;
}

interface ComparisonResult {
    priceComparison: PriceAnalysis;
    technicalComparison: TechnicalAnalysis;
    deliveryComparison: DeliveryAnalysis;
    overallScore: number;
}
```

### 2.5 决策支持Agent (Decision Support)

**职责**：提供决策建议和后续行动计划

```typescript
interface DecisionSupportAgent {
    // 生成决策报告
    generateDecisionReport(analysis: ComparisonResult[]): DecisionReport;
    
    // 推荐行动方案
    recommendActions(report: DecisionReport): ActionPlan;
    
    // 谈判建议
    suggestNegotiationPoints(topSuppliers: Supplier[]): NegotiationStrategy;
}

interface DecisionReport {
    summary: string;
    topRecommendations: Supplier[];
    keyFindings: string[];
    riskFactors: string[];
    nextSteps: string[];
}
```

## 3. 配置设计

### 3.1 系统配置

```yaml
supplier_inquiry_system:
  # Agent配置
  agents:
    task_analyzer:
      type: "task_analysis"
      prompts:
        system_prompt: |
          你是一个专业的采购需求分析专家，负责分析用户的询价需求，
          识别关键信息并检测缺失的必要信息。
      tools:
        - "material_classifier"
        - "requirement_extractor"
    
    info_collector:
      type: "information_management"
      prompts:
        system_prompt: |
          你是信息收集和整理专家，负责准备与供应商沟通所需的
          公司信息、项目信息和物料规格信息。
      data_sources:
        - "company_database"
        - "material_specs_db"
        - "project_database"
    
    supplier_communicator:
      type: "communication"
      prompts:
        system_prompt: |
          你是专业的供应商沟通专家，负责与供应商进行有效沟通，
          回答他们的问题并收集报价信息。保持专业、友好的沟通风格。
      tools:
        - "email_composer"
        - "document_generator"
      memory:
        enabled: true
        provider: "conversation_memory"
    
    quote_analyzer:
      type: "data_analysis"
      prompts:
        system_prompt: |
          你是报价分析专家，负责解析供应商报价，进行多维度比较分析，
          并评估相关风险。
      tools:
        - "price_calculator"
        - "risk_assessor"
        - "comparison_engine"
    
    decision_support:
      type: "decision_making"
      prompts:
        system_prompt: |
          你是采购决策支持专家，基于分析结果提供决策建议和行动方案。
      tools:
        - "report_generator"
        - "strategy_advisor"

  # 工具配置
  tools:
    material_classifier:
      type: "function"
      description: "物料分类和规格识别"
    
    requirement_extractor:
      type: "function"
      description: "需求信息提取"
    
    email_composer:
      type: "function"
      description: "邮件撰写工具"
    
    price_calculator:
      type: "function"
      description: "价格计算和比较"
    
    risk_assessor:
      type: "function"
      description: "供应商风险评估"

  # 数据源配置
  data_sources:
    company_database:
      type: "json"
      path: "./data/company_info.json"
    
    material_specs_db:
      type: "json"
      path: "./data/material_specs.json"
    
    supplier_database:
      type: "json"
      path: "./data/suppliers.json"

  # 记忆配置
  memory:
    conversation_memory:
      type: "structured"
      retention_period: "30d"
      fields:
        - "supplier_id"
        - "conversation_history"
        - "key_information"
        - "pending_questions"
```

### 3.2 业务流程配置

```yaml
workflow_config:
  # 流程定义
  processes:
    inquiry_process:
      steps:
        - name: "需求分析"
          agent: "task_analyzer"
          required_inputs: ["inquiry_request"]
          outputs: ["analyzed_task", "missing_info"]
        
        - name: "信息准备"
          agent: "info_collector"
          required_inputs: ["analyzed_task"]
          outputs: ["company_info", "project_info", "material_specs"]
        
        - name: "供应商沟通"
          agent: "supplier_communicator"
          required_inputs: ["company_info", "project_info", "material_specs"]
          outputs: ["supplier_responses", "quotes"]
          is_iterative: true
        
        - name: "报价分析"
          agent: "quote_analyzer"
          required_inputs: ["quotes"]
          outputs: ["analysis_results", "comparisons"]
        
        - name: "决策支持"
          agent: "decision_support"
          required_inputs: ["analysis_results", "comparisons"]
          outputs: ["decision_report", "action_plan"]

  # 路由规则
  routing_rules:
    - condition: "has_supplier_questions"
      target_agent: "supplier_communicator"
      priority: "high"
    
    - condition: "missing_company_info"
      target_agent: "info_collector"
      priority: "medium"
    
    - condition: "received_new_quote"
      target_agent: "quote_analyzer"
      priority: "high"
```

## 4. 数据模型

### 4.1 核心数据结构

```typescript
// 询价任务
interface InquirySession {
    id: string;
    status: 'active' | 'completed' | 'cancelled';
    createdAt: Date;
    updatedAt: Date;
    
    // 基础信息
    requester: UserInfo;
    task: InquiryTask;
    
    // 供应商信息
    targetSuppliers: Supplier[];
    activeConversations: Map<string, Conversation>;
    
    // 收集的数据
    quotes: Quote[];
    analysis: AnalysisResult;
    
    // 决策信息
    decision?: Decision;
    actionPlan?: ActionPlan;
}

// 对话记录
interface Conversation {
    supplierId: string;
    messages: Message[];
    status: 'pending' | 'active' | 'completed';
    lastActivity: Date;
    
    // 关键信息
    supplierQuestions: Question[];
    providedAnswers: Answer[];
    receivedQuote?: Quote;
}

// 报价信息
interface Quote {
    id: string;
    supplierId: string;
    receivedAt: Date;
    
    // 价格信息
    unitPrice: number;
    totalPrice: number;
    currency: string;
    priceValidUntil: Date;
    
    // 技术规格
    technicalSpecs: TechnicalSpecs;
    
    // 商务条款
    paymentTerms: string;
    deliveryTime: string;
    minimumOrderQuantity: number;
    
    // 质量保证
    qualityStandards: string[];
    certifications: string[];
    
    // 附加信息
    notes?: string;
    attachments?: Attachment[];
}
```

### 4.2 配置数据结构

```json
{
  "company_info": {
    "basic": {
      "name": "示例科技有限公司",
      "industry": "电子制造",
      "established": "2010",
      "employees": "100-500人",
      "location": "深圳市"
    },
    "detailed": {
      "business_scope": "消费电子产品研发制造",
      "annual_revenue": "1-5亿人民币",
      "certifications": ["ISO9001", "ISO14001"],
      "main_customers": "国内外知名品牌",
      "quality_system": "严格的质量管控体系"
    },
    "contact": {
      "department": "采购部",
      "contact_person": "张经理",
      "email": "<EMAIL>",
      "phone": "+86-755-12345678"
    }
  },
  
  "material_specs_templates": {
    "electronic_components": {
      "required_fields": ["part_number", "specifications", "package_type", "operating_temperature"],
      "optional_fields": ["brand_preference", "alternative_parts", "special_requirements"]
    },
    "mechanical_parts": {
      "required_fields": ["material", "dimensions", "tolerance", "surface_treatment"],
      "optional_fields": ["hardness", "coating", "special_properties"]
    }
  }
}
```

## 5. 实现计划

### 5.1 第一阶段：核心框架 (2周)

**目标**：建立基础的多Agent框架和核心功能

**任务清单**：
- [ ] 搭建基础的Agent框架
- [ ] 实现任务分析Agent
- [ ] 实现信息收集Agent
- [ ] 建立基础的配置系统
- [ ] 创建数据模型和存储结构

**交付物**：
- 可运行的基础框架
- 任务分析功能
- 基础信息管理功能

### 5.2 第二阶段：沟通模块 (2周)

**目标**：实现与供应商的沟通功能

**任务清单**：
- [ ] 实现供应商沟通Agent
- [ ] 建立对话记忆系统
- [ ] 实现多轮对话管理
- [ ] 添加邮件/消息发送功能
- [ ] 实现问题回答逻辑

**交付物**：
- 完整的沟通模块
- 对话管理系统
- 基础的自动回复功能

### 5.3 第三阶段：分析决策 (2周)

**目标**：实现报价分析和决策支持功能

**任务清单**：
- [ ] 实现报价分析Agent
- [ ] 实现决策支持Agent
- [ ] 建立比较分析算法
- [ ] 实现风险评估功能
- [ ] 创建报告生成功能

**交付物**：
- 报价分析系统
- 决策支持功能
- 自动化报告生成

### 5.4 第四阶段：优化完善 (1周)

**目标**：系统优化和功能完善

**任务清单**：
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 用户界面优化
- [ ] 测试和调试
- [ ] 文档完善

**交付物**：
- 完整的系统
- 用户手册
- 部署指南

## 6. 技术实现要点

### 6.1 Agent间协作机制

```typescript
class SupplierInquiryOrchestrator {
    private agents: Map<string, Agent>;
    private memory: ConversationMemory;
    private config: SystemConfig;
    
    async processInquiry(request: InquiryRequest): Promise<InquiryResult> {
        // 1. 任务分析
        const task = await this.agents.get('task_analyzer').process(request);
        
        // 2. 信息准备
        const info = await this.agents.get('info_collector').process(task);
        
        // 3. 供应商沟通（可能多轮）
        const quotes = await this.handleSupplierCommunication(info);
        
        // 4. 报价分析
        const analysis = await this.agents.get('quote_analyzer').process(quotes);
        
        // 5. 决策支持
        const decision = await this.agents.get('decision_support').process(analysis);
        
        return {
            task,
            quotes,
            analysis,
            decision
        };
    }
    
    private async handleSupplierCommunication(info: PreparedInfo): Promise<Quote[]> {
        const communicator = this.agents.get('supplier_communicator');
        const quotes: Quote[] = [];
        
        // 并行处理多个供应商
        const supplierPromises = info.targetSuppliers.map(async (supplier) => {
            let conversation = await this.initializeConversation(supplier, info);
            
            // 多轮对话处理
            while (!conversation.isComplete) {
                const response = await communicator.process(conversation);
                conversation = await this.updateConversation(conversation, response);
                
                if (response.quote) {
                    quotes.push(response.quote);
                }
            }
        });
        
        await Promise.all(supplierPromises);
        return quotes;
    }
}
```

### 6.2 记忆管理

```typescript
class ConversationMemory {
    private storage: Map<string, ConversationContext>;
    
    async saveContext(sessionId: string, context: ConversationContext): Promise<void> {
        this.storage.set(sessionId, {
            ...context,
            lastUpdated: new Date()
        });
    }
    
    async getContext(sessionId: string): Promise<ConversationContext | null> {
        return this.storage.get(sessionId) || null;
    }
    
    async updateSupplierInfo(sessionId: string, supplierId: string, info: any): Promise<void> {
        const context = await this.getContext(sessionId);
        if (context) {
            context.supplierInfo[supplierId] = {
                ...context.supplierInfo[supplierId],
                ...info
            };
            await this.saveContext(sessionId, context);
        }
    }
}
```

## 7. 使用示例

### 7.1 基础询价流程

```typescript
// 初始化系统
const inquirySystem = new SupplierInquirySystem(config);

// 提交询价请求
const request: InquiryRequest = {
    materials: [{
        type: "电子元器件",
        partNumber: "STM32F103C8T6",
        quantity: 1000,
        specifications: {
            package: "LQFP48",
            temperature: "-40°C to +85°C"
        }
    }],
    timeline: "30天内交货",
    qualityRequirements: ["原装正品", "提供COC证书"]
};

// 处理询价
const result = await inquirySystem.processInquiry(request);

// 获取结果
console.log("推荐供应商:", result.decision.recommendedSuppliers);
console.log("价格分析:", result.analysis.priceComparison);
console.log("行动计划:", result.decision.actionPlan);
```

### 7.2 交互式沟通

```typescript
// 处理供应商问题
const supplierQuestion = {
    supplierId: "supplier_001",
    question: "请问贵公司的年采购量大概是多少？",
    type: "company_info"
};

const response = await inquirySystem.handleSupplierQuestion(
    sessionId,
    supplierQuestion
);

console.log("回复内容:", response.content);
```

## 8. 部署和运维

### 8.1 系统要求

- Node.js 18+
- Redis (用于会话存储)
- 文件存储空间 (用于文档和附件)

### 8.2 配置文件

```yaml
# config/production.yaml
server:
  port: 3000
  host: "0.0.0.0"

database:
  redis:
    url: "redis://localhost:6379"
    prefix: "supplier_inquiry:"

storage:
  type: "local"
  path: "./storage"

logging:
  level: "info"
  file: "./logs/system.log"

agents:
  max_concurrent: 10
  timeout: 30000
```

### 8.3 监控指标

- 询价处理成功率
- 平均响应时间
- 供应商回复率
- 报价收集完成率
- 系统错误率

这个设计方案专门针对供应商询价场景，相比通用系统更加简洁和专业化，能够有效处理供应商沟通中的各种情况，并提供有价值的决策支持。 