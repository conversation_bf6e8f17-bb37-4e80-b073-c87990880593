# 供应商询价系统实现指南

## 1. 技术方案总结

基于您的需求，我们调整了技术栈：

### ✅ **采用的技术**
- **数据库**: MySQL 8.0+ (现有基础)
- **后端**: FastAPI + Python 3.11+
- **缓存**: Redis
- **AI框架**: 自建轻量Agent框架
- **工具协议**: MCP (Model Context Protocol)
- **LLM**: 直接调用OpenAI/Claude API

### 🔄 **兼容性设计**
- 使用SQLAlchemy ORM，支持后续迁移到PostgreSQL
- 数据库无关的UUID和JSON类型处理
- 统一的数据访问层

### 🎯 **核心优势**
1. **轻量化**: 去掉LangChain依赖，减少复杂性
2. **MCP优先**: 原生支持MCP工具调用
3. **灵活性**: 自建框架更容易定制
4. **兼容性**: 支持多种数据库和LLM

## 2. 核心架构

### 2.1 自建Agent框架特点

```python
# 核心特性
- 统一的LLM客户端接口 (OpenAI/Claude/其他)
- 原生MCP工具支持
- 灵活的消息处理机制
- 内置记忆管理
- 异步任务处理
```

### 2.2 MCP工具集成

```yaml
# MCP服务器配置示例
mcp_servers:
  context7:
    url: "https://mcp.context7.com"
    tools: ["resolve-library-id", "get-library-docs"]
  
  custom_tools:
    url: "http://localhost:8001/mcp"
    tools: ["email_sender", "price_calculator"]
```

### 2.3 数据库兼容层

```python
# 关键设计
class GUID(TypeDecorator):
    """数据库无关的UUID类型"""
    # MySQL: CHAR(36)
    # PostgreSQL: UUID
    
class JSONType(TypeDecorator):
    """数据库无关的JSON类型"""
    # MySQL: JSON
    # PostgreSQL: JSONB
```

## 3. 实现步骤

### 第一阶段：基础框架 (1-2周)

#### 3.1 搭建核心框架
```bash
# 1. 项目初始化
mkdir supplier-inquiry-system
cd supplier-inquiry-system
python -m venv venv
source venv/bin/activate

# 2. 安装基础依赖
pip install fastapi uvicorn sqlalchemy pymysql redis celery

# 3. 创建项目结构
mkdir -p app/{core,agents,models,api,services,tasks}
```

#### 3.2 实现Agent框架核心
```python
# app/core/agent_framework.py
# - LLM客户端抽象层
# - 消息处理机制
# - Agent基础类

# app/core/mcp_client.py
# - MCP协议客户端
# - 工具注册和调用
# - Context7集成
```

#### 3.3 数据库设计
```python
# app/models/base.py
# - 兼容性类型定义
# - 基础模型类

# app/models/inquiry.py
# - 询价会话模型
# - 供应商模型
# - 对话记录模型
```

### 第二阶段：Agent实现 (2-3周)

#### 3.4 实现具体Agent
```python
# app/agents/task_analyzer.py
class TaskAnalyzerAgent(BaseAgent):
    async def process(self, input_data):
        # 1. 分析询价需求
        # 2. 提取关键信息
        # 3. 识别缺失信息
        pass

# app/agents/info_collector.py
class InfoCollectorAgent(BaseAgent):
    async def process(self, input_data):
        # 1. 准备公司信息
        # 2. 整理物料规格
        # 3. 信息脱敏处理
        pass

# app/agents/communicator.py
class CommunicatorAgent(BaseAgent):
    async def process(self, input_data):
        # 1. 与供应商沟通
        # 2. 回答问题
        # 3. 收集报价
        pass
```

#### 3.5 MCP工具开发
```python
# tools/mcp_server.py
# 自定义MCP工具服务器
@app.post("/mcp/call_tool")
async def call_tool(tool_call: ToolCall):
    if tool_call.name == "email_sender":
        return await send_email(**tool_call.arguments)
    elif tool_call.name == "price_calculator":
        return await calculate_price(**tool_call.arguments)
```

### 第三阶段：集成测试 (1周)

#### 3.6 系统集成
```python
# app/core/orchestrator.py
class AgentOrchestrator:
    async def process_inquiry(self, request):
        # 1. 任务分析
        # 2. 信息收集
        # 3. 供应商沟通
        # 4. 报价分析
        # 5. 决策支持
        pass
```

#### 3.7 API接口
```python
# app/api/inquiry.py
@router.post("/create")
async def create_inquiry(request: InquiryRequest):
    # 创建询价任务
    pass

@router.get("/{session_id}/status")
async def get_inquiry_status(session_id: str):
    # 获取询价状态
    pass
```

## 4. 关键实现细节

### 4.1 LLM客户端切换
```python
# 支持多种LLM
llm_clients = {
    "openai": OpenAIClient(api_key=openai_key),
    "claude": ClaudeClient(api_key=claude_key),
}

# Agent配置中指定使用的LLM
agent_config = AgentConfig(
    name="task_analyzer",
    model="gpt-4",  # 或 "claude-3-sonnet"
    llm_provider="openai"  # 或 "claude"
)
```

### 4.2 MCP工具调用
```python
# 统一的工具调用接口
async def call_mcp_tool(tool_name: str, **kwargs):
    # 1. 解析工具名称和服务器
    server_name, actual_tool = tool_name.split("_", 1)
    
    # 2. 调用对应的MCP服务器
    client = mcp_clients[server_name]
    result = await client.call_tool(actual_tool, **kwargs)
    
    return result

# Agent中使用工具
class TaskAnalyzerAgent(BaseAgent):
    async def process(self, input_data):
        # 调用Context7工具
        library_info = await self.call_tool(
            "context7_get-library-docs",
            context7CompatibleLibraryID="/fastapi/fastapi",
            topic="database"
        )
```

### 4.3 数据库操作
```python
# 兼容多数据库的查询
async def get_inquiry_sessions(db: Session, status: str = None):
    query = db.query(InquirySession)
    
    if status:
        query = query.filter(InquirySession.status == status)
    
    # JSON字段查询 (兼容MySQL和PostgreSQL)
    if db.bind.dialect.name == "mysql":
        query = query.filter(
            InquirySession.task_info["material_type"].astext == "电子元器件"
        )
    elif db.bind.dialect.name == "postgresql":
        query = query.filter(
            InquirySession.task_info["material_type"].astext == "电子元器件"
        )
    
    return query.all()
```

## 5. 配置管理

### 5.1 环境配置
```python
# app/config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # 数据库配置
    database_url: str = "mysql+pymysql://user:pass@localhost/db"
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    
    # LLM配置
    openai_api_key: str
    claude_api_key: str
    
    # MCP服务器配置
    mcp_servers: Dict[str, Dict] = {
        "context7": {
            "url": "https://mcp.context7.com",
            "timeout": 30
        }
    }
    
    class Config:
        env_file = ".env"
```

### 5.2 Agent配置
```yaml
# config/agents.yaml
agents:
  task_analyzer:
    system_prompt: |
      你是专业的采购需求分析专家，负责分析用户的询价需求，
      识别关键信息并检测缺失的必要信息。
    model: "gpt-4"
    temperature: 0.1
    tools:
      - "context7_resolve-library-id"
      - "custom_tools_material_classifier"
  
  communicator:
    system_prompt: |
      你是专业的供应商沟通专家，负责与供应商进行有效沟通，
      回答他们的问题并收集报价信息。
    model: "claude-3-sonnet"
    temperature: 0.2
    tools:
      - "custom_tools_email_sender"
      - "context7_get-library-docs"
```

## 6. 部署方案

### 6.1 开发环境
```bash
# 1. 启动MySQL和Redis
docker-compose up -d mysql redis

# 2. 数据库迁移
alembic upgrade head

# 3. 启动MCP工具服务器
python tools/mcp_server.py

# 4. 启动主应用
uvicorn app.main:app --reload

# 5. 启动Celery Worker
celery -A app.tasks worker --loglevel=info
```

### 6.2 生产环境
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  app:
    build: .
    environment:
      - DATABASE_URL=mysql+pymysql://user:pass@mysql:3306/supplier_inquiry
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_DATABASE=supplier_inquiry
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
      - MYSQL_ROOT_PASSWORD=rootpassword
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
```

## 7. 测试策略

### 7.1 单元测试
```python
# tests/test_agents.py
async def test_task_analyzer():
    agent = TaskAnalyzerAgent(config, llm_client, mcp_client, memory)
    
    input_data = {
        "inquiry_text": "需要采购1000个STM32F103C8T6芯片",
        "session_id": "test-session"
    }
    
    result = await agent.process(input_data)
    
    assert result.success
    assert "material_type" in result.data["analyzed_task"]
```

### 7.2 集成测试
```python
# tests/test_integration.py
async def test_full_inquiry_process():
    orchestrator = AgentOrchestrator()
    
    request = InquiryRequest(
        materials=[{
            "type": "电子元器件",
            "part_number": "STM32F103C8T6",
            "quantity": 1000
        }]
    )
    
    result = await orchestrator.process_inquiry(request)
    
    assert result.decision.recommended_suppliers
    assert len(result.quotes) > 0
```

## 8. 监控和维护

### 8.1 性能监控
```python
# app/middleware/monitoring.py
from prometheus_client import Counter, Histogram

REQUEST_COUNT = Counter('requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('request_duration_seconds', 'Request duration')

@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(duration)
    
    return response
```

### 8.2 日志管理
```python
# app/utils/logger.py
import structlog

logger = structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)
```

这个实现指南基于您的需求进行了优化，既保持了技术的先进性，又考虑了现有基础设施的兼容性。您觉得这个方案如何？需要我详细展开某个部分吗？ 