# 供应商询价多Agent系统完整实施计划

## 1. 项目概述

### 1.1 系统目标
构建一个基于多Agent的智能供应商询价系统，自动化处理供应商沟通、信息收集、报价分析和决策支持等全流程。

### 1.2 核心特性
- **智能对话**: 多轮供应商沟通，自动回答问题
- **文件处理**: 支持技术文档、报价单等文件交换
- **数据分析**: 自动化报价比较和风险评估
- **决策支持**: 生成采购建议和行动计划

### 1.3 技术架构

```mermaid
graph TB
    A[询价请求] --> B[任务分析Agent]
    B --> C{智能路由器}
    
    subgraph "核心Agent群"
        D[信息收集Agent]
        E[供应商沟通Agent]
        F[报价分析Agent]
        G[决策支持Agent]
    end
    
    subgraph "支持模块"
        H[公司信息库]
        I[物料规格库]
        J[供应商档案]
        K[对话记忆]
    end
    
    subgraph "工具层"
        L[MCP工具服务器]
        M[Context7集成]
        N[邮件服务]
        O[文件处理]
    end
    
    subgraph "数据层"
        P[MySQL数据库]
        Q[Redis缓存]
        R[阿里云OSS]
    end
    
    C -->|信息准备| D
    C -->|供应商沟通| E
    C -->|报价分析| F
    C -->|决策支持| G
    
    D --> H
    D --> I
    E --> J
    E --> K
    F --> K
    G --> K
    
    D --> L
    E --> M
    F --> N
    G --> O
    
    L --> P
    M --> Q
    N --> R
```

## 2. 技术栈确定

### 2.1 核心技术选择

```yaml
# 后端技术栈
后端框架: FastAPI (Python 3.11+)
数据库: MySQL 8.0+ (兼容PostgreSQL设计)
缓存/记忆: Redis 7+ (对话上下文缓存)
文件存储: 阿里云OSS + 本地缓存

# AI和工具
AI框架: 自建轻量Agent框架
LLM: OpenAI GPT-4.1 / Claude-4
工具协议: MCP (Model Context Protocol)
Context7: MCP客户端集成

# 部署和监控 (预留接口)
容器化: Docker + Docker Compose (可选)
监控接口: 预留Prometheus指标接口
日志: Structlog
```

### 2.2 关键依赖包

```txt
# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
pymysql==1.1.0

# 缓存和记忆
redis==5.0.1

# AI和LLM
openai==1.6.1
anthropic==0.8.1
httpx==0.25.2

# 文件处理
oss2==2.18.4
aiofiles==23.2.1
python-multipart==0.0.6

# 工具库
structlog==23.2.0
jinja2==3.1.2
```

## 3. 详细实施计划

### 第一阶段：基础框架搭建 (2周)

#### 3.1 项目初始化 (2天)

**任务清单**：
- [ ] 创建项目目录结构
- [ ] 配置开发环境
- [ ] 设置版本控制
- [ ] 配置基础依赖

**项目结构**：
```
supplier-inquiry-system/
├── app/
│   ├── __init__.py          # 包初始化
│   ├── main.py              # FastAPI主应用启动文件
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   ├── dependencies.py      # 依赖注入
│   │
│   ├── core/                # 核心框架
│   │   ├── __init__.py
│   │   ├── agent_framework.py # Agent框架
│   │   ├── mcp_client.py     # MCP客户端
│   │   ├── base_agent.py     # Agent基类
│   │   ├── router.py         # 智能路由器
│   │   ├── orchestrator.py   # 编排器
│   │   └── memory.py         # Redis记忆管理
│   │
│   ├── agents/              # 具体Agent
│   │   ├── __init__.py
│   │   ├── task_analyzer.py
│   │   ├── info_collector.py
│   │   ├── communicator.py
│   │   ├── quote_analyzer.py
│   │   └── decision_support.py
│   │
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py          # 兼容类型
│   │   ├── inquiry.py
│   │   ├── supplier.py
│   │   └── conversation.py
│   │
│   ├── services/            # 业务服务
│   │   ├── __init__.py
│   │   ├── file_service.py  # 文件管理
│   │   ├── oss_service.py   # OSS集成
│   │   └── notification_service.py
│   │
│   ├── api/                 # API路由模块
│   │   ├── __init__.py
│   │   ├── v1/              # API版本1
│   │   │   ├── __init__.py
│   │   │   ├── inquiry.py   # 询价相关API
│   │   │   ├── conversation.py # 对话相关API
│   │   │   ├── files.py     # 文件相关API
│   │   │   └── analysis.py  # 分析相关API
│   │   └── deps.py          # API依赖
│   │
│   ├── middleware/          # 中间件
│   │   ├── __init__.py
│   │   ├── monitoring.py    # 监控接口(预留)
│   │   └── logging.py       # 日志中间件
│   │
│   └── utils/               # 工具函数
│       ├── __init__.py
│       ├── logger.py
│       └── validators.py
│
├── tools/                    # MCP工具服务器
│   ├── __init__.py
│   ├── mcp_server.py        # MCP工具服务器主文件
│   └── custom_tools/        # 自定义工具
│       ├── __init__.py
│       ├── email_sender.py
│       ├── price_calculator.py
│       └── risk_assessor.py
│
├── config/                   # 配置文件
│   ├── agents.yaml          # Agent配置
│   ├── mcp_servers.yaml     # MCP服务器配置
│   └── database.yaml        # 数据库配置
│
├── alembic/                  # 数据库迁移
│   ├── versions/
│   ├── env.py
│   └── script.py.mako
│
├── tests/                    # 测试文件
│   ├── __init__.py
│   ├── test_agents.py
│   ├── test_api.py
│   └── test_integration.py
│
├── scripts/                  # 脚本文件
│   ├── start.sh             # 启动脚本
│   └── init_db.py           # 数据库初始化
│
├── .env.example             # 环境变量示例
├── requirements.txt         # Python依赖
├── alembic.ini             # Alembic配置
├── pyproject.toml          # 项目配置
└── README.md               # 项目说明
```

#### 3.2 数据库设计 (3天)

**核心模型设计**：

```python
# app/models/base.py - 兼容性类型
class GUID(TypeDecorator):
    """MySQL/PostgreSQL兼容的UUID类型"""
    impl = CHAR
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(PostgresUUID())
        else:
            return dialect.type_descriptor(CHAR(36))

class JSONType(TypeDecorator):
    """MySQL/PostgreSQL兼容的JSON类型"""
    impl = Text
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'mysql':
            return dialect.type_descriptor(MySQLJSON())
        elif dialect.name == 'postgresql':
            return dialect.type_descriptor(JSONB())

# 核心数据表
- inquiry_sessions: 询价会话
- suppliers: 供应商信息
- conversations: 对话记录
- quotes: 报价信息
- analysis_results: 分析结果
- file_attachments: 文件附件
```

#### 3.3 Agent框架核心 (4天)

**核心组件**：

```python
# app/core/agent_framework.py
class Message(BaseModel):
    role: str  # system, user, assistant, tool
    content: str
    tool_calls: Optional[List[Dict]] = None
    timestamp: datetime = datetime.now()

class AgentConfig(BaseModel):
    name: str
    system_prompt: str
    model: str = "gpt-4"
    temperature: float = 0.1
    tools: List[str] = []
    memory_enabled: bool = True

class BaseAgent(ABC):
    def __init__(self, config, llm_client, mcp_client, memory, 
                 file_service, oss_service):
        self.config = config
        self.llm_client = llm_client
        self.mcp_client = mcp_client
        self.memory = memory
        self.file_service = file_service
        self.oss_service = oss_service
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        pass
```

#### 3.4 MCP工具集成 (2天)

**MCP客户端实现**：

```python
# app/core/mcp_client.py
class MCPClient:
    async def register_mcp_server(self, server_name: str, server_url: str):
        """注册MCP服务器"""
        
    async def call_tool(self, tool_name: str, **kwargs) -> Any:
        """调用MCP工具"""
        
    def get_tools_schema(self) -> List[Dict[str, Any]]:
        """获取工具Schema"""

# Context7集成
class Context7MCPClient(MCPClient):
    async def resolve_library_id(self, library_name: str) -> str:
        """解析库ID"""
        
    async def get_library_docs(self, library_id: str, topic: str = None) -> str:
        """获取库文档"""
```

#### 3.5 Redis记忆管理 (2天)

**对话上下文缓存设计**：

```python
# app/core/memory.py
import redis.asyncio as redis
import json
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

class ConversationMemory:
    """基于Redis的对话记忆管理"""
    
    def __init__(self, redis_url: str):
        self.redis = redis.from_url(redis_url)
        self.default_ttl = 3600 * 24  # 24小时默认过期时间
    
    async def save_conversation_context(self, session_id: str, context: Dict[str, Any]):
        """保存对话上下文"""
        key = f"conversation:{session_id}"
        await self.redis.setex(
            key, 
            self.default_ttl, 
            json.dumps(context, ensure_ascii=False)
        )
    
    async def get_conversation_context(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取对话上下文"""
        key = f"conversation:{session_id}"
        data = await self.redis.get(key)
        if data:
            return json.loads(data)
        return None
    
    async def update_conversation_context(self, session_id: str, updates: Dict[str, Any]):
        """更新对话上下文"""
        context = await self.get_conversation_context(session_id) or {}
        context.update(updates)
        context["last_updated"] = datetime.now().isoformat()
        await self.save_conversation_context(session_id, context)
    
    async def add_message_to_history(self, session_id: str, message: Dict[str, Any]):
        """添加消息到历史记录"""
        key = f"messages:{session_id}"
        await self.redis.lpush(key, json.dumps(message, ensure_ascii=False))
        await self.redis.expire(key, self.default_ttl)
        
        # 限制消息历史长度
        await self.redis.ltrim(key, 0, 99)  # 保留最近100条消息
    
    async def get_message_history(self, session_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取消息历史"""
        key = f"messages:{session_id}"
        messages = await self.redis.lrange(key, 0, limit - 1)
        return [json.loads(msg) for msg in messages]
    
    async def save_agent_state(self, session_id: str, agent_name: str, state: Dict[str, Any]):
        """保存Agent状态"""
        key = f"agent_state:{session_id}:{agent_name}"
        await self.redis.setex(
            key, 
            self.default_ttl, 
            json.dumps(state, ensure_ascii=False)
        )
    
    async def get_agent_state(self, session_id: str, agent_name: str) -> Optional[Dict[str, Any]]:
        """获取Agent状态"""
        key = f"agent_state:{session_id}:{agent_name}"
        data = await self.redis.get(key)
        if data:
            return json.loads(data)
        return None
    
    async def save_session_metadata(self, session_id: str, metadata: Dict[str, Any]):
        """保存会话元数据"""
        key = f"session_meta:{session_id}"
        await self.redis.setex(
            key, 
            self.default_ttl * 7,  # 会话元数据保留7天
            json.dumps(metadata, ensure_ascii=False)
        )
    
    async def get_session_metadata(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话元数据"""
        key = f"session_meta:{session_id}"
        data = await self.redis.get(key)
        if data:
            return json.loads(data)
        return None
    
    async def cleanup_session(self, session_id: str):
        """清理会话相关的所有缓存"""
        patterns = [
            f"conversation:{session_id}",
            f"messages:{session_id}",
            f"agent_state:{session_id}:*",
            f"session_meta:{session_id}"
        ]
        
        for pattern in patterns:
            if "*" in pattern:
                keys = await self.redis.keys(pattern)
                if keys:
                    await self.redis.delete(*keys)
            else:
                await self.redis.delete(pattern)
    
    async def extend_session_ttl(self, session_id: str, extra_seconds: int = 3600):
        """延长会话TTL"""
        patterns = [
            f"conversation:{session_id}",
            f"messages:{session_id}",
            f"session_meta:{session_id}"
        ]
        
        for pattern in patterns:
            await self.redis.expire(pattern, self.default_ttl + extra_seconds)

# 使用示例
class MemoryMixin:
    """为Agent提供记忆功能的Mixin"""
    
    def __init__(self, *args, memory: ConversationMemory, **kwargs):
        super().__init__(*args, **kwargs)
        self.memory = memory
    
    async def save_to_memory(self, session_id: str, key: str, value: Any):
        """保存到记忆"""
        await self.memory.update_conversation_context(session_id, {key: value})
    
    async def get_from_memory(self, session_id: str, key: str, default: Any = None) -> Any:
        """从记忆获取"""
        context = await self.memory.get_conversation_context(session_id)
        return context.get(key, default) if context else default
    
    async def add_message(self, session_id: str, role: str, content: str, metadata: Dict = None):
        """添加消息到历史"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "agent": self.config.name,
            "metadata": metadata or {}
        }
        await self.memory.add_message_to_history(session_id, message)
    
    async def get_recent_messages(self, session_id: str, limit: int = 10) -> List[Dict]:
        """获取最近的消息"""
        return await self.memory.get_message_history(session_id, limit)
```

#### 3.6 文件存储系统 (2天)

**文件处理架构**：

```python
# app/services/oss_service.py
class OSSService:
    async def upload_file(self, local_file_path: str) -> str:
        """上传文件到OSS，返回URL"""
        
    async def download_file(self, oss_url: str, local_path: str) -> str:
        """从OSS下载文件到本地"""

# app/services/file_service.py
class LocalFileService:
    async def save_uploaded_file(self, file, session_id: str) -> str:
        """保存上传文件到本地临时目录"""
        
    async def download_from_oss(self, oss_url: str, session_id: str) -> str:
        """从OSS下载文件到本地"""

# 消息类型设计
class MessageType(str, Enum):
    CHAT = "chat"
    FILE = "file"

class AgentResponse(BaseModel):
    message_type: MessageType
    content: Union[ChatContent, FileContent]
    metadata: Optional[dict] = None
```

**第一阶段交付物**：
- 可运行的基础框架
- 数据库模型和迁移脚本
- MCP工具集成基础
- 文件上传/下载功能
- 基础API接口

---

### 第二阶段：Agent实现 (3周)

#### 3.6 任务分析Agent (3天)

```python
# app/agents/task_analyzer.py
class TaskAnalyzerAgent(BaseAgent):
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        功能：
        1. 分析询价需求文本
        2. 提取物料类型、规格、数量等关键信息
        3. 识别缺失的必要信息
        4. 生成结构化的询价任务
        """
        inquiry_text = input_data.get("inquiry_text")
        session_id = input_data.get("session_id")
        
        # 使用LLM分析需求
        messages = [
            Message(role="system", content=self.config.system_prompt),
            Message(role="user", content=f"分析询价需求：{inquiry_text}")
        ]
        
        response = await self.chat_with_llm(messages, use_tools=True)
        
        # 解析和结构化结果
        analyzed_task = self._parse_analysis_result(response.content)
        missing_info = self._identify_missing_info(analyzed_task)
        
        return AgentResult(
            success=True,
            data={
                "analyzed_task": analyzed_task,
                "missing_info": missing_info
            }
        )
```

#### 3.7 信息收集Agent (3天)

```python
# app/agents/info_collector.py
class InfoCollectorAgent(BaseAgent):
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        功能：
        1. 准备公司基本信息和详细信息
        2. 整理项目相关信息
        3. 准备物料技术规格文档
        4. 信息脱敏处理
        """
        task = input_data.get("analyzed_task")
        session_id = input_data.get("session_id")
        
        # 准备各类信息
        company_info = await self._prepare_company_info()
        project_info = await self._organize_project_info(task)
        material_specs = await self._prepare_material_specs(task["material_type"])
        
        # 可能需要调用Context7获取技术文档
        if self.config.tools and "context7_get-library-docs" in self.config.tools:
            tech_docs = await self.call_tool(
                "context7_get-library-docs",
                context7CompatibleLibraryID=f"/materials/{task['material_type']}",
                topic="specifications"
            )
            material_specs.update(tech_docs)
        
        return AgentResult(
            success=True,
            data={
                "company_info": company_info,
                "project_info": project_info,
                "material_specs": material_specs
            }
        )
```

#### 3.8 供应商沟通Agent (5天)

```python
# app/agents/communicator.py
class CommunicatorAgent(BaseAgent):
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        功能：
        1. 发送初始询价邮件
        2. 回答供应商问题
        3. 处理文件交换
        4. 收集报价信息
        """
        action = input_data.get("action")  # "send_inquiry", "respond_question", "send_file"
        
        if action == "send_inquiry":
            return await self._send_initial_inquiry(input_data)
        elif action == "respond_question":
            return await self._respond_to_question(input_data)
        elif action == "send_file":
            return await self._send_file_to_supplier(input_data)
    
    async def _send_file_to_supplier(self, input_data):
        """发送文件给供应商"""
        oss_url = input_data.get("oss_url")
        session_id = input_data.get("session_id")
        supplier_email = input_data.get("supplier_email")
        
        # 从OSS下载文件到本地
        local_path = await self.file_service.download_from_oss(oss_url, session_id)
        
        # 使用MCP工具发送邮件
        result = await self.call_tool("email_sender",
            to=supplier_email,
            subject="询价相关文档",
            content="请查看附件中的技术规格文档",
            attachments=[local_path]
        )
        
        # 返回文件类型响应
        return AgentResponse(
            message_type=MessageType.FILE,
            content=FileContent(
                filename=Path(local_path).name,
                oss_url=oss_url,
                file_type="application/pdf",
                file_size=Path(local_path).stat().st_size
            ),
            metadata={"sent_to": supplier_email, "status": "sent"}
        )
```

#### 3.9 报价分析Agent (4天)

```python
# app/agents/quote_analyzer.py
class QuoteAnalyzerAgent(BaseAgent):
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        功能：
        1. 解析报价文档（PDF/Excel）
        2. 提取价格、交期、技术规格等信息
        3. 多供应商比较分析
        4. 风险评估
        """
        quotes = input_data.get("quotes")
        session_id = input_data.get("session_id")
        
        # 解析每个报价
        parsed_quotes = []
        for quote in quotes:
            if quote.get("file_url"):
                # 下载并解析报价文件
                local_file = await self.file_service.download_from_oss(
                    quote["file_url"], session_id
                )
                parsed_quote = await self._parse_quote_document(local_file)
            else:
                parsed_quote = quote
            
            parsed_quotes.append(parsed_quote)
        
        # 使用MCP工具进行价格分析
        comparison_result = await self.call_tool("price_calculator",
            quotes=parsed_quotes,
            analysis_type="comprehensive"
        )
        
        # 风险评估
        risk_assessment = await self.call_tool("risk_assessor",
            quotes=parsed_quotes,
            suppliers=input_data.get("suppliers", [])
        )
        
        return AgentResult(
            success=True,
            data={
                "parsed_quotes": parsed_quotes,
                "comparison_result": comparison_result,
                "risk_assessment": risk_assessment
            }
        )
```

#### 3.10 决策支持Agent (3天)

```python
# app/agents/decision_support.py
class DecisionSupportAgent(BaseAgent):
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        功能：
        1. 生成决策报告
        2. 推荐最佳供应商
        3. 提供谈判建议
        4. 制定行动计划
        """
        analysis_data = input_data.get("analysis_data")
        session_id = input_data.get("session_id")
        
        # 生成决策报告内容
        report_content = await self._generate_decision_report(analysis_data)
        
        # 创建PDF报告并上传到OSS
        response = await self.generate_document(
            content=report_content,
            session_id=session_id,
            filename=f"供应商分析报告_{session_id[:8]}.pdf"
        )
        
        # 生成行动计划
        action_plan = await self._generate_action_plan(analysis_data)
        
        return AgentResult(
            success=True,
            data={
                "decision_report": response,
                "action_plan": action_plan,
                "recommendations": analysis_data.get("top_suppliers", [])
            }
        )
```

#### 3.11 MCP工具服务器开发 (2天)

```python
# tools/mcp_server.py
from fastapi import FastAPI

app = FastAPI(title="Supplier Inquiry MCP Tools")

@app.post("/mcp/list_tools")
async def list_tools():
    return {
        "tools": [
            {
                "name": "email_sender",
                "description": "发送邮件给供应商",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "to": {"type": "string"},
                        "subject": {"type": "string"},
                        "content": {"type": "string"},
                        "attachments": {"type": "array"}
                    }
                }
            },
            {
                "name": "price_calculator",
                "description": "价格比较分析",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "quotes": {"type": "array"},
                        "analysis_type": {"type": "string"}
                    }
                }
            },
            {
                "name": "risk_assessor",
                "description": "供应商风险评估",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "quotes": {"type": "array"},
                        "suppliers": {"type": "array"}
                    }
                }
            }
        ]
    }

@app.post("/mcp/call_tool")
async def call_tool(tool_call: ToolCall):
    if tool_call.name == "email_sender":
        return await send_email(**tool_call.arguments)
    elif tool_call.name == "price_calculator":
        return await calculate_price(**tool_call.arguments)
    elif tool_call.name == "risk_assessor":
        return await assess_risk(**tool_call.arguments)
```

**第二阶段交付物**：
- 完整的5个Agent实现
- MCP工具服务器
- Agent间协作机制
- 文件处理集成
- 基础测试用例

---

### 第三阶段：系统集成 (2周)

#### 3.12 智能路由器和Agent编排器 (3天)

```python
# app/core/router.py
class InquiryRouter:
    """智能路由器 - 根据任务类型和状态分类路由到不同Agent"""
    
    def __init__(self, agents):
        self.agents = agents
    
    async def route_request(self, session_id: str, request_type: str, data: Dict[str, Any]) -> str:
        """根据请求类型路由到相应的Agent"""
        routing_rules = {
            "initial_inquiry": "task_analyzer",
            "info_preparation": "info_collector", 
            "supplier_communication": "communicator",
            "quote_analysis": "quote_analyzer",
            "decision_making": "decision_support",
            "supplier_question": "communicator",
            "file_processing": "communicator",
            "price_comparison": "quote_analyzer"
        }
        
        agent_name = routing_rules.get(request_type)
        if not agent_name:
            raise ValueError(f"Unknown request type: {request_type}")
        
        return agent_name
    
    async def determine_next_step(self, session_id: str, current_state: Dict) -> str:
        """根据当前状态确定下一步操作"""
        if not current_state.get("task_analyzed"):
            return "initial_inquiry"
        elif not current_state.get("info_prepared"):
            return "info_preparation"
        elif current_state.get("pending_supplier_questions"):
            return "supplier_communication"
        elif current_state.get("new_quotes"):
            return "quote_analysis"
        elif current_state.get("analysis_complete"):
            return "decision_making"
        else:
            return "supplier_communication"  # 默认继续沟通

# app/core/orchestrator.py
class SupplierInquiryOrchestrator:
    def __init__(self, agents, mcp_client, memory, file_service, oss_service):
        self.agents = agents
        self.router = InquiryRouter(agents)
        self.mcp_client = mcp_client
        self.memory = memory
        self.file_service = file_service
        self.oss_service = oss_service
    
    async def process_inquiry(self, request: InquiryRequest) -> InquiryResult:
        """基于智能路由的询价流程编排"""
        session_id = str(uuid.uuid4())
        session_state = {
            "task_analyzed": False,
            "info_prepared": False,
            "suppliers_contacted": False,
            "quotes_received": [],
            "analysis_complete": False
        }
        
        try:
            # 1. 初始任务分析
            task_result = await self._execute_agent_step(
                session_id, "initial_inquiry", {
                    "inquiry_text": request.inquiry_text,
                    "session_id": session_id
                }
            )
            session_state["task_analyzed"] = True
            
            # 2. 基于路由的动态流程处理
            while not session_state["analysis_complete"]:
                next_step = await self.router.determine_next_step(session_id, session_state)
                
                if next_step == "info_preparation":
                    info_result = await self._execute_agent_step(
                        session_id, "info_preparation", {
                            "analyzed_task": task_result.data["analyzed_task"],
                            "session_id": session_id
                        }
                    )
                    session_state["info_prepared"] = True
                
                elif next_step == "supplier_communication":
                    quotes = await self._handle_supplier_communication(
                        session_id, info_result.data, request.target_suppliers
                    )
                    session_state["quotes_received"].extend(quotes)
                    session_state["suppliers_contacted"] = True
                
                elif next_step == "quote_analysis":
                    analysis_result = await self._execute_agent_step(
                        session_id, "quote_analysis", {
                            "quotes": session_state["quotes_received"],
                            "suppliers": request.target_suppliers,
                            "session_id": session_id
                        }
                    )
                    session_state["analysis_complete"] = True
                
                elif next_step == "decision_making":
                    decision_result = await self._execute_agent_step(
                        session_id, "decision_making", {
                            "analysis_data": analysis_result.data,
                            "session_id": session_id
                        }
                    )
                    break
            
            return InquiryResult(
                session_id=session_id,
                task=task_result.data,
                quotes=session_state["quotes_received"],
                analysis=analysis_result.data,
                decision=decision_result.data
            )
            
        except Exception as e:
            logger.error("Inquiry processing failed", session_id=session_id, error=str(e))
            raise
    
    async def _execute_agent_step(self, session_id: str, request_type: str, data: Dict[str, Any]):
        """执行单个Agent步骤"""
        agent_name = await self.router.route_request(session_id, request_type, data)
        agent = self.agents[agent_name]
        
        logger.info("Executing agent step", 
                   session_id=session_id, 
                   agent=agent_name, 
                   request_type=request_type)
        
        return await agent.process(data)
    
    async def handle_dynamic_request(self, session_id: str, request_type: str, data: Dict[str, Any]):
        """处理动态请求（如供应商问题、文件上传等）"""
        agent_name = await self.router.route_request(session_id, request_type, data)
        agent = self.agents[agent_name]
        
        return await agent.process({
            **data,
            "session_id": session_id,
            "action": request_type
        })
    
    async def _handle_supplier_communication(self, session_id, info_data, suppliers):
        """处理与多个供应商的并行沟通"""
        communicator = self.agents["communicator"]
        quotes = []
        
        # 并行处理多个供应商
        tasks = []
        for supplier in suppliers:
            task = self._communicate_with_supplier(
                communicator, session_id, info_data, supplier
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, Exception):
                logger.error("Supplier communication failed", error=str(result))
            else:
                quotes.extend(result)
        
        return quotes
    
    async def _communicate_with_supplier(self, communicator, session_id, info_data, supplier):
        """与单个供应商的完整沟通流程"""
        quotes = []
        
        # 发送初始询价
        initial_result = await communicator.process({
            "action": "send_inquiry",
            "session_id": session_id,
            "supplier": supplier,
            "info_data": info_data
        })
        
        # 模拟多轮对话（实际中会根据供应商回复触发）
        conversation_active = True
        max_rounds = 5
        round_count = 0
        
        while conversation_active and round_count < max_rounds:
            # 检查是否有新的供应商问题
            pending_questions = await self._check_supplier_questions(session_id, supplier["id"])
            
            if pending_questions:
                # 回答供应商问题
                for question in pending_questions:
                    response_result = await communicator.process({
                        "action": "respond_question",
                        "session_id": session_id,
                        "supplier_id": supplier["id"],
                        "question": question
                    })
            
            # 检查是否收到报价
            new_quotes = await self._check_new_quotes(session_id, supplier["id"])
            if new_quotes:
                quotes.extend(new_quotes)
                conversation_active = False
            
            round_count += 1
            await asyncio.sleep(1)  # 避免过于频繁的检查
        
        return quotes
```

#### 3.13 API接口完善 (4天)

```python
# app/api/inquiry.py
from fastapi import APIRouter, Depends, UploadFile, File
from app.core.orchestrator import SupplierInquiryOrchestrator

router = APIRouter()

@router.post("/create")
async def create_inquiry(
    request: InquiryRequest,
    orchestrator: SupplierInquiryOrchestrator = Depends(get_orchestrator)
):
    """创建新的询价任务"""
    try:
        result = await orchestrator.process_inquiry(request)
        return {
            "success": True,
            "data": {
                "session_id": result.session_id,
                "status": "processing",
                "estimated_completion": "30分钟"
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.get("/{session_id}/status")
async def get_inquiry_status(session_id: str):
    """获取询价状态"""
    # 从数据库或缓存获取状态
    status = await get_session_status(session_id)
    return {"success": True, "data": status}

@router.get("/{session_id}/result")
async def get_inquiry_result(session_id: str):
    """获取询价结果"""
    result = await get_session_result(session_id)
    return {"success": True, "data": result}

# app/api/conversation.py
@router.post("/{session_id}/message")
async def send_message(
    session_id: str,
    message: ConversationMessage,
    orchestrator: SupplierInquiryOrchestrator = Depends(get_orchestrator)
):
    """发送消息给Agent - 通过智能路由分发"""
    try:
        # 使用智能路由器分发请求
        result = await orchestrator.handle_dynamic_request(
            session_id=session_id,
            request_type=message.type,  # supplier_question, file_processing, etc.
            data={
                "content": message.content,
                "metadata": message.metadata
            }
        )
        
        return {
            "success": True,
            "data": {
                "message_type": result.message_type,
                "content": result.content,
                "metadata": result.metadata
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.post("/{session_id}/route")
async def route_request(
    session_id: str,
    request: RouteRequest,
    orchestrator: SupplierInquiryOrchestrator = Depends(get_orchestrator)
):
    """通用路由接口 - 根据请求类型智能分发"""
    try:
        result = await orchestrator.handle_dynamic_request(
            session_id=session_id,
            request_type=request.type,
            data=request.data
        )
        
        return {
            "success": True,
            "data": result.data,
            "routed_to": await orchestrator.router.route_request(
                session_id, request.type, request.data
            )
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

# app/api/files.py
@router.post("/upload/{session_id}")
async def upload_file(
    session_id: str,
    file: UploadFile = File(...),
    file_service: LocalFileService = Depends(get_file_service),
    oss_service: OSSService = Depends(get_oss_service)
):
    """上传文件到OSS"""
    try:
        # 保存到本地临时目录
        local_path = await file_service.save_uploaded_file(file, session_id)
        
        # 上传到OSS
        oss_url = await oss_service.upload_file(local_path)
        
        return {
            "success": True,
            "data": {
                "filename": file.filename,
                "oss_url": oss_url,
                "file_type": file.content_type,
                "file_size": file.size
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}
```

#### 3.14 监控接口预留 (1天)

```python
# app/middleware/monitoring.py
from fastapi import Request, Response
from prometheus_client import Counter, Histogram, generate_latest
import time
from typing import Optional

# 预留监控指标
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
AGENT_EXECUTION_TIME = Histogram('agent_execution_seconds', 'Agent execution time', ['agent_name'])
AGENT_SUCCESS_RATE = Counter('agent_success_total', 'Agent success count', ['agent_name', 'status'])

class MonitoringMiddleware:
    """监控中间件 - 预留接口"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)
            start_time = time.time()
            
            # 处理请求
            response = await self.app(scope, receive, send)
            
            # 记录指标 (可选启用)
            duration = time.time() - start_time
            REQUEST_DURATION.observe(duration)
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status=getattr(response, 'status_code', 200)
            ).inc()
            
            return response
        else:
            return await self.app(scope, receive, send)

# 监控端点
async def metrics_endpoint():
    """Prometheus指标端点"""
    return Response(generate_latest(), media_type="text/plain")

async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": time.time()}

# Agent监控装饰器
def monitor_agent_execution(agent_name: str):
    """Agent执行监控装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                AGENT_SUCCESS_RATE.labels(agent_name=agent_name, status="success").inc()
                return result
            except Exception as e:
                AGENT_SUCCESS_RATE.labels(agent_name=agent_name, status="error").inc()
                raise
            finally:
                duration = time.time() - start_time
                AGENT_EXECUTION_TIME.labels(agent_name=agent_name).observe(duration)
        return wrapper
    return decorator
```

#### 3.15 配置管理 (1天)

```python
# app/config.py
from pydantic_settings import BaseSettings
from typing import Dict, List

class Settings(BaseSettings):
    # 基础配置
    app_name: str = "供应商询价系统"
    debug: bool = False
    enable_monitoring: bool = False  # 监控开关
    
    # 数据库配置
    database_url: str = "mysql+pymysql://user:pass@localhost/supplier_inquiry"
    
    # Redis配置 (短期记忆缓存)
    redis_url: str = "redis://localhost:6379/0"
    redis_ttl: int = 86400  # 24小时默认TTL
    
    # LLM配置
    openai_api_key: str
    claude_api_key: str
    default_llm_provider: str = "openai"
    
    # 阿里云OSS配置
    oss_access_key_id: str
    oss_access_key_secret: str
    oss_endpoint: str = "oss-cn-hangzhou.aliyuncs.com"
    oss_bucket_name: str
    
    # 文件配置
    temp_files_dir: str = "./temp_files"
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: List[str] = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".png"]
    
    # MCP服务器配置
    mcp_servers: Dict[str, Dict] = {
        "context7": {
            "url": "https://mcp.context7.com",
            "timeout": 30
        },
        "custom_tools": {
            "url": "http://localhost:8001/mcp",
            "timeout": 10
        }
    }
    
    # Agent配置
    agent_configs: Dict[str, Dict] = {
        "task_analyzer": {
            "model": "gpt-4",
            "temperature": 0.1,
            "tools": ["context7_resolve-library-id"]
        },
        "communicator": {
            "model": "claude-3-sonnet",
            "temperature": 0.2,
            "tools": ["custom_tools_email_sender"]
        }
    }
    
    class Config:
        env_file = ".env"
```

**第三阶段交付物**：
- 完整的系统集成
- 智能路由器实现
- API接口完善
- Redis记忆管理
- 监控接口预留
- 配置管理系统

---

### 第四阶段：测试和优化 (1周)

#### 3.16 单元测试 (2天)

```python
# tests/test_agents.py
import pytest
from app.agents.task_analyzer import TaskAnalyzerAgent

@pytest.mark.asyncio
async def test_task_analyzer():
    """测试任务分析Agent"""
    config = AgentConfig(
        name="task_analyzer",
        system_prompt="你是专业的采购需求分析专家",
        model="gpt-4",
        tools=[]
    )
    
    agent = TaskAnalyzerAgent(config, mock_llm_client, mock_mcp_client, mock_memory)
    
    input_data = {
        "inquiry_text": "需要采购1000个STM32F103C8T6芯片，LQFP48封装",
        "session_id": "test-session"
    }
    
    result = await agent.process(input_data)
    
    assert result.success
    assert "material_type" in result.data["analyzed_task"]
    assert result.data["analyzed_task"]["quantity"] == 1000

# tests/test_integration.py
@pytest.mark.asyncio
async def test_full_inquiry_process():
    """测试完整询价流程"""
    orchestrator = SupplierInquiryOrchestrator(
        agents=mock_agents,
        mcp_client=mock_mcp_client,
        memory=mock_memory,
        file_service=mock_file_service,
        oss_service=mock_oss_service
    )
    
    request = InquiryRequest(
        inquiry_text="需要采购STM32芯片",
        target_suppliers=[
            {"id": "supplier_1", "name": "供应商A", "email": "<EMAIL>"}
        ]
    )
    
    result = await orchestrator.process_inquiry(request)
    
    assert result.session_id
    assert result.decision
    assert len(result.quotes) > 0
```

#### 3.17 集成测试 (2天)

```python
# tests/test_api.py
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_inquiry():
    """测试创建询价API"""
    response = client.post("/api/v1/inquiry/create", json={
        "inquiry_text": "需要采购1000个STM32芯片",
        "target_suppliers": [
            {"id": "supplier_1", "name": "供应商A"}
        ]
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"]
    assert "session_id" in data["data"]

def test_file_upload():
    """测试文件上传API"""
    with open("test_file.pdf", "rb") as f:
        response = client.post(
            "/api/v1/files/upload/test-session",
            files={"file": ("test.pdf", f, "application/pdf")}
        )
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"]
    assert "oss_url" in data["data"]
```

#### 3.18 性能优化 (2天)

**优化重点**：
- 数据库查询优化
- Redis缓存策略
- 异步任务优化
- 文件处理优化

```python
# 数据库查询优化
async def get_inquiry_sessions_optimized(db: Session, status: str = None):
    """优化的查询方法"""
    query = db.query(InquirySession).options(
        selectinload(InquirySession.conversations),
        selectinload(InquirySession.quotes)
    )
    
    if status:
        query = query.filter(InquirySession.status == status)
    
    return query.all()

# Redis缓存策略
class CacheService:
    async def get_or_set(self, key: str, factory_func, ttl: int = 3600):
        """获取缓存或设置缓存"""
        cached = await self.redis.get(key)
        if cached:
            return json.loads(cached)
        
        value = await factory_func()
        await self.redis.setex(key, ttl, json.dumps(value))
        return value
```

#### 3.16 主应用和API实现 (2天)

**主启动文件**：

```python
# app/main.py
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.config import get_settings
from app.database import init_db
from app.middleware.monitoring import MonitoringMiddleware, metrics_endpoint, health_check
from app.api.v1 import inquiry, conversation, files, analysis
from app.core.memory import ConversationMemory
from app.core.orchestrator import SupplierInquiryOrchestrator
from app.dependencies import get_orchestrator

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    settings = get_settings()
    
    # 初始化数据库
    await init_db()
    
    # 初始化Redis记忆
    memory = ConversationMemory(settings.redis_url)
    app.state.memory = memory
    
    print(f"🚀 {settings.app_name} 启动成功!")
    print(f"📖 API文档: http://localhost:8000/docs")
    print(f"🔍 健康检查: http://localhost:8000/health")
    
    yield
    
    # 关闭时清理
    print("🛑 应用正在关闭...")

def create_app() -> FastAPI:
    """创建FastAPI应用"""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.app_name,
        description="供应商询价多Agent系统 - 智能化供应商沟通和报价分析",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境需要限制
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 添加监控中间件 (可选启用)
    if settings.enable_monitoring:
        app.add_middleware(MonitoringMiddleware)
    
    # 注册API路由
    app.include_router(
        inquiry.router, 
        prefix="/api/v1/inquiry", 
        tags=["询价管理"]
    )
    app.include_router(
        conversation.router, 
        prefix="/api/v1/conversation", 
        tags=["对话管理"]
    )
    app.include_router(
        files.router, 
        prefix="/api/v1/files", 
        tags=["文件管理"]
    )
    app.include_router(
        analysis.router, 
        prefix="/api/v1/analysis", 
        tags=["分析报告"]
    )
    
    # 系统端点
    app.add_api_route("/health", health_check, methods=["GET"], tags=["系统"])
    app.add_api_route("/metrics", metrics_endpoint, methods=["GET"], tags=["监控"])
    
    @app.get("/", tags=["系统"])
    async def root():
        """根路径"""
        return {
            "message": "供应商询价多Agent系统",
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/health"
        }
    
    return app

# 创建应用实例
app = create_app()

# 开发环境启动
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
```

**API接口实现**：

```python
# app/api/v1/inquiry.py
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import List, Optional
from pydantic import BaseModel

from app.core.orchestrator import SupplierInquiryOrchestrator
from app.dependencies import get_orchestrator
from app.models.inquiry import InquirySession

router = APIRouter()

class InquiryRequest(BaseModel):
    inquiry_text: str
    target_suppliers: List[dict] = []
    priority: str = "normal"  # normal, high, urgent
    deadline: Optional[str] = None

class InquiryResponse(BaseModel):
    session_id: str
    status: str
    message: str
    estimated_completion: Optional[str] = None

@router.post("/create", response_model=InquiryResponse)
async def create_inquiry(
    request: InquiryRequest,
    background_tasks: BackgroundTasks,
    orchestrator: SupplierInquiryOrchestrator = Depends(get_orchestrator)
):
    """创建新的询价任务"""
    try:
        # 启动询价流程
        result = await orchestrator.process_inquiry(request)
        
        return InquiryResponse(
            session_id=result.session_id,
            status="processing",
            message="询价任务已创建，正在处理中",
            estimated_completion="预计30分钟内完成"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建询价失败: {str(e)}")

@router.get("/{session_id}/status")
async def get_inquiry_status(session_id: str):
    """获取询价状态"""
    try:
        # 从数据库或缓存获取状态
        status = await get_session_status(session_id)
        return {"success": True, "data": status}
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"会话不存在: {str(e)}")

@router.get("/{session_id}/result")
async def get_inquiry_result(session_id: str):
    """获取询价结果"""
    try:
        result = await get_session_result(session_id)
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"结果不存在: {str(e)}")

@router.delete("/{session_id}")
async def cancel_inquiry(session_id: str):
    """取消询价任务"""
    try:
        await cancel_session(session_id)
        return {"success": True, "message": "询价任务已取消"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消失败: {str(e)}")
```

```python
# app/api/v1/conversation.py
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from pydantic import BaseModel

from app.core.orchestrator import SupplierInquiryOrchestrator
from app.dependencies import get_orchestrator

router = APIRouter()

class ConversationMessage(BaseModel):
    type: str  # supplier_question, file_processing, etc.
    content: str
    metadata: Optional[dict] = None

class RouteRequest(BaseModel):
    type: str
    data: dict

@router.post("/{session_id}/message")
async def send_message(
    session_id: str,
    message: ConversationMessage,
    orchestrator: SupplierInquiryOrchestrator = Depends(get_orchestrator)
):
    """发送消息给Agent - 通过智能路由分发"""
    try:
        result = await orchestrator.handle_dynamic_request(
            session_id=session_id,
            request_type=message.type,
            data={
                "content": message.content,
                "metadata": message.metadata
            }
        )
        
        return {
            "success": True,
            "data": {
                "message_type": result.message_type,
                "content": result.content,
                "metadata": result.metadata
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"消息处理失败: {str(e)}")

@router.post("/{session_id}/route")
async def route_request(
    session_id: str,
    request: RouteRequest,
    orchestrator: SupplierInquiryOrchestrator = Depends(get_orchestrator)
):
    """通用路由接口 - 根据请求类型智能分发"""
    try:
        result = await orchestrator.handle_dynamic_request(
            session_id=session_id,
            request_type=request.type,
            data=request.data
        )
        
        routed_agent = await orchestrator.router.route_request(
            session_id, request.type, request.data
        )
        
        return {
            "success": True,
            "data": result.data,
            "routed_to": routed_agent
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"路由失败: {str(e)}")

@router.get("/{session_id}/history")
async def get_conversation_history(
    session_id: str,
    limit: int = 20,
    orchestrator: SupplierInquiryOrchestrator = Depends(get_orchestrator)
):
    """获取对话历史"""
    try:
        history = await orchestrator.memory.get_message_history(session_id, limit)
        return {"success": True, "data": history}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史失败: {str(e)}")
```

**启动脚本**：

```bash
# scripts/start.sh
#!/bin/bash

echo "🚀 启动供应商询价系统..."

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 检查环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "⚠️  警告: DATABASE_URL 未设置，使用默认值"
    export DATABASE_URL="mysql+pymysql://user:password@localhost/supplier_inquiry"
fi

if [ -z "$REDIS_URL" ]; then
    echo "⚠️  警告: REDIS_URL 未设置，使用默认值"
    export REDIS_URL="redis://localhost:6379/0"
fi

# 启动MCP工具服务器 (后台)
echo "🔧 启动MCP工具服务器..."
cd tools && python mcp_server.py &
MCP_PID=$!
cd ..

# 等待MCP服务器启动
sleep 2

# 启动主应用
echo "🌟 启动主应用..."
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 清理后台进程
echo "🧹 清理后台进程..."
kill $MCP_PID 2>/dev/null || true
```

**第四阶段交付物**：
- 完整的测试套件
- 性能优化
- 简化部署配置
- 系统文档

---

## 4. 部署配置 (预留接口)

### 4.1 环境要求

**开发环境**：
- Python 3.11+
- MySQL 8.0+
- Redis 7+

**生产环境 (可选)**：
- 服务器：2核4GB内存（最低配置）
- 数据库：MySQL 8.0+
- 缓存：Redis 7+
- 存储：阿里云OSS

### 4.2 简化部署

```bash
# 1. 本地开发启动
pip install -r requirements.txt
uvicorn app.main:app --reload

# 2. 配置环境变量
export DATABASE_URL="mysql+pymysql://user:pass@localhost/supplier_inquiry"
export REDIS_URL="redis://localhost:6379/0"
export OSS_ACCESS_KEY_ID="your_key"
export OSS_ACCESS_KEY_SECRET="your_secret"

# 3. 数据库迁移
alembic upgrade head

# 4. 验证启动
curl http://localhost:8000/health
```

### 4.3 监控接口 (预留)

**预留监控端点**：
- `/metrics` - Prometheus指标 (可选启用)
- `/health` - 健康检查
- `/status` - 系统状态

**关键指标 (预留)**：
- 询价处理成功率
- Agent执行时间
- Redis缓存状态
- 文件处理状态

## 5. 风险评估和应对

### 5.1 技术风险

| 风险 | 影响 | 概率 | 应对措施 |
|------|------|------|----------|
| LLM API限流 | 高 | 中 | 多提供商备份、请求队列 |
| 数据库性能 | 中 | 低 | 索引优化、读写分离 |
| 文件存储故障 | 中 | 低 | OSS多区域备份 |
| MCP服务不稳定 | 中 | 中 | 降级策略、重试机制 |

### 5.2 业务风险

| 风险 | 影响 | 概率 | 应对措施 |
|------|------|------|----------|
| 供应商不配合 | 高 | 中 | 人工介入机制 |
| 报价解析错误 | 中 | 中 | 人工审核、多重验证 |
| 信息泄露 | 高 | 低 | 数据脱敏、权限控制 |

## 6. 成本估算

### 6.1 开发成本

- **人力成本**：1名全栈开发工程师 × 8周 = 2个人月
- **LLM API成本**：约500-1000元/月（取决于使用量）
- **云服务成本**：约300-500元/月（OSS + 服务器）

### 6.2 运维成本

- **服务器**：约200-500元/月
- **数据库**：约100-300元/月
- **存储**：约50-100元/月
- **监控**：约50-100元/月

## 7. 后续扩展计划

### 7.1 短期扩展 (3个月内)

- [ ] 增加更多物料类型支持
- [ ] 优化报价解析准确率
- [ ] 添加移动端支持
- [ ] 集成更多供应商平台

### 7.2 中期扩展 (6个月内)

- [ ] 智能供应商推荐
- [ ] 历史数据分析
- [ ] 自动化合同生成
- [ ] 多语言支持

### 7.3 长期扩展 (1年内)

- [ ] 供应链风险预警
- [ ] 市场价格趋势分析
- [ ] 智能采购决策
- [ ] 区块链溯源集成

---

## 总结

这个完整的实施计划涵盖了从技术选型到部署运维的全流程，采用了基于MySQL的兼容性设计、自建Agent框架、MCP工具集成和阿里云OSS文件存储等技术方案。

**核心优势**：
1. **技术先进**：自建Agent框架，MCP工具标准化
2. **架构灵活**：支持多种LLM，数据库兼容设计
3. **功能完整**：覆盖询价全流程，支持文件处理
4. **部署简单**：Docker化部署，配置化管理
5. **可扩展性**：模块化设计，便于后续扩展

按照这个计划实施，预计8周内可以完成一个功能完整、性能稳定的供应商询价多Agent系统。

**配置管理和依赖注入**：

```python
# app/config.py
from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # 应用配置
    app_name: str = "供应商询价多Agent系统"
    debug: bool = False
    enable_monitoring: bool = False
    
    # 数据库配置
    database_url: str = "mysql+pymysql://user:password@localhost/supplier_inquiry"
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    
    # AI模型配置
    openai_api_key: Optional[str] = None
    claude_api_key: Optional[str] = None
    default_model: str = "gpt-4"
    
    # OSS配置
    oss_access_key_id: Optional[str] = None
    oss_access_key_secret: Optional[str] = None
    oss_bucket_name: Optional[str] = None
    oss_endpoint: Optional[str] = None
    
    # MCP配置
    mcp_server_url: str = "http://localhost:8001"
    context7_enabled: bool = True
    
    # 文件配置
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: list = [".pdf", ".doc", ".docx", ".txt", ".xlsx"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False

def get_settings() -> Settings:
    return Settings()
```

```python
# app/dependencies.py
from fastapi import Depends
from typing import Annotated, Optional

from app.config import get_settings, Settings
from app.core.orchestrator import SupplierInquiryOrchestrator
from app.core.memory import ConversationMemory
from app.services.file_service import FileService
from app.services.oss_service import OSSService

# 全局实例缓存
_orchestrator: Optional[SupplierInquiryOrchestrator] = None

async def get_orchestrator(
    settings: Annotated[Settings, Depends(get_settings)]
) -> SupplierInquiryOrchestrator:
    """获取编排器实例"""
    global _orchestrator
    if _orchestrator is None:
        _orchestrator = SupplierInquiryOrchestrator(settings)
        await _orchestrator.initialize()
    return _orchestrator
```

## 8. 启动方式和API接口

### 8.1 开发环境启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd supplier-inquiry-system

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置数据库、Redis、OSS等配置

# 4. 初始化数据库
python scripts/init_db.py

# 5. 启动系统
chmod +x scripts/start.sh
./scripts/start.sh
```

### 8.2 直接启动主应用

```bash
# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export DATABASE_URL="mysql+pymysql://user:password@localhost/supplier_inquiry"
export REDIS_URL="redis://localhost:6379/0"

# 启动主应用
python app/main.py
# 或者
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 8.3 API接口访问

启动成功后，可以通过以下地址访问：

- **主页**: http://localhost:8000/
- **API文档**: http://localhost:8000/docs  
- **健康检查**: http://localhost:8000/health
- **监控指标**: http://localhost:8000/metrics

### 8.4 主要API端点

#### 询价管理 API

```bash
# 创建询价任务
POST /api/v1/inquiry/create
{
    "inquiry_text": "需要采购100个LED灯珠，规格为...",
    "target_suppliers": [{"name": "供应商A", "contact": "..."}],
    "priority": "normal",
    "deadline": "2024-01-15"
}

# 获取询价状态
GET /api/v1/inquiry/{session_id}/status

# 获取询价结果
GET /api/v1/inquiry/{session_id}/result

# 取消询价任务
DELETE /api/v1/inquiry/{session_id}
```

#### 对话管理 API

```bash
# 发送消息给Agent
POST /api/v1/conversation/{session_id}/message
{
    "type": "supplier_question",
    "content": "供应商询问产品的具体规格要求",
    "metadata": {"supplier_id": "123"}
}

# 智能路由请求
POST /api/v1/conversation/{session_id}/route
{
    "type": "file_processing",
    "data": {"file_url": "https://...", "action": "analyze"}
}

# 获取对话历史
GET /api/v1/conversation/{session_id}/history?limit=20
```

#### 文件管理 API

```bash
# 上传文件
POST /api/v1/files/upload
Content-Type: multipart/form-data

# 下载文件
GET /api/v1/files/{file_id}/download

# 获取文件信息
GET /api/v1/files/{file_id}/info
```

#### 分析报告 API

```bash
# 获取报价分析
GET /api/v1/analysis/{session_id}/quotes

# 获取决策建议
GET /api/v1/analysis/{session_id}/recommendations

# 导出分析报告
GET /api/v1/analysis/{session_id}/export?format=pdf
```

### 8.5 使用示例

#### 完整询价流程示例

```python
import requests
import json

# 1. 创建询价任务
inquiry_data = {
    "inquiry_text": "需要采购1000个LED灯珠，功率3W，色温6500K，要求高亮度低功耗",
    "target_suppliers": [
        {"name": "光电科技", "contact": "<EMAIL>"},
        {"name": "明亮电子", "contact": "<EMAIL>"}
    ],
    "priority": "high",
    "deadline": "2024-01-20"
}

response = requests.post("http://localhost:8000/api/v1/inquiry/create", 
                        json=inquiry_data)
session_id = response.json()["session_id"]

# 2. 监控处理状态
status_response = requests.get(f"http://localhost:8000/api/v1/inquiry/{session_id}/status")
print(f"当前状态: {status_response.json()['data']['status']}")

# 3. 处理供应商问题
if status_response.json()["data"]["status"] == "waiting_for_response":
    message_data = {
        "type": "supplier_question",
        "content": "我们的LED灯珠支持调光功能，请问是否需要？",
        "metadata": {"supplier_id": "supplier_001"}
    }
    
    requests.post(f"http://localhost:8000/api/v1/conversation/{session_id}/message",
                 json=message_data)

# 4. 获取最终结果
result_response = requests.get(f"http://localhost:8000/api/v1/inquiry/{session_id}/result")
print("询价结果:", json.dumps(result_response.json(), indent=2, ensure_ascii=False))
```

### 8.6 错误处理

所有API都遵循统一的错误响应格式：

```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "参数验证失败",
        "details": {
            "field": "inquiry_text",
            "reason": "不能为空"
        }
    }
}
```

常见错误码：
- `VALIDATION_ERROR`: 参数验证失败
- `SESSION_NOT_FOUND`: 会话不存在
- `AGENT_ERROR`: Agent处理错误
- `FILE_ERROR`: 文件处理错误
- `SYSTEM_ERROR`: 系统内部错误

## 9. 总结

这个完整的实施方案解决了您提出的关键问题：

### 9.1 目录结构优化
- ✅ 添加了 `app/main.py` 作为主启动文件
- ✅ 完善了API路由结构 (`app/api/v1/`)
- ✅ 添加了配置管理 (`app/config.py`, `app/dependencies.py`)
- ✅ 包含了所有必要的 `__init__.py` 文件
- ✅ 添加了启动脚本 (`scripts/start.sh`)

### 9.2 API接口实现
- ✅ 询价管理API：创建、状态查询、结果获取
- ✅ 对话管理API：消息发送、智能路由、历史记录
- ✅ 文件管理API：上传、下载、信息查询
- ✅ 分析报告API：报价分析、决策建议、报告导出

### 9.3 启动方式
- ✅ 开发环境一键启动脚本
- ✅ 直接启动主应用的方法
- ✅ 环境变量配置说明
- ✅ API访问地址和文档

### 9.4 核心特性
- 🚀 **FastAPI主应用**: `app/main.py` 包含完整的应用初始化
- 🔧 **智能路由**: 通过 `/api/v1/conversation/{session_id}/route` 实现动态分发
- 📁 **文件处理**: 支持OSS存储和本地缓存的混合方案
- 💾 **记忆管理**: Redis专门用于对话上下文缓存
- 🛠️ **MCP工具**: 集成Context7和自定义工具服务器
- 📊 **监控预留**: 健康检查和指标端点

按照这个方案，您可以：
1. 使用 `./scripts/start.sh` 一键启动整个系统
2. 通过 `http://localhost:8000/docs` 查看完整的API文档
3. 使用智能路由API实现灵活的Agent调度
4. 通过配置文件轻松调整系统参数

这个架构既满足了您的技术需求，又提供了清晰的启动和使用方式。