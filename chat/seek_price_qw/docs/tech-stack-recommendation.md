# 供应商询价系统技术栈推荐

## 1. 推荐技术栈

### 1.1 核心技术栈

```yaml
后端框架: FastAPI (Python 3.11+)
数据库: MySQL 8.0+ (兼容PostgreSQL)
缓存: Redis
消息队列: Redis + Celery
AI框架: 自建Agent框架 + 直接LLM API调用
工具协议: MCP (Model Context Protocol)
文件存储: MinIO (S3兼容)
监控: Prometheus + Grafana
容器化: Docker + Docker Compose
```

### 1.2 技术选择理由

| 组件 | 选择 | 理由 |
|------|------|------|
| **后端框架** | FastAPI | 高性能、自动API文档、异步支持、类型提示 |
| **主数据库** | MySQL 8.0+ | 现有基础设施、JSON字段支持、成熟稳定 |
| **数据库兼容** | SQLAlchemy ORM | 支持多数据库，便于后续迁移PostgreSQL |
| **缓存** | Redis | 高性能、支持多种数据结构、持久化选项 |
| **消息队列** | Celery + Redis | 成熟稳定、支持分布式任务、监控完善 |
| **AI框架** | 自建轻量框架 | 更灵活、更好的MCP集成、减少依赖 |
| **工具协议** | MCP客户端 | 标准化工具调用、可扩展性强 |

## 2. 自建Agent框架设计

### 2.1 Agent框架架构

```mermaid
graph TB
    subgraph "Agent框架核心"
        A[Agent管理器]
        B[任务调度器]
        C[工具注册表]
        D[记忆管理器]
    end
    
    subgraph "LLM集成层"
        E[OpenAI客户端]
        F[Claude客户端]
        G[其他LLM客户端]
    end
    
    subgraph "MCP工具层"
        H[Context7客户端]
        I[自定义MCP工具]
        J[标准工具集]
    end
    
    subgraph "业务Agent"
        K[任务分析Agent]
        L[信息收集Agent]
        M[沟通Agent]
        N[分析Agent]
        O[决策Agent]
    end
    
    A --> B
    A --> C
    A --> D
    B --> K
    B --> L
    B --> M
    B --> N
    B --> O
    
    K --> E
    L --> F
    M --> G
    
    C --> H
    C --> I
    C --> J
```

### 2.2 核心框架代码

```python
# app/core/agent_framework.py
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel
import asyncio
import json
from datetime import datetime

class Message(BaseModel):
    role: str  # system, user, assistant, tool
    content: str
    tool_calls: Optional[List[Dict]] = None
    tool_call_id: Optional[str] = None
    timestamp: datetime = datetime.now()

class AgentConfig(BaseModel):
    name: str
    system_prompt: str
    model: str = "gpt-4"
    temperature: float = 0.1
    max_tokens: int = 2000
    tools: List[str] = []
    memory_enabled: bool = True

class AgentResult(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    messages: List[Message] = []
    error: Optional[str] = None
    metadata: Dict[str, Any] = {}

class LLMClient(ABC):
    """LLM客户端抽象基类"""
    
    @abstractmethod
    async def chat_completion(
        self, 
        messages: List[Message], 
        model: str = "gpt-4",
        temperature: float = 0.1,
        max_tokens: int = 2000,
        tools: Optional[List[Dict]] = None
    ) -> Message:
        pass

class OpenAIClient(LLMClient):
    """OpenAI客户端实现"""
    
    def __init__(self, api_key: str):
        import openai
        self.client = openai.AsyncOpenAI(api_key=api_key)
    
    async def chat_completion(
        self, 
        messages: List[Message], 
        model: str = "gpt-4",
        temperature: float = 0.1,
        max_tokens: int = 2000,
        tools: Optional[List[Dict]] = None
    ) -> Message:
        # 转换消息格式
        openai_messages = [
            {"role": msg.role, "content": msg.content}
            for msg in messages
        ]
        
        kwargs = {
            "model": model,
            "messages": openai_messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        if tools:
            kwargs["tools"] = tools
            kwargs["tool_choice"] = "auto"
        
        response = await self.client.chat.completions.create(**kwargs)
        
        message = response.choices[0].message
        return Message(
            role="assistant",
            content=message.content or "",
            tool_calls=message.tool_calls if hasattr(message, 'tool_calls') else None
        )

class ClaudeClient(LLMClient):
    """Claude客户端实现"""
    
    def __init__(self, api_key: str):
        import anthropic
        self.client = anthropic.AsyncAnthropic(api_key=api_key)
    
    async def chat_completion(
        self, 
        messages: List[Message], 
        model: str = "claude-3-sonnet-20240229",
        temperature: float = 0.1,
        max_tokens: int = 2000,
        tools: Optional[List[Dict]] = None
    ) -> Message:
        # Claude的消息格式处理
        system_message = ""
        claude_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_message = msg.content
            else:
                claude_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        kwargs = {
            "model": model,
            "messages": claude_messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        if system_message:
            kwargs["system"] = system_message
        
        if tools:
            kwargs["tools"] = tools
        
        response = await self.client.messages.create(**kwargs)
        
        return Message(
            role="assistant",
            content=response.content[0].text if response.content else ""
        )
```

### 2.3 MCP工具集成

```python
# app/core/mcp_client.py
import asyncio
import json
from typing import Any, Dict, List, Optional
import httpx
from pydantic import BaseModel

class MCPTool(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]
    server_url: str
    
class MCPClient:
    """MCP协议客户端"""
    
    def __init__(self):
        self.tools: Dict[str, MCPTool] = {}
        self.clients: Dict[str, httpx.AsyncClient] = {}
    
    async def register_mcp_server(self, server_name: str, server_url: str):
        """注册MCP服务器"""
        client = httpx.AsyncClient(base_url=server_url, timeout=30.0)
        self.clients[server_name] = client
        
        # 获取服务器支持的工具列表
        try:
            response = await client.post("/mcp/list_tools")
            if response.status_code == 200:
                tools_data = response.json()
                for tool_data in tools_data.get("tools", []):
                    tool = MCPTool(
                        name=f"{server_name}_{tool_data['name']}",
                        description=tool_data.get("description", ""),
                        parameters=tool_data.get("parameters", {}),
                        server_url=server_url
                    )
                    self.tools[tool.name] = tool
        except Exception as e:
            print(f"Failed to register MCP server {server_name}: {e}")
    
    async def call_tool(self, tool_name: str, **kwargs) -> Any:
        """调用MCP工具"""
        if tool_name not in self.tools:
            raise ValueError(f"Tool {tool_name} not found")
        
        tool = self.tools[tool_name]
        server_name = tool_name.split("_")[0]
        actual_tool_name = "_".join(tool_name.split("_")[1:])
        
        client = self.clients[server_name]
        
        try:
            response = await client.post("/mcp/call_tool", json={
                "name": actual_tool_name,
                "arguments": kwargs
            })
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"MCP tool call failed: {response.text}")
                
        except Exception as e:
            raise Exception(f"Error calling MCP tool {tool_name}: {e}")
    
    def get_tools_schema(self) -> List[Dict[str, Any]]:
        """获取所有工具的OpenAI函数调用格式"""
        schemas = []
        for tool in self.tools.values():
            schema = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.parameters
                }
            }
            schemas.append(schema)
        return schemas

# Context7 MCP客户端特化
class Context7MCPClient(MCPClient):
    """Context7专用MCP客户端"""
    
    async def resolve_library_id(self, library_name: str) -> str:
        """解析库ID"""
        return await self.call_tool("context7_resolve-library-id", 
                                  libraryName=library_name)
    
    async def get_library_docs(self, library_id: str, topic: str = None, tokens: int = 10000) -> str:
        """获取库文档"""
        kwargs = {
            "context7CompatibleLibraryID": library_id,
            "tokens": tokens
        }
        if topic:
            kwargs["topic"] = topic
            
        return await self.call_tool("context7_get-library-docs", **kwargs)
```

### 2.4 Agent基础框架

```python
# app/core/base_agent.py
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import structlog

from app.core.agent_framework import AgentConfig, AgentResult, Message, LLMClient
from app.core.mcp_client import MCPClient
from app.core.memory import Memory

logger = structlog.get_logger()

class BaseAgent(ABC):
    """Agent基础类"""
    
    def __init__(
        self, 
        config: AgentConfig, 
        llm_client: LLMClient,
        mcp_client: MCPClient,
        memory: Memory
    ):
        self.config = config
        self.llm_client = llm_client
        self.mcp_client = mcp_client
        self.memory = memory
        self.logger = logger.bind(agent=config.name)
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any]) -> AgentResult:
        """处理输入数据"""
        pass
    
    async def chat_with_llm(
        self, 
        messages: List[Message], 
        use_tools: bool = True
    ) -> Message:
        """与LLM对话"""
        tools = None
        if use_tools and self.config.tools:
            tools = self.mcp_client.get_tools_schema()
            # 只包含当前Agent配置的工具
            tools = [t for t in tools if t["function"]["name"] in self.config.tools]
        
        response = await self.llm_client.chat_completion(
            messages=messages,
            model=self.config.model,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens,
            tools=tools
        )
        
        # 处理工具调用
        if response.tool_calls:
            for tool_call in response.tool_calls:
                tool_name = tool_call["function"]["name"]
                tool_args = json.loads(tool_call["function"]["arguments"])
                
                try:
                    tool_result = await self.mcp_client.call_tool(tool_name, **tool_args)
                    # 添加工具调用结果到消息历史
                    messages.append(Message(
                        role="tool",
                        content=json.dumps(tool_result),
                        tool_call_id=tool_call["id"]
                    ))
                except Exception as e:
                    self.logger.error("Tool call failed", tool=tool_name, error=str(e))
                    messages.append(Message(
                        role="tool",
                        content=f"Error: {str(e)}",
                        tool_call_id=tool_call["id"]
                    ))
            
            # 再次调用LLM处理工具结果
            response = await self.llm_client.chat_completion(
                messages=messages,
                model=self.config.model,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
        
        return response
    
    async def save_memory(self, session_id: str, key: str, value: Any):
        """保存记忆"""
        if self.config.memory_enabled:
            await self.memory.save(session_id, key, value)
    
    async def get_memory(self, session_id: str, key: str) -> Any:
        """获取记忆"""
        if self.config.memory_enabled:
            return await self.memory.get(session_id, key)
        return None
```

## 3. 数据库兼容设计

### 3.1 SQLAlchemy模型设计

```python
# app/models/base.py
from sqlalchemy import Column, String, DateTime, Text, Integer, Decimal
from sqlalchemy.dialects.mysql import JSON as MySQLJSON
from sqlalchemy.dialects.postgresql import JSONB, UUID as PostgresUUID
from sqlalchemy.types import TypeDecorator, CHAR
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine
import uuid
import json

Base = declarative_base()

class GUID(TypeDecorator):
    """数据库无关的UUID类型"""
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(PostgresUUID())
        else:
            return dialect.type_descriptor(CHAR(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return str(uuid.UUID(value))
            return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value

class JSONType(TypeDecorator):
    """数据库无关的JSON类型"""
    impl = Text
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'mysql':
            return dialect.type_descriptor(MySQLJSON())
        elif dialect.name == 'postgresql':
            return dialect.type_descriptor(JSONB())
        else:
            return dialect.type_descriptor(Text())

    def process_bind_param(self, value, dialect):
        if value is not None:
            if dialect.name in ('mysql', 'postgresql'):
                return value
            else:
                return json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            if dialect.name in ('mysql', 'postgresql'):
                return value
            else:
                return json.loads(value)
        return value

# app/models/inquiry.py
from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.models.base import Base, GUID, JSONType
import uuid

class InquirySession(Base):
    __tablename__ = "inquiry_sessions"
    
    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    status = Column(String(20), nullable=False, default='active')
    requester_info = Column(JSONType, nullable=False)
    task_info = Column(JSONType, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    conversations = relationship("Conversation", back_populates="session")
    quotes = relationship("Quote", back_populates="session")
    analysis_results = relationship("AnalysisResult", back_populates="session")

class Supplier(Base):
    __tablename__ = "suppliers"
    
    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    contact_info = Column(JSONType, nullable=False)
    capabilities = Column(JSONType)
    rating = Column(Decimal(3, 2))
    status = Column(String(20), default='active')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    conversations = relationship("Conversation", back_populates="supplier")
    quotes = relationship("Quote", back_populates="supplier")
```

### 3.2 数据库配置

```python
# app/database.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import os

# 数据库URL配置
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://user:password@localhost/supplier_inquiry")

# 支持多种数据库
if DATABASE_URL.startswith("mysql"):
    # MySQL配置
    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=False
    )
elif DATABASE_URL.startswith("postgresql"):
    # PostgreSQL配置
    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=False
    )
else:
    # SQLite配置（开发用）
    engine = create_engine(
        DATABASE_URL,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def init_db():
    """初始化数据库"""
    from app.models.base import Base
    Base.metadata.create_all(bind=engine)
```

## 4. MCP工具配置

### 4.1 MCP服务配置

```yaml
# config/mcp_servers.yaml
mcp_servers:
  context7:
    url: "https://mcp.context7.com"
    timeout: 30
    tools:
      - "resolve-library-id"
      - "get-library-docs"
  
  custom_tools:
    url: "http://localhost:8001/mcp"
    timeout: 10
    tools:
      - "email_sender"
      - "price_calculator"
      - "risk_assessor"

# Agent工具配置
agent_tools:
  task_analyzer:
    - "context7_resolve-library-id"
    - "custom_tools_price_calculator"
  
  info_collector:
    - "context7_get-library-docs"
  
  communicator:
    - "custom_tools_email_sender"
  
  quote_analyzer:
    - "custom_tools_price_calculator"
    - "custom_tools_risk_assessor"
```

### 4.2 自定义MCP工具服务器

```python
# tools/mcp_server.py
from fastapi import FastAPI
from pydantic import BaseModel
from typing import Any, Dict, List
import smtplib
from email.mime.text import MIMEText

app = FastAPI(title="Custom MCP Tools Server")

class ToolCall(BaseModel):
    name: str
    arguments: Dict[str, Any]

class ToolResult(BaseModel):
    success: bool
    result: Any
    error: str = None

@app.post("/mcp/list_tools")
async def list_tools():
    """列出支持的工具"""
    return {
        "tools": [
            {
                "name": "email_sender",
                "description": "发送邮件给供应商",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "to": {"type": "string", "description": "收件人邮箱"},
                        "subject": {"type": "string", "description": "邮件主题"},
                        "content": {"type": "string", "description": "邮件内容"}
                    },
                    "required": ["to", "subject", "content"]
                }
            },
            {
                "name": "price_calculator",
                "description": "计算价格比较和分析",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "quotes": {"type": "array", "description": "报价列表"},
                        "analysis_type": {"type": "string", "description": "分析类型"}
                    },
                    "required": ["quotes"]
                }
            }
        ]
    }

@app.post("/mcp/call_tool")
async def call_tool(tool_call: ToolCall):
    """调用工具"""
    try:
        if tool_call.name == "email_sender":
            result = await send_email(**tool_call.arguments)
        elif tool_call.name == "price_calculator":
            result = await calculate_price(**tool_call.arguments)
        else:
            return ToolResult(success=False, error=f"Unknown tool: {tool_call.name}")
        
        return ToolResult(success=True, result=result)
    
    except Exception as e:
        return ToolResult(success=False, error=str(e))

async def send_email(to: str, subject: str, content: str):
    """发送邮件实现"""
    # 这里实现实际的邮件发送逻辑
    return {"message": f"Email sent to {to}", "subject": subject}

async def calculate_price(quotes: List[Dict], analysis_type: str = "comparison"):
    """价格计算实现"""
    # 这里实现价格分析逻辑
    return {"analysis": "price_comparison_result", "type": analysis_type}
```

## 5. 更新的项目结构

```
supplier-inquiry-system/
├── app/
│   ├── core/                  # 核心框架
│   │   ├── agent_framework.py # Agent框架核心
│   │   ├── mcp_client.py     # MCP客户端
│   │   ├── base_agent.py     # Agent基础类
│   │   ├── orchestrator.py   # Agent编排器
│   │   └── memory.py         # 记忆管理
│   │
│   ├── agents/               # 具体Agent实现
│   │   ├── task_analyzer.py
│   │   ├── info_collector.py
│   │   ├── communicator.py
│   │   ├── quote_analyzer.py
│   │   └── decision_support.py
│   │
│   ├── models/               # 数据库模型（兼容设计）
│   │   ├── base.py          # 基础类型定义
│   │   ├── inquiry.py
│   │   ├── supplier.py
│   │   └── conversation.py
│   │
│   └── ...                  # 其他模块保持不变
│
├── tools/                   # 自定义MCP工具服务器
│   ├── mcp_server.py
│   └── custom_tools/
│
├── config/
│   ├── mcp_servers.yaml    # MCP服务器配置
│   └── agents.yaml         # Agent配置
│
└── requirements.txt        # 更新的依赖
```

## 6. 更新的依赖包

```txt
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# 数据库 - 兼容多种数据库
sqlalchemy==2.0.23
alembic==1.13.1
# MySQL
pymysql==1.1.0
# PostgreSQL (可选)
psycopg2-binary==2.9.9
# SQLite (开发用)
aiosqlite==0.19.0

# 缓存和队列
redis==5.0.1
celery==5.3.4

# LLM客户端 - 直接调用
openai==1.6.1
anthropic==0.8.1
httpx==0.25.2

# 工具库
python-multipart==0.0.6
python-email-validator==2.1.0
jinja2==3.1.2

# 监控和日志
structlog==23.2.0
prometheus-client==0.19.0

# 开发工具
pytest==7.4.3
black==23.11.0
isort==5.12.0
```

这个调整后的方案有以下优势：

1. **数据库兼容**: 使用SQLAlchemy ORM，支持MySQL和PostgreSQL无缝切换
2. **轻量化**: 去掉LangChain依赖，自建更灵活的Agent框架
3. **MCP优先**: 原生支持MCP协议，便于工具扩展
4. **现有基础**: 基于您现有的MySQL数据库
5. **可扩展**: 框架设计支持后续功能扩展

您觉得这个调整后的方案如何？ 