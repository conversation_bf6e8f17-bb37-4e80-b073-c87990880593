# 供应商询价多Agent系统设计方案

## 1. 项目概述

### 1.1 系统目标
构建一个基于多Agent的智能供应商询价系统，自动化处理供应商沟通、信息收集、报价分析和决策支持等全流程。

### 1.2 核心特性
- **智能对话**: 多轮供应商沟通，自动回答问题
- **文件处理**: 支持技术文档、报价单等文件交换
- **数据分析**: 自动化报价比较和风险评估
- **决策支持**: 生成采购建议和行动计划

### 1.3 技术架构设计

系统采用分层架构设计：

**Agent层**：
- 任务分析Agent：解析询价需求，提取关键信息
- 信息收集Agent：准备公司信息和技术规格
- 供应商沟通Agent：处理邮件往来和问题回答
- 报价分析Agent：解析报价文档，进行比较分析
- 决策支持Agent：生成决策报告和建议

**路由层**：
- 智能路由器：根据请求类型分发到相应Agent
- 编排器：协调多Agent协作流程

**工具层**：
- MCP工具服务器：标准化工具接口
- Context7集成：获取技术文档和规格
- 邮件服务：自动化邮件发送
- 文件处理：支持多种格式解析

**数据层**：
- MySQL数据库：持久化存储
- Redis缓存：对话上下文记忆
- 阿里云OSS：文件存储

## 2. 技术栈选择

### 2.1 核心技术决策

**后端框架**：FastAPI
- 选择理由：高性能、自动API文档、类型检查支持

**数据库**：MySQL 8.0+
- 选择理由：成熟稳定、兼容性好、支持JSON字段

**缓存系统**：Redis 7+
- 用途：对话上下文缓存、会话状态管理

**文件存储**：阿里云OSS + 本地缓存
- 策略：混合存储，本地缓存提高访问速度

**AI框架**：自建轻量Agent框架
- 优势：灵活定制、易于扩展、减少依赖

**LLM支持**：OpenAI GPT-4 / Claude-4
- 策略：多提供商支持，避免单点依赖

**工具协议**：MCP (Model Context Protocol)
- 优势：标准化工具接口、易于集成第三方服务

### 2.2 架构设计原则

**模块化设计**：
- 每个Agent独立实现，便于单独测试和优化
- 工具层标准化，支持插件式扩展

**兼容性设计**：
- 数据库类型兼容（MySQL/PostgreSQL）
- 多LLM提供商支持
- 文件格式多样化支持

**可扩展性**：
- 预留监控接口
- 配置化管理
- 容器化部署支持

## 3. 详细设计方案

### 第一阶段：基础框架搭建

#### 3.1 项目结构设计

采用标准的Python项目结构，清晰的模块分层和职责划分：

```
supplier-inquiry-system/
├── app/                          # 主应用目录
│   ├── __init__.py              # 包初始化
│   ├── main.py                  # FastAPI主应用启动文件
│   ├── config.py                # 配置管理
│   ├── database.py              # 数据库连接
│   ├── dependencies.py          # 依赖注入
│   │
│   ├── core/                    # 核心框架
│   │   ├── __init__.py
│   │   ├── agent_framework.py   # Agent框架基础
│   │   ├── mcp_client.py        # MCP客户端
│   │   ├── base_agent.py        # Agent基类
│   │   ├── router.py            # 智能路由器
│   │   ├── orchestrator.py      # 编排器
│   │   └── memory.py            # Redis记忆管理
│   │
│   ├── agents/                  # 具体Agent实现
│   │   ├── __init__.py
│   │   ├── task_analyzer.py     # 任务分析Agent
│   │   ├── info_collector.py    # 信息收集Agent
│   │   ├── communicator.py      # 供应商沟通Agent
│   │   ├── quote_analyzer.py    # 报价分析Agent
│   │   └── decision_support.py  # 决策支持Agent
│   │
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py              # 兼容性基础类型
│   │   ├── inquiry.py           # 询价相关模型
│   │   ├── supplier.py          # 供应商模型
│   │   ├── conversation.py      # 对话模型
│   │   ├── quote.py             # 报价模型
│   │   └── analysis.py          # 分析结果模型
│   │
│   ├── services/                # 业务服务层
│   │   ├── __init__.py
│   │   ├── file_service.py      # 文件管理服务
│   │   ├── oss_service.py       # OSS集成服务
│   │   ├── notification_service.py # 通知服务
│   │   ├── llm_service.py       # LLM服务封装
│   │   └── cache_service.py     # 缓存服务
│   │
│   ├── api/                     # API路由模块
│   │   ├── __init__.py
│   │   ├── v1/                  # API版本1
│   │   │   ├── __init__.py
│   │   │   ├── inquiry.py       # 询价相关API
│   │   │   ├── conversation.py  # 对话相关API
│   │   │   ├── files.py         # 文件相关API
│   │   │   ├── analysis.py      # 分析相关API
│   │   │   └── suppliers.py     # 供应商管理API
│   │   └── deps.py              # API依赖
│   │
│   ├── middleware/              # 中间件
│   │   ├── __init__.py
│   │   ├── monitoring.py        # 监控中间件
│   │   ├── logging.py           # 日志中间件
│   │   ├── auth.py              # 认证中间件（预留）
│   │   └── rate_limit.py        # 限流中间件（预留）
│   │
│   └── utils/                   # 工具函数
│       ├── __init__.py
│       ├── logger.py            # 日志工具
│       ├── validators.py        # 验证器
│       ├── formatters.py        # 格式化工具
│       └── exceptions.py        # 自定义异常
│
├── tools/                       # MCP工具服务器
│   ├── __init__.py
│   ├── mcp_server.py           # MCP工具服务器主文件
│   └── custom_tools/           # 自定义工具
│       ├── __init__.py
│       ├── email_sender.py     # 邮件发送工具
│       ├── price_calculator.py # 价格计算工具
│       ├── risk_assessor.py    # 风险评估工具
│       └── document_generator.py # 文档生成工具
│
├── config/                      # 配置文件
│   ├── agents.yaml             # Agent配置
│   ├── mcp_servers.yaml        # MCP服务器配置
│   ├── database.yaml           # 数据库配置
│   └── logging.yaml            # 日志配置
│
├── alembic/                     # 数据库迁移
│   ├── versions/               # 迁移版本文件
│   ├── env.py                  # 迁移环境配置
│   └── script.py.mako          # 迁移脚本模板
│
├── tests/                       # 测试文件
│   ├── __init__.py
│   ├── conftest.py             # 测试配置
│   ├── unit/                   # 单元测试
│   │   ├── test_agents.py
│   │   ├── test_services.py
│   │   └── test_utils.py
│   ├── integration/            # 集成测试
│   │   ├── test_api.py
│   │   ├── test_workflows.py
│   │   └── test_mcp.py
│   └── e2e/                    # 端到端测试
│       └── test_full_flow.py
│
├── scripts/                     # 脚本文件
│   ├── start.sh                # 启动脚本
│   ├── stop.sh                 # 停止脚本
│   ├── init_db.py              # 数据库初始化
│   ├── migrate.py              # 数据迁移脚本
│   └── backup.py               # 备份脚本
│
├── docs/                        # 文档目录
│   ├── api/                    # API文档
│   ├── design/                 # 设计文档
│   ├── deployment/             # 部署文档
│   └── user_guide/             # 用户指南
│
├── docker/                      # Docker相关文件
│   ├── Dockerfile              # 主应用镜像
│   ├── docker-compose.yml      # 开发环境编排
│   ├── docker-compose.prod.yml # 生产环境编排
│   └── nginx/                  # Nginx配置（可选）
│
├── .env.example                # 环境变量示例
├── .env.local                  # 本地环境变量
├── .gitignore                  # Git忽略文件
├── requirements.txt            # Python依赖
├── requirements-dev.txt        # 开发依赖
├── alembic.ini                # Alembic配置
├── pyproject.toml             # 项目配置
├── Makefile                   # 构建脚本
└── README.md                  # 项目说明
```

**目录设计原则**：

**分层清晰**：
- `core/`：核心框架和基础组件
- `agents/`：具体业务Agent实现
- `services/`：业务服务层，封装外部依赖
- `api/`：API接口层，处理HTTP请求
- `models/`：数据模型定义

**职责明确**：
- `middleware/`：横切关注点处理
- `utils/`：通用工具函数
- `config/`：配置文件集中管理
- `tests/`：测试代码分类组织

**扩展友好**：
- `tools/`：独立的MCP工具服务器
- `scripts/`：运维和管理脚本
- `docs/`：文档资料集中存放
- `docker/`：容器化部署配置

#### 3.2 数据库设计思路

**核心表设计**：
- 询价会话表：记录每次询价的基本信息
- 供应商信息表：维护供应商档案
- 对话记录表：存储完整的沟通历史
- 报价信息表：结构化存储报价数据
- 分析结果表：保存分析和决策结果
- 文件附件表：管理文件元数据

**兼容性设计**：
- 使用TypeDecorator实现跨数据库类型兼容
- JSON字段兼容MySQL和PostgreSQL
- UUID类型统一处理

#### 3.3 Agent框架核心设计

**基础组件**：
- Message类：统一消息格式
- AgentConfig类：Agent配置管理
- BaseAgent抽象类：定义Agent接口规范

**核心功能**：
- LLM客户端集成
- MCP工具调用
- 记忆管理接口
- 文件服务集成

#### 3.4 MCP工具集成策略

**客户端设计**：
- 通用MCP客户端：处理标准MCP协议
- Context7专用客户端：集成文档检索功能
- 工具注册机制：动态发现和注册工具

**工具类型**：
- 邮件发送工具
- 价格计算工具
- 风险评估工具
- 文档生成工具

#### 3.5 Redis记忆管理设计

**缓存策略**：
- 对话上下文：24小时TTL
- 消息历史：限制100条最新消息
- Agent状态：会话级别缓存
- 会话元数据：7天保留期

**数据结构**：
- 分层键值设计
- JSON序列化存储
- 自动过期清理

#### 3.6 文件存储系统设计

**存储策略**：
- 本地临时存储：处理中的文件
- OSS永久存储：长期保存的文件
- 缓存机制：提高访问速度

**文件处理流程**：
- 上传验证：类型、大小检查
- 本地处理：格式转换、内容提取
- OSS上传：永久存储
- 元数据记录：数据库索引

### 第二阶段：Agent实现

#### 3.6 任务分析Agent设计

**核心功能**：
- 自然语言需求解析
- 关键信息提取（物料类型、规格、数量）
- 缺失信息识别
- 结构化任务生成

**处理流程**：
- 接收询价文本
- LLM分析处理
- 结果结构化
- 缺失信息标记

#### 3.7 信息收集Agent设计

**信息类型**：
- 公司基本信息
- 项目详细信息
- 物料技术规格
- 相关技术文档

**处理策略**：
- 信息模板化管理
- 动态信息组装
- 脱敏处理
- Context7文档集成

#### 3.8 供应商沟通Agent设计

**沟通功能**：
- 初始询价邮件发送
- 供应商问题智能回答
- 文件交换处理
- 报价信息收集

**交互模式**：
- 多轮对话支持
- 异步消息处理
- 文件附件处理
- 状态跟踪管理

#### 3.9 报价分析Agent设计

**分析能力**：
- 多格式文档解析（PDF、Excel）
- 价格信息提取
- 技术规格比较
- 交期分析

**比较维度**：
- 价格竞争力
- 技术符合度
- 供应商信誉
- 交期可靠性

#### 3.10 决策支持Agent设计

**决策功能**：
- 综合分析报告生成
- 最佳供应商推荐
- 谈判策略建议
- 风险评估报告

**输出格式**：
- 结构化决策数据
- PDF报告生成
- 行动计划制定
- 关键指标总结

#### 3.11 MCP工具服务器设计

**工具类型**：
- 邮件发送工具：支持附件、模板
- 价格计算工具：多维度比较分析
- 风险评估工具：供应商风险评级
- 文档生成工具：报告自动生成

**接口设计**：
- 标准MCP协议
- RESTful API
- 工具发现机制
- 错误处理机制

### 第三阶段：系统集成

#### 3.12 智能路由器设计

**路由策略**：
- 请求类型识别
- Agent能力匹配
- 负载均衡考虑
- 故障转移机制

**路由规则**：
- 静态路由表
- 动态状态判断
- 优先级管理
- 异常处理

#### 3.13 编排器设计

**编排功能**：
- 流程状态管理
- Agent协调调度
- 并行任务处理
- 异常恢复机制

**流程控制**：
- 状态机设计
- 条件分支处理
- 循环控制
- 超时管理

#### 3.14 API接口设计

**接口分类**：
- 询价管理API：CRUD操作
- 对话管理API：消息处理
- 文件管理API：上传下载
- 分析报告API：结果查询

**设计原则**：
- RESTful风格
- 统一响应格式
- 错误码标准化
- 版本管理

#### 3.15 监控接口设计

**监控维度**：
- 系统健康状态
- Agent执行性能
- 资源使用情况
- 业务指标统计

**接口类型**：
- 健康检查端点
- Prometheus指标端点
- 日志聚合接口
- 告警通知接口

#### 3.16 配置管理设计

**配置类型**：
- 应用基础配置
- 数据库连接配置
- AI模型配置
- 第三方服务配置

**管理策略**：
- 环境变量优先
- 配置文件备选
- 运行时更新
- 敏感信息加密

### 第四阶段：测试和优化

#### 3.17 测试策略

**测试层次**：
- 单元测试：Agent功能测试
- 集成测试：API接口测试
- 端到端测试：完整流程测试
- 性能测试：负载和压力测试

**测试重点**：
- Agent逻辑正确性
- 文件处理稳定性
- 并发处理能力
- 异常恢复机制

#### 3.18 性能优化策略

**优化方向**：
- 数据库查询优化
- Redis缓存策略优化
- 异步任务处理优化
- 文件处理性能优化

**监控指标**：
- 响应时间
- 吞吐量
- 资源利用率
- 错误率

## 4. 部署架构设计

### 4.1 环境规划

**开发环境**：
- 本地开发：单机部署
- 依赖服务：MySQL、Redis本地实例
- 调试工具：日志、监控面板

**生产环境**：
- 服务器配置：2核4GB起步
- 数据库：独立MySQL实例
- 缓存：独立Redis实例
- 存储：阿里云OSS

### 4.2 部署策略

**容器化部署**：
- Docker镜像构建
- Docker Compose编排
- 环境变量配置
- 数据卷管理

**传统部署**：
- 虚拟环境隔离
- 系统服务配置
- 进程管理
- 日志轮转

### 4.3 监控运维

**监控体系**：
- 应用性能监控
- 基础设施监控
- 业务指标监控
- 日志聚合分析

**运维自动化**：
- 自动化部署
- 健康检查
- 故障恢复
- 备份策略

## 5. 风险评估和应对

### 5.1 技术风险

**LLM服务风险**：
- 风险：API限流、服务不稳定
- 应对：多提供商备份、请求队列、降级策略

**数据存储风险**：
- 风险：数据库性能瓶颈、数据丢失
- 应对：索引优化、读写分离、定期备份

**文件存储风险**：
- 风险：OSS服务故障、文件损坏
- 应对：多区域备份、本地缓存、校验机制

**集成服务风险**：
- 风险：MCP服务不稳定、第三方API变更
- 应对：降级策略、重试机制、版本锁定

### 5.2 业务风险

**供应商配合风险**：
- 风险：供应商不响应、信息不完整
- 应对：人工介入机制、多渠道联系、催办提醒

**数据准确性风险**：
- 风险：报价解析错误、信息理解偏差
- 应对：人工审核、多重验证、置信度评估

**信息安全风险**：
- 风险：敏感信息泄露、数据被篡改
- 应对：数据脱敏、权限控制、审计日志

**业务连续性风险**：
- 风险：系统故障、服务中断
- 应对：高可用架构、故障转移、应急预案

## 6. 成本分析

### 6.1 开发成本

**人力投入**：
- 开发周期：8周
- 人员配置：1名全栈工程师
- 技能要求：Python、AI、数据库、云服务

**技术成本**：
- LLM API费用：根据使用量计费
- 云服务费用：服务器、存储、网络
- 第三方服务：邮件、监控等

### 6.2 运营成本

**基础设施成本**：
- 服务器租赁
- 数据库服务
- 存储服务
- 网络带宽

**维护成本**：
- 系统监控
- 安全防护
- 数据备份
- 技术支持

### 6.3 成本优化策略

**资源优化**：
- 按需扩缩容
- 资源池化
- 缓存策略
- 压缩存储

**服务优化**：
- API调用优化
- 批量处理
- 异步处理
- 智能路由

## 7. 扩展规划

### 7.1 功能扩展

**短期扩展**（3个月）：
- 支持更多物料类型
- 提升报价解析准确率
- 增加移动端支持
- 集成更多供应商平台

**中期扩展**（6个月）：
- 智能供应商推荐系统
- 历史数据分析挖掘
- 自动化合同生成
- 多语言国际化支持

**长期扩展**（1年）：
- 供应链风险预警系统
- 市场价格趋势分析
- 智能采购决策引擎
- 区块链溯源集成

### 7.2 技术演进

**架构演进**：
- 微服务化改造
- 事件驱动架构
- 分布式部署
- 边缘计算支持

**AI能力提升**：
- 模型微调优化
- 多模态处理
- 知识图谱集成
- 强化学习应用

### 7.3 生态建设

**开放平台**：
- API开放
- 插件机制
- 第三方集成
- 开发者社区

**行业应用**：
- 垂直行业定制
- 标准化模板
- 最佳实践分享
- 案例库建设

## 8. 总结

### 8.1 方案优势

**技术先进性**：
- 采用最新的Agent架构设计
- 集成MCP标准化工具协议
- 支持多种LLM提供商
- 兼容性强的数据库设计

**架构灵活性**：
- 模块化设计便于扩展
- 智能路由支持动态调度
- 配置化管理降低维护成本
- 容器化部署提高可移植性

**功能完整性**：
- 覆盖询价全流程
- 支持多种文件格式
- 提供智能决策支持
- 具备完善的监控体系

**可扩展性**：
- 预留扩展接口
- 支持水平扩展
- 插件化架构
- 开放API设计

### 8.2 实施建议

**分阶段实施**：
- 先搭建基础框架
- 逐步实现各个Agent
- 完善系统集成
- 持续优化改进

**风险控制**：
- 制定详细的测试计划
- 建立完善的监控体系
- 准备应急预案
- 定期安全评估

**团队建设**：
- 技能培训
- 文档完善
- 知识分享
- 经验积累

按照这个设计方案实施，可以构建一个功能完整、性能稳定、易于扩展的供应商询价多Agent系统，为企业采购流程的数字化转型提供强有力的技术支撑。 