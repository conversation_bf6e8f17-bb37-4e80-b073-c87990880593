#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.database import get_db_session
from app.database import db_manager
from app.models.user import SysUser
from app.core.auth import auth_manager
from datetime import datetime


def create_test_users():
    """创建测试用户"""
    with db_manager.get_db_context("material") as db:
        # 创建管理员用户
        admin_user = db.query(SysUser).filter(SysUser.user_name == "admin").first()
        if not admin_user:
            hashed_password = auth_manager.get_password_hash("admin123")
            admin_user = SysUser(
                user_name="admin",
                password_=hashed_password,
                nick_name="系统管理员",
                email_="<EMAIL>",
                status_=1,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            db.add(admin_user)
            print("✅ 创建管理员用户: admin / admin123")
        else:
            print("ℹ️  管理员用户已存在")
        
        # 创建测试用户
        test_user = db.query(SysUser).filter(SysUser.user_name == "lhh-aopu1").first()
        if not test_user:
            hashed_password = auth_manager.get_password_hash("bluemen.123")
            test_user = SysUser(
                user_name="lhh-aopu1",
                password_=hashed_password,
                nick_name="aopu测试用户",
                email_="<EMAIL>",
                phone_="13535035299",
                status_=1,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            db.add(test_user)
            print("✅ 创建测试用户: lhh-aopu / bluemen.123")
        else:
            print("ℹ️  测试用户已存在")
        
        db.commit()


if __name__ == "__main__":
    print("=== 数据库初始化 ===")
    
    try:
        create_test_users()
        print("\n🎉 数据库初始化完成！")
        print("\n可用的测试账户：")
        print("1. 管理员: admin / admin123")
        print("2. 测试用户: lhh-aopu / bluemen.123")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        import traceback
        traceback.print_exc() 