from dotenv import load_dotenv
import os

load_dotenv()

# 第一个数据库配置（现有的）
DB_MATERIAL_HOST = os.getenv("DB_MATERIAL_HOST")
DB_MATERIAL_PORT = os.getenv("DB_MATERIAL_PORT")
DB_MATERIAL_USERNAME = os.getenv("DB_MATERIAL_USERNAME")
DB_MATERIAL_PASSWORD = os.getenv("DB_MATERIAL_PASSWORD")
DB_MATERIAL_DATABASE = os.getenv("DB_MATERIAL_DATABASE")

# 第二个数据库配置（新增）
DB_SECOND_DATABASE = os.getenv("DB_SECOND_DATABASE")  # 第二个数据库名

# 数据库配置字典
DATABASE_CONFIGS = {
    "material": {
        "host": DB_MATERIAL_HOST,
        "port": DB_MATERIAL_PORT,
        "username": DB_MATERIAL_USERNAME,
        "password": DB_MATERIAL_PASSWORD,
        "database": DB_MATERIAL_DATABASE,
    },
    "second": {
        "host": DB_MATERIAL_HOST,  # 相同的主机
        "port": DB_MATERIAL_PORT,  # 相同的端口
        "username": DB_MATERIAL_USERNAME,  # 相同的用户名
        "password": DB_MATERIAL_PASSWORD,  # 相同的密码
        "database": DB_SECOND_DATABASE,  # 不同的数据库名
    }
}

REDIS_CONFIG = {
    "host": os.getenv("REDIS_HOST", "localhost"),
    "port": int(os.getenv("REDIS_PORT", 6379)),
    "db": int(os.getenv("REDIS_DB", 0)),
    "password": os.getenv("REDIS_PASSWORD"),
    "timeout": 10,
    "connect_timeout": 10,
    "session_prefix": "supplier_session",
    "message_prefix": "supplier_messages",
    "session_list_key": "supplier_session_list"
}