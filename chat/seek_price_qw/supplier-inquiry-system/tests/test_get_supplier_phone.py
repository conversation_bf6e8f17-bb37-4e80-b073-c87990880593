from app.services.component_supplier_service import ComponentSupplierService
from app.services.search_supplier_service import SearchSupplierService


def test_get_supplier_phone():
    component_supplier_service = ComponentSupplierService()
    phone = component_supplier_service.get_supplier_phone_by_name("广东兴发铝业有限公司")
    print(phone)

def test_get_supplier_phone_by_id():
    search_supplier_service = SearchSupplierService()
    phone_info_lists = search_supplier_service.get_suppliers_by_chat_status("ready")
    print(len(phone_info_lists))
    for phone_info in phone_info_lists:
        print(phone_info)
        print(phone_info.get("phone", ""))
    

def test_update_chat_status():
    search_supplier_service = SearchSupplierService()
    reuslt = search_supplier_service.update_chat_status_by_supplier_name("苏州华恒精密模具有限公司", "chatting")

    print(reuslt)

if __name__ == "__main__":
    # test_get_supplier_phone_by_id()

    test_update_chat_status()