"""供应商能力询问Agent

负责主动询问供应商的基本能力和信息，包括：
- 供应商基本情况
- 产品能力范围
- 认证资质
- 生产规模等
"""

import json
from typing import Dict, Any, List, Union
from datetime import datetime

from app.core.base_agent import (
    BaseAgent, AgentResult, AgentMessage, MessageRole, AgentConfig,
    create_success_result, create_error_result
)
from app.core.llm_client import LLMClientFactory
from app.utils.get_material_info import get_supplier_material_info
from app.utils.tool_function import extract_largest_json
from config.llm_config import DEFAULT_MODEL


class SupplierCapabilityAgent(BaseAgent):
    """供应商能力询问Agent"""
    
    def __init__(self, config: AgentConfig = None, llm_client=None):
        if config is None:
            config = AgentConfig(
                name="supplier_capability_agent",
                description="主动询问供应商能力和基本信息的Agent",
                system_prompt=self._get_system_prompt(),
                # model="gpt-4.1-2025-04-14",
                # model="deepseek/deepseek-chat-v3-0324",
                model=DEFAULT_MODEL,
                # temperature=0.7,
                # max_tokens=1000
            )
        super().__init__(config)

        # 注入LLM客户端
        self.llm_client = llm_client
        
        # 初始化LLM客户端
        if not self.llm_client:
            try:
                from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
                self.llm_client = LLMClientFactory.create_openai_client(
                    base_url=OPENAI_BASE_URL,
                    api_key=OPENAI_API_KEY,
                    default_model=self.config.model
                )
            except ImportError:
                # 如果没有配置文件，使用环境变量
                self.llm_client = LLMClientFactory.create_from_env()
        
        self.user_prompt = self.get_user_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一名专业的采购人员，负责与供应商进行商务沟通，主要任务是询问供应商对特定物料的供货能力。

工作要求：
1. 根据提供的物料信息，生成专业、礼貌、直接的询问消息
2. 开门见山询问供货能力，避免冗余的寒暄
3. 保持商务沟通的专业性和效率

输出格式：
必须返回JSON格式：
{
    "message": "询问内容",
    "reason": "选择这种询问方式的理由"
}

示例输出：
{
    "message": "您好，我们需要采购304不锈钢板材，厚度2.0mm，规格1500×3000mm，想了解贵司是否有相应的供货能力？",
    "reason": "直接说明物料规格和需求，便于供应商快速评估供货能力"
}
"""

    def get_user_prompt(self) -> str:
        """获取用户提示词"""
        return """
当前待询问的物料信息：
{material_info}

当前用户的问题：
{user_message}

工作要求：
1. 根据提供的物料信息，生成专业、礼貌、直接的询问消息
2. 开门见山询问供货能力，避免冗余的寒暄
3. 保持商务沟通的专业性和效率

输出格式：
必须返回JSON格式：
{{
    "message": "询问内容",
    "reason": "选择这种询问方式的理由"
}}

示例输出：
{{
    "message": "您好，我们需要采购304不锈钢板材，厚度2.0mm，规格1500×3000mm，想了解贵司是否有相应的供货能力？",
    "reason": "直接说明物料规格和需求，便于供应商快速评估供货能力"
}}
"""
    
    def get_supported_types(self) -> List[str]:
        """返回支持的请求类型"""
        return [
            "supplier_capability_inquiry",
            "basic_info_request",
            "qualification_inquiry",
            "production_capacity_check"
        ]
    
    async def process(self, session_id: str, data: Dict[str, Any]) -> AgentResult:
        """处理供应商能力询问请求"""
        start_time = datetime.now()
        
        try:
            # 验证输入
            if not await self.validate_input(data):
                return create_error_result("输入数据验证失败")
            
            # 预处理
            processed_data = await self.pre_process(session_id, data)
            
            # 获取用户消息和历史上下文
            user_message = processed_data.get("message", "")
            message_history = processed_data.get("message_history", [])
            supplier_info = processed_data.get("supplier_info", {})

            supplier_name = supplier_info.get("company_name", "")
            component_code = processed_data.get("component_code", [])

            material_info = get_supplier_material_info(supplier_name, component_code)
            
            # 构建对话上下文
            messages = await self._build_conversation_context(
                user_message, message_history, supplier_info, material_info
            )
            # print(f"对话上下文内容: {messages}")
            
            # 调用LLM生成回复
            response_message = await self.call_llm(messages)

            response_json = extract_largest_json(response_message.content)
            
            # 构建结果
            result = create_success_result(
                data={
                    "response": response_json.get("message", "提取大模型回复失败"),
                    "agent_type": "supplier_capability_agent",
                    "session_id": session_id,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                message="供应商能力询问处理完成"
            )
            
            # 添加响应消息到结果
            result.add_message(
                role=MessageRole.ASSISTANT,
                content=response_json.get("message", "提取大模型回复失败"),
                metadata={
                    "agent": self.name,
                    "processing_time": (datetime.now() - start_time).total_seconds()
                }
            )
            
            # 后处理
            result = await self.post_process(session_id, result)
            
            return result
            
        except Exception as e:
            self.log_error(f"处理供应商能力询问失败: {str(e)}")
            return create_error_result(
                error=f"处理失败: {str(e)}",
                data={"session_id": session_id}
            )
    
    async def _build_conversation_context(
        self, 
        user_message: str, 
        message_history: Union[List[Dict[str, Any]], List[AgentMessage]], 
        supplier_info: Dict[str, Any] = None,
        material_info: Dict[str, Any] = None
    ) -> List[AgentMessage]:
        """构建对话上下文"""
        messages = []
        
        # 系统提示
        system_prompt = self.config.system_prompt
        
        # 如果有供应商信息，添加到系统提示中
        if supplier_info:
            system_prompt += f"\n\n当前供应商信息：\n{json.dumps(supplier_info, ensure_ascii=False, indent=2)}"
        
        messages.append(AgentMessage(
            role=MessageRole.SYSTEM,
            content=system_prompt
        ))
        
        # 添加历史消息（最近20条）
        recent_history = message_history[-20:] if len(message_history) > 20 else message_history
        for msg in recent_history:
            if isinstance(msg, dict):
                messages.append(AgentMessage(
                    role=MessageRole(msg.get("role", "user")),
                    content=msg.get("content", "")
                ))
            elif isinstance(msg, AgentMessage):
                messages.append(msg)
        
        # 添加当前用户消息
        if user_message:
            user_question = self.user_prompt.format(
                material_info=json.dumps(material_info, ensure_ascii=False, indent=2),
                user_message=user_message
            )
            messages.append(AgentMessage(
                role=MessageRole.USER,
                content=user_question
            ))
        
        return messages
    
    async def validate_input(self, data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        if not isinstance(data, dict):
            return False
        
        # 检查必要字段
        if "message" not in data and "message_history" not in data:
            return False
        
        return True
    
    async def pre_process(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理数据"""
        # 获取会话记忆上下文
        memory_context = await self.get_memory_context(session_id)
        
        # 合并上下文信息
        processed_data = data.copy()
        if memory_context:
            processed_data["memory_context"] = memory_context
        
        return processed_data
    
    async def post_process(self, session_id: str, result: AgentResult) -> AgentResult:
        """后处理结果"""
        # 保存会话上下文到记忆
        if result.success and result.messages:
            context_to_save = {
                "last_interaction": datetime.now().isoformat(),
                "agent_type": "supplier_capability_agent",
                "last_response": result.messages[-1].content if result.messages else None
            }
            await self.save_memory_context(session_id, context_to_save)
        
        return result
    
    def get_capability_questions(self) -> List[str]:
        """获取常用的能力询问问题模板"""
        return [
            "请问贵公司主要生产什么类型的产品？",
            "能介绍一下贵公司的基本情况吗？比如成立时间、规模等？",
            "贵公司有哪些相关的认证资质？比如ISO认证？",
            "请问贵公司的年产能大概是多少？",
            "贵公司在技术研发方面有什么优势？",
            "能否提供定制化的产品和服务？",
            "贵公司的主要客户群体是哪些？",
            "请问贵公司的工厂位置在哪里？",
            "贵公司在质量控制方面有什么措施？",
            "能介绍一下贵公司的售后服务体系吗？"
        ]
    
    def __str__(self) -> str:
        return f"SupplierCapabilityAgent({self.name})"
    
    def __repr__(self) -> str:
        return f"SupplierCapabilityAgent(name='{self.name}', supported_types={self.get_supported_types()})"


# 使用示例
if __name__ == "__main__":
    import asyncio
    from app.core.llm_client import LLMClientFactory
    
    async def test_agent():
        # 创建Agent（使用自带的初始化配置）
        agent = SupplierCapabilityAgent()

        
        # 模拟处理请求
        test_data = {
            "message": "你好",
            "message_history": [],
            "supplier_info": {
                "company_name": "测试供应商",
                # "contact_person": "张经理"
            },
            "material_info": {
                "material_type": "304不锈钢板材",
                "other_info": "厚度2.0mm，规格1500×3000mm"
            }
        }
        
        result = await agent.process("test_session_001", test_data)
        print(f"处理结果: {result.success}")
        print(f"响应消息: {result.data.get('response') if result.data else 'None'}")
        print(f"错误信息: {result.error}")
    
    # 运行测试
    asyncio.run(test_agent())