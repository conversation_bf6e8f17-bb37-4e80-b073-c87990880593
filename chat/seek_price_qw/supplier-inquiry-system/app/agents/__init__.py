"""Agent模块

包含所有业务Agent的实现：
- SupplierCapabilityAgent: 供应商能力询问
- InfoResponseAgent: 信息回应
- QuoteRequestAgent: 报价请求
- GeneralChatAgent: 通用聊天
"""

from .supplier_capability_agent import SupplierCapabilityAgent
from .info_response_agent import InfoResponseAgent
from .quote_request_agent import QuoteRequestAgent

__all__ = [
    "SupplierCapabilityAgent",
    "InfoResponseAgent",
    "QuoteRequestAgent"
]

# Agent注册表
AGENT_REGISTRY = {
    "supplier_capability_agent": SupplierCapabilityAgent,
    "info_response_agent": InfoResponseAgent,
    "quote_request_agent": QuoteRequestAgent
}


def get_agent_class(agent_name: str):
    """根据名称获取Agent类"""
    return AGENT_REGISTRY.get(agent_name)


def list_available_agents():
    """列出所有可用的Agent"""
    return list(AGENT_REGISTRY.keys())