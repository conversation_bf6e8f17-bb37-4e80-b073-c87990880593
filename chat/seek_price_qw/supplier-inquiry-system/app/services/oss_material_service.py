"""
OSS物料服务类
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, distinct
from app.models.oss_material import OssMaterial
from app.database import db_manager

class OssMaterialService:
    """OSS物料服务类"""
    
    def __init__(self, db: Optional[Session] = None, db_name: str = "second"):
        self.db = db
        self.db_name = db_name
    
    def get_by_id(self, material_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取OSS物料记录，返回字典格式"""
        if self.db:
            record = self.db.query(OssMaterial).filter(OssMaterial.id == material_id).first()
            return record.to_dict() if record else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                record = db.query(OssMaterial).filter(OssMaterial.id == material_id).first()
                return record.to_dict() if record else None
    
    def get_materials_by_category(self, product_category: str, sub_category: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        根据分类信息查找对应的物料
        这是核心功能：根据分类信息来找对应的物料的OSS path
        """
        def _build_query(db: Session):
            query = db.query(OssMaterial).filter(
                OssMaterial.product_category == product_category
            )
            
            if sub_category and sub_category != '未分类':
                query = query.filter(OssMaterial.sub_category == sub_category)
            
            return query.limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def get_materials_by_categories(self, category_pairs: List[tuple], limit: int = 100) -> List[Dict[str, Any]]:
        """
        根据多个分类组合查找物料
        category_pairs: [(product_category, sub_category), ...]
        """
        def _build_query(db: Session):
            conditions = []
            
            for product_category, sub_category in category_pairs:
                if sub_category and sub_category != '未分类' and sub_category != '未细分':
                    # 如果有具体的二级分类，同时匹配一级和二级
                    condition = and_(
                        OssMaterial.product_category == product_category,
                        OssMaterial.sub_category == sub_category
                    )
                else:
                    # 如果没有二级分类或者是"未分类"，只匹配一级分类
                    condition = OssMaterial.product_category == product_category
                conditions.append(condition)
            
            if conditions:
                query = db.query(OssMaterial).filter(or_(*conditions))
            else:
                query = db.query(OssMaterial)
            
            # 只返回有文档的物料
            query = query.filter(
                and_(
                    OssMaterial.document_names != '',
                    OssMaterial.document_names.isnot(None),
                    OssMaterial.oss_paths != '[]',
                    OssMaterial.oss_paths.isnot(None)
                )
            )
            
            return query.limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def search_by_material_name(self, material_name: str, limit: int = 100) -> List[Dict[str, Any]]:
        """根据物料名称搜索"""
        def _build_query(db: Session):
            return db.query(OssMaterial).filter(
                OssMaterial.material_name.like(f"%{material_name}%")
            ).limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def search_by_material_code(self, material_code: str) -> Optional[Dict[str, Any]]:
        """根据物料编码搜索"""
        if self.db:
            record = self.db.query(OssMaterial).filter(OssMaterial.material_code == material_code).first()
            return record.to_dict() if record else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                record = db.query(OssMaterial).filter(OssMaterial.material_code == material_code).first()
                return record.to_dict() if record else None
    
    def get_materials_with_documents(self, product_category: str, sub_category: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取有文档的物料"""
        def _build_query(db: Session):
            query = db.query(OssMaterial).filter(
                and_(
                    OssMaterial.product_category == product_category,
                    OssMaterial.document_names != '',
                    OssMaterial.oss_paths != '[]'
                )
            )
            
            if sub_category and sub_category != '未分类':
                query = query.filter(OssMaterial.sub_category == sub_category)
            
            return query
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def get_all_categories(self) -> List[Dict[str, str]]:
        """获取所有分类组合"""
        if self.db:
            results = self.db.query(
                OssMaterial.product_category,
                OssMaterial.sub_category
            ).distinct().all()
            
            return [
                {
                    'product_category': result.product_category,
                    'sub_category': result.sub_category
                }
                for result in results
            ]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                results = db.query(
                    OssMaterial.product_category,
                    OssMaterial.sub_category
                ).distinct().all()
                
                return [
                    {
                        'product_category': result.product_category,
                        'sub_category': result.sub_category
                    }
                    for result in results
                ]
    
    def complex_search(self,
                      product_category: Optional[str] = None,
                      sub_category: Optional[str] = None,
                      material_name: Optional[str] = None,
                      material_code: Optional[str] = None,
                      material: Optional[str] = None,
                      component_type: Optional[str] = None,
                      has_documents: Optional[bool] = None,
                      limit: int = 100,
                      offset: int = 0) -> List[Dict[str, Any]]:
        """复合条件搜索"""
        def _build_query(db: Session):
            query = db.query(OssMaterial)
            
            conditions = []
            
            if product_category:
                conditions.append(OssMaterial.product_category.like(f"%{product_category}%"))
            
            if sub_category:
                conditions.append(OssMaterial.sub_category.like(f"%{sub_category}%"))
            
            if material_name:
                conditions.append(OssMaterial.material_name.like(f"%{material_name}%"))
            
            if material_code:
                conditions.append(OssMaterial.material_code.like(f"%{material_code}%"))
            
            if material:
                conditions.append(OssMaterial.material.like(f"%{material}%"))
            
            if component_type:
                conditions.append(OssMaterial.component_type.like(f"%{component_type}%"))
            
            if has_documents is not None:
                if has_documents:
                    conditions.append(and_(
                        OssMaterial.document_names != '',
                        OssMaterial.oss_paths != '[]'
                    ))
                else:
                    conditions.append(or_(
                        OssMaterial.document_names == '',
                        OssMaterial.oss_paths == '[]'
                    ))
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(OssMaterial.id).offset(offset).limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records] 