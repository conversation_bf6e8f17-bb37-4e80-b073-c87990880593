"""
用户服务 - 基于数据库实现
"""
from typing import Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models.user import SysUser, UserCreate, UserUpdate, UserInDB, UserResponse
from app.core.auth import auth_manager


class UserService:
    """用户服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_by_username(self, user_name: str) -> Optional[SysUser]:
        """根据用户名获取用户"""
        return self.db.query(SysUser).filter(
            and_(
                SysUser.user_name == user_name,
                SysUser.is_delete == False
            )
        ).first()
    
    def get_user_by_id(self, user_id: int) -> Optional[SysUser]:
        """根据用户ID获取用户"""
        return self.db.query(SysUser).filter(
            and_(
                SysUser.id == user_id,
                SysUser.is_delete == False
            )
        ).first()
    
    def create_user(self, user_create: UserCreate) -> SysUser:
        """创建用户"""
        # 检查用户名是否已存在
        existing_user = self.get_user_by_username(user_create.user_name)
        if existing_user:
            raise ValueError("用户名已存在")
        
        # 加密密码
        hashed_password = auth_manager.get_password_hash(user_create.password_)
        
        # 创建用户对象
        db_user = SysUser(
            user_name=user_create.user_name,
            password_=hashed_password,
            nick_name=user_create.nick_name,
            email_=user_create.email_,
            phone_=user_create.phone_,
            avatar_=user_create.avatar_,
            status_=user_create.status_,
            LIMIT_=user_create.LIMIT_,
            TENANT_ID=user_create.TENANT_ID,
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        
        return db_user
    
    def update_user(self, user_id: int, user_update: UserUpdate) -> Optional[SysUser]:
        """更新用户"""
        db_user = self.get_user_by_id(user_id)
        if not db_user:
            return None
        
        update_data = user_update.dict(exclude_unset=True)
        
        # 如果更新密码，需要加密
        if "password_" in update_data:
            update_data["password_"] = auth_manager.get_password_hash(update_data["password_"])
        
        # 更新字段
        for field, value in update_data.items():
            setattr(db_user, field, value)
        
        db_user.update_time = datetime.now()
        
        self.db.commit()
        self.db.refresh(db_user)
        
        return db_user
    
    def authenticate_user(self, user_name: str, password: str) -> Optional[SysUser]:
        """验证用户"""
        user = self.get_user_by_username(user_name)
        if not user:
            return None
        if not auth_manager.verify_password(password, user.password_):
            return None
        if user.status_ != 1:  # 检查用户状态
            return None
        return user
    
    def update_last_login(self, user_id: int):
        """更新最后登录时间"""
        db_user = self.get_user_by_id(user_id)
        if db_user:
            db_user.lastLogin_time = datetime.now()
            db_user.update_time = datetime.now()
            self.db.commit()
    
    def list_users(self, skip: int = 0, limit: int = 100) -> List[SysUser]:
        """获取用户列表"""
        return self.db.query(SysUser).filter(
            SysUser.is_delete == False
        ).offset(skip).limit(limit).all()
    
    def soft_delete_user(self, user_id: int) -> bool:
        """软删除用户"""
        db_user = self.get_user_by_id(user_id)
        if not db_user:
            return False
        
        db_user.is_delete = True
        db_user.update_time = datetime.now()
        self.db.commit()
        
        return True 