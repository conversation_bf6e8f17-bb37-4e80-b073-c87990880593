#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/6/12 16:01
# <AUTHOR> hihaluemen
# @File    : oss_service.py
# @Description : OSS服务，用于根据供应商名称下载对应物料文件

import os
import sys
from typing import List, Dict, Any
from urllib.parse import urlparse
import pandas as pd
import zipfile
import shutil
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, "../..")
sys.path.append(project_root)

from tools.oss_tools import oss_handler
from app.utils.info_type_tools import get_material_spec_info


class OssService:
    """OSS服务类，用于根据供应商名称下载对应物料文件"""
    
    def __init__(self):
        self.oss_handler = oss_handler
    
    def download_supplier_materials(self, supplier_name: str, target_folder: str, need_num: int = 10) -> Dict[str, Any]:
        """
        根据供应商名称下载对应物料的OSS文件到指定文件夹
        
        Args:
            supplier_name: 供应商名称
            target_folder: 目标文件夹路径
            need_num: 需求数量倍数，默认为10
            
        Returns:
            Dict: 下载结果统计
        """
        try:
            # 确保目标文件夹存在
            if not os.path.exists(target_folder):
                os.makedirs(target_folder, exist_ok=True)
                print(f"创建目标文件夹: {target_folder}")
            
            # 获取供应商对应的物料信息
            material_info = get_material_spec_info(supplier_name, need_num=need_num)
            print(material_info)
            material_list = material_info.get("material_need", [])
            material_types = material_info.get("material_types", [])

            print(f"material_list: {material_list}")
            print(f"material_types: {material_types}")
            
            if not material_list:
                return {
                    "success": False,
                    "message": f"未找到供应商 '{supplier_name}' 对应的物料信息",
                    "total_files": 0,
                    "downloaded_files": 0,
                    "failed_files": 0,
                    "details": []
                }
            
            # 生成基于二级分类的文件夹名称
            if material_types:
                # 使用第一个二级分类作为主文件夹名，如果有多个分类则组合
                if len(material_types) == 1:
                    first_type, second_type = material_types[0]
                    category_folder_name = f"{second_type}"
                else:
                    # 多个分类时，使用所有二级分类组合
                    second_types = [mt[1] for mt in material_types]
                    category_folder_name = "_".join(second_types)
                
                # 更新目标文件夹路径，在原路径下创建分类文件夹
                target_folder = os.path.join(target_folder, category_folder_name)
                
                # 确保分类文件夹存在
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder, exist_ok=True)
                    print(f"创建分类文件夹: {target_folder}")
            
            print(f"物料分类: {material_types}")
            print(f"最终目标文件夹: {target_folder}")
            
            print(f"找到 {len(material_list)} 个物料项目")
            
            # 统计信息
            total_files = 0
            downloaded_files = 0
            failed_files = 0
            download_details = []
            
            # 遍历每个物料项目
            for material in material_list:
                material_name = material.get("component_name", "未知物料")
                material_code = material.get("component_code", "")
                oss_paths = material.get("oss_path", [])
                
                print(f"\n处理物料: {material_name} ({material_code})")
                print(f"OSS路径数量: {len(oss_paths)}")
                
                # 为每个物料创建子文件夹
                material_folder = os.path.join(target_folder, f"{material_code}_{material_name}")
                if not os.path.exists(material_folder):
                    os.makedirs(material_folder, exist_ok=True)
                
                # 下载该物料的所有文件
                for oss_path in oss_paths:
                    if not oss_path or oss_path.strip() == "":
                        continue
                        
                    total_files += 1

                    oss_path = oss_path[56:]
                    
                    # 清理OSS路径（去除引号等）
                    clean_oss_path = oss_path.strip().strip("'\"")
                    
                    # 提取文件名
                    file_name = os.path.basename(clean_oss_path)
                    if not file_name:
                        file_name = f"file_{total_files}"
                    
                    # 本地文件路径
                    local_file_path = os.path.join(material_folder, file_name)
                    
                    try:
                        print(f"  下载文件: {clean_oss_path} -> {local_file_path}")
                        
                        # 使用OSS工具下载文件
                        self.oss_handler.download_file(clean_oss_path, local_file_path)
                        
                        downloaded_files += 1
                        download_details.append({
                            "material_name": material_name,
                            "material_code": material_code,
                            "oss_path": clean_oss_path,
                            "local_path": local_file_path,
                            "status": "success"
                        })
                        
                    except Exception as e:
                        failed_files += 1
                        error_msg = f"下载失败: {str(e)}"
                        print(f"  {error_msg}")
                        
                        download_details.append({
                            "material_name": material_name,
                            "material_code": material_code,
                            "oss_path": clean_oss_path,
                            "local_path": local_file_path,
                            "status": "failed",
                            "error": error_msg
                        })
            
            # 返回下载结果
            result = {
                "success": True,
                "message": f"下载完成！总计 {total_files} 个文件，成功 {downloaded_files} 个，失败 {failed_files} 个",
                "supplier_name": supplier_name,
                "target_folder": target_folder,
                "total_materials": len(material_list),
                "total_files": total_files,
                "downloaded_files": downloaded_files,
                "failed_files": failed_files,
                "details": download_details
            }
            
            print(f"\n下载统计:")
            print(f"供应商: {supplier_name}")
            print(f"目标文件夹: {target_folder}")
            print(f"物料数量: {len(material_list)}")
            print(f"总文件数: {total_files}")
            print(f"成功下载: {downloaded_files}")
            print(f"下载失败: {failed_files}")
            
            return result
            
        except Exception as e:
            error_msg = f"下载过程中发生错误: {str(e)}"
            print(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "total_files": 0,
                "downloaded_files": 0,
                "failed_files": 0,
                "details": []
            }
    
    def list_supplier_materials(self, supplier_name: str, need_num: int = 10) -> Dict[str, Any]:
        """
        列出供应商对应的物料信息（不下载文件）
        
        Args:
            supplier_name: 供应商名称
            need_num: 需求数量倍数，默认为10
            
        Returns:
            Dict: 物料信息
        """
        try:
            material_info = get_material_spec_info(supplier_name, need_num=need_num)
            material_list = material_info.get("material_need", [])
            material_types = material_info.get("material_types", [])
            
            if not material_list:
                return {
                    "success": False,
                    "message": f"未找到供应商 '{supplier_name}' 对应的物料信息",
                    "materials": []
                }
            
            # 统计文件数量
            total_files = 0
            for material in material_list:
                oss_paths = material.get("OSS路径", [])
                total_files += len([path for path in oss_paths if path and path.strip()])
            
            return {
                "success": True,
                "message": f"找到 {len(material_list)} 个物料项目，共 {total_files} 个文件",
                "supplier_name": supplier_name,
                "total_materials": len(material_list),
                "total_files": total_files,
                "materials": material_list,
                "material_types": material_types
            }
            
        except Exception as e:
            error_msg = f"获取物料信息时发生错误: {str(e)}"
            print(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "materials": []
            }
    
    def save_supplier_materials_to_excel(self, supplier_name: str, target_folder: str, need_num: int = 10) -> Dict[str, Any]:
        """
        将供应商对应的物料信息保存为Excel文件（去掉OSS路径列）
        
        Args:
            supplier_name: 供应商名称
            target_folder: 目标文件夹路径
            need_num: 需求数量倍数，默认为10
            
        Returns:
            Dict: 保存结果
        """
        try:
            # 确保目标文件夹存在
            if not os.path.exists(target_folder):
                os.makedirs(target_folder, exist_ok=True)
                print(f"创建目标文件夹: {target_folder}")
            
            # 获取供应商对应的物料信息
            material_info = get_material_spec_info(supplier_name, need_num=need_num)
            material_list = material_info.get("material_need", [])
            material_types = material_info.get("material_types", [])
            
            if not material_list:
                return {
                    "success": False,
                    "message": f"未找到供应商 '{supplier_name}' 对应的物料信息",
                    "excel_path": ""
                }
            
            # 生成基于二级分类的文件夹名称
            category_folder_name = ""
            if material_types:
                # 使用第一个二级分类作为主文件夹名，如果有多个分类则组合
                if len(material_types) == 1:
                    first_type, second_type = material_types[0]
                    category_folder_name = f"{second_type}"
                else:
                    # 多个分类时，使用所有二级分类组合
                    second_types = [mt[1] for mt in material_types]
                    category_folder_name = "_".join(second_types)
                
                # 更新目标文件夹路径，在原路径下创建分类文件夹
                target_folder = os.path.join(target_folder, category_folder_name)
                
                # 确保分类文件夹存在
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder, exist_ok=True)
                    print(f"创建分类文件夹: {target_folder}")
            
            print(f"物料分类: {material_types}")
            print(f"最终目标文件夹: {target_folder}")
            
            print(f"找到 {len(material_list)} 个物料项目")
            
            # 准备Excel数据（去掉OSS路径列）
            excel_data = []
            for material in material_list:
                # 复制物料信息，但去掉OSS路径
                material_copy = material.copy()
                if 'OSS路径' in material_copy:
                    del material_copy['OSS路径']
                
                # 添加文件数量统计
                oss_paths = material.get("OSS路径", [])
                file_count = len([path for path in oss_paths if path and path.strip()])
                material_copy['文件数量'] = file_count
                
                excel_data.append(material_copy)
            
            # 创建DataFrame
            df = pd.DataFrame(excel_data)
            
            # 调整列顺序，让重要信息在前面
            column_order = ['物料名称', '物料规格', '物料编码', '材料', '需求数量', '文件数量', '文档名称']
            # 保留原有列，并添加可能缺失的列
            existing_columns = [col for col in column_order if col in df.columns]
            other_columns = [col for col in df.columns if col not in column_order]
            final_columns = existing_columns + other_columns
            df = df[final_columns]
            
            # 生成Excel文件名
            safe_supplier_name = supplier_name.replace('/', '_').replace('\\', '_').replace(':', '_')
            excel_filename = f"物料清单.xlsx"
            excel_path = os.path.join(target_folder, excel_filename)
            
            # 保存到Excel文件
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='物料清单', index=False)
                
                # 获取工作表并调整列宽
                worksheet = writer.sheets['物料清单']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 统计信息
            total_files = sum(material.get('文件数量', 0) for material in excel_data)
            
            result = {
                "success": True,
                "message": f"成功保存物料清单到Excel文件！共 {len(material_list)} 个物料项目，{total_files} 个文件",
                "supplier_name": supplier_name,
                "excel_path": excel_path,
                "total_materials": len(material_list),
                "total_files": total_files
            }
            
            print(f"\n保存统计:")
            print(f"供应商: {supplier_name}")
            print(f"Excel文件路径: {excel_path}")
            print(f"物料数量: {len(material_list)}")
            print(f"总文件数: {total_files}")
            
            return result
            
        except Exception as e:
            error_msg = f"保存Excel文件时发生错误: {str(e)}"
            print(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "excel_path": ""
            }
    
    def compress_folder(self, folder_path: str, zip_name: str = None, delete_original: bool = False) -> Dict[str, Any]:
        """
        压缩指定文件夹为ZIP文件
        
        Args:
            folder_path: 要压缩的文件夹路径
            zip_name: ZIP文件名（可选，默认使用文件夹名+时间戳）
            delete_original: 是否删除原文件夹，默认False
            
        Returns:
            Dict: 压缩结果
        """
        try:
            # 检查文件夹是否存在
            if not os.path.exists(folder_path):
                return {
                    "success": False,
                    "message": f"文件夹不存在: {folder_path}",
                    "zip_path": ""
                }
            
            if not os.path.isdir(folder_path):
                return {
                    "success": False,
                    "message": f"路径不是文件夹: {folder_path}",
                    "zip_path": ""
                }
            
            # 生成ZIP文件名
            if zip_name is None:
                folder_name = os.path.basename(folder_path.rstrip('/\\'))
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                zip_name = f"{folder_name}_{timestamp}.zip"
            
            # 确保ZIP文件名以.zip结尾
            if not zip_name.lower().endswith('.zip'):
                zip_name += '.zip'
            
            # ZIP文件路径（与原文件夹同级）
            parent_dir = os.path.dirname(folder_path)
            zip_path = os.path.join(parent_dir, zip_name)
            
            print(f"开始压缩文件夹: {folder_path}")
            print(f"ZIP文件路径: {zip_path}")
            
            # 统计文件数量
            total_files = 0
            total_size = 0
            
            # 创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 遍历文件夹中的所有文件
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # 计算相对路径
                        arcname = os.path.relpath(file_path, folder_path)
                        
                        try:
                            zipf.write(file_path, arcname)
                            total_files += 1
                            total_size += os.path.getsize(file_path)
                            print(f"  添加文件: {arcname}")
                        except Exception as e:
                            print(f"  跳过文件 {file_path}: {str(e)}")
            
            # 获取压缩后的文件大小
            zip_size = os.path.getsize(zip_path)
            compression_ratio = (1 - zip_size / total_size) * 100 if total_size > 0 else 0
            
            print(f"\n压缩完成!")
            print(f"原始大小: {total_size / (1024*1024):.2f} MB")
            print(f"压缩后大小: {zip_size / (1024*1024):.2f} MB")
            print(f"压缩率: {compression_ratio:.1f}%")
            print(f"文件数量: {total_files}")
            
            # 是否删除原文件夹
            deleted_original = False
            if delete_original:
                try:
                    shutil.rmtree(folder_path)
                    deleted_original = True
                    print(f"已删除原文件夹: {folder_path}")
                except Exception as e:
                    print(f"删除原文件夹失败: {str(e)}")
            
            return {
                "success": True,
                "message": f"成功压缩文件夹！压缩率: {compression_ratio:.1f}%",
                "zip_path": zip_path,
                "original_folder": folder_path,
                "total_files": total_files,
                "original_size_mb": round(total_size / (1024*1024), 2),
                "zip_size_mb": round(zip_size / (1024*1024), 2),
                "compression_ratio": round(compression_ratio, 1),
                "deleted_original": deleted_original
            }
            
        except Exception as e:
            error_msg = f"压缩文件夹时发生错误: {str(e)}"
            print(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "zip_path": ""
            }
    
    def download_and_compress(self, supplier_name: str, target_folder: str, need_num: int = 10, 
                            compress_after_download: bool = True, delete_original_after_compress: bool = False) -> Dict[str, Any]:
        """
        下载供应商物料文件并可选择压缩
        
        Args:
            supplier_name: 供应商名称
            target_folder: 目标文件夹路径
            need_num: 需求数量倍数，默认为10
            compress_after_download: 下载完成后是否压缩，默认True
            delete_original_after_compress: 压缩后是否删除原文件夹，默认False
            
        Returns:
            Dict: 下载和压缩结果
        """
        try:
            # 1. 保存Excel清单
            print("=== 第1步：保存物料清单到Excel ===")
            excel_result = self.save_supplier_materials_to_excel(supplier_name, target_folder, need_num)
            
            if not excel_result["success"]:
                return excel_result
            
            # 2. 下载文件
            print("\n=== 第2步：下载OSS文件 ===")
            download_result = self.download_supplier_materials(supplier_name, target_folder, need_num)
            
            result = {
                "excel_result": excel_result,
                "download_result": download_result,
                "compress_result": None
            }
            
            # 3. 可选压缩
            if compress_after_download and download_result["success"]:
                print("\n=== 第3步：压缩文件夹 ===")
                compress_result = self.compress_folder(target_folder, delete_original=delete_original_after_compress)
                result["compress_result"] = compress_result
                
                if compress_result["success"]:
                    result["success"] = True
                    result["message"] = f"完整流程完成！Excel保存、文件下载、文件夹压缩均成功"
                    result["final_output"] = compress_result["zip_path"]
                else:
                    result["success"] = True  # 下载成功，压缩失败不影响整体成功
                    result["message"] = f"下载完成但压缩失败: {compress_result['message']}"
                    result["final_output"] = target_folder
            else:
                result["success"] = download_result["success"]
                result["message"] = download_result["message"]
                result["final_output"] = target_folder
            
            return result
            
        except Exception as e:
            error_msg = f"完整流程执行时发生错误: {str(e)}"
            print(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "excel_result": None,
                "download_result": None,
                "compress_result": None
            }


# 创建全局服务实例
oss_service = OssService()


def test_oss_service():
    """测试OSS服务"""
    supplier_name = "广东兴发铝业有限公司"
    target_folder = "data/downloads/material_data"
    
    print("=== 测试OSS服务 ===")
    
    # 测试完整流程：下载+压缩
    print("\n测试完整流程（下载+压缩）:")
    result = oss_service.download_and_compress(
        supplier_name=supplier_name,
        target_folder=target_folder,
        compress_after_download=True,
        delete_original_after_compress=False  # 不删除原文件夹，便于查看
        # delete_original_after_compress=True
    )
    
    print(f"\n最终结果: {result['message']}")
    if result.get("compress_result"):
        print(f"压缩文件路径: {result['compress_result']['zip_path']}")


def test_compress_only():
    """测试单独的压缩功能"""
    test_folder = "data/downloads/test_supplier"
    
    print("=== 测试压缩功能 ===")
    
    # 测试压缩（不删除原文件夹）
    print(f"\n1. 压缩文件夹（保留原文件夹）:")
    result1 = oss_service.compress_folder(test_folder, delete_original=False)
    print(f"结果: {result1['message']}")
    
    # 测试压缩（删除原文件夹）
    print(f"\n2. 压缩文件夹（删除原文件夹）:")
    result2 = oss_service.compress_folder(test_folder, zip_name="test_compressed.zip", delete_original=True)
    print(f"结果: {result2['message']}")
    
    if result2["success"]:
        print(f"压缩文件: {result2['zip_path']}")
        print(f"压缩率: {result2['compression_ratio']}%")
        print(f"原文件夹已删除: {result2['deleted_original']}")


if __name__ == "__main__":
    test_oss_service()
