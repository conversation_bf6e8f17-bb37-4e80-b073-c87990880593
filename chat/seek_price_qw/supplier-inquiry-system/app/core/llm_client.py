"""
LLM客户端封装

支持OpenAI API和兼容的API服务
包含同步和异步客户端，支持可配置参数
"""

import os
import asyncio
import json
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
from abc import ABC, abstractmethod
from datetime import datetime

from openai import OpenAI, AsyncOpenAI
from openai.types.chat import ChatCompletion, ChatCompletionChunk
from openai._exceptions import OpenAIError, APIStatusError, APITimeoutError, RateLimitError
from pydantic import BaseModel, Field

from app.core.base_agent import AgentMessage, MessageRole


class LLMConfig(BaseModel):
    """LLM配置"""
    api_key: Optional[str] = Field(default=None, description="API密钥")
    base_url: Optional[str] = Field(default=None, description="API基础URL")
    organization: Optional[str] = Field(default=None, description="组织ID")
    project: Optional[str] = Field(default=None, description="项目ID")
    timeout: Optional[float] = Field(default=60.0, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    default_model: str = Field(default="gpt-4.1-2025-04-14", description="默认模型")
    default_temperature: Optional[float] = Field(default=None, description="默认温度")
    default_max_tokens: Optional[int] = Field(default=None, description="默认最大token数")
    
    class Config:
        extra = "allow"


class LLMResponse(BaseModel):
    """LLM响应"""
    content: str
    model: str
    usage: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None
    request_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def to_agent_message(self) -> AgentMessage:
        """转换为Agent消息格式"""
        return AgentMessage(
            role=MessageRole.ASSISTANT,
            content=self.content,
            metadata={
                "model": self.model,
                "usage": self.usage,
                "finish_reason": self.finish_reason,
                "request_id": self.request_id,
                **self.metadata
            }
        )


class StreamEvent(BaseModel):
    """流式响应事件"""
    type: str  # delta, done, error
    content: Optional[str] = None
    delta: Optional[str] = None
    finish_reason: Optional[str] = None
    usage: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class BaseLLMClient(ABC):
    """LLM客户端抽象基类"""
    
    def __init__(self, config: LLMConfig, logger=None):
        self.config = config
        self.logger = logger  # 使用注入的logger，如果没有则为None
    
    @abstractmethod
    async def chat_completion(
        self,
        messages: List[AgentMessage],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[LLMResponse, AsyncGenerator[StreamEvent, None]]:
        """聊天完成"""
        pass
    
    @abstractmethod
    async def text_completion(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """文本完成"""
        pass
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        if self.logger:
            self.logger.info(f"[LLMClient] {message}", **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """记录错误日志"""
        if self.logger:
            self.logger.error(f"[LLMClient] {message}", **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """记录调试日志"""
        if self.logger:
            self.logger.debug(f"[LLMClient] {message}", **kwargs)
    
    def _prepare_messages(self, messages: List[AgentMessage]) -> List[Dict[str, Any]]:
        """准备消息格式"""
        formatted_messages = []
        for msg in messages:
            formatted_msg = {
                "role": msg.role,
                "content": msg.content
            }
            
            # 添加工具调用信息
            if msg.tool_calls:
                formatted_msg["tool_calls"] = msg.tool_calls
            
            if msg.tool_call_id:
                formatted_msg["tool_call_id"] = msg.tool_call_id
            
            formatted_messages.append(formatted_msg)
        
        return formatted_messages
    
    def _get_model(self, model: Optional[str]) -> str:
        """获取模型名称"""
        return model or self.config.default_model
    
    def _get_temperature(self, temperature: Optional[float]) -> Optional[float]:
        """获取温度参数"""
        if temperature is not None:
            return temperature
        return self.config.default_temperature
    
    def _get_max_tokens(self, max_tokens: Optional[int]) -> Optional[int]:
        """获取最大token数"""
        if max_tokens is not None:
            return max_tokens
        return self.config.default_max_tokens


class OpenAIClient(BaseLLMClient):
    """OpenAI客户端实现"""
    
    def __init__(self, config: LLMConfig, logger=None):
        super().__init__(config, logger)
        
        # 初始化同步客户端
        self._sync_client = self._create_sync_client()
        
        # 初始化异步客户端
        self._async_client = self._create_async_client()
    
    def _create_sync_client(self) -> OpenAI:
        """创建同步客户端"""
        client_kwargs = {
            "timeout": self.config.timeout,
            "max_retries": self.config.max_retries,
        }
        
        # API密钥
        if self.config.api_key:
            client_kwargs["api_key"] = self.config.api_key
        elif os.getenv("OPENAI_API_KEY"):
            client_kwargs["api_key"] = os.getenv("OPENAI_API_KEY")
        
        # 基础URL
        if self.config.base_url:
            client_kwargs["base_url"] = self.config.base_url
        elif os.getenv("OPENAI_BASE_URL"):
            client_kwargs["base_url"] = os.getenv("OPENAI_BASE_URL")
        
        # 组织和项目
        # if self.config.organization:
        #     client_kwargs["organization"] = self.config.organization
        
        # if self.config.project:
        #     client_kwargs["project"] = self.config.project
        
        return OpenAI(**client_kwargs)
    
    def _create_async_client(self) -> AsyncOpenAI:
        """创建异步客户端"""
        client_kwargs = {
            "timeout": self.config.timeout,
            "max_retries": self.config.max_retries,
        }
        
        # API密钥
        if self.config.api_key:
            client_kwargs["api_key"] = self.config.api_key
        elif os.getenv("OPENAI_API_KEY"):
            client_kwargs["api_key"] = os.getenv("OPENAI_API_KEY")
        
        # 基础URL
        if self.config.base_url:
            client_kwargs["base_url"] = self.config.base_url
        elif os.getenv("OPENAI_BASE_URL"):
            client_kwargs["base_url"] = os.getenv("OPENAI_BASE_URL")
        
        # 组织和项目
        # if self.config.organization:
        #     client_kwargs["organization"] = self.config.organization
        
        # if self.config.project:
        #     client_kwargs["project"] = self.config.project
        
        return AsyncOpenAI(**client_kwargs)
    
    async def chat_completion(
        self,
        messages: List[AgentMessage],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[LLMResponse, AsyncGenerator[StreamEvent, None]]:
        """聊天完成"""
        try:
            # 准备参数
            formatted_messages = self._prepare_messages(messages)
            
            request_params = {
                "model": self._get_model(model),
                "messages": formatted_messages,
                "stream": stream,
                **kwargs
            }
            
            # 添加可选参数
            if temperature := self._get_temperature(temperature):
                request_params["temperature"] = temperature
            
            if max_tokens := self._get_max_tokens(max_tokens):
                request_params["max_tokens"] = max_tokens
            
            self.log_debug(f"发送聊天完成请求: {request_params}")
            # print(f"发送聊天完成请求: {json.dumps(request_params, ensure_ascii=False, indent=2)}")
            
            if stream:
                return self._handle_stream_response(request_params)
            else:
                return await self._handle_single_response(request_params)
        
        except Exception as e:
            self.log_error(f"聊天完成请求失败: {str(e)}")
            raise self._handle_exception(e)
    
    async def _handle_single_response(self, request_params: Dict[str, Any]) -> LLMResponse:
        """处理单次响应"""
        response = await self._async_client.chat.completions.create(**request_params)
        
        # 提取响应内容
        content = ""
        if response.choices and len(response.choices) > 0:
            choice = response.choices[0]
            if choice.message and choice.message.content:
                content = choice.message.content
        
        # 构建响应对象
        return LLMResponse(
            content=content,
            model=response.model,
            usage=response.usage.model_dump() if response.usage else None,
            finish_reason=response.choices[0].finish_reason if response.choices else None,
            request_id=getattr(response, '_request_id', None),
            metadata={
                "created": response.created,
                "system_fingerprint": getattr(response, 'system_fingerprint', None)
            }
        )
    
    async def _handle_stream_response(self, request_params: Dict[str, Any]) -> AsyncGenerator[StreamEvent, None]:
        """处理流式响应"""
        try:
            stream = await self._async_client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    # 处理内容增量
                    if choice.delta and choice.delta.content:
                        yield StreamEvent(
                            type="delta",
                            delta=choice.delta.content,
                            content=choice.delta.content
                        )
                    
                    # 处理完成
                    if choice.finish_reason:
                        yield StreamEvent(
                            type="done",
                            finish_reason=choice.finish_reason,
                            usage=chunk.usage.model_dump() if chunk.usage else None
                        )
        
        except Exception as e:
            yield StreamEvent(
                type="error",
                error=str(e)
            )
    
    async def text_completion(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """文本完成（通过聊天接口实现）"""
        # 将文本提示转换为聊天消息
        messages = [AgentMessage(role=MessageRole.USER, content=prompt)]
        
        response = await self.chat_completion(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=False,
            **kwargs
        )
        
        return response
    
    def _handle_exception(self, e: Exception) -> Exception:
        """处理异常"""
        if isinstance(e, APIStatusError):
            self.log_error(f"API状态错误: {e.status_code} - {e.message}")
            return e
        elif isinstance(e, APITimeoutError):
            self.log_error(f"API超时错误: {str(e)}")
            return e
        elif isinstance(e, RateLimitError):
            self.log_error(f"API限流错误: {str(e)}")
            return e
        elif isinstance(e, OpenAIError):
            self.log_error(f"OpenAI错误: {str(e)}")
            return e
        else:
            self.log_error(f"未知错误: {str(e)}")
            return e
    
    def get_sync_client(self) -> OpenAI:
        """获取同步客户端"""
        return self._sync_client
    
    def get_async_client(self) -> AsyncOpenAI:
        """获取异步客户端"""
        return self._async_client


class LLMClientFactory:
    """LLM客户端工厂"""
    
    @staticmethod
    def create_openai_client(
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        organization: Optional[str] = None,
        project: Optional[str] = None,
        timeout: Optional[float] = None,
        max_retries: Optional[int] = None,
        default_model: Optional[str] = None,
        logger=None,
        **kwargs
    ) -> OpenAIClient:
        """创建OpenAI客户端"""
        config = LLMConfig(
            api_key=api_key,
            base_url=base_url,
            organization=organization,
            project=project,
            timeout=timeout,
            max_retries=max_retries or 3,
            default_model=default_model or "gpt-4.1-2025-04-14",
            **kwargs
        )
        
        return OpenAIClient(config, logger)
    
    @staticmethod
    def create_from_config(config: Dict[str, Any], logger=None) -> OpenAIClient:
        """从配置字典创建客户端"""
        llm_config = LLMConfig(**config)
        return OpenAIClient(llm_config, logger)
    
    @staticmethod
    def create_from_env(logger=None) -> OpenAIClient:
        """从环境变量创建客户端"""
        config = LLMConfig(
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_BASE_URL"),
            organization=os.getenv("OPENAI_ORGANIZATION"),
            project=os.getenv("OPENAI_PROJECT"),
            timeout=float(os.getenv("OPENAI_TIMEOUT", "60.0")),
            max_retries=int(os.getenv("OPENAI_MAX_RETRIES", "3")),
            default_model=os.getenv("OPENAI_DEFAULT_MODEL", "gpt-4.1-2025-04-14")
        )
        
        return OpenAIClient(config, logger)


# 便捷函数
async def create_chat_completion(
    messages: List[AgentMessage],
    model: str = "gpt-4.1-2025-04-14",
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
    logger=None,
    **kwargs
) -> LLMResponse:
    """便捷的聊天完成函数"""
    client = LLMClientFactory.create_openai_client(
        api_key=api_key,
        base_url=base_url,
        default_model=model,
        logger=logger
    )
    
    return await client.chat_completion(
        messages=messages,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        **kwargs
    )


async def create_text_completion(
    prompt: str,
    model: str = "gpt-4.1-2025-04-14",
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
    logger=None,
    **kwargs
) -> LLMResponse:
    """便捷的文本完成函数"""
    client = LLMClientFactory.create_openai_client(
        api_key=api_key,
        base_url=base_url,
        default_model=model,
        logger=logger
    )
    
    return await client.text_completion(
        prompt=prompt,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        **kwargs
    )


# 使用示例
if __name__ == "__main__":
    async def test_client():
        # 创建客户端
        # client = LLMClientFactory.create_from_env()
        from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
        client = LLMClientFactory.create_openai_client(
            base_url=OPENAI_BASE_URL,
            api_key=OPENAI_API_KEY,
            default_model="deepseek/deepseek-chat-v3-0324:free"
        )
        
        # 准备消息
        messages = [
            AgentMessage(role=MessageRole.SYSTEM, content="你是一个有用的助手"),
            AgentMessage(role=MessageRole.USER, content="你好，请介绍一下自己")
        ]
        
        # 发送请求
        response = await client.chat_completion(messages)
        print(f"响应: {response.content}")
        print(json.dumps(response.to_agent_message().to_dict(), indent=2, ensure_ascii=False))
        
        # 流式请求
        # print("\n流式响应:")
        # async for event in await client.chat_completion(messages, stream=True):
        #     if event.type == "delta":
        #         print(event.delta, end="", flush=True)
        #     elif event.type == "done":
        #         print(f"\n完成原因: {event.finish_reason}")
    
    # 运行测试
    asyncio.run(test_client()) 