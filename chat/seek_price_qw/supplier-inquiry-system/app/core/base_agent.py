"""
Agent系统基础类定义

包含：
- 消息格式定义
- Agent处理结果格式
- Agent基类抽象定义
- 工具调用接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid
import json


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"


class AgentMessage(BaseModel):
    """Agent消息格式"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    role: MessageRole
    content: str
    metadata: Optional[Dict[str, Any]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    
    class Config:
        use_enum_values = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "role": self.role,
            "content": self.content,
            "metadata": self.metadata,
            "tool_calls": self.tool_calls,
            "tool_call_id": self.tool_call_id,
            "timestamp": self.timestamp.isoformat()
        }


class AgentResult(BaseModel):
    """Agent处理结果"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    error: Optional[str] = None
    messages: List[AgentMessage] = Field(default_factory=list)
    next_agent: Optional[str] = None  # 建议下一个处理的Agent
    metadata: Dict[str, Any] = Field(default_factory=dict)
    processing_time: Optional[float] = None  # 处理耗时（秒）
    
    def add_message(self, role: MessageRole, content: str, **kwargs):
        """添加消息到结果中"""
        message = AgentMessage(
            role=role,
            content=content,
            metadata=kwargs.get('metadata'),
            tool_calls=kwargs.get('tool_calls'),
            tool_call_id=kwargs.get('tool_call_id')
        )
        self.messages.append(message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "success": self.success,
            "data": self.data,
            "message": self.message,
            "error": self.error,
            "messages": [msg.to_dict() for msg in self.messages],
            "next_agent": self.next_agent,
            "metadata": self.metadata,
            "processing_time": self.processing_time
        }


class AgentConfig(BaseModel):
    """Agent配置"""
    name: str
    description: str = ""
    system_prompt: str = ""
    model: str = "gpt-4.1-2025-04-14"
    temperature: Optional[float] = None  # 修复类型
    max_tokens: Optional[int] = None     # 修复类型
    timeout: Optional[int] = None        # 修复类型
    tools: List[str] = Field(default_factory=list)
    memory_enabled: bool = True
    retry_count: int = 3  # 重试次数
    
    class Config:
        extra = "allow"  # 允许额外字段


class ToolCall(BaseModel):
    """工具调用格式"""
    name: str
    arguments: Dict[str, Any]
    call_id: str = Field(default_factory=lambda: str(uuid.uuid4()))


class ToolResult(BaseModel):
    """工具调用结果"""
    call_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class BaseAgent(ABC):
    """Agent基类"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.name = config.name
        self.llm_client = None  # 由编排器注入
        self.mcp_client = None  # MCP工具客户端
        self.memory = None      # 记忆管理器
        self.logger = None      # 日志记录器
        
    @abstractmethod
    async def process(self, session_id: str, data: Dict[str, Any]) -> AgentResult:
        """
        处理请求的抽象方法
        
        Args:
            session_id: 会话ID
            data: 输入数据
            
        Returns:
            AgentResult: 处理结果
        """
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """
        返回支持的请求类型列表
        
        Returns:
            List[str]: 支持的请求类型
        """
        pass
    
    async def can_handle(self, request_type: str, data: Dict[str, Any]) -> bool:
        """
        判断是否能处理该请求类型
        
        Args:
            request_type: 请求类型
            data: 请求数据
            
        Returns:
            bool: 是否能处理
        """
        return request_type in self.get_supported_types()
    
    async def validate_input(self, data: Dict[str, Any]) -> bool:
        """
        验证输入数据
        
        Args:
            data: 输入数据
            
        Returns:
            bool: 验证是否通过
        """
        # 基础验证，子类可以重写
        return isinstance(data, dict)
    
    async def pre_process(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理钩子方法
        
        Args:
            session_id: 会话ID
            data: 输入数据
            
        Returns:
            Dict[str, Any]: 预处理后的数据
        """
        return data
    
    async def post_process(self, session_id: str, result: AgentResult) -> AgentResult:
        """
        后处理钩子方法
        
        Args:
            session_id: 会话ID
            result: 处理结果
            
        Returns:
            AgentResult: 后处理后的结果
        """
        return result
    
    async def call_llm(self, messages: List[AgentMessage], **kwargs) -> AgentMessage:
        """
        调用LLM
        
        Args:
            messages: 消息列表
            **kwargs: 额外参数
            
        Returns:
            AgentMessage: LLM响应消息
        """
        if not self.llm_client:
            raise RuntimeError(f"Agent {self.name} 未配置LLM客户端")
        
        # 使用配置中的参数，kwargs可以覆盖
        llm_params = {
            "model": self.config.model,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens,
            **kwargs
        }
        
        # 调用LLM并转换为AgentMessage
        response = await self.llm_client.chat_completion(messages, **llm_params)
        return response.to_agent_message()
    
    async def call_tool(self, tool_call: ToolCall) -> ToolResult:
        """
        调用工具
        
        Args:
            tool_call: 工具调用请求
            
        Returns:
            ToolResult: 工具调用结果
        """
        if not self.mcp_client:
            raise RuntimeError(f"Agent {self.name} 未配置MCP客户端")
        
        start_time = datetime.now()
        try:
            result = await self.mcp_client.call_tool(tool_call.name, tool_call.arguments)
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return ToolResult(
                call_id=tool_call.call_id,
                success=True,
                result=result,
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return ToolResult(
                call_id=tool_call.call_id,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    async def get_memory_context(self, session_id: str) -> Dict[str, Any]:
        """
        获取会话记忆上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 记忆上下文
        """
        if self.memory and self.config.memory_enabled:
            return await self.memory.get_context(session_id)
        return {}
    
    async def save_memory_context(self, session_id: str, context: Dict[str, Any]):
        """
        保存会话记忆上下文
        
        Args:
            session_id: 会话ID
            context: 要保存的上下文
        """
        if self.memory and self.config.memory_enabled:
            await self.memory.update_context(session_id, context)
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        if self.logger:
            self.logger.info(f"[{self.name}] {message}", **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """记录错误日志"""
        if self.logger:
            self.logger.error(f"[{self.name}] {message}", **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """记录调试日志"""
        if self.logger:
            self.logger.debug(f"[{self.name}] {message}", **kwargs)
    
    def __str__(self) -> str:
        return f"Agent({self.name})"
    
    def __repr__(self) -> str:
        return f"Agent(name='{self.name}', supported_types={self.get_supported_types()})"


class AgentStatus(str, Enum):
    """Agent状态枚举"""
    IDLE = "idle"           # 空闲
    PROCESSING = "processing"  # 处理中
    WAITING = "waiting"     # 等待中
    ERROR = "error"         # 错误状态
    DISABLED = "disabled"   # 已禁用


class AgentMetrics(BaseModel):
    """Agent性能指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_processing_time: float = 0.0
    last_request_time: Optional[datetime] = None
    status: AgentStatus = AgentStatus.IDLE
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    def update_metrics(self, success: bool, processing_time: float):
        """更新指标"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        # 更新平均处理时间
        if self.total_requests == 1:
            self.average_processing_time = processing_time
        else:
            self.average_processing_time = (
                (self.average_processing_time * (self.total_requests - 1) + processing_time) 
                / self.total_requests
            )
        
        self.last_request_time = datetime.now()


# 工具函数
def create_agent_message(role: MessageRole, content: str, **kwargs) -> AgentMessage:
    """创建Agent消息的便捷函数"""
    return AgentMessage(role=role, content=content, **kwargs)


def create_success_result(data: Dict[str, Any] = None, message: str = None, **kwargs) -> AgentResult:
    """创建成功结果的便捷函数"""
    return AgentResult(success=True, data=data, message=message, **kwargs)


def create_error_result(error: str, data: Dict[str, Any] = None, **kwargs) -> AgentResult:
    """创建错误结果的便捷函数"""
    return AgentResult(success=False, error=error, data=data, **kwargs)
