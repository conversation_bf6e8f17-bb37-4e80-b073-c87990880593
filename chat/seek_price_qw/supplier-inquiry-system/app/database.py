"""
数据库连接配置
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator, Dict
import os
from contextlib import contextmanager
from config.db_config import DATABASE_CONFIGS

class DatabaseManager:
    """数据库管理类，支持多个数据库连接"""
    
    def __init__(self):
        self.engines: Dict[str, any] = {}
        self.session_locals: Dict[str, sessionmaker] = {}
        self._init_databases()
    
    def _init_databases(self):
        """初始化所有数据库连接"""
        for db_name, config in DATABASE_CONFIGS.items():
            # 构建数据库URL
            database_url = f"mysql+pymysql://{config['username']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}?charset=utf8mb4"
            
            # 创建引擎
            engine = create_engine(
                database_url,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False,
                connect_args={
                    "charset": "utf8mb4",
                    "autocommit": False
                }
            )
            
            # 创建会话工厂
            session_local = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=engine
            )
            
            self.engines[db_name] = engine
            self.session_locals[db_name] = session_local
            print(f"数据库 '{db_name}' 连接初始化完成")
    
    def get_engine(self, db_name: str):
        """获取指定数据库的引擎"""
        if db_name not in self.engines:
            raise ValueError(f"数据库 '{db_name}' 未配置")
        return self.engines[db_name]
    
    def get_session_local(self, db_name: str):
        """获取指定数据库的会话工厂"""
        if db_name not in self.session_locals:
            raise ValueError(f"数据库 '{db_name}' 未配置")
        return self.session_locals[db_name]
    
    def get_db_session(self, db_name: str = "material") -> Generator[Session, None, None]:
        """获取数据库会话（用于FastAPI依赖注入）"""
        session_local = self.get_session_local(db_name)
        db = session_local()
        try:
            yield db
        finally:
            db.close()
    
    @contextmanager
    def get_db_context(self, db_name: str = "material"):
        """获取数据库会话的上下文管理器"""
        session_local = self.get_session_local(db_name)
        db = session_local()
        try:
            yield db
            db.commit()
        except Exception:
            db.rollback()
            raise
        finally:
            db.close()
    
    def check_connection(self, db_name: str = None):
        """检查数据库连接"""
        if db_name:
            # 检查指定数据库
            try:
                engine = self.get_engine(db_name)
                with engine.connect() as connection:
                    connection.execute(text("SELECT 1"))
                print(f"数据库 '{db_name}' 连接正常")
                return True
            except Exception as e:
                print(f"数据库 '{db_name}' 连接失败: {e}")
                return False
        else:
            # 检查所有数据库
            results = {}
            for name in self.engines.keys():
                results[name] = self.check_connection(name)
            return results
    
    def init_tables(self, db_name: str = "material"):
        """初始化指定数据库的表"""
        from app.models.base import Base
        engine = self.get_engine(db_name)
        Base.metadata.create_all(bind=engine)
        print(f"数据库 '{db_name}' 表初始化完成")

# 创建全局数据库管理器实例
db_manager = DatabaseManager()

# 兼容原有代码的函数
def get_db() -> Generator[Session, None, None]:
    """获取默认数据库会话（兼容原有代码）"""
    yield from db_manager.get_db_session("material")

def get_db_session():
    """获取默认数据库会话的上下文管理器（兼容原有代码）"""
    return db_manager.get_db_context("material")

def init_database():
    """初始化默认数据库表（兼容原有代码）"""
    db_manager.init_tables("material")

def check_database_connection():
    """检查默认数据库连接（兼容原有代码）"""
    return db_manager.check_connection("material")

# 新增便捷函数
def get_material_db():
    """获取材料数据库会话"""
    yield from db_manager.get_db_session("material")

def get_second_db():
    """获取第二个数据库会话"""
    yield from db_manager.get_db_session("second")
