"""
工具函数集合

包含各种通用的工具函数，如JSON解析、字符串处理等
"""

import json
import re
from typing import Dict, Any, Optional, List, Union


def extract_largest_json(text: str) -> Optional[Dict[str, Any]]:
    """
    从字符串中提取最大的JSON字典
    
    常用于解析大模型的输出，大模型有时会在JSON前后添加额外的文本
    
    Args:
        text: 包含JSON的字符串
        
    Returns:
        Dict[str, Any]: 解析后的JSON字典，如果没有找到有效JSON则返回None
        
    Examples:
        >>> text = "这是一个JSON: {'name': 'test', 'value': 123} 还有其他文本"
        >>> result = extract_largest_json(text)
        >>> print(result)  # {'name': 'test', 'value': 123}
        
        >>> text = "```json\n{'a': 1}\n```\n还有一个更大的: {'b': 2, 'c': {'d': 3}}"
        >>> result = extract_largest_json(text)
        >>> print(result)  # {'b': 2, 'c': {'d': 3}}
    """
    if not text or not isinstance(text, str):
        return None
    
    # 找到所有可能的JSON字典
    json_candidates = _find_json_candidates(text)
    
    if not json_candidates:
        return None
    
    # 解析并找到最大的有效JSON
    largest_json = None
    max_size = 0
    
    for candidate in json_candidates:
        try:
            parsed = json.loads(candidate)
            if isinstance(parsed, dict):
                # 计算JSON的"大小"（字符数）
                size = len(candidate)
                if size > max_size:
                    max_size = size
                    largest_json = parsed
        except (json.JSONDecodeError, ValueError):
            continue
    
    return largest_json


def _find_json_candidates(text: str) -> List[str]:
    """
    查找字符串中所有可能的JSON字典候选项
    
    Args:
        text: 输入文本
        
    Returns:
        List[str]: JSON候选字符串列表
    """
    candidates = []
    
    # 方法1: 查找被```json```包围的内容
    json_block_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
    json_blocks = re.findall(json_block_pattern, text, re.DOTALL | re.IGNORECASE)
    candidates.extend(json_blocks)
    
    # 方法2: 查找所有花括号包围的内容
    brace_candidates = _extract_balanced_braces(text)
    candidates.extend(brace_candidates)
    
    # 去重并返回
    return list(set(candidates))


def _extract_balanced_braces(text: str) -> List[str]:
    """
    提取所有平衡的花括号内容
    
    Args:
        text: 输入文本
        
    Returns:
        List[str]: 平衡花括号内容列表
    """
    candidates = []
    stack = []
    start_positions = []
    
    for i, char in enumerate(text):
        if char == '{':
            stack.append(char)
            if len(stack) == 1:  # 第一个开括号
                start_positions.append(i)
        elif char == '}':
            if stack and stack[-1] == '{':
                stack.pop()
                if len(stack) == 0:  # 找到完整的匹配
                    start_pos = start_positions.pop()
                    candidate = text[start_pos:i+1]
                    candidates.append(candidate)
            else:
                # 不匹配，重置
                stack.clear()
                start_positions.clear()
    
    return candidates


def extract_json_with_fallback(text: str, fallback_keys: List[str] = None) -> Dict[str, Any]:
    """
    提取JSON，如果失败则尝试从文本中提取关键信息
    
    Args:
        text: 输入文本
        fallback_keys: 如果JSON解析失败，尝试提取的关键字段
        
    Returns:
        Dict[str, Any]: 解析结果或提取的关键信息
    """
    # 首先尝试提取JSON
    result = extract_largest_json(text)
    if result:
        return result
    
    # JSON解析失败，尝试提取关键信息
    fallback_result = {}
    
    if fallback_keys:
        for key in fallback_keys:
            value = _extract_key_value(text, key)
            if value:
                fallback_result[key] = value
    
    # 如果没有指定fallback_keys，尝试一些常见的模式
    if not fallback_result:
        common_patterns = {
            'target_agent': r'(?:target_agent|agent)["\']?\s*[:=]\s*["\']?([^"\',\s]+)',
            'confidence': r'(?:confidence)["\']?\s*[:=]\s*([0-9.]+)',
            'reason': r'(?:reason)["\']?\s*[:=]\s*["\']([^"\']+)',
            'need_human': r'(?:need_human)["\']?\s*[:=]\s*(true|false)',
        }
        
        for key, pattern in common_patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = match.group(1)
                # 尝试转换类型
                if key == 'confidence':
                    try:
                        value = float(value)
                    except ValueError:
                        pass
                elif key == 'need_human':
                    value = value.lower() == 'true'
                
                fallback_result[key] = value
    
    return fallback_result if fallback_result else {"error": "无法解析JSON或提取关键信息", "raw_text": text}


def _extract_key_value(text: str, key: str) -> Optional[str]:
    """
    从文本中提取指定键的值
    
    Args:
        text: 输入文本
        key: 要提取的键名
        
    Returns:
        Optional[str]: 提取的值，如果没有找到则返回None
    """
    # 尝试多种模式
    patterns = [
        rf'{key}["\']?\s*[:=]\s*["\']([^"\']+)["\']',  # key: "value" 或 key = "value"
        rf'{key}["\']?\s*[:=]\s*([^,\s}}]+)',          # key: value (无引号)
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    
    return None


def clean_json_string(text: str) -> str:
    """
    清理JSON字符串，移除常见的格式问题
    
    Args:
        text: 原始JSON字符串
        
    Returns:
        str: 清理后的JSON字符串
    """
    if not text:
        return text
    
    # 移除前后的markdown标记
    text = re.sub(r'^```(?:json)?\s*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'\s*```$', '', text)
    
    # 移除前后的空白字符
    text = text.strip()
    
    # 修复常见的JSON格式问题
    # 1. 单引号替换为双引号（但要小心字符串内容中的单引号）
    text = _fix_quotes(text)
    
    # 2. 移除尾随逗号
    text = re.sub(r',(\s*[}\]])', r'\1', text)
    
    return text


def _fix_quotes(text: str) -> str:
    """
    修复JSON中的引号问题
    
    Args:
        text: 输入文本
        
    Returns:
        str: 修复后的文本
    """
    # 这是一个简化的实现，处理最常见的情况
    # 将键名的单引号替换为双引号
    text = re.sub(r"'([^']*)'(\s*:)", r'"\1"\2', text)
    
    # 将字符串值的单引号替换为双引号（简单情况）
    text = re.sub(r":\s*'([^']*)'", r': "\1"', text)
    
    return text


def safe_json_loads(text: str, default: Any = None) -> Any:
    """
    安全的JSON加载，失败时返回默认值
    
    Args:
        text: JSON字符串
        default: 解析失败时的默认返回值
        
    Returns:
        Any: 解析结果或默认值
    """
    try:
        return json.loads(text)
    except (json.JSONDecodeError, ValueError, TypeError):
        return default


# 便捷函数
def parse_llm_json_response(response_text: str, expected_keys: List[str] = None) -> Dict[str, Any]:
    """
    解析大模型的JSON响应的便捷函数
    
    Args:
        response_text: 大模型的响应文本
        expected_keys: 期望的键列表，用于fallback提取
        
    Returns:
        Dict[str, Any]: 解析结果
    """
    # 首先清理文本
    cleaned_text = clean_json_string(response_text)
    
    # 尝试直接解析
    direct_result = safe_json_loads(cleaned_text)
    if direct_result and isinstance(direct_result, dict):
        return direct_result
    
    # 尝试提取最大的JSON
    extracted_result = extract_largest_json(response_text)
    if extracted_result:
        return extracted_result
    
    # 使用fallback方法
    return extract_json_with_fallback(response_text, expected_keys)


# 使用示例和测试
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        # 标准JSON
        '{"name": "test", "value": 123}',
        
        # 带markdown的JSON
        '```json\n{"agent": "router", "confidence": 0.95}\n```',
        
        # 混合文本中的JSON
        '这是分析结果：{"target_agent": "supplier_capability_agent", "confidence": 0.9, "reason": "询问供应商能力"} 请查收',
        
        # 多个JSON，选择最大的
        '小的：{"a": 1} 大的：{"target_agent": "info_response_agent", "confidence": 0.85, "reason": "回答供应商问题", "metadata": {"type": "info"}}',
        
        # 单引号JSON
        "{'target_agent': 'quote_request_agent', 'confidence': 0.92}",
        
        # 格式不完整的情况
        'target_agent: supplier_capability_agent, confidence: 0.8, reason: 询问供应商能力',
    ]
    
    print("=== 测试 extract_largest_json ===")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case}")
        result = extract_largest_json(test_case)
        print(f"输出: {result}")
    
    print("\n=== 测试 parse_llm_json_response ===")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case}")
        result = parse_llm_json_response(test_case, ['target_agent', 'confidence', 'reason'])
        print(f"输出: {result}")
