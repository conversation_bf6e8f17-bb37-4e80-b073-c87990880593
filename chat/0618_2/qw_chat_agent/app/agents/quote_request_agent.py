"""报价请求Agent

负责在供应商确认能力后，发送报价请求，包括：
- 询问产品价格
- 交期要求
- 付款条件
- 包装运输要求
- 质量认证要求
"""

import json
from typing import Dict, Any, List, Union
from datetime import datetime

from app.core.base_agent import (
    BaseAgent, AgentResult, AgentMessage, MessageRole, AgentConfig,
    create_success_result, create_error_result
)
from app.core.llm_client import LLMClientFactory
from app.utils.get_material_info import get_supplier_material_info
from app.utils.tool_function import extract_largest_json
from config.llm_config import DEFAULT_MODEL


class QuoteRequestAgent(BaseAgent):
    """报价请求Agent"""
    
    def __init__(self, config: AgentConfig = None, llm_client=None):
        if config is None:
            config = AgentConfig(
                name="quote_request_agent",
                description="在供应商确认能力后发送报价请求的Agent",
                system_prompt=self._get_system_prompt(),
                # model="deepseek/deepseek-chat-v3-0324",
                model=DEFAULT_MODEL,
            )
        super().__init__(config)

        # 注入LLM客户端
        self.llm_client = llm_client
        
        # 初始化LLM客户端
        if not self.llm_client:
            try:
                from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
                self.llm_client = LLMClientFactory.create_openai_client(
                    base_url=OPENAI_BASE_URL,
                    api_key=OPENAI_API_KEY,
                    default_model=self.config.model
                )
            except ImportError:
                # 如果没有配置文件，使用环境变量
                self.llm_client = LLMClientFactory.create_from_env()
        
        self.user_prompt = self.get_user_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一名专业的采购人员，负责与供应商进行报价相关的商务沟通。

工作场景：
- 此阶段已经向供应商发送了物料需求信息
- 现在主要进行报价相关的对话交流
- 包括询问报价、回应报价单、确认交期等

工作要求：
1. 专注于报价相关的对话，保持商务沟通的专业性
2. 根据供应商的回复给出合适的响应
3. 如果信息不足以回答供应商的提问，需要转人工处理
4. 常见场景包括：询问报价进展、感谢报价单、确认需要时间等

输出格式：
必须返回JSON格式：
{
    "message": "回复内容",
    "reason": "选择这种回复方式的理由",
    "need_human": false
}

示例输出：
{
    "message": "好的，请问大概什么时候能提供报价单？",
    "reason": "询问报价时间安排",
    "need_human": false
}

{
    "message": "感谢您提供的报价单，我们会仔细评估，有问题会及时联系您。",
    "reason": "收到报价单后的礼貌回复",
    "need_human": false
}

{
    "message": "好的，我们等您的报价单。",
    "reason": "供应商表示需要时间准备报价",
    "need_human": false
}
"""

    def get_user_prompt(self) -> str:
        """获取用户提示词"""
        return """
供应商的消息：
{user_message}

工作要求：
1. 这是报价阶段的对话，物料需求信息之前已经发送给供应商
2. 根据供应商的回复，给出合适的商务回应
3. 如果信息不足以回答供应商的提问，设置need_human为true
4. 保持专业的商务沟通语调

常见场景处理：
- 询问报价进展："请问报价准备得怎么样了？"
- 收到报价单："感谢您的报价单，我们会仔细评估"
- 供应商需要时间："好的，我们等您的消息"
- 价格谈判：根据具体情况回应
- 交期确认：确认具体的交货时间

输出格式：
必须返回JSON格式：
{{
    "message": "回复内容",
    "reason": "选择这种回复方式的理由",
    "need_human": false
}}

示例输出：
{{
    "message": "好的，请问大概什么时候能提供报价单？",
    "reason": "询问报价时间安排",
    "need_human": false
}}

{{
    "message": "感谢您提供的报价单，我们会仔细评估，有问题会及时联系您。",
    "reason": "收到报价单后的礼貌回复",
    "need_human": false
}}

{{
    "message": "抱歉，关于这个技术细节我需要和技术部门确认，稍后回复您。",
    "reason": "涉及复杂技术问题，信息不足需要人工处理",
    "need_human": true
}}

请根据以上要求生成专业的回复。
"""
    
    def get_supported_types(self) -> List[str]:
        """返回支持的请求类型"""
        return [
            "quote_discussion",
            "price_inquiry",
            "quote_follow_up",
            "quote_response",
            "delivery_confirmation",
            "payment_terms_discussion"
        ]
    
    async def process(self, session_id: str, data: Dict[str, Any]) -> AgentResult:
        """处理报价请求"""
        start_time = datetime.now()
        
        try:
            # 验证输入
            if not await self.validate_input(data):
                return create_error_result("输入数据验证失败")
            
            # 预处理
            processed_data = await self.pre_process(session_id, data)
            
            # 获取用户消息
            user_message = processed_data.get("message", "")
            
            # 构建对话上下文
            messages = await self._build_conversation_context(user_message)
            
            # 调用LLM生成报价请求
            response_message = await self.call_llm(messages)

            response_json = extract_largest_json(response_message.content)
            
            # 构建结果
            result = create_success_result(
                data={
                    "response": response_json.get("message", "提取大模型回复失败"),
                    "agent_type": "quote_request_agent",
                    "session_id": session_id,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "need_human": response_json.get("need_human", False),
                    "reason": response_json.get("reason", ""),
                },
                message="报价对话处理完成"
            )
            
            # 添加响应消息到结果
            result.add_message(
                role=MessageRole.ASSISTANT,
                content=response_json.get("message", "提取大模型回复失败"),
                metadata={
                    "agent": self.name,
                    "processing_time": (datetime.now() - start_time).total_seconds(),
                    "need_human": response_json.get("need_human", False)
                }
            )
            
            # 后处理
            result = await self.post_process(session_id, result)
            
            return result
            
        except Exception as e:
            self.log_error(f"处理报价请求失败: {str(e)}")
            return create_error_result(
                error=f"处理失败: {str(e)}",
                data={"session_id": session_id}
            )
    
    async def _build_conversation_context(self, user_message: str) -> List[AgentMessage]:
        """构建对话上下文"""
        messages = []
        
        # 系统提示
        messages.append(AgentMessage(
            role=MessageRole.SYSTEM,
            content=self.config.system_prompt
        ))
        
        # 添加当前用户消息
        if user_message:
            user_question = self.user_prompt.format(
                user_message=user_message
            )
            messages.append(AgentMessage(
                role=MessageRole.USER,
                content=user_question
            ))
        
        return messages
    

    
    async def validate_input(self, data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        if not isinstance(data, dict):
            return False
        
        # 检查必要字段
        if "message" not in data and "message_history" not in data:
            return False
        
        return True
    
    async def pre_process(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理数据"""
        # 获取会话记忆上下文
        memory_context = await self.get_memory_context(session_id)
        
        # 合并上下文信息
        processed_data = data.copy()
        if memory_context:
            processed_data["memory_context"] = memory_context
        
        return processed_data
    
    async def post_process(self, session_id: str, result: AgentResult) -> AgentResult:
        """后处理结果"""
        # 保存会话上下文到记忆
        if result.success and result.messages:
            context_to_save = {
                "last_interaction": datetime.now().isoformat(),
                "agent_type": "quote_request_agent",
                "last_response": result.messages[-1].content if result.messages else None,
                "need_human": result.data.get("need_human") if result.data else False
            }
            await self.save_memory_context(session_id, context_to_save)
        
        return result
    
    def get_common_scenarios(self) -> Dict[str, str]:
        """获取常见报价对话场景"""
        return {
            "quote_inquiry": "询问报价进展",
            "quote_received": "收到报价单回复",
            "need_time": "供应商需要时间准备",
            "price_negotiation": "价格谈判",
            "delivery_confirmation": "交期确认",
            "payment_terms": "付款条件讨论"
        }
    
    def get_response_templates(self) -> Dict[str, str]:
        """获取回复模板"""
        return {
            "follow_up": "请问报价准备得怎么样了？",
            "thank_quote": "感谢您的报价单，我们会仔细评估，有问题会及时联系您。",
            "wait_response": "好的，我们等您的消息。",
            "need_clarification": "关于{topic}，能否提供更详细的信息？",
            "confirm_delivery": "请确认一下交货时间，我们这边需要安排生产计划。"
        }
    
    def __str__(self) -> str:
        return f"QuoteRequestAgent({self.name})"
    
    def __repr__(self) -> str:
        return f"QuoteRequestAgent(name='{self.name}', supported_types={self.get_supported_types()})"


# 使用示例
if __name__ == "__main__":
    import asyncio
    from app.core.llm_client import LLMClientFactory
    
    async def test_agent():
        # 创建Agent（使用自带的初始化配置）
        agent = QuoteRequestAgent()
        
        # 模拟处理请求
        test_data = {
            "message": "报价单我们正在准备，大概明天能发给您。"
        }
        
        result = await agent.process("test_session_001", test_data)
        print(f"处理结果: {result.success}")
        print(f"响应消息: {result.data.get('response') if result.data else 'None'}")
        print(f"需要人工: {result.data.get('need_human') if result.data else 'None'}")
        print(json.dumps(result.to_dict(), ensure_ascii=False, indent=2))
        print(f"错误信息: {result.error}")
    
    # 运行测试
    asyncio.run(test_agent())