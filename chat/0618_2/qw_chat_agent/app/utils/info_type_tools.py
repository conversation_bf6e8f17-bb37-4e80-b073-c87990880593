import pandas as pd
from typing import Dict, Any, List, Union
import os
from app.services.oss_material_service import OssMaterialService
from app.services.component_supplier_service import ComponentSupplierService

current_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(current_dir, "../..", "data", "cy3000")

def get_material_spec_info(supplier_name: str, component_codes: List[str] = None, need_num: int = 10) -> Dict[str, Any]:
    """
    根据供应商名称获取物料规格信息
    新版本：通过数据库视图获取OSS路径，替代Excel文件读取
    """
    # 首先通过供应商获取分类信息
    component_supplier_service = ComponentSupplierService()
    categories = component_supplier_service.get_categories_by_recommended_supplier(supplier_name)
    
    if not categories:
        print("未找到该供应商对应的分类信息")
        return {"material_need": [], "material_types": []}
    
    # 提取分类信息，保持与原版本相同的格式
    material_types = []
    category_pairs = []
    
    for category in categories:
        primary_cat = category.get('primary_category', '')
        secondary_cat = category.get('secondary_category', '')
        
        if primary_cat and (primary_cat, secondary_cat) not in material_types:
            material_types.append((primary_cat, secondary_cat))
            category_pairs.append((primary_cat, secondary_cat))
    
    print(material_types)
    
    # 通过OSS物料服务获取物料信息
    oss_material_service = OssMaterialService()
    materials = oss_material_service.get_materials_by_categories(category_pairs)
    
    material_need = []
    
    for material in materials:
        # 检查是否有文档，跳过没有文档的物料
        document_names = material.get('document_names', '')
        oss_paths_str = material.get('oss_paths', '[]')
        
        if not document_names or oss_paths_str == '[]':
            continue
        
        # 解析OSS路径 - 与原版本格式保持一致
        try:
            if oss_paths_str and oss_paths_str != '[]':
                # 移除首尾的方括号并按逗号分割，然后清理每个路径
                paths_str = oss_paths_str.strip('[]')
                if paths_str:
                    oss_paths = [path.strip().strip('"').strip("'") for path in paths_str.split(',')]
                else:
                    oss_paths = []
            else:
                oss_paths = []
        except:
            oss_paths = []
        
        # 构建物料项，保持与原版本完全相同的字段格式
        tmp = {
            'component_name': material.get('material_name', ''),
            'component_spec': material.get('material_spec', ''),
            'component_code': material.get('material_code', ''),
            'component_type': material.get('material', ''),  # 注意：这里使用 material 字段
            'component_num': int(material.get('quantity', 1)) * need_num,
            'file_name': document_names,  # 直接使用分号分隔的字符串
            'oss_path': oss_paths  # OSS路径列表
        }
        
        # 避免重复添加
        if tmp not in material_need:
            material_need.append(tmp)
    
    print(len(material_need))
    
    return {
        "material_need": material_need, 
        "material_types": material_types
    }

def get_material_spec_info_legacy(supplier_name: str, component_codes: List[str] = None, need_num: int = 10) -> Dict[str, Any]:
    """
    原始版本：从Excel文件读取（保留作为备用）
    """
    supplier_info_df = pd.read_excel(os.path.join(data_dir, "supplier.xlsx"), sheet_name="供应链数据总览")
    supplier_info_spec = supplier_info_df[supplier_info_df["推荐供应商"] == supplier_name]
    if supplier_info_spec.empty:
        return {"material_need": []}
    
    material_types = list()
    for row in supplier_info_spec.itertuples():
        if not hasattr(row, '一级分类') or not hasattr(row, '二级分类'):
            continue
        first_type = row.一级分类
        second_type = row.二级分类
        if first_type and second_type and (first_type, second_type) not in material_types:
            material_types.append((first_type, second_type))
    print(material_types)

    material_df = pd.read_excel(os.path.join(data_dir, "oss_material.xlsx"))
    material_need = list()
    for material_type in material_types:
        first_type, second_type = material_type
        material_need_df = material_df[(material_df["一级分类"] == first_type) & (material_df["二级分类"] == second_type)]
        if not material_need_df.empty:
            for row in material_need_df.itertuples():
                if pd.isna(row.文档名称) or "未找到匹配文件" in row.文档名称:
                    continue

                tmp = dict()
                tmp['component_name'] = row.物料名称
                tmp['component_spec'] = row.物料规格
                tmp['component_code'] = row.物料编码
                tmp['component_type'] = row.材料
                tmp['component_num'] = int(row.数量) * need_num
                tmp['file_name'] = row.文档名称
                tmp['oss_path'] = row.OSS路径[1:-1].split(",") if row.OSS路径 != '[]' else []
                if tmp not in material_need:
                    material_need.append(tmp)
    print(len(material_need))

    return {"material_need": material_need, "material_types": material_types}

if __name__ == "__main__":
    result = get_material_spec_info("广东兴发铝业有限公司")
    print("数据库版本结果:")
    print(result)
    
    print("\n" + "="*50 + "\n")
    
    # 对比原版本
    result_legacy = get_material_spec_info_legacy("广东兴发铝业有限公司")
    print("Excel版本结果:")
    print(result_legacy)


