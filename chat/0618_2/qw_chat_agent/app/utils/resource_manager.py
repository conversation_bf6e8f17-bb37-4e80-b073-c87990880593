"""
资源管理工具类
用于统一管理临时文件、目录和其他系统资源，防止内存泄漏
"""

import os
import shutil
import tempfile
import weakref
import atexit
import threading
from contextlib import contextmanager
from typing import Dict, List, Optional, Any, Generator
from datetime import datetime, timedelta
import logging


class ResourceManager:
    """资源管理器 - 单例模式，线程安全"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._temp_dirs: Dict[str, str] = {}  # 临时目录注册表
        self._temp_files: Dict[str, str] = {}  # 临时文件注册表
        self._cleanup_callbacks: List[callable] = []  # 清理回调函数
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        
        # 注册程序退出时的清理函数
        atexit.register(self.cleanup_all)
    
    def register_temp_dir(self, temp_dir: str, identifier: str = None) -> str:
        """
        注册临时目录
        
        Args:
            temp_dir: 临时目录路径
            identifier: 标识符，用于后续引用
            
        Returns:
            str: 目录标识符
        """
        with self._lock:
            if identifier is None:
                identifier = f"temp_dir_{len(self._temp_dirs)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self._temp_dirs[identifier] = temp_dir
            self._logger.debug(f"注册临时目录: {identifier} -> {temp_dir}")
            return identifier
    
    def register_temp_file(self, temp_file: str, identifier: str = None) -> str:
        """
        注册临时文件
        
        Args:
            temp_file: 临时文件路径
            identifier: 标识符，用于后续引用
            
        Returns:
            str: 文件标识符
        """
        with self._lock:
            if identifier is None:
                identifier = f"temp_file_{len(self._temp_files)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self._temp_files[identifier] = temp_file
            self._logger.debug(f"注册临时文件: {identifier} -> {temp_file}")
            return identifier
    
    def cleanup_temp_dir(self, identifier: str) -> bool:
        """
        清理指定的临时目录
        
        Args:
            identifier: 目录标识符
            
        Returns:
            bool: 清理是否成功
        """
        with self._lock:
            if identifier not in self._temp_dirs:
                self._logger.warning(f"临时目录标识符不存在: {identifier}")
                return False
            
            temp_dir = self._temp_dirs[identifier]
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    self._logger.info(f"成功清理临时目录: {temp_dir}")
                
                del self._temp_dirs[identifier]
                return True
            except Exception as e:
                self._logger.error(f"清理临时目录失败 {temp_dir}: {str(e)}")
                return False
    
    def cleanup_temp_file(self, identifier: str) -> bool:
        """
        清理指定的临时文件
        
        Args:
            identifier: 文件标识符
            
        Returns:
            bool: 清理是否成功
        """
        with self._lock:
            if identifier not in self._temp_files:
                self._logger.warning(f"临时文件标识符不存在: {identifier}")
                return False
            
            temp_file = self._temp_files[identifier]
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self._logger.info(f"成功清理临时文件: {temp_file}")
                
                del self._temp_files[identifier]
                return True
            except Exception as e:
                self._logger.error(f"清理临时文件失败 {temp_file}: {str(e)}")
                return False
    
    def cleanup_all(self):
        """清理所有注册的资源"""
        with self._lock:
            self._logger.info("开始清理所有资源...")
            
            # 清理临时目录
            for identifier, temp_dir in list(self._temp_dirs.items()):
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir, ignore_errors=True)
                        self._logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    self._logger.error(f"清理临时目录失败 {temp_dir}: {str(e)}")
            
            # 清理临时文件
            for identifier, temp_file in list(self._temp_files.items()):
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        self._logger.info(f"清理临时文件: {temp_file}")
                except Exception as e:
                    self._logger.error(f"清理临时文件失败 {temp_file}: {str(e)}")
            
            # 执行清理回调
            for callback in self._cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    self._logger.error(f"执行清理回调失败: {str(e)}")
            
            # 清空注册表
            self._temp_dirs.clear()
            self._temp_files.clear()
            self._cleanup_callbacks.clear()
            
            self._logger.info("资源清理完成")
    
    def add_cleanup_callback(self, callback: callable):
        """添加清理回调函数"""
        with self._lock:
            self._cleanup_callbacks.append(callback)
    
    def get_resource_status(self) -> Dict[str, Any]:
        """获取资源状态"""
        with self._lock:
            return {
                "temp_dirs_count": len(self._temp_dirs),
                "temp_files_count": len(self._temp_files),
                "cleanup_callbacks_count": len(self._cleanup_callbacks),
                "temp_dirs": list(self._temp_dirs.keys()),
                "temp_files": list(self._temp_files.keys())
            }


# 全局资源管理器实例
resource_manager = ResourceManager()


@contextmanager
def managed_temp_dir(prefix: str = "temp_", suffix: str = "") -> Generator[str, None, None]:
    """
    上下文管理器：创建并自动清理临时目录
    
    Args:
        prefix: 目录前缀
        suffix: 目录后缀
        
    Yields:
        str: 临时目录路径
    """
    temp_dir = tempfile.mkdtemp(prefix=prefix, suffix=suffix)
    identifier = resource_manager.register_temp_dir(temp_dir)
    
    try:
        yield temp_dir
    finally:
        resource_manager.cleanup_temp_dir(identifier)


@contextmanager
def managed_temp_file(suffix: str = "", prefix: str = "temp_", dir: str = None) -> Generator[str, None, None]:
    """
    上下文管理器：创建并自动清理临时文件
    
    Args:
        suffix: 文件后缀
        prefix: 文件前缀
        dir: 临时文件目录
        
    Yields:
        str: 临时文件路径
    """
    fd, temp_file = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
    os.close(fd)  # 关闭文件描述符
    identifier = resource_manager.register_temp_file(temp_file)
    
    try:
        yield temp_file
    finally:
        resource_manager.cleanup_temp_file(identifier)


class ManagedResource:
    """托管资源类 - 用于自动管理资源生命周期"""
    
    def __init__(self, resource_path: str, is_directory: bool = True):
        self.resource_path = resource_path
        self.is_directory = is_directory
        self._identifier = None
        
        if is_directory:
            self._identifier = resource_manager.register_temp_dir(resource_path)
        else:
            self._identifier = resource_manager.register_temp_file(resource_path)
    
    def __enter__(self):
        return self.resource_path
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
    
    def cleanup(self):
        """手动清理资源"""
        if self._identifier:
            if self.is_directory:
                resource_manager.cleanup_temp_dir(self._identifier)
            else:
                resource_manager.cleanup_temp_file(self._identifier)
            self._identifier = None
    
    def __del__(self):
        """析构函数 - 确保资源被清理"""
        self.cleanup()


def create_managed_temp_dir(prefix: str = "temp_", suffix: str = "") -> ManagedResource:
    """
    创建托管的临时目录
    
    Args:
        prefix: 目录前缀
        suffix: 目录后缀
        
    Returns:
        ManagedResource: 托管资源对象
    """
    temp_dir = tempfile.mkdtemp(prefix=prefix, suffix=suffix)
    return ManagedResource(temp_dir, is_directory=True)


def create_managed_temp_file(suffix: str = "", prefix: str = "temp_", dir: str = None) -> ManagedResource:
    """
    创建托管的临时文件
    
    Args:
        suffix: 文件后缀
        prefix: 文件前缀
        dir: 临时文件目录
        
    Returns:
        ManagedResource: 托管资源对象
    """
    fd, temp_file = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
    os.close(fd)  # 关闭文件描述符
    return ManagedResource(temp_file, is_directory=False)


# 装饰器：自动资源管理
def auto_cleanup_temp_resources(func):
    """
    装饰器：自动清理函数执行过程中创建的临时资源
    """
    def wrapper(*args, **kwargs):
        # 记录执行前的资源状态
        initial_status = resource_manager.get_resource_status()
        
        try:
            return func(*args, **kwargs)
        finally:
            # 执行后清理新增的资源（可选实现）
            pass
    
    return wrapper 