from typing import Dict, Any, List
from app.services.product_category_service import ProductCategoryService
from app.services.component_supplier_service import ComponentSupplierService
import os
import pandas as pd


current_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(current_dir, "../..", "data", "cy3000")

def get_supplier_material_info(supplier_name: str, component_code: List[str] = None) -> Dict[str, Any]:
    """获取推荐供应商对应的物料信息"""
    
    component_supplier_service = ComponentSupplierService()
    
    # 修正：调用正确的方法
    categories = component_supplier_service.get_categories_by_recommended_supplier(supplier_name)
    print("供应商对应的分类信息:", categories)
    
    if not categories:
        return {
            "material_type": "",
            "feature": ""
        }
    
    # 返回第一个分类的信息，或者返回所有分类信息
    first_category = categories[0]
    
    # 构建材料类型描述
    material_type = f"{first_category.get('primary_category', '')} - {first_category.get('secondary_category', '')}"
    
    return {
        "material_type": material_type,
        "feature": first_category.get('category_feature', ''),
        # "all_categories": categories  # 返回所有分类信息
    }


if __name__ == "__main__":
    result = get_supplier_material_info("广东兴发铝业有限公司")
    print("结果:", result)
