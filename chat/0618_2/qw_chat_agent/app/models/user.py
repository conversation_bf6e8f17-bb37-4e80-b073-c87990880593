"""
用户模型 - 基于sys_user表
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Boolean
from sqlalchemy.sql import func
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from app.models.base import Base


class SysUser(Base):
    """系统用户表模型"""
    __tablename__ = 'sys_user'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    user_name = Column(String(50), nullable=False, unique=True, comment='用户名')
    password_ = Column(String(100), nullable=False, comment='密码')
    nick_name = Column(String(50), comment='昵称')
    email_ = Column(String(100), comment='邮箱')
    phone_ = Column(String(20), comment='手机号')
    avatar_ = Column(String(255), comment='头像')
    status_ = Column(Integer, nullable=False, default=1, comment='状态')
    LIMIT_ = Column(Integer, comment='消耗额度')
    is_delete = Column(Boolean, nullable=False, default=False, comment='是否删除（0-未删除 1-已删除）')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='修改时间')
    lastLogin_time = Column(DateTime, comment='最后登录时间')
    TENANT_ID = Column(BigInteger, default=-1, comment='租户ID')


# Pydantic模型用于API
class UserBase(BaseModel):
    """用户基础模型"""
    user_name: str
    nick_name: Optional[str] = None
    email_: Optional[EmailStr] = None
    phone_: Optional[str] = None
    avatar_: Optional[str] = None
    status_: int = 1
    LIMIT_: Optional[int] = None
    TENANT_ID: int = -1


class UserCreate(UserBase):
    """创建用户模型"""
    password_: str


class UserUpdate(BaseModel):
    """更新用户模型"""
    nick_name: Optional[str] = None
    email_: Optional[EmailStr] = None
    phone_: Optional[str] = None
    avatar_: Optional[str] = None
    status_: Optional[int] = None
    LIMIT_: Optional[int] = None
    password_: Optional[str] = None


class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    create_time: datetime
    update_time: datetime
    lastLogin_time: Optional[datetime] = None
    is_delete: bool

    class Config:
        from_attributes = True


class UserInDB(UserResponse):
    """数据库中的用户模型"""
    password_: str


# Token相关模型
class Token(BaseModel):
    """Token响应模型"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenData(BaseModel):
    """Token数据模型"""
    user_name: Optional[str] = None
    user_id: Optional[int] = None


class LoginRequest(BaseModel):
    """登录请求模型"""
    user_name: str
    password_: str


class RefreshTokenRequest(BaseModel):
    """刷新Token请求模型"""
    refresh_token: str 