"""
供应商询价相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.models.user import UserResponse
from app.api.dependencies import get_current_user_info_optional
from app.core.orchestrator import create_orchestrator
from app.agents.supplier_capability_agent import SupplierCapabilityAgent
from app.agents.info_response_agent import InfoResponseAgent
from app.agents.quote_request_agent import QuoteRequestAgent
from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
from config.llm_config import DEFAULT_MODEL
from config.db_config import REDIS_CONFIG

router = APIRouter(prefix="/inquiry", tags=["供应商询价"])

# 全局编排器实例
orchestrator = None


def get_orchestrator():
    """获取编排器实例"""
    global orchestrator
    if orchestrator is None:
        # 创建编排器配置
        config = {
            "use_redis": True,  # 可以根据需要启用Redis
            "redis": REDIS_CONFIG,
            "llm": {
                "base_url": OPENAI_BASE_URL,
                "api_key": OPENAI_API_KEY,
                # "default_model": "deepseek/deepseek-chat-v3-0324"
                "default_model": DEFAULT_MODEL
            }
        }
        
        # 创建编排器实例
        orchestrator = create_orchestrator(config)
        
        # 注册所有Agent
        orchestrator.register_agent(SupplierCapabilityAgent())
        orchestrator.register_agent(InfoResponseAgent())
        orchestrator.register_agent(QuoteRequestAgent())
    
    return orchestrator


# 请求和响应模型
class InquiryRequest(BaseModel):
    """询价请求模型"""
    message: str
    session_id: Optional[str] = None
    sid: Optional[str] = None
    uid: Optional[str] = None
    supplier_info: Optional[Dict[str, Any]] = None
    component_code: Optional[List[str]] = None


class InquiryResponse(BaseModel):
    """询价响应模型"""
    success: bool
    session_id: str
    response: str
    agent_type: Optional[str] = None
    need_human: bool = False
    human_reason: Optional[str] = None
    processing_time: Optional[float] = None
    response_type: str = "text"
    material_spec: Optional[Dict[str, Any]] = None


class SessionStatusResponse(BaseModel):
    """会话状态响应模型"""
    session_id: str
    status: str
    created_at: str
    updated_at: str
    message_count: int
    current_agent: Optional[str] = None


class SystemStatusResponse(BaseModel):
    """系统状态响应模型"""
    agents: Dict[str, Any]
    sessions: Dict[str, Any]


@router.post("/chat", response_model=InquiryResponse, summary="发送询价消息")
async def send_inquiry_message(
    request: InquiryRequest,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    发送询价消息给AI系统
    
    - **message**: 用户消息内容
    - **session_id**: 会话ID（可选，不提供则自动创建）
    - **supplier_info**: 供应商信息（可选）
    - **component_code**: 组件代码列表（可选）
    """
    try:
        orch = get_orchestrator()
        
        # 构建请求上下文，如果没有用户信息，使用匿名用户
        if current_user:
            request_context = {
                "user_id": current_user.id,
                "user_name": current_user.user_name,
                "sid": request.sid,
                "uid": request.uid,
                "supplier_info": request.supplier_info or {},
                "component_code": request.component_code or []
            }
        else:
            # 匿名用户
            request_context = {
                "user_id": "anonymous",
                "user_name": "anonymous",
                "sid": request.sid,
                "uid": request.uid,
                "supplier_info": request.supplier_info or {},
                "component_code": request.component_code or []
            }
        
        # 如果没有提供session_id，创建新会话
        session_id = request.session_id
        print(f"session_id: {session_id}")
        if not session_id:
            session_id = await orch.create_session(inquiry_data=request_context)
        
        # 处理消息
        result = await orch.handle_message(
            session_id=session_id,
            user_message=request.message,
            request_context=request_context
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"消息处理失败: {result.error}"
            )
        
        return InquiryResponse(
            success=result.success,
            session_id=session_id,
            response=result.data.get("response", ""),
            agent_type=result.data.get("agent_type"),
            need_human=result.data.get("need_human", False),
            human_reason=result.data.get("human_reason"),
            processing_time=result.processing_time,
            response_type = "file" if (result.data and "material_spec" in result.data) else "text",
            material_spec=result.data.get("material_spec", {})
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/session/{session_id}/status", response_model=SessionStatusResponse, summary="获取会话状态")
async def get_session_status(
    session_id: str,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    获取指定会话的状态信息
    
    - **session_id**: 会话ID
    """
    try:
        orch = get_orchestrator()
        session_context = await orch.get_session(session_id)
        
        if not session_context:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在"
            )
        
        return SessionStatusResponse(
            session_id=session_context.session_id,
            status=session_context.status.value,
            created_at=session_context.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            updated_at=session_context.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
            message_count=len(session_context.message_history),
            current_agent=session_context.current_agent
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话状态失败: {str(e)}"
        )


@router.post("/session/{session_id}/update-status", summary="更新会话状态")
async def update_session_status(
    session_id: str,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    更新指定会话的状态（从人工接管状态恢复到激活状态）
    
    - **session_id**: 会话ID
    """
    try:
        orch = get_orchestrator()
        result = await orch.update_session_status(session_id)
        
        if result.get("error"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新会话状态失败: {str(e)}"
        )


@router.delete("/session/{session_id}", summary="删除会话")
async def delete_session(
    session_id: str,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    删除指定的会话
    
    - **session_id**: 会话ID
    """
    try:
        orch = get_orchestrator()
        await orch.delete_session(session_id)
        return {"message": f"会话 {session_id} 已删除"}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除会话失败: {str(e)}"
        )


@router.get("/system/status", response_model=SystemStatusResponse, summary="获取系统状态")
async def get_system_status(current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)):
    """
    获取系统状态信息
    
    包括Agent状态和会话统计信息
    """
    try:
        orch = get_orchestrator()
        status = orch.get_system_status()
        
        return SystemStatusResponse(
            agents=status["agents"],
            sessions=status["sessions"]
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统状态失败: {str(e)}"
        )


@router.get("/sessions/human-taken", summary="获取人工接管的会话列表")
async def get_human_taken_sessions(current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)):
    """
    获取所有被人工接管的会话列表
    """
    try:
        orch = get_orchestrator()
        sessions = await orch.get_human_taken_sessions()
        return {"sessions": sessions}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取人工接管会话列表失败: {str(e)}"
        ) 