"""
OSS服务相关API路由 - 修复版本
"""
import os
import tempfile
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import FileResponse
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from app.models.user import UserResponse
from app.api.dependencies import get_current_user_info
from app.services.oss_service import oss_service
from app.utils.resource_manager import managed_temp_dir
from app.utils.delayed_file_response import create_download_response

router = APIRouter(prefix="/oss", tags=["OSS服务"])


class DownloadRequest(BaseModel):
    """下载请求模型"""
    supplier_name: str = Field(..., description="供应商名称")
    need_num: int = Field(default=10, description="需求数量倍数", ge=1, le=100)
    compress_after_download: bool = Field(default=True, description="下载完成后是否压缩")
    delete_original_after_compress: bool = Field(default=True, description="压缩后是否删除原文件夹")


class DownloadResponse(BaseModel):
    """下载响应模型"""
    success: bool
    message: str
    supplier_name: str
    total_materials: Optional[int] = None
    total_files: Optional[int] = None
    downloaded_files: Optional[int] = None
    failed_files: Optional[int] = None
    processing_time: Optional[float] = None
    file_path: Optional[str] = None
    file_size_mb: Optional[float] = None


class MaterialListResponse(BaseModel):
    """物料清单响应模型"""
    success: bool
    message: str
    supplier_name: str
    total_materials: int
    total_files: int
    materials: list


@router.post("/download-supplier-materials", summary="下载供应商物料文件")
async def download_supplier_materials(
    request: DownloadRequest,
    current_user: UserResponse = Depends(get_current_user_info)
):
    """
    根据供应商名称下载对应物料文件并返回压缩包
    
    - **supplier_name**: 供应商名称
    - **need_num**: 需求数量倍数（1-100）
    - **compress_after_download**: 是否在下载后压缩文件
    - **delete_original_after_compress**: 压缩后是否删除原文件夹
    """
    import time
    start_time = time.time()
    
    # 使用上下文管理器自动管理临时目录
    with managed_temp_dir(prefix=f"supplier_{request.supplier_name}_") as temp_dir:
        try:
            target_folder = os.path.join(temp_dir, f"supplier_{request.supplier_name}")
            
            # 调用OSS服务下载并压缩文件
            result = oss_service.download_and_compress(
                supplier_name=request.supplier_name,
                target_folder=target_folder,
                need_num=request.need_num,
                compress_after_download=request.compress_after_download,
                delete_original_after_compress=request.delete_original_after_compress
            )
            
            processing_time = time.time() - start_time
            
            if not result["success"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"]
                )
            
            # 获取下载统计信息
            download_result = result.get("download_result", {})
            compress_result = result.get("compress_result", {})
            
            # 准备响应数据
            response_data = {
                "success": True,
                "message": result["message"],
                "supplier_name": request.supplier_name,
                "total_materials": download_result.get("total_materials", 0),
                "total_files": download_result.get("total_files", 0),
                "downloaded_files": download_result.get("downloaded_files", 0),
                "failed_files": download_result.get("failed_files", 0),
                "processing_time": round(processing_time, 2)
            }
            
            # 如果有压缩文件，准备文件下载
            if compress_result and compress_result.get("success"):
                zip_path = compress_result["zip_path"]
                if os.path.exists(zip_path):
                    # 生成安全的文件名
                    safe_supplier_name = request.supplier_name.replace('/', '_').replace('\\', '_').replace(':', '_')
                    final_zip_name = f"{safe_supplier_name}_materials.zip"
                    
                    response_data["file_path"] = zip_path
                    response_data["file_size_mb"] = compress_result.get("zip_size_mb", 0)
                    
                    # 使用延迟清理的文件响应
                    return create_download_response(
                        source_path=zip_path,
                        filename=final_zip_name,
                        media_type='application/zip',
                        headers={
                            "X-Total-Materials": str(response_data["total_materials"]),
                            "X-Total-Files": str(response_data["total_files"]),
                            "X-Downloaded-Files": str(response_data["downloaded_files"]),
                            "X-Failed-Files": str(response_data["failed_files"]),
                            "X-Processing-Time": str(response_data["processing_time"]),
                            "X-File-Size-MB": str(response_data.get("file_size_mb", 0))
                        },
                        cleanup_delay=300  # 5分钟后清理
                    )
            
            # 如果没有压缩文件，返回普通响应
            return DownloadResponse(**response_data)
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"下载服务器内部错误: {str(e)}"
            )
    # 临时目录会在with语句结束时自动清理，但下载文件已经复制到持久目录


@router.get("/list-supplier-materials/{supplier_name}", response_model=MaterialListResponse, summary="获取供应商物料清单")
async def list_supplier_materials(
    supplier_name: str,
    need_num: int = 10,
    current_user: UserResponse = Depends(get_current_user_info)
):
    """
    获取供应商对应的物料信息清单（不下载文件）
    
    - **supplier_name**: 供应商名称
    - **need_num**: 需求数量倍数（默认10）
    """
    try:
        # 调用OSS服务获取物料清单
        result = oss_service.list_supplier_materials(
            supplier_name=supplier_name,
            need_num=need_num
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result["message"]
            )
        
        return MaterialListResponse(
            success=result["success"],
            message=result["message"],
            supplier_name=result["supplier_name"],
            total_materials=result["total_materials"],
            total_files=result["total_files"],
            materials=result["materials"]
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取物料清单服务器内部错误: {str(e)}"
        )


@router.post("/download-excel-only", summary="仅下载供应商物料Excel清单")
async def download_excel_only(
    request: DownloadRequest,
    current_user: UserResponse = Depends(get_current_user_info)
):
    """
    仅下载供应商物料的Excel清单文件
    
    - **supplier_name**: 供应商名称
    - **need_num**: 需求数量倍数
    """
    # 使用上下文管理器自动管理临时目录
    with managed_temp_dir(prefix=f"excel_{request.supplier_name}_") as temp_dir:
        try:
            # 调用OSS服务保存Excel文件
            result = oss_service.save_supplier_materials_to_excel(
                supplier_name=request.supplier_name,
                target_folder=temp_dir,
                need_num=request.need_num
            )
            
            if not result["success"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"]
                )
            
            excel_path = result["excel_path"]
            if not os.path.exists(excel_path):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Excel文件生成失败"
                )
            
            # 生成下载文件名
            safe_supplier_name = request.supplier_name.replace('/', '_').replace('\\', '_').replace(':', '_')
            download_filename = f"{safe_supplier_name}_物料清单.xlsx"
            
            # 使用延迟清理的文件响应
            return create_download_response(
                source_path=excel_path,
                filename=download_filename,
                media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={
                    "X-Total-Materials": str(result["total_materials"]),
                    "X-Total-Files": str(result["total_files"])
                },
                cleanup_delay=300  # 5分钟后清理
            )
                
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"下载Excel服务器内部错误: {str(e)}"
            )
    # 临时目录会在with语句结束时自动清理，但下载文件已经复制到持久目录


@router.get("/resource-status", summary="获取资源管理状态")
async def get_resource_status(
    current_user: UserResponse = Depends(get_current_user_info)
):
    """
    获取当前资源管理状态（调试用）
    """
    from app.utils.resource_manager import resource_manager
    from app.utils.background_tasks import background_task_manager
    
    try:
        resource_status = resource_manager.get_resource_status()
        task_status = background_task_manager.get_task_status()
        
        return {
            "success": True,
            "message": "获取资源状态成功",
            "data": {
                "resource_manager": resource_status,
                "background_tasks": task_status
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资源状态失败: {str(e)}"
        )


@router.post("/cleanup-resources", summary="手动清理所有资源")
async def cleanup_all_resources(
    current_user: UserResponse = Depends(get_current_user_info)
):
    """
    手动清理所有注册的资源（管理员功能）
    """
    from app.utils.resource_manager import resource_manager
    
    try:
        resource_manager.cleanup_all()
        return {
            "success": True,
            "message": "资源清理完成"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"资源清理失败: {str(e)}"
        ) 