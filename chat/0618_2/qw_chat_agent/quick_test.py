#!/usr/bin/env python3
"""
快速测试脚本 - 验证服务器状态和路由
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {str(e)}")
        return False

def test_routes():
    """测试路由调试"""
    print("\n=== 测试路由调试 ===")
    try:
        response = requests.get(f"{BASE_URL}/debug/routes")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            routes = response.json().get("routes", [])
            print("注册的路由:")
            for route in routes:
                print(f"  {route['methods']} {route['path']}")
            
            # 检查关键路由是否存在
            auth_routes = [r for r in routes if "/api/v1/auth" in r["path"]]
            if auth_routes:
                print(f"\n✅ 找到 {len(auth_routes)} 个认证路由")
            else:
                print("\n❌ 没有找到认证路由")
        else:
            print(f"获取路由失败: {response.text}")
    except Exception as e:
        print(f"路由调试失败: {str(e)}")

def test_login():
    """测试登录"""
    print("\n=== 测试登录 ===")
    try:
        login_data = {
            # "user_name": "admin",
            "user_name": "lhh-aopu",
            "password_": "bluemen.123"
        }
        response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return response.json().get("access_token")
        else:
            print("❌ 登录失败")
            return None
    except Exception as e:
        print(f"登录测试失败: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("🔍 快速测试开始...\n")
    
    # 1. 测试健康检查
    if not test_health():
        print("❌ 服务器未运行或健康检查失败")
        return
    
    # 2. 测试路由
    test_routes()
    
    # 3. 测试登录
    token = test_login()
    
    if token:
        print(f"\n🎉 所有测试通过！Token: {token[:20]}...")
    else:
        print("\n❌ 部分测试失败")

if __name__ == "__main__":
    main() 