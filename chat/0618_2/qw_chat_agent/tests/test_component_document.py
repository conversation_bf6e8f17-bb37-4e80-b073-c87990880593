# test_component_document.py
"""
ComponentDocument模型使用示例和测试
"""

from app.database import check_database_connection, init_database
from app.services.component_document_service import ComponentDocumentService
from app.services.component_service import ComponentService
import json

def main():
    # 1. 检查数据库连接
    if not check_database_connection():
        print("数据库连接失败，请检查配置")
        return
    
    # 2. 初始化数据库表（如果需要）
    # init_database()
    
    # 3. 创建服务实例
    doc_service = ComponentDocumentService()
    component_service = ComponentService()
    
    # 4. 先查找一个存在的物料ID用于测试
    print("=== 查找测试用的物料 ===")
    test_component = component_service.get_component_by_id(17)  # 使用一个已知存在的ID
    if not test_component:
        print("未找到测试用的物料，请先创建一些物料数据")
        return
    
    component_id = test_component['id']
    print(f"使用物料进行测试: {test_component['name']} (ID: {component_id})")
    
    # 5. 创建新的物料图纸
    # print("\n=== 创建物料图纸 ===")
    # document_data = {
    #     "component_id": component_id,
    #     "document_name": "STM32F103_原理图.pdf",
    #     "oss_path": "https://oss.example.com/documents/stm32f103_schematic.pdf",
    #     "description": "STM32F103微控制器的原理图设计文件，包含完整的电路连接图"
    # }
    
    # 创建图纸
    # new_document = doc_service.create_document(document_data)
    # print(f"创建成功: {new_document}")
    
    # 6. 再创建几个测试图纸
    # test_documents = [
    #     {
    #         "component_id": component_id,
    #         "document_name": "STM32F103_PCB布局图.pdf",
    #         "oss_path": "https://oss.example.com/documents/stm32f103_pcb.pdf",
    #         "description": "PCB板布局设计图"
    #     },
    #     {
    #         "component_id": component_id,
    #         "document_name": "STM32F103_封装图.dwg",
    #         "oss_path": "https://oss.example.com/documents/stm32f103_package.dwg",
    #         "description": "芯片封装尺寸图"
    #     }
    # ]
    
    # created_docs = []
    # for doc_data in test_documents:
    #     doc = doc_service.create_document(doc_data)
    #     created_docs.append(doc)
    #     print(f"创建图纸: {doc['document_name']}")
    
    # 7. 查询测试
    print("\n=== 查询测试 ===")
    
    # 根据ID查询
    # document_result = doc_service.get_document_by_id(new_document['id'])
    # print(f"根据ID查询结果: {document_result['document_name'] if document_result else '未找到'}")
    
    # 根据物料ID查询所有图纸
    # component_documents = doc_service.get_documents_by_component_id(component_id)
    # print(f"物料 {component_id} 的所有图纸数量: {len(component_documents)}")
    # for doc in component_documents:
    #     print(f"  - {doc['document_name']}")
    
    # 根据图纸名称搜索
    # search_results = doc_service.get_documents_by_name("STM32")
    # print(f"名称包含'STM32'的图纸数量: {len(search_results)}")
    
    # 根据OSS路径查询
    # oss_result = doc_service.get_document_by_oss_path(new_document['oss_path'])
    # print(f"根据OSS路径查询: {oss_result['document_name'] if oss_result else '未找到'}")
    
    # 8. 复合条件搜索
    # print("\n=== 复合条件搜索 ===")
    # search_results = doc_service.search_documents(
    #     component_id=component_id,
    #     document_name="PCB",
    #     limit=10
    # )
    # print(f"复合搜索结果: {len(search_results)} 个图纸")
    # for doc in search_results:
    #     print(f"  - {doc['document_name']}: {doc['description']}")
    
    # 9. 更新图纸
    # print("\n=== 更新图纸 ===")
    # update_data = {
    #     "description": "更新后的描述：STM32F103微控制器完整原理图，包含电源管理和时钟电路",
    #     "document_name": "STM32F103_完整原理图_v2.pdf"
    # }
    # updated_document = doc_service.update_document(new_document['id'], update_data)
    # if updated_document:
    #     print(f"更新成功: {updated_document['document_name']}")
    #     print(f"新描述: {updated_document['description']}")
    
    # 10. 统计信息
    print("\n=== 统计信息 ===")
    total_count = doc_service.get_document_count()
    component_doc_count = doc_service.get_document_count_by_component(component_id)
    print(f"总图纸数: {total_count}")
    print(f"物料 {component_id} 的图纸数: {component_doc_count}")
    
    # 11. 获取所有图纸（分页）
    print("\n=== 分页查询所有图纸 ===")
    all_documents = doc_service.get_all_documents(limit=5, offset=0)
    print(f"前5个图纸:")
    for doc in all_documents:
        print(f"  - ID:{doc['id']} | {doc['document_name']} | 物料ID:{doc['component_id']}")
    
    # 12. 删除测试（可选，取消注释以执行删除）
    print("\n=== 删除测试 ===")
    print("注意：以下删除操作已注释，如需测试请取消注释")
    
    # # 删除单个图纸
    # if created_docs:
    #     delete_result = doc_service.delete_document(created_docs[0]['id'])
    #     print(f"删除图纸结果: {delete_result}")
    
    # # 删除指定物料的所有图纸
    # deleted_count = doc_service.delete_documents_by_component_id(component_id)
    # print(f"删除物料 {component_id} 的所有图纸，共删除: {deleted_count} 个")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main() 