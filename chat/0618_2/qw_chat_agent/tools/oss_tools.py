#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/2/20 16:01
# <AUTHOR> hihaluemen
# @File    : oss_handler.py
# @Description :
from config.oss_config import OSS_ACCESS_KEY_ID,OSS_ACCESS_KEY_SECRET,OSS_ENDPOINT,OSS_BUCKET_NAME
import oss2
from itertools import islice


class OssTools:
    def __init__(self, config):
        self.config = config
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        self.bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)

    def list_objects(self):
        try:
            objects = list(islice(oss2.ObjectIterator(self.bucket), 10))
            for obj in objects:
                print(obj.key)
        except oss2.exceptions.OssError as e:
            print(f"Failed to list objects: {e}")

    def create_bucket(self):
        try:
            self.bucket.create_bucket(oss2.models.BUCKET_ACL_PRIVATE)
            print("Bucket created successfully")
        except oss2.exceptions.OssError as e:
            print(f"Failed to create bucket: {e}")

    def upload_file(self, object_name, file_path):
        try:
            with open(file_path, 'rb') as file:
                result = self.bucket.put_object(object_name, file)
            print(f"File uploaded successfully, status code: {result.status}")
        except oss2.exceptions.OssError as e:
            print(f"Failed to upload file: {e}")

    # 下载文件
    def download_file(self, object_name, file_path):
        try:
            self.bucket.get_object_to_file(object_name, file_path)
            print("download success")
        except Exception as e:
            print(f'error, msg: {e}')

    def delete_objects_all(self):
        try:
            objects = list(islice(oss2.ObjectIterator(self.bucket), 100))
            if objects:
                for obj in objects:
                    self.bucket.delete_object(obj.key)
                    print(f"Deleted object: {obj.key}")
            else:
                print("No objects to delete")
        except oss2.exceptions.OssError as e:
            print(f"Failed to delete objects: {e}")

    def delete_objects(self, obj_name):
        try:
            self.bucket.delete_object(obj_name)
            print(f"Deleted object: {obj_name}")
        except oss2.exceptions.OssError as e:
            print(f"Failed to delete objects: {e}")


# 全局变量
OSS_CONFIG = {
    "access_key_id": OSS_ACCESS_KEY_ID,
    "access_key_secret": OSS_ACCESS_KEY_SECRET,
    "endpoint": OSS_ENDPOINT,
    "bucket_name": OSS_BUCKET_NAME
}

oss_handler = OssTools(OSS_CONFIG)


def test():
    # 上传文件
    # file_path = '../data/product.json'
    # file_name = 'test/product.json'
    # file_path = '../data/product_list.json'
    # file_name = 'test/id_9cf4_product_list.json'
    # oss_handler.upload_file(file_name, file_path)
    # oss_handler.list_objects()

    # 下载文件
    # download_path = '../data/download-test.json'
    # file_name = 'test/product.json'
    browser_url = "https://yj-ai4science-aopu.oss-cn-shanghai.aliyuncs.com/chat/non_standard/id_3cg6/12060108901/UP25.580.193 PMT保护钣金.PDF"
    print(browser_url[56:])
    download_path = "data/" + browser_url.split("/")[-1]
    oss_handler.download_file(browser_url[56:], download_path)

    # download_path = '../data/product/id_3cg6/8/POM/UP25.580.169 传动轴承.PDF'
    # file_name = 'chat/non_standard/id_3cg6/12060602601/UP25.580.169 传动轴承.PDF'
    # oss_handler.download_file(file_name, download_path)


if __name__ == "__main__":
    test()

