#!/usr/bin/env python3
"""
系统设置检查脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_dependencies():
    """检查依赖包"""
    print("=== 检查依赖包 ===")
    
    required_packages = [
        "fastapi", "uvicorn", "sqlalchemy", "pymysql", 
        "python-jose", "passlib", "pydantic", "aiohttp"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True


def check_database():
    """检查数据库连接"""
    print("\n=== 检查数据库连接 ===")
    
    try:
        from app.database import check_database_connection
        if check_database_connection():
            print("✅ 数据库连接正常")
            return True
        else:
            print("❌ 数据库连接失败")
            return False
    except Exception as e:
        print(f"❌ 数据库检查异常: {str(e)}")
        return False


def check_config():
    """检查配置文件"""
    print("\n=== 检查配置文件 ===")
    
    try:
        from config.auth_config import JWT_SECRET_KEY, API_V1_PREFIX
        from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
        
        print("✅ 认证配置正常")
        print("✅ LLM配置正常")
        return True
    except Exception as e:
        print(f"❌ 配置检查异常: {str(e)}")
        return False


def check_models():
    """检查模型定义"""
    print("\n=== 检查模型定义 ===")
    
    try:
        from app.models.user import SysUser, UserResponse, Token
        from app.core.auth import auth_manager
        
        print("✅ 用户模型正常")
        print("✅ 认证管理器正常")
        return True
    except Exception as e:
        print(f"❌ 模型检查异常: {str(e)}")
        return False


def main():
    """主检查函数"""
    print("🔍 系统设置检查开始...\n")
    
    checks = [
        ("依赖包", check_dependencies),
        ("配置文件", check_config),
        ("模型定义", check_models),
        ("数据库连接", check_database),
    ]
    
    all_passed = True
    
    for name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {name}检查异常: {str(e)}")
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有检查通过！系统可以正常启动")
        print("\n下一步:")
        print("1. 运行 'python init_db.py' 初始化数据库用户")
        print("2. 运行 'python run_server.py' 启动服务")
        print("3. 运行 'python test_api.py' 测试API")
    else:
        print("❌ 部分检查失败，请修复后再启动系统")


if __name__ == "__main__":
    main() 