#!/usr/bin/env python3
"""
测试所有Agent的综合示例

这个文件展示了如何：
1. 创建 orchestrator 实例
2. 注册所有三个 agent：supplier_capability_agent、info_response_agent、quote_request_agent
3. 测试完整的对话流程
"""

import asyncio
import json
from app.core.orchestrator import create_orchestrator
from app.agents.supplier_capability_agent import SupplierCapabilityAgent
from app.agents.info_response_agent import InfoResponseAgent
from app.agents.quote_request_agent import QuoteRequestAgent
from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
from config.db_config import REDIS_CONFIG
from config.llm_config import DEFAULT_MODEL


async def main():
    """主函数：演示如何注册和使用所有三个 agent"""
    
    # 1. 创建 orchestrator 配置
    config = {
        "use_redis": True,  # 可以设置为 True 启用 Redis
        "redis": REDIS_CONFIG,
        "llm": {
            "base_url": OPENAI_BASE_URL,
            "api_key": OPENAI_API_KEY,
            # "default_model": "deepseek/deepseek-chat-v3-0324"
            # "default_model": "gpt-4.1-2025-04-14"
            "default_model": DEFAULT_MODEL
        }
    }
    
    # 2. 创建 orchestrator 实例
    print("创建 orchestrator...")
    orchestrator = create_orchestrator(config)
    
    # 3. 创建所有 agent 实例
    print("创建所有 agent...")
    supplier_agent = SupplierCapabilityAgent()
    info_agent = InfoResponseAgent()
    quote_agent = QuoteRequestAgent()
    
    # 4. 将所有 agent 注册到 orchestrator
    print("注册所有 agent 到 orchestrator...")
    orchestrator.register_agent(supplier_agent)
    orchestrator.register_agent(info_agent)
    orchestrator.register_agent(quote_agent)
    
    # 5. 验证注册成功
    print(f"已注册的 agents: {orchestrator.list_agents()}")
    
    # 6. 创建测试会话
    print("\n创建测试会话...")
    session_id = await orchestrator.create_session(inquiry_data={"sid": "AOPU_1", "uid": "user001"})
    print(f"会话ID: {session_id}")
    
    # 7. 测试完整的对话流程
    test_scenarios = [
        {
            "name": "场景1：会话开始 - 应该路由到 supplier_capability_agent",
            "message": "你好",
            "expected_agent": "supplier_capability_agent"
        },
        {
            "name": "场景2：供应商询问公司信息 - 应该路由到 info_response_agent",
            "message": "请问贵公司在哪里？主要做什么业务的？",
            "expected_agent": "info_response_agent"
        },
        {
            "name": "场景3：供应商询问技术规格 - 应该路由到 info_response_agent",
            "message": "你们要什么尺寸的，有什么图纸之类的吗？",
            "expected_agent": "info_response_agent"
        },
        {
            "name": "场景4：供应商提供报价 - 应该路由到 quote_request_agent",
            "message": "我们已经准备好报价单了，单价是150元/件，最小起订量1000件，交期15个工作日。",
            "expected_agent": "quote_request_agent"
        },
        {
            "name": "场景5：供应商回复报价进展 - 应该路由到 quote_request_agent",
            "message": "报价单我们正在准备，大概明天能发给您。",
            "expected_agent": "quote_request_agent"
        },
        {
            "name": "场景6：非相关话题 - 应该转人工",
            "message": "今天天气怎么样？",
            "expected_agent": "human_takeover"
        }
    ]
    
    # 测试上下文信息
    request_context = {
        # "supplier_id": "test_supplier_001",
        # "component_code": ["SS304-PLATE-001"],
        "supplier_info": {
            "company_name": "大连鸿升机械有限公司",
            # "contact_person": "张经理"
        },
        "sid": "AOPU_1",
        "uid": "user001"
    }
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n=== {scenario['name']} ===")
        print(f"用户消息: {scenario['message']}")
        print(f"预期Agent: {scenario['expected_agent']}")
        
        # 处理消息
        result = await orchestrator.handle_message(
            session_id=session_id,
            user_message=scenario['message'],
            request_context=request_context
        )
        
        print(f"处理成功: {result.success}")
        if result.success:
            actual_agent = result.data.get('agent_type', 'unknown')
            response = result.data.get('response', 'N/A')
            need_human = result.data.get('need_human', False)
            
            print(f"实际使用的Agent: {actual_agent}")
            print(f"需要人工接管: {need_human}")
            print(f"响应: {response}")
            
            # 验证路由是否正确
            if scenario['expected_agent'] == 'human_takeover':
                if need_human:
                    print("✅ 路由正确：成功转人工")
                else:
                    print("❌ 路由错误：应该转人工但没有")
            else:
                if actual_agent == scenario['expected_agent']:
                    print("✅ 路由正确")
                else:
                    print(f"❌ 路由错误：期望 {scenario['expected_agent']}，实际 {actual_agent}")
        else:
            print(f"❌ 处理失败: {result.error}")
        
        # 添加延迟，模拟真实对话
        await asyncio.sleep(0.5)
    
    # 8. 测试会话状态管理
    # print("\n=== 测试会话状态管理 ===")
    # session_context = await orchestrator.get_session(session_id)
    # if session_context:
    #     print(f"会话状态: {session_context.status}")
    #     print(f"消息历史数量: {len(session_context.message_history)}")
    #     print(f"当前Agent: {session_context.current_agent}")
    
    # 9. 显示系统状态
    # print("\n=== 系统状态 ===")
    # status = orchestrator.get_system_status()
    # print(json.dumps(status, indent=2, ensure_ascii=False))
    
    # 10. 测试单独的Agent功能
    # print("\n=== 测试单独Agent功能 ===")
    
    # 测试 supplier_capability_agent
    # print("\n--- 测试 SupplierCapabilityAgent ---")
    # supplier_test_data = {
    #     "message": "你好，我想了解一下贵公司的基本情况",
    #     "message_history": [],
    #     "supplier_info": {
    #         "company_name": "测试供应商公司",
    #         "contact_person": "李经理"
    #     },
    #     "component_code": ["TEST_COMPONENT_001"]
    # }
    
    # supplier_result = await supplier_agent.process("test_session_supplier", supplier_test_data)
    # print(f"SupplierCapabilityAgent 结果: {supplier_result.success}")
    # print(supplier_result.to_dict())
    # if supplier_result.success:
    #     print(f"响应: {supplier_result.data.get('response')}")
    
    # 测试 info_response_agent
    # print("\n--- 测试 InfoResponseAgent ---")
    # info_test_data = {
    #     "message": "请问贵公司在哪里？主要做什么业务的？",
    #     "message_history": [
    #         {"role": "user", "content": "你好"},
    #         {"role": "assistant", "content": "您好，我们需要采购304不锈钢板材，想了解贵司是否有相应的供货能力？"}
    #     ],
    #     "supplier_info": {
    #         "company_name": "大连鸿升机械有限公司",
    #     },
    #     "component_code": ["SS304-PLATE-001"]
    # }
    
    # info_result = await info_agent.process("test_session_info", info_test_data)
    # print(f"InfoResponseAgent 结果: {info_result.success}")
    # if info_result.success:
    #     print(f"响应: {info_result.data.get('response')}")
    #     print(f"信息类型: {info_result.data.get('info_type')}")
    
    # 测试 quote_request_agent
    # print("\n--- 测试 QuoteRequestAgent ---")
    # quote_test_data = {
    #     "message": "报价单我们正在准备，大概明天能发给您。"
    # }
    
    # quote_result = await quote_agent.process("test_session_quote", quote_test_data)
    # print(f"QuoteRequestAgent 结果: {quote_result.success}")
    # if quote_result.success:
    #     print(f"响应: {quote_result.data.get('response')}")
    #     print(f"需要人工: {quote_result.data.get('need_human')}")
    
    print("\n🎉 所有测试完成！")
    session_context = await orchestrator.get_session(session_id)
    print(f"会话状态: {session_context.status}")
    print(f"消息历史数量: {len(session_context.message_history)}")
    print(f"当前Agent: {session_context.current_agent}")
    print(json.dumps(session_context.to_dict(), indent=2, ensure_ascii=False))


async def delete_sessions():
     # 1. 创建 orchestrator 配置
    config = {
        "use_redis": True,  # 可以设置为 True 启用 Redis
        "redis": REDIS_CONFIG,
        "llm": {
            "base_url": OPENAI_BASE_URL,
            "api_key": OPENAI_API_KEY,
            # "default_model": "deepseek/deepseek-chat-v3-0324"
            # "default_model": "gpt-4.1-2025-04-14"
            "default_model": DEFAULT_MODEL
        }
    }
    
    # 2. 创建 orchestrator 实例
    print("创建 orchestrator...")
    orchestrator = create_orchestrator(config)
    
    # 3. 创建所有 agent 实例
    print("创建所有 agent...")
    supplier_agent = SupplierCapabilityAgent()
    info_agent = InfoResponseAgent()
    quote_agent = QuoteRequestAgent()
    
    # 4. 将所有 agent 注册到 orchestrator
    print("注册所有 agent 到 orchestrator...")
    orchestrator.register_agent(supplier_agent)
    orchestrator.register_agent(info_agent)
    orchestrator.register_agent(quote_agent)
    
    # 5. 验证注册成功
    print(f"已注册的 agents: {orchestrator.list_agents()}")

    # example_content = await orchestrator.get_session("AOPU_1_user001")
    # print(f"会话上下文: {example_content}")

    session_ids = await orchestrator.get_all_session_ids()
    print(f"会话ID: {session_ids}")
    for session_id in session_ids:
        await orchestrator.delete_session(session_id)
    print(f"删除会话: {session_ids}")


if __name__ == "__main__":
    # 运行测试
    # asyncio.run(main())

    asyncio.run(delete_sessions())
