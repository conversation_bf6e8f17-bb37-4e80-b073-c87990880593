#!/usr/bin/env python3
"""
API测试脚本
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8650"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
        self.use_auth = True  # 新增：控制是否使用认证
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def set_auth_mode(self, use_auth: bool):
        """设置认证模式"""
        self.use_auth = use_auth
        print(f"🔧 认证模式设置为: {'启用' if use_auth else '禁用'}")
    
    async def test_health(self) -> Dict[str, Any]:
        """测试健康检查"""
        url = f"{self.base_url}/health"
        
        try:
            async with self.session.get(url) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 健康检查成功: {result['status']}")
                else:
                    print(f"❌ 健康检查失败: {result}")
                return result
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return {"error": str(e)}
    
    async def debug_routes(self) -> Dict[str, Any]:
        """调试：查看所有路由"""
        url = f"{self.base_url}/debug/routes"
        
        try:
            async with self.session.get(url) as response:
                result = await response.json()
                if response.status == 200:
                    print("✅ 路由调试信息:")
                    for route in result.get("routes", []):
                        print(f"   {route['methods']} {route['path']}")
                else:
                    print(f"❌ 获取路由信息失败: {result}")
                return result
        except Exception as e:
            print(f"❌ 路由调试异常: {str(e)}")
            return {"error": str(e)}
    
    async def login(self, user_name: str, password_: str) -> Dict[str, Any]:
        """登录获取token"""
        url = f"{self.base_url}/api/v1/auth/login"
        data = {
            "user_name": user_name,
            "password_": password_
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                result = await response.json()
                if response.status == 200:
                    self.access_token = result["access_token"]
                    print(f"✅ 登录成功，获取到token: {self.access_token[:20]}...")
                else:
                    print(f"❌ 登录失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return {"error": str(e)}
    
    def get_headers(self, force_auth: bool = None) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        
        # 如果force_auth指定了，使用指定值；否则使用self.use_auth
        should_use_auth = force_auth if force_auth is not None else self.use_auth
        
        if should_use_auth:
            if not self.access_token:
                raise ValueError("请先登录获取token")
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        return headers
    
    async def get_user_info(self) -> Dict[str, Any]:
        """获取当前用户信息"""
        url = f"{self.base_url}/api/v1/auth/me"
        
        try:
            async with self.session.get(url, headers=self.get_headers(force_auth=True)) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取用户信息成功: {result['user_name']}")
                else:
                    print(f"❌ 获取用户信息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取用户信息异常: {str(e)}")
            return {"error": str(e)}
    
    async def send_inquiry(self, message: str, session_id: str = None) -> Dict[str, Any]:
        """发送询价消息"""
        url = f"{self.base_url}/api/v1/inquiry/chat"
        data = {
            "message": message,
            # "session_id": session_id,
            "sid": "AOPU_lhh",
            "uid": "123467",
            "supplier_info": {
                "company_name": "广东兴发铝业有限公司"
            }
        }
        
        try:
            async with self.session.post(url, json=data, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 发送询价消息成功")
                    print(f"   消息: {message}")
                    print(f"   会话ID: {result['session_id']}")
                    print(f"   响应: {result['response']}")
                    print(f"   Agent类型: {result.get('agent_type')}")
                    print(f"   需要人工: {result.get('need_human')}")
                    print(f"   响应类型: {result.get('response_type')}")
                    print(f"   物料规格: {result.get('material_spec')}")
                else:
                    print(f"❌ 发送询价消息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 发送询价消息异常: {str(e)}")
            return {"error": str(e)}
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        url = f"{self.base_url}/api/v1/inquiry/session/{session_id}/status"
        
        try:
            async with self.session.get(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取会话状态成功")
                    print(f"   会话ID: {result['session_id']}")
                    print(f"   状态: {result['status']}")
                    print(f"   创建时间: {result['created_at']}")
                    print(f"   更新时间: {result['updated_at']}")
                    print(f"   消息数量: {result['message_count']}")
                    print(f"   当前Agent: {result.get('current_agent')}")
                else:
                    print(f"❌ 获取会话状态失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取会话状态异常: {str(e)}")
            return {"error": str(e)}


async def test_with_auth():
    """测试带认证的API"""
    async with APITester(base_url="http://localhost:8650") as tester:
        print("=== 带JWT认证的API测试 ===\n")
        
        # 设置认证模式
        tester.set_auth_mode(True)
        
        # 测试登录
        print("1. 测试登录...")
        result = await tester.login("lhh-aopu", "bluemen.123")
        if "access_token" not in result:
            print("❌ 登录失败，跳过认证测试")
            return
        
        # 测试获取用户信息
        print("\n2. 测试获取用户信息...")
        await tester.get_user_info()
        
        # 测试发送询价消息
        print("\n3. 测试发送询价消息（带认证）...")
        result = await tester.send_inquiry("你好，我是认证用户")
        if "session_id" in result:
            session_id = result["session_id"]
            print(f"   获取到会话ID: {session_id}")
            
            # 测试获取会话状态
            print("\n4. 测试获取会话状态（带认证）...")
            await tester.get_session_status(session_id)


async def test_without_auth():
    """测试无认证的API"""
    async with APITester(base_url="http://localhost:8650") as tester:
        print("\n=== 无JWT认证的API测试 ===\n")
        
        # 设置无认证模式
        tester.set_auth_mode(False)
        
        # 测试发送询价消息
        print("1. 测试发送询价消息（无认证）...")
        result = await tester.send_inquiry("你好，我是匿名用户")
        if "session_id" in result:
            session_id = result["session_id"]
            print(f"   获取到会话ID: {session_id}")
            
            # 测试会话连续性
            print("\n2. 测试会话连续性（无认证）...")
            result2 = await tester.send_inquiry("请问贵公司在哪里？", session_id)
            
            # 测试获取会话状态
            print("\n3. 测试获取会话状态（无认证）...")
            await tester.get_session_status(session_id)


async def test_api():
    """测试API功能"""
    async with APITester(base_url="http://localhost:8650") as tester:
        print("=== 供应商询价系统API测试 ===\n")
        
        # 0. 测试健康检查
        print("0. 测试健康检查...")
        await tester.test_health()
        print()
        
        # 0.5. 调试路由信息
        print("0.5. 调试路由信息...")
        await tester.debug_routes()
        print()
    
    # 测试带认证的场景
    # await test_with_auth()
    
    # 测试无认证的场景
    await test_without_auth()
    
    print("\n🎉 所有API测试完成！")
    print("\n💡 提示：")
    print("   - 带认证的API会使用JWT token")
    print("   - 无认证的API会以匿名用户身份运行")
    print("   - 两种模式下的会话都会正常保存到Redis")


if __name__ == "__main__":
    asyncio.run(test_api())