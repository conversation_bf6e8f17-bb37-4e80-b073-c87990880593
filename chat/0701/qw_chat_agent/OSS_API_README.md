# OSS服务API使用说明

## 概述

OSS服务API提供了根据供应商名称下载对应物料文件的功能，支持Excel清单导出和文件压缩下载。所有API都需要JWT认证。

## API端点

### 1. 获取供应商物料清单

**GET** `/api/v1/oss/list-supplier-materials/{supplier_name}`

获取指定供应商的物料信息清单（不下载文件）。

**参数：**
- `supplier_name` (路径参数): 供应商名称
- `need_num` (查询参数，可选): 需求数量倍数，默认10

**响应：**
```json
{
  "success": true,
  "message": "找到 5 个物料项目，共 25 个文件",
  "supplier_name": "深圳市亚美三兄吸塑有限公司",
  "total_materials": 5,
  "total_files": 25,
  "materials": [...]
}
```

### 2. 下载供应商物料Excel清单

**POST** `/api/v1/oss/download-excel-only`

仅下载供应商物料的Excel清单文件。

**请求体：**
```json
{
  "supplier_name": "深圳市亚美三兄吸塑有限公司",
  "need_num": 10
}
```

**响应：**
- 成功时返回Excel文件下载
- 失败时返回错误信息

### 3. 下载供应商物料文件（压缩包）

**POST** `/api/v1/oss/download-supplier-materials`

根据供应商名称下载对应物料文件并返回压缩包。

**请求体：**
```json
{
  "supplier_name": "深圳市亚美三兄吸塑有限公司",
  "need_num": 10,
  "compress_after_download": true,
  "delete_original_after_compress": true
}
```

**参数说明：**
- `supplier_name`: 供应商名称（必填）
- `need_num`: 需求数量倍数，范围1-100，默认10
- `compress_after_download`: 是否在下载后压缩文件，默认true
- `delete_original_after_compress`: 压缩后是否删除原文件夹，默认true

**响应：**
- 成功时返回ZIP压缩文件下载
- 失败时返回错误信息JSON

## 认证

所有API都需要JWT认证，请在请求头中包含：

```
Authorization: Bearer <your_jwt_token>
```

## 使用示例

### Python示例

```python
import requests

# 获取认证token
login_response = requests.post("http://localhost:8000/api/v1/auth/login", json={
    "username": "your_username",
    "password": "your_password"
})
token = login_response.json()["access_token"]

headers = {"Authorization": f"Bearer {token}"}

# 1. 获取物料清单
response = requests.get(
    "http://localhost:8000/api/v1/oss/list-supplier-materials/深圳市亚美三兄吸塑有限公司",
    headers=headers,
    params={"need_num": 5}
)
print(response.json())

# 2. 下载Excel清单
response = requests.post(
    "http://localhost:8000/api/v1/oss/download-excel-only",
    headers=headers,
    json={
        "supplier_name": "深圳市亚美三兄吸塑有限公司",
        "need_num": 5
    }
)
with open("物料清单.xlsx", "wb") as f:
    f.write(response.content)

# 3. 下载物料文件压缩包
response = requests.post(
    "http://localhost:8000/api/v1/oss/download-supplier-materials",
    headers=headers,
    json={
        "supplier_name": "深圳市亚美三兄吸塑有限公司",
        "need_num": 3,
        "compress_after_download": True,
        "delete_original_after_compress": True
    },
    timeout=300  # 5分钟超时
)
with open("物料文件.zip", "wb") as f:
    f.write(response.content)
```

### JavaScript示例

```javascript
// 获取认证token
const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        username: 'your_username',
        password: 'your_password'
    })
});
const { access_token } = await loginResponse.json();

const headers = {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
};

// 获取物料清单
const listResponse = await fetch(
    'http://localhost:8000/api/v1/oss/list-supplier-materials/深圳市亚美三兄吸塑有限公司?need_num=5',
    { headers }
);
const materials = await listResponse.json();
console.log(materials);

// 下载物料文件压缩包
const downloadResponse = await fetch(
    'http://localhost:8000/api/v1/oss/download-supplier-materials',
    {
        method: 'POST',
        headers,
        body: JSON.stringify({
            supplier_name: '深圳市亚美三兄吸塑有限公司',
            need_num: 3,
            compress_after_download: true,
            delete_original_after_compress: true
        })
    }
);

if (downloadResponse.ok) {
    const blob = await downloadResponse.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '物料文件.zip';
    a.click();
}
```

## 错误处理

API可能返回以下错误状态码：

- `400 Bad Request`: 请求参数错误或供应商不存在
- `401 Unauthorized`: 认证失败或token无效
- `404 Not Found`: 供应商物料信息不存在
- `500 Internal Server Error`: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 注意事项

1. **文件大小限制**: 下载的文件可能较大，请设置合适的超时时间
2. **并发限制**: 避免同时发起过多下载请求
3. **临时文件**: 服务器会自动清理临时文件，无需担心存储空间
4. **网络稳定性**: 大文件下载建议在稳定的网络环境下进行
5. **供应商名称**: 确保供应商名称准确，区分大小写

## 测试

使用提供的测试脚本 `test_oss_api.py` 来测试API功能：

```bash
python test_oss_api.py
```

测试脚本会自动测试所有API端点并保存下载的文件。 