#!/usr/bin/env python3
"""
供应商管理API测试脚本
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional


class SupplierAPITester:
    """供应商API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8650"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
        self.use_auth = False  # 默认不使用认证
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def set_auth_mode(self, use_auth: bool):
        """设置认证模式"""
        self.use_auth = use_auth
        print(f"🔧 认证模式设置为: {'启用' if use_auth else '禁用'}")
    
    async def login(self, user_name: str, password_: str) -> Dict[str, Any]:
        """登录获取token"""
        url = f"{self.base_url}/api/v1/auth/login"
        data = {
            "user_name": user_name,
            "password_": password_
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                result = await response.json()
                if response.status == 200:
                    self.access_token = result["access_token"]
                    print(f"✅ 登录成功，获取到token: {self.access_token[:20]}...")
                else:
                    print(f"❌ 登录失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return {"error": str(e)}
    
    def get_headers(self, force_auth: bool = None) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        
        # 如果force_auth指定了，使用指定值；否则使用self.use_auth
        should_use_auth = force_auth if force_auth is not None else self.use_auth
        
        if should_use_auth:
            if not self.access_token:
                raise ValueError("请先登录获取token")
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        return headers
    
    async def get_suppliers_by_status(self, chat_status: str = "ready", limit: int = 10) -> Dict[str, Any]:
        """获取指定状态的供应商列表"""
        url = f"{self.base_url}/api/v1/supplier/list"
        params = {
            "chat_status": chat_status,
            "limit": limit,
            "offset": 0
        }
        
        try:
            async with self.session.get(url, params=params, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取供应商列表成功")
                    print(f"   状态: {chat_status}")
                    print(f"   数量: {result['total']}")
                    for supplier in result['data'][:3]:  # 只显示前3个
                        print(f"   - {supplier['supplier_name']}: {supplier['phone']}")
                    if result['total'] > 3:
                        print(f"   ... 还有 {result['total'] - 3} 个供应商")
                else:
                    print(f"❌ 获取供应商列表失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取供应商列表异常: {str(e)}")
            return {"error": str(e)}
    
    async def get_supplier_contact(self, supplier_name: str) -> Dict[str, Any]:
        """获取供应商联系信息"""
        url = f"{self.base_url}/api/v1/supplier/contact/{supplier_name}"
        
        try:
            async with self.session.get(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    if result['success']:
                        data = result['data']
                        print(f"✅ 获取供应商联系信息成功")
                        print(f"   供应商: {data['supplier_name']}")
                        print(f"   电话: {data['phone']}")
                        print(f"   联系人: {data['contact_person']}")
                        print(f"   地区: {data['region']}")
                    else:
                        print(f"⚠️ {result['message']}")
                else:
                    print(f"❌ 获取供应商联系信息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取供应商联系信息异常: {str(e)}")
            return {"error": str(e)}
    
    async def update_supplier_status(self, supplier_name: str, chat_status: str) -> Dict[str, Any]:
        """更新供应商聊天状态"""
        url = f"{self.base_url}/api/v1/supplier/status/update"
        data = {
            "supplier_name": supplier_name,
            "chat_status": chat_status
        }
        
        try:
            async with self.session.put(url, json=data, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    if result['success']:
                        print(f"✅ 更新供应商状态成功")
                        print(f"   供应商: {result['supplier_name']}")
                        print(f"   新状态: {result['new_status']}")
                        print(f"   消息: {result['message']}")
                    else:
                        print(f"⚠️ {result['message']}")
                else:
                    print(f"❌ 更新供应商状态失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 更新供应商状态异常: {str(e)}")
            return {"error": str(e)}
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取供应商聊天状态统计"""
        url = f"{self.base_url}/api/v1/supplier/statistics"
        
        try:
            async with self.session.get(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取统计信息成功")
                    print(f"   统计数据:")
                    for status, count in result['data'].items():
                        print(f"   - {status}: {count}个")
                else:
                    print(f"❌ 获取统计信息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取统计信息异常: {str(e)}")
            return {"error": str(e)}
    
    async def search_suppliers(self, supplier_name: str, exact_match: bool = False) -> Dict[str, Any]:
        """搜索供应商"""
        url = f"{self.base_url}/api/v1/supplier/search"
        params = {
            "supplier_name": supplier_name,
            "exact_match": exact_match
        }
        
        try:
            async with self.session.get(url, params=params, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 搜索供应商成功")
                    print(f"   搜索词: {supplier_name}")
                    print(f"   匹配模式: {'精确匹配' if exact_match else '模糊匹配'}")
                    print(f"   找到: {result['total']}个")
                    for supplier in result['data'][:3]:  # 只显示前3个
                        print(f"   - {supplier['supplier_name']}: {supplier['chat_status']}")
                else:
                    print(f"❌ 搜索供应商失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 搜索供应商异常: {str(e)}")
            return {"error": str(e)}


async def test_supplier_api_without_auth():
    """测试无认证的供应商API"""
    async with SupplierAPITester(base_url="http://localhost:8650") as tester:
    # async with SupplierAPITester(base_url="http://*************:8650") as tester:
        print("=== 供应商管理API测试（无认证） ===\n")
        
        # 设置无认证模式
        tester.set_auth_mode(False)
        
        # 1. 获取ready状态的供应商列表
        print("1. 测试获取ready状态的供应商列表...")
        result = await tester.get_suppliers_by_status("ready", limit=5)
        suppliers = result.get('data', [])

        print(suppliers)
        
        if suppliers:
            # 获取第一个供应商的详细信息
            first_supplier = suppliers[0]
            supplier_name = first_supplier['supplier_name']
            
            print(f"\n2. 测试获取供应商联系信息...")
            await tester.get_supplier_contact(supplier_name)
            
            print(f"\n3. 测试更新供应商状态...")
            await tester.update_supplier_status(supplier_name, "chatting")
            
            print(f"\n4. 验证状态更新...")
            await tester.get_supplier_contact(supplier_name)
            
            # print(f"\n5. 恢复供应商状态...")
            # await tester.update_supplier_status(supplier_name, "ready")
        
        print(f"\n6. 测试获取统计信息...")
        await tester.get_statistics()
        
        # print(f"\n7. 测试搜索供应商...")
        # await tester.search_suppliers("华恒", exact_match=False)


async def test_supplier_api_with_auth():
    """测试带认证的供应商API"""
    async with SupplierAPITester(base_url="http://localhost:8650") as tester:
        print("\n=== 供应商管理API测试（带认证） ===\n")
        
        # 设置认证模式
        tester.set_auth_mode(True)
        
        # 登录
        print("1. 测试登录...")
        result = await tester.login("lhh-aopu", "bluemen.123")
        if "access_token" not in result:
            print("❌ 登录失败，跳过认证测试")
            return
        
        # 其他测试与无认证版本相同
        print("\n2. 测试获取ready状态的供应商列表（带认证）...")
        await tester.get_suppliers_by_status("ready", limit=3)


async def test_all():
    """运行所有测试"""
    print("🚀 开始供应商管理API测试\n")
    
    # 测试无认证API
    await test_supplier_api_without_auth()
    
    # 测试带认证API（可选）
    # await test_supplier_api_with_auth()
    
    print("\n🎉 所有供应商API测试完成！")
    print("\n💡 提示：")
    print("   - 这些API支持JWT认证和匿名访问")
    print("   - 可以获取指定状态的供应商联系信息")
    print("   - 可以更新供应商的聊天状态")
    print("   - 提供搜索和统计功能")


if __name__ == "__main__":
    asyncio.run(test_all()) 