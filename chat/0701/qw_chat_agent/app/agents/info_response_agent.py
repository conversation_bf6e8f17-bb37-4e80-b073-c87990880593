"""信息回复Agent

负责回答供应商的各种提问，包括：
- 公司基本信息
- 物料技术规格和图纸
- 采购需求详情
- 商务条件说明
- 其他相关信息
"""

import json
from typing import Dict, Any, List, Union
from datetime import datetime

from app.core.base_agent import (
    BaseAgent, AgentResult, AgentMessage, MessageRole, AgentConfig,
    create_success_result, create_error_result
)
from app.core.llm_client import LLMClientFactory
from app.utils.get_material_info import get_supplier_material_info
from app.utils.tool_function import extract_largest_json
from app.utils.info_type_tools import get_material_spec_info
from config.llm_config import DEFAULT_MODEL


class InfoResponseAgent(BaseAgent):
    """信息回复Agent"""
    
    def __init__(self, config: AgentConfig = None, llm_client=None):
        if config is None:
            config = AgentConfig(
                name="info_response_agent",
                description="回答供应商提问的Agent，提供公司信息、物料规格等",
                system_prompt=self._get_system_prompt(),
                # model="deepseek/deepseek-chat-v3-0324",
                model=DEFAULT_MODEL,
            )
        super().__init__(config)

        # 注入LLM客户端
        self.llm_client = llm_client
        
        # 初始化LLM客户端
        if not self.llm_client:
            try:
                from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
                self.llm_client = LLMClientFactory.create_openai_client(
                    base_url=OPENAI_BASE_URL,
                    api_key=OPENAI_API_KEY,
                    default_model=self.config.model
                )
            except ImportError:
                # 如果没有配置文件，使用环境变量
                self.llm_client = LLMClientFactory.create_from_env()
        
        self.user_prompt = self.get_user_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一名经验丰富的采购经理，代表采购方与供应商沟通。你们是主动寻求合作的一方，目前正在平台上与供应商对话。

核心原则：
1. 你是买方，主要回答供应商对采购需求的询问
2. 记住你们是主动寻求合作的一方，态度友好但专业
3. 不要反向询问供应商的公司信息或产品能力
4. 回答简练直接，提供必要的项目信息
5. 涉及敏感信息时谨慎处理，必要时转人工

沟通风格：
- 已在对话中，回复要符合当前聊天情境
- 主要回答问題，不主动提问
- 重点信息直接说明，无需过多铺垫
- 不要引导到其他联系渠道（已经在聊天了）
- 不使用"感谢关注"等供应商话术
- 关于个人身份询问：简明扼要介绍自己，避免提及"后续对接"等已处于对话中的表述

信息类型：company_info | project_info | material_spec | personal_contact | other

输出JSON格式：
{
    "message": "自然简练的回复内容",
    "reason": "回复策略说明",
    "info_type": "信息类型",
    "need_human": false
}
"""

    def get_user_prompt(self) -> str:
        """获取用户提示词"""
        return """
当前问题：{user_message}

请根据对话历史和当前问题，提供自然、专业的回复：

关键要求：
1. 仔细查看对话历史，避免重复询问已讨论过的内容
2. 回复简练直接，突出关键信息
3. 根据上下文灵活调整回复策略
4. 敏感信息（商务条款、合作方信息）需转人工处理
5. 涉及material_spec时，直接表示"我们有相关图纸，等会发给您"即可
6. 已在对话中，不要引导到其他联系渠道
7. 个人身份相关回复（personal_contact类型）要简明直接，避免提及"后续对接沟通"等表述

特殊处理规则：
- 关于数量询问：
  * 如果对话历史中已发送过图纸资料，回复"具体数量信息在已发送的图纸资料中有详细说明"
  * 如果未发送图纸，可以提供简要数量信息，并表示后续发送详细图纸
  * 如果对方反复询问相同问题，设置need_human=true转人工

- 关于技术图纸/资料询问（重要）：
  * 如果已发送图纸后，供应商连续2次以上询问图纸内容相关问题（如"图纸不清晰"、"看不出尺寸"、"标注不清楚"等），设置need_human=true转人工
  * 如果对话历史显示已发送图纸，但供应商连续反映没有收到图纸（如"没看到图纸"、"没收到文件"、"图纸在哪里"等），设置need_human=true转人工
  * 判断标准：检测对话历史中是否存在多次关于图纸清晰度、尺寸标注、技术细节的询问，或多次表示未收到图纸文件
  * 关键词包括但不限于：清晰、尺寸、标注、看不出来、不清楚、模糊、看不清、没看到、没收到、在哪里等

输出JSON：
{{
    "message": "简练自然的回复",
    "reason": "回复策略",
    "info_type": "信息类型", 
    "need_human": false
}}
"""
    
    def get_supported_types(self) -> List[str]:
        """返回支持的请求类型"""
        return [
            "info_response",
            "company_info_request",
            "material_spec_inquiry",
            "quality_requirement_question",
            "business_terms_inquiry",
            "technical_drawing_request"
        ]
    
    async def process(self, session_id: str, data: Dict[str, Any]) -> AgentResult:
        """处理信息回复请求"""
        start_time = datetime.now()
        
        try:
            # 验证输入
            if not await self.validate_input(data):
                return create_error_result("输入数据验证失败")
            
            # 存储会话上下文供post_process使用
            self._current_session_context = data.get("session_context")
            
            # 预处理
            processed_data = await self.pre_process(session_id, data)
            
            # 获取用户消息和历史上下文
            user_message = processed_data.get("message", "")
            message_history = processed_data.get("message_history", [])
            supplier_info = processed_data.get("supplier_info", {})

            supplier_name = supplier_info.get("company_name", "")
            component_code = processed_data.get("component_code", [])

            material_info = get_supplier_material_info(supplier_name, component_code)
            
            # 构建对话上下文
            messages = await self._build_conversation_context(
                user_message, message_history, supplier_info, material_info
            )
            
            # 调用LLM生成回复
            response_message = await self.call_llm(messages)

            response_json = extract_largest_json(response_message.content)

            print(f"info tyep: {response_json.get('info_type', '') if response_json else ''}")
            
            # 构建结果
            result = create_success_result(
                data={
                    "response": response_json.get("message", "提取大模型回复失败") if response_json else "提取大模型回复失败",
                    "agent_type": "info_response_agent",
                    "session_id": session_id,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "info_type": response_json.get("info_type", "") if response_json else "",
                    "need_human": response_json.get("need_human", False) if response_json else False,
                    "reason": response_json.get("reason", "") if response_json else "",
                    "supplier_name": supplier_info.get("company_name", ""),
                },
                message="信息回复处理完成"
            )
            
            # 添加响应消息到结果
            result.add_message(
                role=MessageRole.ASSISTANT,
                content=response_json.get("message", "提取大模型回复失败") if response_json else "提取大模型回复失败",
                metadata={
                    "agent": self.name,
                    "processing_time": (datetime.now() - start_time).total_seconds(),
                    "info_type": response_json.get("info_type", "general") if response_json else "general"
                }
            )
            
            # 后处理
            result = await self.post_process(session_id, result)
            
            return result
            
        except Exception as e:
            self.log_error(f"处理信息回复失败: {str(e)}")
            return create_error_result(
                error=f"处理失败: {str(e)}",
                data={"session_id": session_id}
            )
    
    async def _build_conversation_context(
        self, 
        user_message: str, 
        message_history: Union[List[Dict[str, Any]], List[AgentMessage]], 
        supplier_info: Dict[str, Any] = None,
        material_info: Dict[str, Any] = None
    ) -> List[AgentMessage]:
        """构建对话上下文"""
        messages = []
        
        # 系统提示
        system_prompt = self.config.system_prompt
        
        # 如果有供应商信息，添加到系统提示中
        if supplier_info:
            system_prompt += f"\n\n当前供应商信息：\n{json.dumps(supplier_info, ensure_ascii=False, indent=2)}"
        
        # 如果有物料信息，添加到系统提示中
        if material_info:
            system_prompt += f"\n\n相关物料信息：\n{json.dumps(material_info, ensure_ascii=False, indent=2)}"
        
        # 添加公司信息模板
        company_info_template = self._get_company_info_template()
        system_prompt += f"\n\n公司信息参考：\n{company_info_template}"
        
        messages.append(AgentMessage(
            role=MessageRole.SYSTEM,
            content=system_prompt
        ))
        
        # 添加历史消息（最近10条，避免上下文过长）
        recent_history = message_history[-10:] if len(message_history) > 10 else message_history
        
        # 检测重复问题模式和图纸发送状态
        user_questions = []
        assistant_responses = []
        has_sent_drawings = False
        drawing_issues_count = 0  # 图纸技术问题计数
        no_file_received_count = 0  # 未收到文件问题计数
        
        for msg in recent_history:
            if isinstance(msg, dict):
                role = msg.get("role", "user")
                content = msg.get("content", "")
            elif isinstance(msg, AgentMessage):
                role = msg.role
                content = msg.content
            else:
                continue
                
            if role == "user":
                user_questions.append(content)
                # 精确检测图纸技术问题
                if (("看不出" in content and ("尺寸" in content or "标注" in content or "参数" in content)) or 
                    ("图纸" in content and ("不清晰" in content or "模糊" in content or "不完整" in content)) or
                    ("看不清" in content and ("图" in content or "尺寸" in content or "细节" in content)) or
                    ("打不开" in content) or
                    ("标注不清" in content) or
                    ("规格看不懂" in content)):
                    drawing_issues_count += 1
                
                # 检测未收到图纸文件问题
                if (("没看到" in content and "图纸" in content) or
                    ("没收到" in content and ("图纸" in content or "文件" in content or "资料" in content)) or
                    ("图纸在哪" in content) or
                    ("哪里看" in content and ("图纸" in content or "文件" in content)) or
                    ("没有图纸" in content) or
                    ("图纸呢" in content)):
                    no_file_received_count += 1
                    
            elif role == "assistant":
                assistant_responses.append(content)
                # 检测是否已发送图纸资料
                if any(keyword in content for keyword in ["图纸", "资料", "发给您", "已发送", "material_spec"]):
                    has_sent_drawings = True
        
        # 添加历史消息
        for msg in recent_history:
            if isinstance(msg, dict):
                messages.append(AgentMessage(
                    role=MessageRole(msg.get("role", "user")),
                    content=msg.get("content", "")
                ))
            elif isinstance(msg, AgentMessage):
                messages.append(msg)
        
        # 添加当前用户消息
        if user_message:
            # 检测当前消息是否也是"没收到文件"问题
            current_no_file_received = False
            if (("没看到" in user_message and "图纸" in user_message) or
                ("没收到" in user_message and ("图纸" in user_message or "文件" in user_message or "资料" in user_message)) or
                ("图纸在哪" in user_message) or
                ("哪里看" in user_message and ("图纸" in user_message or "文件" in user_message)) or
                ("没有图纸" in user_message) or
                ("图纸呢" in user_message)):
                current_no_file_received = True
                no_file_received_count += 1
                
            # 构建上下文提示，包含重复问题检测和图纸状态
            context_info = ""
            if len(user_questions) > 1:
                context_info = f"\n\n对话上下文提醒：\n近期已讨论的问题包括：{'; '.join(user_questions[-3:])}\n请避免重复询问相同问题，根据已有信息灵活回复。"
            
            # 检查会话上下文中的文件发送状态
            session_context = getattr(self, '_current_session_context', None)
            supplier_name = ""
            if supplier_info:
                supplier_name = supplier_info.get("company_name", "")
            
            # 添加图纸发送状态提醒
            if has_sent_drawings or (session_context and supplier_name and session_context.has_files_sent_to_supplier(supplier_name)):
                context_info += f"\n\n重要提醒：已向供应商发送过图纸资料。如果供应商询问数量相关问题，应提醒对方查看已发送的图纸资料中的信息。避免重复发送文件。"
                
                # 检测图纸技术问题是否需要转人工
                if drawing_issues_count >= 2:
                    context_info += f"\n\n转人工提醒：供应商已连续{drawing_issues_count}次反映图纸技术问题（如清晰度、尺寸标注等），建议设置need_human=true转人工处理。"
                
                # 检测未收到文件问题是否需要转人工（包含当前消息）
                if no_file_received_count >= 2:
                    context_info += f"\n\n转人工提醒：供应商已连续{no_file_received_count}次反映没有收到图纸文件，可能存在文件传输问题，建议设置need_human=true转人工处理。"
            
            user_question = self.user_prompt.format(
                user_message=user_message
            ) + context_info
            
            messages.append(AgentMessage(
                role=MessageRole.USER,
                content=user_question
            ))
        
        return messages
    
    def _get_company_info_template(self) -> str:
        """获取公司信息模板"""
        return """
公司基本信息：
- 公司名称：上海优谦智能科技有限公司
- 公司地址：中国上海市浦东新区
- 主营业务：制造业智能采购，企业智能化转型等

当前的项目信息：
- 项目名称：合作方的一个小型的医疗检测器械
- 目前处于量产前期
- 采购的物料需求量在图纸信息中有，如果对方反复提问，需要人工介入，转人工
- 项目目标：合作方在市场上推出一款功能完善、性能优越的医疗检测设备；我们在为合作方提供专业的采购支持和解决方案
- 不能透露具体的合作方信息，如果对方执意要问，这个时候需要人工介入

个人信息
- 姓卢
- 采购经理
- 身份说明要简明，避免提及"后续对接沟通"等在对话中的多余表述

采购相关信息：
- 采购流程：询价 → 技术确认 → 报价单询问 → 后续商务洽谈
- 质量要求：严格按照国家标准和行业标准执行
- 付款方式：根据合作情况协商确定
- 交期要求：根据具体项目需求确定

注意：回复要符合当前对话情境，不要引导到其他联系渠道。
"""
    
    async def validate_input(self, data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        if not isinstance(data, dict):
            return False
        
        # 检查必要字段
        if "message" not in data and "message_history" not in data:
            return False
        
        return True
    
    async def pre_process(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理数据"""
        # 获取会话记忆上下文
        memory_context = await self.get_memory_context(session_id)
        
        # 合并上下文信息
        processed_data = data.copy()
        if memory_context:
            processed_data["memory_context"] = memory_context
        
        return processed_data
    
    async def post_process(self, session_id: str, result: AgentResult) -> AgentResult:
        """后处理结果"""
        
        # 开始处理不同的info_type
        if result.data and "material_spec" in result.data.get("info_type", ""):
            # 从供应商信息中获取物料信息
            supplier_name = result.data.get("supplier_name", "") if result.data else ""
            if supplier_name:
                # 检查是否已向该供应商发送过文件
                session_context = getattr(self, '_current_session_context', None)
                if session_context and session_context.has_files_sent_to_supplier(supplier_name):
                    # 已发送过文件，不再重复发送，保持response_type为text
                    self.log_info(f"文件已发送给供应商 {supplier_name}，跳过重复发送")
                else:
                    # 首次发送文件
                    start_time = datetime.now()
                    material_spec_info = get_material_spec_info(supplier_name)
                    if result.data and material_spec_info:
                        result.data["material_spec"] = material_spec_info
                        
                        # 标记文件已发送
                        if session_context:
                            session_context.mark_files_sent(supplier_name, material_spec_info)
                        
                        # 添加图纸资料发送消息
                        result.add_message(
                            role=MessageRole.ASSISTANT,
                            content="图纸资料已发送",
                            metadata={
                                "agent": self.name,
                                "processing_time": (datetime.now() - start_time).total_seconds(),
                                "info_type": "material_spec"
                            }
                        )
        
        # 保存会话上下文到记忆
        if result.success and result.messages:
            context_to_save = {
                "last_interaction": datetime.now().isoformat(),
                "agent_type": "info_response_agent",
                "last_response": result.messages[-1].content if result.messages else None,
                "info_type": result.data.get("info_type") if result.data else None,
                "need_human": result.data.get("need_human", False) if result.data else False
            }
            await self.save_memory_context(session_id, context_to_save)
        
        return result
    
    def get_common_questions(self) -> Dict[str, List[str]]:
        """获取常见问题分类"""
        return {
            "company_info": [
                "贵公司在哪里？",
                "公司规模多大？",
                "主要做什么业务？",
                "联系方式是什么？"
            ],
            "material_spec": [
                "具体规格要求是什么？",
                "有技术图纸吗？",
                "材质有什么要求？",
                "尺寸公差是多少？"
            ],
            "quality_req": [
                "质量标准是什么？",
                "需要什么认证？",
                "检验要求有哪些？",
                "需要提供质保书吗？"
            ],
            "business_terms": [
                "付款方式是什么？",
                "交期要求多久？",
                "包装有什么要求？",
                "运输方式如何？"
            ],
            "quantity_delivery": [
                "采购数量是多少？",
                "什么时候要货？",
                "可以分批交货吗？",
                "有长期合作计划吗？"
            ]
        }
    
    def get_response_templates(self) -> Dict[str, str]:
        """获取回复模板"""
        return {
            "company_info": "我们是一家位于{location}的{business_type}企业，主要从事{main_business}。具体的联系方式建议通过平台正式渠道获取。",
            "material_spec": "关于{material_name}的技术规格要求：{specifications}。如需详细图纸，我们可以在后续正式合作中提供。",
            "quality_req": "我们对产品质量要求严格，需要符合{standards}标准，并提供{certifications}。",
            "business_terms": "关于商务条件：付款方式为{payment_terms}，交期要求{delivery_time}，具体条款可进一步协商。",
            "quantity_delivery": "本次采购数量为{quantity}，期望交期{delivery_date}，后续可能有持续合作机会。"
        }
    
    def __str__(self) -> str:
        return f"InfoResponseAgent({self.name})"
    
    def __repr__(self) -> str:
        return f"InfoResponseAgent(name='{self.name}', supported_types={self.get_supported_types()})"


# 使用示例
if __name__ == "__main__":
    import asyncio
    from app.core.llm_client import LLMClientFactory
    
    async def test_agent():
        # 创建Agent（使用自带的初始化配置）
        agent = InfoResponseAgent()
        
        # 模拟处理请求
        test_data = {
            # "message": "请问贵公司在哪里？主要做什么业务的？",
            "message": "想问下你们这个是要用在哪里的？",
            # "message": "有具体合作方的信息吗？报价需要登记的",
            # "message": "您好，请问您贵姓？",
            "message_history": [
                {"role": "user", "content": "你好"},
                {"role": "assistant", "content": "您好，我们需要采购304不锈钢板材，想了解贵司是否有相应的供货能力？"}
            ],
            "supplier_info": {
                "company_name": "大连鸿升机械有限公司1",
            },
            "component_code": ["SS304-PLATE-001"]
        }
        
        result = await agent.process("test_session_001", test_data)
        print(f"处理结果: {result.success}")
        print(f"响应消息: {result.data.get('response') if result.data else 'None'}")
        print(f"信息类型: {result.data.get('info_type') if result.data else 'None'}")
        print(json.dumps(result.to_dict(), ensure_ascii=False, indent=2))
        print(f"错误信息: {result.error}")
    
    # 运行测试
    asyncio.run(test_agent())