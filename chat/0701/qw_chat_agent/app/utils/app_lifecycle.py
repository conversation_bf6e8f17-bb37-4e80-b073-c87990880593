"""
应用生命周期管理
"""

import atexit
import logging
from app.utils.resource_manager import resource_manager
from app.utils.background_tasks import background_task_manager


logger = logging.getLogger(__name__)


def setup_app_lifecycle():
    """设置应用生命周期管理"""
    
    def cleanup_on_exit():
        """应用退出时的清理函数"""
        logger.info("应用正在关闭，开始清理资源...")
        
        try:
            # 关闭后台任务管理器
            background_task_manager.shutdown()
            logger.info("后台任务管理器已关闭")
        except Exception as e:
            logger.error(f"关闭后台任务管理器失败: {str(e)}")
        
        try:
            # 清理所有资源
            resource_manager.cleanup_all()
            logger.info("资源管理器清理完成")
        except Exception as e:
            logger.error(f"资源管理器清理失败: {str(e)}")
        
        logger.info("应用清理完成")
    
    # 注册退出清理函数
    atexit.register(cleanup_on_exit)
    logger.info("应用生命周期管理已设置")


def get_app_status():
    """获取应用状态"""
    return {
        "resource_manager": resource_manager.get_resource_status(),
        "background_tasks": background_task_manager.get_task_status()
    } 