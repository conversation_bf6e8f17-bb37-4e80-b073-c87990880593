"""
组件供应商视图模型

对应数据库视图：v_component_supplier
"""

from sqlalchemy import Column, Integer, String, Text
from .base import Base

class ComponentSupplier(Base):
    """组件供应商视图模型"""
    
    __tablename__ = "v_component_supplier"
    
    # 根据DDL，视图没有id字段，使用component_id作为主键
    component_id = Column(Integer, primary_key=True, comment='组件ID')
    
    # 产品信息
    product_name = Column(String(255), comment='产品名称')
    product_id = Column(Integer, comment='产品ID')
    
    # 分类信息
    primary_category = Column(String(100), comment='一级分类')
    secondary_category = Column(String(100), comment='二级分类')
    category_feature = Column(Text, comment='分类特征描述')
    category_id = Column(Integer, comment='分类ID')
    
    # 组件信息
    component_name = Column(String(100), comment='组件名称')
    component_spec = Column(Text, comment='组件规格')
    component_tag = Column(String(100), comment='组件标签')
    
    # 供应商信息
    original_supplier = Column(String(100), comment='原始供应商')
    recommended_supplier = Column(String(100), comment='推荐供应商')
    supplier_type = Column(String(20), comment='供应商类型')
    supplier_region = Column(String(100), comment='供应商地区')
    supplier_website = Column(String(255), comment='供应商网站')
    supplier_status = Column(String(20), comment='供应商状态')
    supplier_status_code = Column(Integer, comment='供应商状态码')
    supplier_phone = Column(String(50), comment='供应商电话')
    supplier_email = Column(String(100), comment='供应商邮箱')
    supplier_certifications = Column(String(255), comment='供应商认证')
    supplier_id = Column(Integer, comment='供应商ID')
    
    # 匹配信息
    matching_reason = Column(Text, comment='匹配原因')
    
    def __repr__(self):
        return f"<ComponentSupplier(component_id={self.component_id}, product='{self.product_name}', component='{self.component_name}', recommended_supplier='{self.recommended_supplier}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'component_id': self.component_id,
            'product_name': self.product_name,
            'product_id': self.product_id,
            'primary_category': self.primary_category,
            'secondary_category': self.secondary_category,
            'category_feature': self.category_feature,
            'category_id': self.category_id,
            'component_name': self.component_name,
            'component_spec': self.component_spec,
            'component_tag': self.component_tag,
            'original_supplier': self.original_supplier,
            'recommended_supplier': self.recommended_supplier,
            'supplier_type': self.supplier_type,
            'supplier_region': self.supplier_region,
            'supplier_website': self.supplier_website,
            'supplier_status': self.supplier_status,
            'supplier_status_code': self.supplier_status_code,
            'supplier_phone': self.supplier_phone,
            'supplier_email': self.supplier_email,
            'supplier_certifications': self.supplier_certifications,
            'supplier_id': self.supplier_id,
            'matching_reason': self.matching_reason
        }
    
    def get_category_info(self):
        """获取分类信息"""
        return {
            'primary_category': self.primary_category,
            'secondary_category': self.secondary_category,
            'category_feature': self.category_feature,
            'category_id': self.category_id
        }
    
    def get_supplier_info(self):
        """获取供应商信息"""
        return {
            'recommended_supplier': self.recommended_supplier,
            'original_supplier': self.original_supplier,
            'supplier_type': self.supplier_type,
            'supplier_region': self.supplier_region,
            'supplier_website': self.supplier_website,
            'supplier_status': self.supplier_status,
            'supplier_status_code': self.supplier_status_code,
            'supplier_phone': self.supplier_phone,
            'supplier_email': self.supplier_email,
            'supplier_certifications': self.supplier_certifications,
            'supplier_id': self.supplier_id,
            'matching_reason': self.matching_reason
        } 