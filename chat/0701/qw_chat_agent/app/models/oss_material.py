"""
OSS物料视图模型

对应数据库视图：v_oss_material
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, DECIMAL
from .base import Base
import json

class OssMaterial(Base):
    """OSS物料视图模型"""
    
    __tablename__ = "v_oss_material"
    
    # 主键
    id = Column(Integer, primary_key=True, comment='组件ID')
    
    # 分类信息
    product_category = Column(String(100), comment='产品分类（一级分类）')
    sub_category = Column(String(100), comment='子分类（二级分类）')
    
    # 物料信息
    material_name = Column(String(100), comment='物料名称')
    material_spec = Column(Text, comment='物料规格')
    material_code = Column(String(50), comment='物料编码')
    material_price = Column(String(20), comment='物料价格（格式化字符串）')
    material = Column(String(50), comment='材料')
    component_type = Column(String(50), comment='组件类型')
    
    # 数量信息
    quantity = Column(Integer, comment='数量')
    unit_count = Column(Integer, comment='单位数量')
    total_count = Column(Integer, comment='总数量')
    
    # 文档和OSS信息
    document_names = Column(Text, comment='文档名称（分号分隔）')
    oss_paths = Column(Text, comment='OSS路径（JSON数组格式）')
    
    # 关联信息
    product_id = Column(Integer, comment='产品ID')
    created_at = Column(DateTime, comment='创建时间')
    
    def __repr__(self):
        return f"<OssMaterial(id={self.id}, material_name='{self.material_name}', product_category='{self.product_category}', sub_category='{self.sub_category}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'product_category': self.product_category,
            'sub_category': self.sub_category,
            'material_name': self.material_name,
            'material_spec': self.material_spec,
            'material_code': self.material_code,
            'material_price': self.material_price,
            'material': self.material,
            'component_type': self.component_type,
            'quantity': self.quantity,
            'unit_count': self.unit_count,
            'total_count': self.total_count,
            'document_names': self.document_names,
            'oss_paths': self.oss_paths,
            'product_id': self.product_id,
            'created_at': self.created_at
        }
    
    def get_oss_paths_list(self):
        """将OSS路径字符串转换为列表"""
        if not self.oss_paths or self.oss_paths == '[]':
            return []
        try:
            # 移除首尾的方括号并按逗号分割
            paths_str = self.oss_paths.strip('[]')
            if not paths_str:
                return []
            return [path.strip() for path in paths_str.split(',')]
        except:
            return []
    
    def get_document_names_list(self):
        """将文档名称字符串转换为列表"""
        if not self.document_names:
            return []
        return [name.strip() for name in self.document_names.split(';') if name.strip()]
    
    def get_category_info(self):
        """获取分类信息"""
        return {
            'product_category': self.product_category,
            'sub_category': self.sub_category
        }
    
    def get_material_info(self):
        """获取物料基本信息"""
        return {
            'material_name': self.material_name,
            'material_spec': self.material_spec,
            'material_code': self.material_code,
            'material_price': self.material_price,
            'material': self.material,
            'component_type': self.component_type
        }
    
    def get_quantity_info(self):
        """获取数量信息"""
        return {
            'quantity': self.quantity,
            'unit_count': self.unit_count,
            'total_count': self.total_count
        } 