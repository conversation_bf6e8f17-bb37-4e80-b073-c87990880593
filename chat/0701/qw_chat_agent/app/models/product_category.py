"""
产品分类信息模型

对应数据库表：product_categories
"""

from sqlalchemy import Column, Integer, String, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from .base import Base

class ProductCategory(Base):
    """产品分类信息模型"""
    
    __tablename__ = "product_categories"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='分类ID，自动递增的主键')
    
    # 关联信息
    product_id = Column(Integer, nullable=False, comment='产品id，每个产品id有多个一级分类和二级分类')
    
    # 基础信息
    category = Column(String(100), comment='category_hierarchy决定')
    prev = Column(Integer, nullable=False, default=0, comment='存储它上一级分类的id，如果是一级标题，它的上一级分类是0')
    feature = Column(Text, comment='从这个分类中提取feature的共性')
    
    # 索引定义
    __table_args__ = (
        Index('product_categories_client_product_FK', 'product_id'),
        Index('idx_category', 'category'),
        Index('idx_prev', 'prev'),
        {
            'mysql_engine': 'InnoDB',
            'mysql_charset': 'utf8mb4',
            'mysql_collate': 'utf8mb4_unicode_ci'
        }
    )
    
    def __repr__(self):
        return f"<ProductCategory(id={self.id}, product_id={self.product_id}, category='{self.category}', prev={self.prev})>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'category': self.category,
            'prev': self.prev,
            'feature': self.feature
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        """从字典创建实例"""
        return cls(
            product_id=data.get('product_id'),
            category=data.get('category'),
            prev=data.get('prev', 0),
            feature=data.get('feature')
        )
    
    def update_from_dict(self, data: dict):
        """从字典更新实例"""
        for key, value in data.items():
            if hasattr(self, key) and key != 'id':  # 不允许更新ID
                setattr(self, key, value) 