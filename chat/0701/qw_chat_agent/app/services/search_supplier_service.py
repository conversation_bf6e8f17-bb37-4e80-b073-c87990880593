"""
搜索供应商服务类
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from app.models.search_supplier import SearchSupplier
from app.database import db_manager

class SearchSupplierService:
    """搜索供应商服务类"""
    
    def __init__(self, db: Optional[Session] = None, db_name: str = "second"):
        self.db = db
        self.db_name = db_name
    
    def get_by_id(self, supplier_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取供应商记录"""
        if self.db:
            record = self.db.query(SearchSupplier).filter(
                SearchSupplier.id == supplier_id,
                SearchSupplier.is_deleted == False
            ).first()
            return record.to_dict() if record else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                record = db.query(SearchSupplier).filter(
                    SearchSupplier.id == supplier_id,
                    SearchSupplier.is_deleted == False
                ).first()
                return record.to_dict() if record else None
    
    def get_suppliers_by_chat_status(self, chat_status: str = "ready", limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取指定聊天状态的供应商数据（默认获取ready状态）"""
        # 验证聊天状态是否有效
        if chat_status not in SearchSupplier.get_valid_chat_statuses():
            return []
        
        if self.db:
            records = self.db.query(SearchSupplier).filter(
                SearchSupplier.chat_status == chat_status,
                SearchSupplier.is_deleted == False
            ).order_by(SearchSupplier.created_at.desc()).offset(offset).limit(limit).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = db.query(SearchSupplier).filter(
                    SearchSupplier.chat_status == chat_status,
                    SearchSupplier.is_deleted == False
                ).order_by(SearchSupplier.created_at.desc()).offset(offset).limit(limit).all()
                return [record.to_dict() for record in records]
    
    def update_chat_status_by_supplier_name(self, supplier_name: str, chat_status: str = "chatting") -> bool:
        """根据供应商名称修改聊天状态（默认修改为chatting）"""
        # 验证聊天状态是否有效
        if chat_status not in SearchSupplier.get_valid_chat_statuses():
            return False
        
        if self.db:
            updated_count = self.db.query(SearchSupplier).filter(
                SearchSupplier.supplier_name == supplier_name,
                SearchSupplier.is_deleted == False
            ).update({
                SearchSupplier.chat_status: chat_status
            })
            self.db.commit()
            return updated_count > 0
        else:
            with db_manager.get_db_context(self.db_name) as db:
                updated_count = db.query(SearchSupplier).filter(
                    SearchSupplier.supplier_name == supplier_name,
                    SearchSupplier.is_deleted == False
                ).update({
                    SearchSupplier.chat_status: chat_status
                })
                db.commit()
                return updated_count > 0
    
    def get_supplier_contact_info(self, supplier_name: str) -> Optional[Dict[str, Any]]:
        """辅助函数：根据供应商名称获取联系信息"""
        if self.db:
            record = self.db.query(SearchSupplier).filter(
                SearchSupplier.supplier_name == supplier_name,
                SearchSupplier.is_deleted == False
            ).first()
            if record:
                contact_info = record.get_contact_info()
                contact_info['supplier_name'] = record.supplier_name
                contact_info['region'] = record.region
                return contact_info
            return None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                record = db.query(SearchSupplier).filter(
                    SearchSupplier.supplier_name == supplier_name,
                    SearchSupplier.is_deleted == False
                ).first()
                if record:
                    contact_info = record.get_contact_info()
                    contact_info['supplier_name'] = record.supplier_name
                    contact_info['region'] = record.region
                    return contact_info
                return None
    
    def search_by_category(self, category_id: int, 
                          chat_status: Optional[str] = None,
                          status: Optional[int] = None,
                          limit: int = 100, 
                          offset: int = 0) -> List[Dict[str, Any]]:
        """根据分类ID搜索供应商"""
        def _build_query(db: Session):
            query = db.query(SearchSupplier).filter(
                SearchSupplier.category_id == category_id,
                SearchSupplier.is_deleted == False
            )
            
            if chat_status and chat_status in SearchSupplier.get_valid_chat_statuses():
                query = query.filter(SearchSupplier.chat_status == chat_status)
            
            if status is not None:
                query = query.filter(SearchSupplier.status == status)
            
            return query.order_by(SearchSupplier.created_at.desc()).offset(offset).limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def search_by_supplier_name(self, supplier_name: str, exact_match: bool = False) -> List[Dict[str, Any]]:
        """根据供应商名称搜索"""
        if self.db:
            if exact_match:
                records = self.db.query(SearchSupplier).filter(
                    SearchSupplier.supplier_name == supplier_name,
                    SearchSupplier.is_deleted == False
                ).all()
            else:
                records = self.db.query(SearchSupplier).filter(
                    SearchSupplier.supplier_name.like(f"%{supplier_name}%"),
                    SearchSupplier.is_deleted == False
                ).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                if exact_match:
                    records = db.query(SearchSupplier).filter(
                        SearchSupplier.supplier_name == supplier_name,
                        SearchSupplier.is_deleted == False
                    ).all()
                else:
                    records = db.query(SearchSupplier).filter(
                        SearchSupplier.supplier_name.like(f"%{supplier_name}%"),
                        SearchSupplier.is_deleted == False
                    ).all()
                return [record.to_dict() for record in records]
    
    def get_chat_status_statistics(self) -> Dict[str, int]:
        """获取各个聊天状态的统计信息"""
        if self.db:
            results = self.db.query(
                SearchSupplier.chat_status,
                func.count(SearchSupplier.id).label('count')
            ).filter(
                SearchSupplier.is_deleted == False
            ).group_by(SearchSupplier.chat_status).all()
            
            return {result.chat_status: result.count for result in results}
        else:
            with db_manager.get_db_context(self.db_name) as db:
                results = db.query(
                    SearchSupplier.chat_status,
                    func.count(SearchSupplier.id).label('count')
                ).filter(
                    SearchSupplier.is_deleted == False
                ).group_by(SearchSupplier.chat_status).all()
                
                return {result.chat_status: result.count for result in results}
    
    def create_supplier(self, supplier_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建新的供应商记录"""
        try:
            new_supplier = SearchSupplier(**supplier_data)
            
            if self.db:
                self.db.add(new_supplier)
                self.db.commit()
                self.db.refresh(new_supplier)
                return new_supplier.to_dict()
            else:
                with db_manager.get_db_context(self.db_name) as db:
                    db.add(new_supplier)
                    db.commit()
                    db.refresh(new_supplier)
                    return new_supplier.to_dict()
        except Exception as e:
            if self.db:
                self.db.rollback()
            return None
    
    def update_supplier(self, supplier_id: int, update_data: Dict[str, Any]) -> bool:
        """更新供应商记录"""
        try:
            if self.db:
                updated_count = self.db.query(SearchSupplier).filter(
                    SearchSupplier.id == supplier_id,
                    SearchSupplier.is_deleted == False
                ).update(update_data)
                self.db.commit()
                return updated_count > 0
            else:
                with db_manager.get_db_context(self.db_name) as db:
                    updated_count = db.query(SearchSupplier).filter(
                        SearchSupplier.id == supplier_id,
                        SearchSupplier.is_deleted == False
                    ).update(update_data)
                    db.commit()
                    return updated_count > 0
        except Exception:
            if self.db:
                self.db.rollback()
            return False
    
    def soft_delete_supplier(self, supplier_id: int) -> bool:
        """软删除供应商记录"""
        return self.update_supplier(supplier_id, {'is_deleted': True}) 