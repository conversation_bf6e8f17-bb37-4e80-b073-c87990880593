"""
供应商匹配相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.models.user import UserResponse
from app.api.dependencies import get_current_user_info_optional
from app.services.component_supplier_service import ComponentSupplierService
from app.core.llm_client import LLMClientFactory, AgentMessage, MessageRole
from app.utils.tool_function import parse_llm_json_response
import os

router = APIRouter(prefix="/supplier-match", tags=["供应商匹配"])


# 请求和响应模型
class SupplierMatchRequest(BaseModel):
    """供应商匹配请求模型"""
    company_info: str
    

class SupplierMatchResponse(BaseModel):
    """供应商匹配响应模型"""
    success: bool
    matched_supplier: Optional[str] = None
    confidence: Optional[float] = None
    reason: Optional[str] = None
    available_suppliers: List[str] = []
    message: str


@router.post("/match", response_model=SupplierMatchResponse, summary="智能匹配推荐供应商")
async def match_supplier(
    request: SupplierMatchRequest,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    根据添加好友申请语智能匹配最合适的推荐供应商
    
    - **company_info**: 包含公司信息的添加好友申请语，如"老板您好，我是xx公司的营销人员"
    """
    try:
        # 获取所有推荐供应商
        service = ComponentSupplierService()
        all_suppliers = service.get_all_recommended_suppliers()
        
        if not all_suppliers:
            return SupplierMatchResponse(
                success=False,
                message="数据库中没有找到推荐供应商",
                available_suppliers=[]
            )
        
        # 使用大模型进行匹配
        from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY, DEFAULT_MODEL
        llm_client = LLMClientFactory.create_openai_client(
            base_url=OPENAI_BASE_URL,
            api_key=OPENAI_API_KEY,
            default_model=DEFAULT_MODEL
        )
        
        # 构建提示词
        prompt = f"""
请严格基于公司名称进行匹配，从添加好友申请语中提取公司名称，并在推荐供应商列表中寻找最相似的名称。

添加好友申请语：
"{request.company_info}"

可选的推荐供应商列表：
{', '.join(all_suppliers)}

**匹配规则（严格按名称相似度）**：

**高匹配度情况（置信度0.9+）**：
- 完全相同：如"上海鸣志" → "上海鸣志电器股份有限公司"（缩写匹配）
- 核心名称一致：如"苏州金澄" → "苏州金澄精密铸造有限公司"

**中等匹配度情况（置信度0.6-0.8）**：
- 同音字替换：如"晶台" → "精泰"、"景台"
- 部分名称匹配但有明显差异

**低匹配度情况（置信度0.1-0.5）**：
- 仅地区相同但公司核心名称完全不同
- 仅行业相关但名称无关联

**无匹配情况（置信度0.0）**：
- 申请语中的公司名称与列表中任何供应商都无明显相似性

**案例参考**：
- "上海鸣志的采购经理" → "上海鸣志电器股份有限公司"（置信度0.98，核心名称完全匹配）
- "苏州金澄精密铸造的营销" → "苏州金澄精密铸造有限公司"（置信度0.98，完全匹配）
- "苏州某某制造公司" → "苏州市东望精密制造有限公司"（置信度0.3，仅地区+行业相似）
- "比亚迪汽车" → 任何供应商（置信度0.1，名称无关联）

请以JSON格式返回结果：
{{
    "matched_supplier": "最匹配的供应商名称",
    "confidence": 0.85,
    "reason": "基于名称相似度分析的匹配逻辑"
}}

**重要**：严格控制置信度，避免过度推测，主要依据名称相似程度评分。
"""
        
        # 构建消息
        messages = [
            AgentMessage(role=MessageRole.SYSTEM, content="你是一个专业的供应商匹配专家，擅长从添加好友申请语中提取公司信息并匹配合适的供应商。"),
            AgentMessage(role=MessageRole.USER, content=prompt)
        ]
        
        # 调用大模型
        response = await llm_client.chat_completion(
            messages=messages,
            temperature=0.3,
            max_tokens=500
        )
        
        # 解析响应
        result = parse_llm_json_response(
            response.content, 
            expected_keys=['matched_supplier', 'confidence', 'reason']
        )
        
        # 验证匹配结果
        matched_supplier = result.get('matched_supplier')
        confidence = result.get('confidence', 0.0)
        reason = result.get('reason', '未提供匹配原因')
        
        # 确保匹配的供应商在列表中
        if matched_supplier and matched_supplier not in all_suppliers:
            # 如果大模型返回的供应商不在列表中，尝试模糊匹配
            matched_supplier = _find_best_match(matched_supplier, all_suppliers)
        
        if matched_supplier:
            return SupplierMatchResponse(
                success=True,
                matched_supplier=matched_supplier,
                confidence=confidence,
                reason=reason,
                available_suppliers=all_suppliers,
                message=f"成功匹配到供应商: {matched_supplier}"
            )
        else:
            return SupplierMatchResponse(
                success=False,
                matched_supplier=None,
                confidence=0.0,
                reason="未能找到合适的匹配供应商",
                available_suppliers=all_suppliers,
                message="无法根据提供的申请语匹配到合适的供应商"
            )
            
    except Exception as e:
        return SupplierMatchResponse(
            success=False,
            matched_supplier=None,
            confidence=0.0,
            reason=f"匹配过程中发生错误: {str(e)}",
            available_suppliers=[],
            message=f"供应商匹配失败: {str(e)}"
        )


def _find_best_match(target_supplier: str, available_suppliers: List[str]) -> Optional[str]:
    """
    在可用供应商列表中找到与目标供应商最相似的名称
    
    Args:
        target_supplier: 目标供应商名称
        available_suppliers: 可用供应商列表
        
    Returns:
        最匹配的供应商名称，如果找不到则返回None
    """
    if not target_supplier or not available_suppliers:
        return None
    
    target_lower = target_supplier.lower()
    
    # 精确匹配
    for supplier in available_suppliers:
        if supplier.lower() == target_lower:
            return supplier
    
    # 包含匹配
    for supplier in available_suppliers:
        if target_lower in supplier.lower() or supplier.lower() in target_lower:
            return supplier
    
    # 如果都没匹配上，返回第一个供应商作为默认值
    return available_suppliers[0] if available_suppliers else None


@router.get("/suppliers", summary="获取所有推荐供应商列表")
async def get_all_suppliers(
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    获取数据库中所有的推荐供应商列表
    """
    try:
        service = ComponentSupplierService()
        suppliers = service.get_all_recommended_suppliers()
        
        return {
            "success": True,
            "data": suppliers,
            "total": len(suppliers),
            "message": f"成功获取{len(suppliers)}个推荐供应商"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取供应商列表失败: {str(e)}"
        )