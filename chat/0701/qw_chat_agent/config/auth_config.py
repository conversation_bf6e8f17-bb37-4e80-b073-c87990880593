"""
JWT认证配置
"""
import os
from datetime import timedelta

# JWT配置
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "YJ1234567890")
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
JWT_REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))

# 密码加密配置
PWD_CONTEXT_SCHEMES = ["bcrypt"]
PWD_CONTEXT_DEPRECATED = "auto"

# Token配置
ACCESS_TOKEN_EXPIRE_DELTA = timedelta(minutes=JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
REFRESH_TOKEN_EXPIRE_DELTA = timedelta(days=JWT_REFRESH_TOKEN_EXPIRE_DAYS)

# API配置
API_V1_PREFIX = "/api/v1" 