# example_usage.py
"""
Component模型使用示例
"""

from app.database import check_database_connection, init_database
from app.services.component_service import ComponentService
import json

def main():
    # 1. 检查数据库连接
    if not check_database_connection():
        print("数据库连接失败，请检查配置")
        return
    
    # 2. 初始化数据库表（如果需要）
    # init_database()
    
    # 3. 创建服务实例
    service = ComponentService()
    
    # 4. 创建新的物料组件
    # component_data = {
    #     "name": "STM32F103C8T6",
    #     "spec": "ARM Cortex-M3, 64KB Flash, LQFP48封装",
    #     "category": "微控制器",
    #     "material": "硅",
    #     "component_code": "MCU-STM32-001",
    #     "tag": "标件",
    #     "original_supplier": "ST官方代理",
    #     "price": 15.50,
    #     "quantity": 1000,
    #     "client_company_id": 1,
    #     "client_product_id": 10
    # }
    
    # 创建组件
    # new_component = service.create_component(component_data)
    # print(f"创建成功: {new_component}")
    
    # 5. 查询组件
    # 根据ID查询
    component_result = service.get_component_by_id(17)
    print(f"查询结果: {component_result}")
    
    # 根据名称搜索
    components = service.get_components_by_name("PMT")
    # print(json.dumps(components, indent=4, ensure_ascii=False))
    print(f"搜索到 {len(components)} 个组件")

    # 精确查询 - 根据完整的物料编码
    component = service.get_component_by_code("12040008101")
    if component:
        print(f"找到组件: {component['name']}")
    else:
        print("未找到该编码的组件")
    
    # 2. 模糊查询 - 根据编码模式搜索
    components = service.get_components_by_code_pattern("1204")
    print(f"找到 {len(components)} 个包含'1204'的组件")
    
    # 复合条件搜索
    # search_results = service.search_components(
    #     name="%PMT%",
    #     category="B",
    #     min_price=10.0,
    #     max_price=220.0,
    #     limit=10
    # )
    # print(f"复合搜索结果: {len(search_results)} 个组件")
    
    # 6. 更新组件
    # update_data = {
    #     "price": 16.80,
    #     "quantity": 1200,
    #     "tag": "常用标件"
    # }
    # updated_component = service.update_component(new_component.id, update_data)
    # print(f"更新后: {updated_component.to_dict()}")
    
    # 7. 获取统计信息
    total_count = service.get_component_count()
    categories = service.get_categories()
    print(f"总组件数: {total_count}")
    print(f"所有类别: {categories}")

if __name__ == "__main__":
    main()