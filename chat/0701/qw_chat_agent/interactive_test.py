#!/usr/bin/env python3
"""
交互式API测试脚本
支持命令行对话测试和会话管理
"""
import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any, Optional


class InteractiveAPITester:
    """交互式API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8650"):
        self.base_url = base_url
        self.session = None
        self.current_session_id = None
        self.use_auth = False
        self.access_token = None
        
        # 默认供应商信息
        self.supplier_info = {
            "company_name": "苏州志祥精密科技有限公司"
        }
        
        # 默认用户信息
        self.default_data = {
            "sid": "AOPU_lhh",
            "uid": "123467"
        }
    
    async def __aenter__(self):
        # 配置超时和连接参数
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        connector = aiohttp.TCPConnector(
            limit=10,  # 连接池大小
            limit_per_host=5,  # 每个主机的连接数
            ttl_dns_cache=300,  # DNS缓存时间
            use_dns_cache=True,
        )
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.use_auth and self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    async def send_message(self, message: str, max_retries: int = 3) -> Dict[str, Any]:
        """发送消息到API"""
        url = f"{self.base_url}/api/v1/inquiry/chat"
        data = {
            "message": message,
            **self.default_data,
            "supplier_info": self.supplier_info
        }
        
        # 如果有当前会话ID，添加到请求中
        if self.current_session_id:
            data["session_id"] = self.current_session_id
            
        for attempt in range(max_retries):
            try:
                async with self.session.post(url, json=data, headers=self.get_headers()) as response:
                    result = await response.json()
                    if response.status == 200:
                        # 更新当前会话ID
                        if "session_id" in result:
                            self.current_session_id = result["session_id"]
                        return result
                    else:
                        return {"error": f"HTTP {response.status}: {result}"}
            except (aiohttp.ClientError, asyncio.TimeoutError, ConnectionResetError) as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避：1s, 2s, 4s
                    print(f"⚠️  连接失败，{wait_time}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    return {"error": f"连接失败 (已重试{max_retries}次): {str(e)}"}
            except Exception as e:
                return {"error": str(e)}
    
    async def delete_session(self, session_id: str, max_retries: int = 2) -> Dict[str, Any]:
        """删除会话"""
        url = f"{self.base_url}/api/v1/inquiry/session/{session_id}"
        
        for attempt in range(max_retries):
            try:
                async with self.session.delete(url, headers=self.get_headers()) as response:
                    result = await response.json()
                    if response.status == 200:
                        return result
                    else:
                        return {"error": f"HTTP {response.status}: {result}"}
            except (aiohttp.ClientError, asyncio.TimeoutError, ConnectionResetError) as e:
                if attempt < max_retries - 1:
                    print(f"⚠️  删除会话连接失败，1秒后重试...")
                    await asyncio.sleep(1)
                    continue
                else:
                    return {"error": f"删除会话失败: {str(e)}"}
            except Exception as e:
                return {"error": str(e)}
    
    async def get_session_status(self, session_id: str, max_retries: int = 2) -> Dict[str, Any]:
        """获取会话状态"""
        url = f"{self.base_url}/api/v1/inquiry/session/{session_id}/status"
        
        for attempt in range(max_retries):
            try:
                async with self.session.get(url, headers=self.get_headers()) as response:
                    result = await response.json()
                    if response.status == 200:
                        return result
                    else:
                        return {"error": f"HTTP {response.status}: {result}"}
            except (aiohttp.ClientError, asyncio.TimeoutError, ConnectionResetError) as e:
                if attempt < max_retries - 1:
                    print(f"⚠️  获取状态连接失败，1秒后重试...")
                    await asyncio.sleep(1)
                    continue
                else:
                    return {"error": f"获取状态失败: {str(e)}"}
            except Exception as e:
                return {"error": str(e)}
    
    def print_response(self, result: Dict[str, Any]):
        """格式化打印响应结果"""
        if "error" in result:
            print(f"❌ 错误: {result['error']}")
            return
            
        print("📤 API响应:")
        print(f"   💬 回复: {result.get('response', 'N/A')}")
        print(f"   🏷️  Agent类型: {result.get('agent_type', 'N/A')}")
        print(f"   🆔 会话ID: {result.get('session_id', 'N/A')}")
        print(f"   🔄 响应类型: {result.get('response_type', 'N/A')}")
        
        # 检查是否需要转人工
        need_human = result.get('need_human', False)
        if need_human:
            print(f"   🚨 需要转人工: {need_human}")
        
        # 检查其他字段
        if result.get('info_type'):
            print(f"   ℹ️  信息类型: {result.get('info_type')}")
        if result.get('material_spec'):
            print(f"   📋 物料规格: {result.get('material_spec')}")
        if result.get('reason'):
            print(f"   💭 原因: {result.get('reason')}")
    
    def print_help(self):
        """打印帮助信息"""
        print("\n📖 可用命令:")
        print("   - 直接输入消息进行对话")
        print("   - /help 或 /h       - 显示帮助")
        print("   - /status 或 /s     - 查看当前会话状态")
        print("   - /delete 或 /d     - 删除当前会话")
        print("   - /delete <ID>      - 删除指定会话ID")
        print("   - /new 或 /n        - 开始新会话")
        print("   - /supplier         - 设置供应商信息")
        print("   - /quit 或 /q       - 退出程序")
        print("   - /clear            - 清屏")
        print()
    
    def print_banner(self):
        """打印启动横幅"""
        print("=" * 60)
        print("🤖 供应商询价系统 - 交互式测试工具")
        print(f"🌐 API地址: {self.base_url}")
        print(f"🏢 当前供应商: {self.supplier_info.get('company_name', 'N/A')}")
        print("=" * 60)
        print("💡 输入 /help 查看可用命令")
        print()
    
    async def handle_command(self, command: str) -> bool:
        """处理命令，返回是否继续运行"""
        command = command.strip().lower()
        
        if command in ['/quit', '/q']:
            return False
        elif command in ['/help', '/h']:
            self.print_help()
        elif command in ['/clear']:
            print("\033[2J\033[H")  # 清屏
            self.print_banner()
        elif command in ['/new', '/n']:
            self.current_session_id = None
            print("🆕 开始新会话")
        elif command in ['/status', '/s']:
            if self.current_session_id:
                print(f"📊 查询会话状态: {self.current_session_id}")
                result = await self.get_session_status(self.current_session_id)
                if "error" not in result:
                    print(f"   状态: {result.get('status', 'N/A')}")
                    print(f"   创建时间: {result.get('created_at', 'N/A')}")
                    print(f"   更新时间: {result.get('updated_at', 'N/A')}")
                    print(f"   消息数量: {result.get('message_count', 'N/A')}")
                    print(f"   当前Agent: {result.get('current_agent', 'N/A')}")
                else:
                    print(f"❌ 获取状态失败: {result['error']}")
            else:
                print("⚠️  当前没有活跃会话")
        elif command in ['/delete', '/d']:
            if self.current_session_id:
                print(f"🗑️  删除当前会话: {self.current_session_id}")
                result = await self.delete_session(self.current_session_id)
                if "error" not in result:
                    print("✅ 会话删除成功")
                    self.current_session_id = None
                else:
                    print(f"❌ 删除失败: {result['error']}")
            else:
                print("⚠️  当前没有活跃会话")
        elif command.startswith('/delete '):
            # 删除指定会话ID
            session_id = command.replace('/delete ', '').strip()
            if session_id:
                print(f"🗑️  删除会话: {session_id}")
                result = await self.delete_session(session_id)
                if "error" not in result:
                    print("✅ 会话删除成功")
                    # 如果删除的是当前会话，清空当前会话ID
                    if self.current_session_id == session_id:
                        self.current_session_id = None
                else:
                    print(f"❌ 删除失败: {result['error']}")
            else:
                print("⚠️  请提供要删除的会话ID")
        elif command == '/supplier':
            print("🏢 当前供应商信息:")
            print(f"   公司名称: {self.supplier_info.get('company_name', 'N/A')}")
            new_name = input("请输入新的供应商名称 (回车跳过): ").strip()
            if new_name:
                self.supplier_info['company_name'] = new_name
                print(f"✅ 供应商信息已更新: {new_name}")
        else:
            print(f"❓ 未知命令: {command}")
            print("💡 输入 /help 查看可用命令")
        
        return True
    
    async def run(self):
        """运行交互式测试"""
        self.print_banner()
        
        while True:
            try:
                # 显示当前会话提示
                session_prompt = f"({self.current_session_id[-8:]})" if self.current_session_id else "(新会话)"
                user_input = input(f"🎯 {session_prompt} 请输入消息 (或输入 /help 查看命令): ").strip()
                
                if not user_input:
                    continue
                
                # 处理命令
                if user_input.startswith('/'):
                    should_continue = await self.handle_command(user_input)
                    if not should_continue:
                        break
                    continue
                
                # 发送消息
                print(f"📨 发送消息: {user_input}")
                result = await self.send_message(user_input)
                self.print_response(result)
                
                # 检查是否需要转人工
                if result.get('need_human'):
                    print("\n🚨 系统检测到需要转人工，自动结束对话...")
                    if self.current_session_id:
                        print(f"🗑️  自动删除会话: {self.current_session_id}")
                        delete_result = await self.delete_session(self.current_session_id)
                        if "error" not in delete_result:
                            print("✅ 会话已自动删除")
                        else:
                            print(f"❌ 自动删除失败: {delete_result['error']}")
                        self.current_session_id = None
                    print("💡 可以开始新的对话")
                
                print("-" * 50)
                
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，正在退出...")
                break
            except EOFError:
                print("\n\n👋 输入结束，正在退出...")
                break
            except Exception as e:
                print(f"❌ 发生错误: {str(e)}")
                continue
        
        # 清理当前会话（可选）
        if self.current_session_id:
            print(f"\n🗑️  是否删除当前会话 {self.current_session_id}? (y/N): ", end="")
            try:
                choice = input().strip().lower()
                if choice in ['y', 'yes']:
                    result = await self.delete_session(self.current_session_id)
                    if "error" not in result:
                        print("✅ 会话已删除")
                    else:
                        print(f"❌ 删除失败: {result['error']}")
                else:
                    print("ℹ️  会话已保留")
            except (KeyboardInterrupt, EOFError):
                print("\n会话已保留")
        
        print("👋 再见！")


async def main():
    """主函数"""
    # 可以通过命令行参数指定API地址
    base_url = "http://localhost:8650"
    # base_url = "http://*************:8650"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    async with InteractiveAPITester(base_url) as tester:
        await tester.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序异常: {str(e)}")