"""
物料图纸服务类
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from app.models.component_document import ComponentDocument
from app.database import db_manager

class ComponentDocumentService:
    """物料图纸服务类"""
    
    def __init__(self, db: Optional[Session] = None, db_name: str = "second"):
        self.db = db
        self.db_name = db_name
    
    def create_document(self, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新的物料图纸，返回字典格式"""
        if self.db:
            document = ComponentDocument.from_dict(document_data)
            self.db.add(document)
            self.db.commit()
            self.db.refresh(document)
            return document.to_dict()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                document = ComponentDocument.from_dict(document_data)
                db.add(document)
                db.commit()
                db.refresh(document)
                return document.to_dict()  # 在会话关闭前转换为字典
    
    def get_document_by_id(self, document_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取物料图纸，返回字典格式"""
        if self.db:
            document = self.db.query(ComponentDocument).filter(ComponentDocument.id == document_id).first()
            return document.to_dict() if document else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                document = db.query(ComponentDocument).filter(ComponentDocument.id == document_id).first()
                return document.to_dict() if document else None  # 在会话关闭前转换为字典
    
    def get_documents_by_component_id(self, component_id: int) -> List[Dict[str, Any]]:
        """根据物料ID获取所有相关图纸，返回字典列表"""
        if self.db:
            documents = self.db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).all()
            return [document.to_dict() for document in documents]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                documents = db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).all()
                return [document.to_dict() for document in documents]  # 在会话关闭前转换为字典列表
    
    def get_documents_by_name(self, document_name: str) -> List[Dict[str, Any]]:
        """根据图纸名称搜索物料图纸，返回字典列表"""
        if self.db:
            documents = self.db.query(ComponentDocument).filter(ComponentDocument.document_name.like(f"%{document_name}%")).all()
            return [document.to_dict() for document in documents]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                documents = db.query(ComponentDocument).filter(ComponentDocument.document_name.like(f"%{document_name}%")).all()
                return [document.to_dict() for document in documents]  # 在会话关闭前转换为字典列表
    
    def get_document_by_oss_path(self, oss_path: str) -> Optional[Dict[str, Any]]:
        """根据OSS路径获取物料图纸，返回字典格式"""
        if self.db:
            document = self.db.query(ComponentDocument).filter(ComponentDocument.oss_path == oss_path).first()
            return document.to_dict() if document else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                document = db.query(ComponentDocument).filter(ComponentDocument.oss_path == oss_path).first()
                return document.to_dict() if document else None  # 在会话关闭前转换为字典
    
    def search_documents(self, 
                        component_id: Optional[int] = None,
                        document_name: Optional[str] = None,
                        description: Optional[str] = None,
                        limit: int = 100,
                        offset: int = 0) -> List[Dict[str, Any]]:
        """复合条件搜索物料图纸，返回字典列表"""
        
        def _build_query(db: Session):
            query = db.query(ComponentDocument)
            
            # 构建查询条件
            conditions = []
            
            if component_id:
                conditions.append(ComponentDocument.component_id == component_id)
            
            if document_name:
                conditions.append(ComponentDocument.document_name.like(f"%{document_name}%"))
            
            if description:
                conditions.append(ComponentDocument.description.like(f"%{description}%"))
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(desc(ComponentDocument.created_at)).offset(offset).limit(limit)
        
        if self.db:
            documents = _build_query(self.db).all()
            return [document.to_dict() for document in documents]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                documents = _build_query(db).all()
                return [document.to_dict() for document in documents]  # 在会话关闭前转换为字典列表
    
    def update_document(self, document_id: int, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新物料图纸，返回字典格式"""
        if self.db:
            document = self.db.query(ComponentDocument).filter(ComponentDocument.id == document_id).first()
            if document:
                document.update_from_dict(update_data)
                self.db.commit()
                self.db.refresh(document)
                return document.to_dict()
            return None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                document = db.query(ComponentDocument).filter(ComponentDocument.id == document_id).first()
                if document:
                    document.update_from_dict(update_data)
                    db.commit()
                    db.refresh(document)
                    return document.to_dict()  # 在会话关闭前转换为字典
                return None
    
    def delete_document(self, document_id: int) -> bool:
        """删除物料图纸"""
        if self.db:
            document = self.db.query(ComponentDocument).filter(ComponentDocument.id == document_id).first()
            if document:
                self.db.delete(document)
                self.db.commit()
                return True
            return False
        else:
            with db_manager.get_db_context(self.db_name) as db:
                document = db.query(ComponentDocument).filter(ComponentDocument.id == document_id).first()
                if document:
                    db.delete(document)
                    db.commit()
                    return True
                return False
    
    def delete_documents_by_component_id(self, component_id: int) -> int:
        """删除指定物料的所有图纸，返回删除的数量"""
        if self.db:
            count = self.db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).count()
            self.db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).delete()
            self.db.commit()
            return count
        else:
            with db_manager.get_db_context(self.db_name) as db:
                count = db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).count()
                db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).delete()
                db.commit()
                return count
    
    def get_document_count(self) -> int:
        """获取物料图纸总数"""
        if self.db:
            return self.db.query(ComponentDocument).count()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                return db.query(ComponentDocument).count()
    
    def get_document_count_by_component(self, component_id: int) -> int:
        """获取指定物料的图纸数量"""
        if self.db:
            return self.db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).count()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                return db.query(ComponentDocument).filter(ComponentDocument.component_id == component_id).count()
    
    def get_all_documents(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取所有物料图纸，支持分页"""
        if self.db:
            documents = self.db.query(ComponentDocument).order_by(desc(ComponentDocument.created_at)).offset(offset).limit(limit).all()
            return [document.to_dict() for document in documents]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                documents = db.query(ComponentDocument).order_by(desc(ComponentDocument.created_at)).offset(offset).limit(limit).all()
                return [document.to_dict() for document in documents]  # 在会话关闭前转换为字典列表 