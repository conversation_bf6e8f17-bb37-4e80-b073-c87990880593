"""
物料组件服务类
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from app.models.component import Component
from app.database import db_manager

class ComponentService:
    """物料组件服务类"""
    
    def __init__(self, db: Optional[Session] = None, db_name: str = "second"):
        self.db = db
        self.db_name = db_name
    
    def create_component(self, component_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新的物料组件，返回字典格式"""
        if self.db:
            component = Component.from_dict(component_data)
            self.db.add(component)
            self.db.commit()
            self.db.refresh(component)
            return component.to_dict()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                component = Component.from_dict(component_data)
                db.add(component)
                db.commit()
                db.refresh(component)
                return component.to_dict()  # 在会话关闭前转换为字典
    
    def get_component_by_id(self, component_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取物料组件，返回字典格式"""
        if self.db:
            component = self.db.query(Component).filter(Component.id == component_id).first()
            return component.to_dict() if component else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                component = db.query(Component).filter(Component.id == component_id).first()
                return component.to_dict() if component else None  # 在会话关闭前转换为字典
    
    def get_components_by_name(self, name: str) -> List[Dict[str, Any]]:
        """根据名称搜索物料组件，返回字典列表"""
        if self.db:
            components = self.db.query(Component).filter(Component.name.like(f"%{name}%")).all()
            return [component.to_dict() for component in components]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                components = db.query(Component).filter(Component.name.like(f"%{name}%")).all()
                return [component.to_dict() for component in components]  # 在会话关闭前转换为字典列表
    
    def get_components_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据类别获取物料组件，返回字典列表"""
        if self.db:
            components = self.db.query(Component).filter(Component.category == category).all()
            return [component.to_dict() for component in components]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                components = db.query(Component).filter(Component.category == category).all()
                return [component.to_dict() for component in components]  # 在会话关闭前转换为字典列表
    
    def get_components_by_company(self, client_company_id: int) -> List[Dict[str, Any]]:
        """根据客户公司ID获取物料组件，返回字典列表"""
        if self.db:
            components = self.db.query(Component).filter(Component.client_company_id == client_company_id).all()
            return [component.to_dict() for component in components]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                components = db.query(Component).filter(Component.client_company_id == client_company_id).all()
                return [component.to_dict() for component in components]  # 在会话关闭前转换为字典列表
    
    def get_component_by_code(self, component_code: str) -> Optional[Dict[str, Any]]:
        """根据物料编码获取物料组件，返回字典格式"""
        if self.db:
            component = self.db.query(Component).filter(Component.component_code == component_code).first()
            return component.to_dict() if component else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                component = db.query(Component).filter(Component.component_code == component_code).first()
                return component.to_dict() if component else None  # 在会话关闭前转换为字典
    
    def get_components_by_code_pattern(self, code_pattern: str) -> List[Dict[str, Any]]:
        """根据物料编码模糊搜索物料组件，返回字典列表"""
        if self.db:
            components = self.db.query(Component).filter(Component.component_code.like(f"%{code_pattern}%")).all()
            return [component.to_dict() for component in components]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                components = db.query(Component).filter(Component.component_code.like(f"%{code_pattern}%")).all()
                return [component.to_dict() for component in components]  # 在会话关闭前转换为字典列表
    
    def search_components(self, 
                         name: Optional[str] = None,
                         category: Optional[str] = None,
                         tag: Optional[str] = None,
                         client_company_id: Optional[int] = None,
                         min_price: Optional[float] = None,
                         max_price: Optional[float] = None,
                         limit: int = 100,
                         offset: int = 0) -> List[Dict[str, Any]]:
        """复合条件搜索物料组件，返回字典列表"""
        
        def _build_query(db: Session):
            query = db.query(Component)
            
            # 构建查询条件
            conditions = []
            
            if name:
                conditions.append(Component.name.like(f"%{name}%"))
            
            if category:
                conditions.append(Component.category == category)
            
            if tag:
                conditions.append(Component.tag == tag)
            
            if client_company_id:
                conditions.append(Component.client_company_id == client_company_id)
            
            if min_price is not None:
                conditions.append(Component.price >= min_price)
            
            if max_price is not None:
                conditions.append(Component.price <= max_price)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(desc(Component.created_at)).offset(offset).limit(limit)
        
        if self.db:
            components = _build_query(self.db).all()
            return [component.to_dict() for component in components]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                components = _build_query(db).all()
                return [component.to_dict() for component in components]  # 在会话关闭前转换为字典列表
    
    def update_component(self, component_id: int, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新物料组件，返回字典格式"""
        if self.db:
            component = self.db.query(Component).filter(Component.id == component_id).first()
            if component:
                component.update_from_dict(update_data)
                self.db.commit()
                self.db.refresh(component)
                return component.to_dict()
            return None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                component = db.query(Component).filter(Component.id == component_id).first()
                if component:
                    component.update_from_dict(update_data)
                    db.commit()
                    db.refresh(component)
                    return component.to_dict()  # 在会话关闭前转换为字典
                return None
    
    def delete_component(self, component_id: int) -> bool:
        """删除物料组件"""
        if self.db:
            component = self.db.query(Component).filter(Component.id == component_id).first()
            if component:
                self.db.delete(component)
                self.db.commit()
                return True
            return False
        else:
            with db_manager.get_db_context(self.db_name) as db:
                component = db.query(Component).filter(Component.id == component_id).first()
                if component:
                    db.delete(component)
                    db.commit()
                    return True
                return False
    
    def get_component_count(self) -> int:
        """获取物料组件总数"""
        if self.db:
            return self.db.query(Component).count()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                return db.query(Component).count()
    
    def get_categories(self) -> List[str]:
        """获取所有物料类别"""
        if self.db:
            result = self.db.query(Component.category).distinct().filter(Component.category.isnot(None)).all()
            return [row[0] for row in result]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                result = db.query(Component.category).distinct().filter(Component.category.isnot(None)).all()
                return [row[0] for row in result]
