"""
组件供应商服务类
"""

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, distinct
from app.models.component_supplier import ComponentSupplier
from app.database import db_manager

class ComponentSupplierService:
    """组件供应商服务类"""
    
    def __init__(self, db: Optional[Session] = None, db_name: str = "second"):
        self.db = db
        self.db_name = db_name
    
    def get_by_component_id(self, component_id: int) -> Optional[Dict[str, Any]]:
        """根据组件ID获取组件供应商记录，返回字典格式"""
        if self.db:
            record = self.db.query(ComponentSupplier).filter(ComponentSupplier.component_id == component_id).first()
            return record.to_dict() if record else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                record = db.query(ComponentSupplier).filter(ComponentSupplier.component_id == component_id).first()
                return record.to_dict() if record else None
    
    def get_categories_by_recommended_supplier(self, recommended_supplier: str) -> List[Dict[str, Any]]:
        """
        根据推荐供应商名称查找对应的分类组合
        返回包含 primary_category 和 secondary_category 的字典列表
        """
        if self.db:
            results = self.db.query(
                ComponentSupplier.primary_category,
                ComponentSupplier.secondary_category,
                ComponentSupplier.category_feature,
                ComponentSupplier.category_id
            ).filter(
                ComponentSupplier.recommended_supplier == recommended_supplier
            ).distinct().all()
            
            return [
                {
                    'primary_category': result.primary_category,
                    'secondary_category': result.secondary_category,
                    'category_feature': result.category_feature,
                    'category_id': result.category_id
                }
                for result in results
            ]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                results = db.query(
                    ComponentSupplier.primary_category,
                    ComponentSupplier.secondary_category,
                    ComponentSupplier.category_feature,
                    ComponentSupplier.category_id
                ).filter(
                    ComponentSupplier.recommended_supplier == recommended_supplier
                ).distinct().all()
                
                return [
                    {
                        'primary_category': result.primary_category,
                        'secondary_category': result.secondary_category,
                        'category_feature': result.category_feature,
                        'category_id': result.category_id
                    }
                    for result in results
                ]
    
    def get_suppliers_by_category(self, primary_category: str, secondary_category: Optional[str] = None) -> List[Dict[str, Any]]:
        """根据分类查找对应的供应商"""
        def _build_query(db: Session):
            query = db.query(ComponentSupplier).filter(
                ComponentSupplier.primary_category == primary_category
            )
            
            if secondary_category:
                query = query.filter(ComponentSupplier.secondary_category == secondary_category)
            
            return query
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def search_by_component(self, component_name: Optional[str] = None, 
                          component_spec: Optional[str] = None,
                          component_tag: Optional[str] = None,
                          limit: int = 100,
                          offset: int = 0) -> List[Dict[str, Any]]:
        """根据组件信息搜索"""
        def _build_query(db: Session):
            query = db.query(ComponentSupplier)
            
            conditions = []
            
            if component_name:
                conditions.append(ComponentSupplier.component_name.like(f"%{component_name}%"))
            
            if component_spec:
                conditions.append(ComponentSupplier.component_spec.like(f"%{component_spec}%"))
            
            if component_tag:
                conditions.append(ComponentSupplier.component_tag.like(f"%{component_tag}%"))
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(ComponentSupplier.component_id).offset(offset).limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def search_by_supplier(self, 
                          recommended_supplier: Optional[str] = None,
                          original_supplier: Optional[str] = None,
                          supplier_region: Optional[str] = None,
                          supplier_status: Optional[str] = None,
                          limit: int = 100,
                          offset: int = 0) -> List[Dict[str, Any]]:
        """根据供应商信息搜索"""
        def _build_query(db: Session):
            query = db.query(ComponentSupplier)
            
            conditions = []
            
            if recommended_supplier:
                conditions.append(ComponentSupplier.recommended_supplier.like(f"%{recommended_supplier}%"))
            
            if original_supplier:
                conditions.append(ComponentSupplier.original_supplier.like(f"%{original_supplier}%"))
            
            if supplier_region:
                conditions.append(ComponentSupplier.supplier_region.like(f"%{supplier_region}%"))
            
            if supplier_status:
                conditions.append(ComponentSupplier.supplier_status == supplier_status)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(ComponentSupplier.component_id).offset(offset).limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def get_supplier_statistics(self, recommended_supplier: str) -> Dict[str, Any]:
        """获取指定供应商的统计信息"""
        if self.db:
            # 总记录数
            total_count = self.db.query(ComponentSupplier).filter(
                ComponentSupplier.recommended_supplier == recommended_supplier
            ).count()
            
            # 不同分类数
            category_count = self.db.query(
                ComponentSupplier.primary_category,
                ComponentSupplier.secondary_category
            ).filter(
                ComponentSupplier.recommended_supplier == recommended_supplier
            ).distinct().count()
            
            # 不同组件数
            component_count = self.db.query(
                ComponentSupplier.component_name
            ).filter(
                ComponentSupplier.recommended_supplier == recommended_supplier
            ).distinct().count()
            
            return {
                'supplier_name': recommended_supplier,
                'total_records': total_count,
                'category_count': category_count,
                'component_count': component_count
            }
        else:
            with db_manager.get_db_context(self.db_name) as db:
                # 总记录数
                total_count = db.query(ComponentSupplier).filter(
                    ComponentSupplier.recommended_supplier == recommended_supplier
                ).count()
                
                # 不同分类数
                category_count = db.query(
                    ComponentSupplier.primary_category,
                    ComponentSupplier.secondary_category
                ).filter(
                    ComponentSupplier.recommended_supplier == recommended_supplier
                ).distinct().count()
                
                # 不同组件数
                component_count = db.query(
                    ComponentSupplier.component_name
                ).filter(
                    ComponentSupplier.recommended_supplier == recommended_supplier
                ).distinct().count()
                
                return {
                    'supplier_name': recommended_supplier,
                    'total_records': total_count,
                    'category_count': category_count,
                    'component_count': component_count
                }
    
    def get_all_recommended_suppliers(self) -> List[str]:
        """获取所有推荐供应商列表"""
        if self.db:
            results = self.db.query(ComponentSupplier.recommended_supplier).distinct().all()
            return [result.recommended_supplier for result in results if result.recommended_supplier]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                results = db.query(ComponentSupplier.recommended_supplier).distinct().all()
                return [result.recommended_supplier for result in results if result.recommended_supplier]
    
    def get_all_categories(self) -> List[Dict[str, str]]:
        """获取所有分类组合"""
        if self.db:
            results = self.db.query(
                ComponentSupplier.primary_category,
                ComponentSupplier.secondary_category
            ).distinct().all()
            
            return [
                {
                    'primary_category': result.primary_category,
                    'secondary_category': result.secondary_category
                }
                for result in results
            ]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                results = db.query(
                    ComponentSupplier.primary_category,
                    ComponentSupplier.secondary_category
                ).distinct().all()
                
                return [
                    {
                        'primary_category': result.primary_category,
                        'secondary_category': result.secondary_category
                    }
                    for result in results
                ]
    
    def complex_search(self,
                      product_name: Optional[str] = None,
                      primary_category: Optional[str] = None,
                      secondary_category: Optional[str] = None,
                      component_name: Optional[str] = None,
                      recommended_supplier: Optional[str] = None,
                      supplier_region: Optional[str] = None,
                      supplier_status: Optional[str] = None,
                      limit: int = 100,
                      offset: int = 0) -> List[Dict[str, Any]]:
        """复合条件搜索"""
        def _build_query(db: Session):
            query = db.query(ComponentSupplier)
            
            conditions = []
            
            if product_name:
                conditions.append(ComponentSupplier.product_name.like(f"%{product_name}%"))
            
            if primary_category:
                conditions.append(ComponentSupplier.primary_category.like(f"%{primary_category}%"))
            
            if secondary_category:
                conditions.append(ComponentSupplier.secondary_category.like(f"%{secondary_category}%"))
            
            if component_name:
                conditions.append(ComponentSupplier.component_name.like(f"%{component_name}%"))
            
            if recommended_supplier:
                conditions.append(ComponentSupplier.recommended_supplier.like(f"%{recommended_supplier}%"))
            
            if supplier_region:
                conditions.append(ComponentSupplier.supplier_region.like(f"%{supplier_region}%"))
            
            if supplier_status:
                conditions.append(ComponentSupplier.supplier_status == supplier_status)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(ComponentSupplier.component_id).offset(offset).limit(limit)
        
        if self.db:
            records = _build_query(self.db).all()
            return [record.to_dict() for record in records]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                records = _build_query(db).all()
                return [record.to_dict() for record in records]
    
    def get_supplier_phone_by_name(self, supplier_name: str) -> Optional[str]:
        """根据供应商名称获取供应商手机号"""
        if self.db:
            result = self.db.query(ComponentSupplier.supplier_phone).filter(
                ComponentSupplier.recommended_supplier == supplier_name
            ).first()
            return result.supplier_phone if result and result.supplier_phone else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                result = db.query(ComponentSupplier.supplier_phone).filter(
                    ComponentSupplier.recommended_supplier == supplier_name
                ).first()
                return result.supplier_phone if result and result.supplier_phone else None 