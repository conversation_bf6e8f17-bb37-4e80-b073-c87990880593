"""
产品分类服务类
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from app.models.product_category import ProductCategory
from app.database import db_manager

class ProductCategoryService:
    """产品分类服务类"""
    
    def __init__(self, db: Optional[Session] = None, db_name: str = "second"):
        self.db = db
        self.db_name = db_name
    
    def create_category(self, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新的产品分类，返回字典格式"""
        if self.db:
            category = ProductCategory.from_dict(category_data)
            self.db.add(category)
            self.db.commit()
            self.db.refresh(category)
            return category.to_dict()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                category = ProductCategory.from_dict(category_data)
                db.add(category)
                db.commit()
                db.refresh(category)
                return category.to_dict()  # 在会话关闭前转换为字典
    
    def get_category_by_id(self, category_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取产品分类，返回字典格式"""
        if self.db:
            category = self.db.query(ProductCategory).filter(ProductCategory.id == category_id).first()
            return category.to_dict() if category else None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                category = db.query(ProductCategory).filter(ProductCategory.id == category_id).first()
                return category.to_dict() if category else None  # 在会话关闭前转换为字典
    
    def get_categories_by_product_id(self, product_id: int) -> List[Dict[str, Any]]:
        """根据产品ID获取所有相关分类，返回字典列表"""
        if self.db:
            categories = self.db.query(ProductCategory).filter(ProductCategory.product_id == product_id).all()
            return [category.to_dict() for category in categories]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                categories = db.query(ProductCategory).filter(ProductCategory.product_id == product_id).all()
                return [category.to_dict() for category in categories]  # 在会话关闭前转换为字典列表
    
    def get_categories_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据分类名称搜索产品分类，返回字典列表"""
        if self.db:
            categories = self.db.query(ProductCategory).filter(ProductCategory.category.like(f"%{category}%")).all()
            return [cat.to_dict() for cat in categories]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                categories = db.query(ProductCategory).filter(ProductCategory.category.like(f"%{category}%")).all()
                return [cat.to_dict() for cat in categories]  # 在会话关闭前转换为字典列表
    
    def get_categories_by_prev(self, prev_id: int) -> List[Dict[str, Any]]:
        """根据上级分类ID获取下级分类，返回字典列表"""
        if self.db:
            categories = self.db.query(ProductCategory).filter(ProductCategory.prev == prev_id).all()
            return [category.to_dict() for category in categories]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                categories = db.query(ProductCategory).filter(ProductCategory.prev == prev_id).all()
                return [category.to_dict() for category in categories]  # 在会话关闭前转换为字典列表
    
    def get_first_level_categories(self, product_id: int) -> List[Dict[str, Any]]:
        """获取指定产品的一级分类（prev=0），返回字典列表"""
        if self.db:
            categories = self.db.query(ProductCategory).filter(
                and_(ProductCategory.product_id == product_id, ProductCategory.prev == 0)
            ).all()
            return [category.to_dict() for category in categories]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                categories = db.query(ProductCategory).filter(
                    and_(ProductCategory.product_id == product_id, ProductCategory.prev == 0)
                ).all()
                return [category.to_dict() for category in categories]  # 在会话关闭前转换为字典列表
    
    def search_categories(self, 
                         product_id: Optional[int] = None,
                         category: Optional[str] = None,
                         prev: Optional[int] = None,
                         feature: Optional[str] = None,
                         limit: int = 100,
                         offset: int = 0) -> List[Dict[str, Any]]:
        """复合条件搜索产品分类，返回字典列表"""
        
        def _build_query(db: Session):
            query = db.query(ProductCategory)
            
            # 构建查询条件
            conditions = []
            
            if product_id:
                conditions.append(ProductCategory.product_id == product_id)
            
            if category:
                conditions.append(ProductCategory.category.like(f"%{category}%"))
            
            if prev is not None:
                conditions.append(ProductCategory.prev == prev)
            
            if feature:
                conditions.append(ProductCategory.feature.like(f"%{feature}%"))
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(ProductCategory.id).offset(offset).limit(limit)
        
        if self.db:
            categories = _build_query(self.db).all()
            return [category.to_dict() for category in categories]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                categories = _build_query(db).all()
                return [category.to_dict() for category in categories]  # 在会话关闭前转换为字典列表
    
    def update_category(self, category_id: int, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新产品分类，返回字典格式"""
        if self.db:
            category = self.db.query(ProductCategory).filter(ProductCategory.id == category_id).first()
            if category:
                category.update_from_dict(update_data)
                self.db.commit()
                self.db.refresh(category)
                return category.to_dict()
            return None
        else:
            with db_manager.get_db_context(self.db_name) as db:
                category = db.query(ProductCategory).filter(ProductCategory.id == category_id).first()
                if category:
                    category.update_from_dict(update_data)
                    db.commit()
                    db.refresh(category)
                    return category.to_dict()  # 在会话关闭前转换为字典
                return None
    
    def delete_category(self, category_id: int) -> bool:
        """删除产品分类"""
        if self.db:
            category = self.db.query(ProductCategory).filter(ProductCategory.id == category_id).first()
            if category:
                self.db.delete(category)
                self.db.commit()
                return True
            return False
        else:
            with db_manager.get_db_context(self.db_name) as db:
                category = db.query(ProductCategory).filter(ProductCategory.id == category_id).first()
                if category:
                    db.delete(category)
                    db.commit()
                    return True
                return False
    
    def delete_categories_by_product_id(self, product_id: int) -> int:
        """删除指定产品的所有分类，返回删除的数量"""
        if self.db:
            count = self.db.query(ProductCategory).filter(ProductCategory.product_id == product_id).count()
            self.db.query(ProductCategory).filter(ProductCategory.product_id == product_id).delete()
            self.db.commit()
            return count
        else:
            with db_manager.get_db_context(self.db_name) as db:
                count = db.query(ProductCategory).filter(ProductCategory.product_id == product_id).count()
                db.query(ProductCategory).filter(ProductCategory.product_id == product_id).delete()
                db.commit()
                return count
    
    def get_category_count(self) -> int:
        """获取产品分类总数"""
        if self.db:
            return self.db.query(ProductCategory).count()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                return db.query(ProductCategory).count()
    
    def get_category_count_by_product(self, product_id: int) -> int:
        """获取指定产品的分类数量"""
        if self.db:
            return self.db.query(ProductCategory).filter(ProductCategory.product_id == product_id).count()
        else:
            with db_manager.get_db_context(self.db_name) as db:
                return db.query(ProductCategory).filter(ProductCategory.product_id == product_id).count()
    
    def get_all_categories(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取所有产品分类，支持分页"""
        if self.db:
            categories = self.db.query(ProductCategory).order_by(ProductCategory.id).offset(offset).limit(limit).all()
            return [category.to_dict() for category in categories]
        else:
            with db_manager.get_db_context(self.db_name) as db:
                categories = db.query(ProductCategory).order_by(ProductCategory.id).offset(offset).limit(limit).all()
                return [category.to_dict() for category in categories]  # 在会话关闭前转换为字典列表 