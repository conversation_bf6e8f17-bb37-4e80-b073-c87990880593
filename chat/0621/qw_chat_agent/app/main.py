"""
FastAPI主应用
"""
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from contextlib import asynccontextmanager
import logging
import sys
from pathlib import Path
from app.utils.app_lifecycle import setup_app_lifecycle
from config.sys_config import SERVICE_PORT, SERVICE_HOST

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("供应商询价系统启动中...")
    
    try:
        # 检查数据库连接
        from app.database import check_database_connection
        if not check_database_connection():
            logger.error("数据库连接失败，请检查数据库配置")
            # 在开发环境中继续启动，但记录警告
            logger.warning("数据库连接失败，某些功能可能不可用")
        else:
            logger.info("数据库连接正常")
    except Exception as e:
        logger.error(f"启动检查失败: {str(e)}")
        logger.warning("继续启动服务，但某些功能可能不可用")
    
    # 设置应用生命周期管理
    setup_app_lifecycle()
    
    logger.info("供应商询价系统启动完成")
    yield
    
    # 关闭时的清理
    logger.info("供应商询价系统关闭中...")


# 创建FastAPI应用
app = FastAPI(
    title="供应商询价系统API",
    description="基于AI的供应商询价和报价管理系统",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"全局异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "服务器内部错误", "error": str(exc)}
    )


# 直接注册路由
try:
    from app.api import auth, inquiry, oss, supplier, supplier_match
    from config.auth_config import API_V1_PREFIX
    
    # 注册路由
    app.include_router(auth.router, prefix=API_V1_PREFIX)
    app.include_router(inquiry.router, prefix=API_V1_PREFIX)
    app.include_router(oss.router, prefix=API_V1_PREFIX)
    app.include_router(supplier.router, prefix=API_V1_PREFIX)
    app.include_router(supplier_match.router, prefix=API_V1_PREFIX)
    
    logger.info("API路由注册完成")
except Exception as e:
    logger.error(f"路由注册失败: {str(e)}")
    # 打印详细错误信息
    import traceback
    traceback.print_exc()


@app.get("/", summary="根路径")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "供应商询价系统API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health", summary="健康检查")
async def health_check():
    """健康检查接口"""
    try:
        from app.database import check_database_connection
        db_status = check_database_connection()
        
        return {
            "status": "healthy" if db_status else "degraded",
            "message": "供应商询价系统运行正常",
            "database": "connected" if db_status else "disconnected"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"健康检查失败: {str(e)}"
        }


# 添加一个调试路由来查看所有注册的路由
@app.get("/debug/routes", summary="调试：查看所有路由")
async def debug_routes():
    """调试用：查看所有注册的路由"""
    routes = []
    for route in app.routes:
        if hasattr(route, 'methods') and hasattr(route, 'path'):
            routes.append({
                "path": route.path,
                "methods": list(route.methods),
                "name": getattr(route, 'name', 'unknown')
            })
    return {"routes": routes}


# @app.on_event("startup")
# async def startup_event():
#     """应用启动事件"""
#     logger.info("供应商询价系统启动中...")
#
#     # 检查数据库连接
#     if check_database_connection():
#         logger.info("数据库连接正常")
#     else:
#         logger.error("数据库连接失败")
#         raise Exception("数据库连接失败，无法启动应用")
#
#     # 设置应用生命周期管理
#     setup_app_lifecycle()
#
#     logger.info("供应商询价系统启动完成")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=SERVICE_HOST,
        port=SERVICE_PORT,
        reload=True,
        log_level="info"
    ) 