"""
Redis会话管理器

负责会话数据在Redis中的存储和管理，包括：
- 会话上下文的序列化和反序列化
- 消息历史的存储
- 会话列表管理
- 过期会话清理
"""

import json
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from abc import ABC, abstractmethod

from pydantic import BaseModel

# 可选的Redis支持
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class SessionStorageInterface(ABC):
    """会话存储接口 - 抽象基类"""
    
    @abstractmethod
    async def save_session(self, session_context: 'SessionContext') -> bool:
        """保存会话上下文"""
        pass
    
    @abstractmethod
    async def load_session(self, session_id: str) -> Optional['SessionContext']:
        """加载会话上下文"""
        pass
    
    @abstractmethod
    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        pass
    
    @abstractmethod
    async def list_sessions(self) -> List[str]:
        """列出所有会话ID"""
        pass
    
    @abstractmethod
    async def clear_all_sessions(self) -> Dict[str, Any]:
        """清理所有会话"""
        pass


class RedisSessionManager(SessionStorageInterface):
    """Redis会话管理器"""
    
    def __init__(self, config: Dict[str, Any] = None, logger=None):
        self.config = config or {}
        self.logger = logger
        self.redis_client: Optional[redis.Redis] = None
        self.is_available = REDIS_AVAILABLE
        
        # Redis键前缀配置
        self.session_prefix = self.config.get("session_prefix", "session")
        self.message_prefix = self.config.get("message_prefix", "messages")
        self.session_list_key = self.config.get("session_list_key", "session_list")
        
        if self.is_available:
            self._initialize_redis()
    
    def _initialize_redis(self):
        """初始化Redis客户端"""
        try:
            # 支持多种连接方式
            if "url" in self.config:
                # 方式1: 使用URL连接
                self.redis_client = redis.from_url(
                    self.config["url"],
                    decode_responses=True,
                    socket_timeout=self.config.get("timeout", 5),
                    socket_connect_timeout=self.config.get("connect_timeout", 5),
                    retry_on_timeout=True
                )
            else:
                # 方式2: 使用参数连接（支持你的配置格式）
                self.redis_client = redis.Redis(
                    host=self.config.get("host", "localhost"),
                    port=self.config.get("port", 6379),
                    db=self.config.get("db", 0),
                    password=self.config.get("password"),
                    decode_responses=True,
                    socket_timeout=self.config.get("timeout", 5),
                    socket_connect_timeout=self.config.get("connect_timeout", 5),
                    retry_on_timeout=True,
                    max_connections=self.config.get("max_connections", 20),
                    health_check_interval=self.config.get("health_check_interval", 30),
                )
            
            self.log_info("Redis客户端初始化成功")
        except Exception as e:
            self.log_error(f"Redis客户端初始化失败: {str(e)}")
            self.is_available = False
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        if self.logger:
            self.logger.info(f"[RedisSessionManager] {message}", **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """记录错误日志"""
        if self.logger:
            self.logger.error(f"[RedisSessionManager] {message}", **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """记录调试日志"""
        if self.logger:
            self.logger.debug(f"[RedisSessionManager] {message}", **kwargs)
    
    # Redis键管理
    def _get_session_key(self, session_id: str) -> str:
        """获取会话在Redis中的键"""
        return f"{self.session_prefix}:{session_id}"
    
    def _get_message_history_key(self, session_id: str) -> str:
        """获取消息历史在Redis中的键"""
        return f"{self.message_prefix}:{session_id}"
    
    async def save_session(self, session_context: 'SessionContext') -> bool:
        """保存会话到Redis（不设置过期时间，手动清理）"""
        if not self.is_available or not self.redis_client:
            return False
        
        try:
            # 分离消息历史和会话上下文
            messages = session_context.message_history
            session_context.message_history = []  # 临时清空
            
            # 保存会话上下文（JSON格式，无TTL）
            session_key = self._get_session_key(session_context.session_id)
            session_data = session_context.json()
            await self.redis_client.set(session_key, session_data)
            
            # 保存消息历史（JSON格式，无TTL）
            if messages:
                message_key = self._get_message_history_key(session_context.session_id)
                message_data = json.dumps([msg.dict() for msg in messages], ensure_ascii=False, default=str)
                await self.redis_client.set(message_key, message_data)
            
            # 添加到会话列表
            await self.redis_client.sadd(self.session_list_key, session_context.session_id)
            
            # 恢复消息历史
            session_context.message_history = messages
            
            self.log_debug(f"会话保存成功: {session_context.session_id}")
            return True
            
        except Exception as e:
            self.log_error(f"保存会话到Redis失败: {str(e)}")
            return False
    
    async def load_session(self, session_id: str) -> Optional['SessionContext']:
        """从Redis加载会话"""
        if not self.is_available or not self.redis_client:
            return None
        
        try:
            # 动态导入避免循环导入
            from app.core.orchestrator import SessionContext
            from app.core.base_agent import AgentMessage
            
            # 加载会话上下文
            session_key = self._get_session_key(session_id)
            session_data = await self.redis_client.get(session_key)
            
            if not session_data:
                return None
            
            session_dict = json.loads(session_data)
            
            # 处理datetime字段
            if 'created_at' in session_dict:
                session_dict['created_at'] = datetime.fromisoformat(session_dict['created_at'])
            if 'updated_at' in session_dict:
                session_dict['updated_at'] = datetime.fromisoformat(session_dict['updated_at'])
            
            session_context = SessionContext(**session_dict)
            
            # 加载消息历史
            message_key = self._get_message_history_key(session_id)
            message_data = await self.redis_client.get(message_key)
            
            if message_data:
                messages_dict = json.loads(message_data)
                session_context.message_history = []
                for msg_dict in messages_dict:
                    # 处理datetime字段
                    if 'timestamp' in msg_dict:
                        msg_dict['timestamp'] = datetime.fromisoformat(msg_dict['timestamp'])
                    session_context.message_history.append(AgentMessage(**msg_dict))
            
            self.log_debug(f"会话加载成功: {session_id}")
            return session_context
            
        except Exception as e:
            self.log_error(f"从Redis加载会话失败: {str(e)}")
            return None
    
    async def delete_session(self, session_id: str) -> bool:
        """从Redis删除会话"""
        if not self.is_available or not self.redis_client:
            return False
        
        try:
            session_key = self._get_session_key(session_id)
            message_key = self._get_message_history_key(session_id)
            
            # 删除会话数据
            await self.redis_client.delete(session_key, message_key)
            
            # 从会话列表中移除
            await self.redis_client.srem(self.session_list_key, session_id)
            
            self.log_debug(f"会话删除成功: {session_id}")
            return True
            
        except Exception as e:
            self.log_error(f"从Redis删除会话失败: {str(e)}")
            return False
    
    async def list_sessions(self) -> List[str]:
        """获取Redis中所有会话ID"""
        if not self.is_available or not self.redis_client:
            return []
        
        try:
            session_ids = await self.redis_client.smembers(self.session_list_key)
            return list(session_ids) if session_ids else []
        except Exception as e:
            self.log_error(f"获取Redis会话列表失败: {str(e)}")
            return []
    
    async def clear_all_sessions(self, save_to_database: bool = True) -> Dict[str, Any]:
        """清理Redis中的所有会话"""
        if not self.is_available or not self.redis_client:
            return {"error": "Redis未启用"}
        
        try:
            # 获取所有会话ID
            session_ids = await self.list_sessions()
            
            cleared_sessions = 0
            saved_sessions = 0
            errors = []
            
            for session_id in session_ids:
                try:
                    # 如果需要保存到数据库
                    if save_to_database:
                        session_context = await self.load_session(session_id)
                        if session_context:
                            # TODO: 保存到数据库的逻辑
                            # await self._save_session_to_database(session_context)
                            saved_sessions += 1
                            self.log_debug(f"会话已保存到数据库: {session_id}")
                    
                    # 删除Redis中的会话
                    if await self.delete_session(session_id):
                        cleared_sessions += 1
                    
                except Exception as e:
                    error_msg = f"清理会话失败 {session_id}: {str(e)}"
                    errors.append(error_msg)
                    self.log_error(error_msg)
            
            # 清理会话列表
            await self.redis_client.delete(self.session_list_key)
            
            result = {
                "success": True,
                "cleared_sessions": cleared_sessions,
                "saved_sessions": saved_sessions if save_to_database else 0,
                "errors": errors
            }
            
            self.log_info(f"Redis缓存清理完成: {result}")
            return result
            
        except Exception as e:
            error_msg = f"Redis缓存清理失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def clear_sessions_by_status(self, status_list: List[str]) -> Dict[str, Any]:
        """根据状态清理会话"""
        if not self.is_available or not self.redis_client:
            return {"error": "Redis未启用"}
        
        cleared_sessions = 0
        errors = []
        
        try:
            session_ids = await self.list_sessions()
            
            for session_id in session_ids:
                try:
                    session_context = await self.load_session(session_id)
                    if session_context and session_context.status.value in status_list:
                        if await self.delete_session(session_id):
                            cleared_sessions += 1
                            self.log_debug(f"会话已清理: {session_id} (状态: {session_context.status.value})")
                        
                except Exception as e:
                    error_msg = f"清理会话失败 {session_id}: {str(e)}"
                    errors.append(error_msg)
                    self.log_error(error_msg)
            
            result = {
                "success": True,
                "cleared_sessions": cleared_sessions,
                "target_statuses": status_list,
                "errors": errors
            }
            
            self.log_info(f"按状态清理会话完成: {result}")
            return result
            
        except Exception as e:
            error_msg = f"按状态清理会话失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def clear_sessions_by_age(self, max_age_hours: int = 24, exclude_statuses: List[str] = None) -> Dict[str, Any]:
        """根据时间清理会话（排除指定状态）"""
        if not self.is_available or not self.redis_client:
            return {"error": "Redis未启用"}
        
        from datetime import timedelta
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        exclude_statuses = exclude_statuses or ["human_taken"]  # 默认不清理人工接管的会话
        
        cleared_sessions = 0
        errors = []
        
        try:
            session_ids = await self.list_sessions()
            
            for session_id in session_ids:
                try:
                    session_context = await self.load_session(session_id)
                    if (session_context and 
                        session_context.updated_at < cutoff_time and 
                        session_context.status.value not in exclude_statuses):
                        
                        if await self.delete_session(session_id):
                            cleared_sessions += 1
                            self.log_debug(f"过期会话已清理: {session_id}")
                        
                except Exception as e:
                    error_msg = f"清理过期会话失败 {session_id}: {str(e)}"
                    errors.append(error_msg)
                    self.log_error(error_msg)
            
            result = {
                "success": True,
                "cleared_sessions": cleared_sessions,
                "cutoff_time": cutoff_time.isoformat(),
                "max_age_hours": max_age_hours,
                "excluded_statuses": exclude_statuses,
                "errors": errors
            }
            
            self.log_info(f"按时间清理会话完成: {result}")
            return result
            
        except Exception as e:
            error_msg = f"按时间清理会话失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def get_sessions_by_status(self, status: str) -> List[Dict[str, Any]]:
        """获取指定状态的会话列表"""
        if not self.is_available or not self.redis_client:
            return []
        
        sessions = []
        try:
            session_ids = await self.list_sessions()
            
            for session_id in session_ids:
                try:
                    session_context = await self.load_session(session_id)
                    if session_context and session_context.status.value == status:
                        sessions.append({
                            "session_id": session_id,
                            "status": session_context.status.value,
                            "created_at": session_context.created_at.isoformat(),
                            "updated_at": session_context.updated_at.isoformat(),
                            "message_count": len(session_context.message_history),
                            "current_agent": session_context.current_agent,
                            "human_takeover_reason": session_context.human_takeover_reason,
                            "human_operator_id": session_context.human_operator_id
                        })
                except Exception as e:
                    self.log_error(f"获取会话信息失败 {session_id}: {str(e)}")
            
        except Exception as e:
            self.log_error(f"获取会话列表失败: {str(e)}")
        
        return sessions
    
    async def get_session_statistics(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        if not self.is_available or not self.redis_client:
            return {"error": "Redis未启用"}
        
        try:
            session_ids = await self.list_sessions()
            total_sessions = len(session_ids)
            
            status_counts = {}
            oldest_session = None
            newest_session = None
            
            for session_id in session_ids:
                try:
                    session_context = await self.load_session(session_id)
                    if session_context:
                        # 统计状态
                        status = session_context.status.value
                        status_counts[status] = status_counts.get(status, 0) + 1
                        
                        # 找最老和最新的会话
                        if oldest_session is None or session_context.created_at < oldest_session:
                            oldest_session = session_context.created_at
                        if newest_session is None or session_context.created_at > newest_session:
                            newest_session = session_context.created_at
                            
                except Exception as e:
                    self.log_error(f"统计会话信息失败 {session_id}: {str(e)}")
            
            return {
                "total_sessions": total_sessions,
                "status_counts": status_counts,
                "oldest_session": oldest_session.isoformat() if oldest_session else None,
                "newest_session": newest_session.isoformat() if newest_session else None,
                "redis_config": {
                    "session_prefix": self.session_prefix,
                    "message_prefix": self.message_prefix,
                    "session_list_key": self.session_list_key
                }
            }
            
        except Exception as e:
            error_msg = f"获取会话统计失败: {str(e)}"
            self.log_error(error_msg)
            return {"error": error_msg}

    async def clear_expired_sessions(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """清理过期会话 - clear_sessions_by_age的别名，保持接口一致性"""
        return await self.clear_sessions_by_age(max_age_hours, exclude_statuses=["human_taken"])


class MemorySessionManager(SessionStorageInterface):
    """内存会话管理器 - 作为Redis的备选方案"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self.sessions: Dict[str, 'SessionContext'] = {}
    
    def log_info(self, message: str, **kwargs):
        if self.logger:
            self.logger.info(f"[MemorySessionManager] {message}", **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        if self.logger:
            self.logger.debug(f"[MemorySessionManager] {message}", **kwargs)
    
    async def save_session(self, session_context: 'SessionContext') -> bool:
        """保存会话到内存"""
        try:
            self.sessions[session_context.session_id] = session_context
            self.log_debug(f"会话保存到内存: {session_context.session_id}")
            return True
        except Exception as e:
            return False
    
    async def load_session(self, session_id: str) -> Optional['SessionContext']:
        """从内存加载会话"""
        return self.sessions.get(session_id)
    
    async def delete_session(self, session_id: str) -> bool:
        """从内存删除会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            self.log_debug(f"会话从内存删除: {session_id}")
            return True
        return False
    
    async def list_sessions(self) -> List[str]:
        """列出内存中所有会话ID"""
        return list(self.sessions.keys())
    
    async def clear_all_sessions(self) -> Dict[str, Any]:
        """清理内存中所有会话"""
        count = len(self.sessions)
        self.sessions.clear()
        self.log_info(f"内存会话清理完成，清理了 {count} 个会话")
        return {"success": True, "cleared_sessions": count}


def create_session_manager(config: Dict[str, Any] = None, logger=None) -> SessionStorageInterface:
    """
    创建会话管理器工厂函数
    
    Args:
        config: 配置字典，包含Redis配置
        logger: 日志记录器
        
    Returns:
        SessionStorageInterface: 会话管理器实例
    """
    config = config or {}
    use_redis = config.get("use_redis", False)

    print(f"使用Redis会话管理器: {use_redis}")
    
    if use_redis and REDIS_AVAILABLE:
        print("REDIS_AVAILABLE:", REDIS_AVAILABLE)
        redis_config = config.get("redis", {})
        return RedisSessionManager(redis_config, logger)
    else:
        if use_redis and not REDIS_AVAILABLE:
            if logger:
                logger.warning("Redis配置已启用但Redis库未安装，使用内存存储")
        print("使用内存会话管理器")
        return MemorySessionManager(logger) 