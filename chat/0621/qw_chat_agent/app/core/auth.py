"""
JWT认证工具类 - 使用PyJWT
"""
from datetime import datetime, timedelta
from typing import Optional
import jwt  # PyJWT
from passlib.context import CryptContext
from fastapi import HTTPException, status
from config.auth_config import (
    JWT_SECRET_KEY, JWT_ALGORITHM, ACCESS_TOKEN_EXPIRE_DELTA, 
    REFRESH_TOKEN_EXPIRE_DELTA, PWD_CONTEXT_SCHEMES, PWD_CONTEXT_DEPRECATED
)
from app.models.user import TokenData


class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.pwd_context = CryptContext(
            schemes=PWD_CONTEXT_SCHEMES, 
            deprecated=PWD_CONTEXT_DEPRECATED
        )
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """获取密码哈希"""
        return self.pwd_context.hash(password)
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + ACCESS_TOKEN_EXPIRE_DELTA
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        return encoded_jwt
    
    def create_refresh_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + REFRESH_TOKEN_EXPIRE_DELTA
        
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> TokenData:
        """验证令牌"""
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            user_name: str = payload.get("sub")
            user_id: int = payload.get("user_id")
            token_type_in_payload: str = payload.get("type")
            
            if user_name is None or token_type_in_payload != token_type:
                raise credentials_exception
            
            token_data = TokenData(user_name=user_name, user_id=user_id)
            return token_data
        
        except jwt.InvalidTokenError:  # PyJWT 的异常类型
            raise credentials_exception


# 创建全局认证管理器实例
auth_manager = AuthManager() 