"""
搜索供应商数据模型

对应数据库表：search_suppliers
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.sql import func
from .base import Base

class SearchSupplier(Base):
    """搜索供应商数据模型"""
    
    __tablename__ = "search_suppliers"
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    supplier_name = Column(String(100), nullable=False, comment='供应商名称', index=True)
    region = Column(String(100), comment='所在地区')
    category_id = Column(Integer, nullable=False, comment='产品分类ID', index=True)
    matching_reason = Column(Text, comment='匹配理由')
    website = Column(String(255), comment='供应商网站')
    phone = Column(String(50), comment='联系电话')
    email = Column(String(100), comment='联系邮箱')
    certifications = Column(String(255), comment='认证信息')
    notes = Column(Text, comment='备注说明')
    address = Column(String(255), comment='供应商具体所在的地址')
    created_at = Column(DateTime, nullable=False, default=func.current_timestamp(), comment='记录创建时间')
    status = Column(Integer, nullable=False, default=0, comment='0：没有验证 1：验证pending 2：网站无效 3：网站错误 4：产品不能做 5：报价过高 99：验证通过')
    chat_status = Column(String(20), default='idle', comment='聊天状态: idle, ready, chatting, completed, failed')
    is_deleted = Column(Boolean, default=False, comment='是否删除')
    
    def __repr__(self):
        return f"<SearchSupplier(id={self.id}, supplier_name='{self.supplier_name}', chat_status='{self.chat_status}', category_id={self.category_id})>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'supplier_name': self.supplier_name,
            'region': self.region,
            'category_id': self.category_id,
            'matching_reason': self.matching_reason,
            'website': self.website,
            'phone': self.phone,
            'email': self.email,
            'certifications': self.certifications,
            'notes': self.notes,
            'address': self.address,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'status': self.status,
            'chat_status': self.chat_status,
            'is_deleted': self.is_deleted
        }
    
    def get_contact_info(self):
        """获取联系信息"""
        return {
            'phone': self.phone,
            'email': self.email,
            'website': self.website,
            'address': self.address
        }
    
    def get_status_info(self):
        """获取状态信息"""
        return {
            'status': self.status,
            'chat_status': self.chat_status,
            'is_deleted': self.is_deleted
        }
    
    # 定义聊天状态常量
    CHAT_STATUS_IDLE = 'idle'
    CHAT_STATUS_READY = 'ready'
    CHAT_STATUS_CHATTING = 'chatting'
    CHAT_STATUS_COMPLETED = 'completed'
    CHAT_STATUS_FAILED = 'failed'
    
    @classmethod
    def get_valid_chat_statuses(cls):
        """获取所有有效的聊天状态"""
        return [
            cls.CHAT_STATUS_IDLE,
            cls.CHAT_STATUS_READY,
            cls.CHAT_STATUS_CHATTING,
            cls.CHAT_STATUS_COMPLETED,
            cls.CHAT_STATUS_FAILED
        ] 