"""
物料基础信息模型

对应数据库表：component
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Index, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class Component(Base):
    """物料基础信息模型"""
    
    __tablename__ = "component"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='物料ID，自增主键')
    
    # 基础信息
    name = Column(String(100), nullable=False, comment='物料名称，不可为空')
    spec = Column(Text, comment='规格描述')
    category = Column(String(50), comment='类别')
    material = Column(String(100), comment='材质工艺')
    component_code = Column(String(100), comment='物料编码，每家公司自己给物料的编号')
    tag = Column(String(100), comment='标签，比如"标件"，"非标件"之类的')
    
    # 供应商和价格信息
    original_supplier = Column(String(100), comment='初始供应商')
    price = Column(Numeric(12, 4), comment='参考价格')
    quantity = Column(Integer, comment='数量')
    
    # 关联信息
    client_company_id = Column(Integer, ForeignKey('client_company.id', ondelete='SET NULL', onupdate='CASCADE'), 
                              comment='客户公司ID，关联到该物料所属的客户公司')
    client_product_id = Column(Integer, ForeignKey('client_product.id', ondelete='SET NULL', onupdate='CASCADE'), 
                              comment='客户产品ID，关联到该物料所属的客户产品')
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp(), comment='创建时间')
    
    # 关系定义（如果需要的话）
    # client_company = relationship("ClientCompany", back_populates="components")
    # client_product = relationship("ClientProduct", back_populates="components")
    
    # 索引定义
    __table_args__ = (
        Index('idx_client_company', 'client_company_id'),
        Index('idx_client_product', 'client_product_id'),
        Index('idx_component_name', 'name'),
        Index('idx_component_code', 'component_code'),
        Index('idx_component_category', 'category'),
        {
            'mysql_engine': 'InnoDB',
            'mysql_charset': 'utf8mb4',
            'mysql_collate': 'utf8mb4_unicode_ci'
        }
    )
    
    def __repr__(self):
        return f"<Component(id={self.id}, name='{self.name}', category='{self.category}', original_supplier='{self.original_supplier}', price='{self.price}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'spec': self.spec,
            'category': self.category,
            'material': self.material,
            'component_code': self.component_code,
            'tag': self.tag,
            'original_supplier': self.original_supplier,
            'price': float(self.price) if self.price else None,
            'quantity': self.quantity,
            'client_company_id': self.client_company_id,
            'client_product_id': self.client_product_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        """从字典创建实例"""
        return cls(
            name=data.get('name'),
            spec=data.get('spec'),
            category=data.get('category'),
            material=data.get('material'),
            component_code=data.get('component_code'),
            tag=data.get('tag'),
            original_supplier=data.get('original_supplier'),
            price=data.get('price'),
            quantity=data.get('quantity'),
            client_company_id=data.get('client_company_id'),
            client_product_id=data.get('client_product_id')
        )
    
    def update_from_dict(self, data: dict):
        """从字典更新实例"""
        for key, value in data.items():
            if hasattr(self, key) and key != 'id':  # 不允许更新ID
                setattr(self, key, value)
