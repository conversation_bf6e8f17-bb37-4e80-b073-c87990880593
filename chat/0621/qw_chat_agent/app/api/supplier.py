"""
供应商管理相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.models.user import UserResponse
from app.api.dependencies import get_current_user_info_optional
from app.services.search_supplier_service import SearchSupplierService

router = APIRouter(prefix="/supplier", tags=["供应商管理"])


# 请求和响应模型
class SupplierListRequest(BaseModel):
    """获取供应商列表请求模型"""
    chat_status: str = "ready"
    limit: int = 100
    offset: int = 0


class SupplierStatusUpdateRequest(BaseModel):
    """更新供应商状态请求模型"""
    supplier_name: str
    chat_status: str = "chatting"


class SupplierContactInfo(BaseModel):
    """供应商联系信息模型"""
    supplier_name: str
    phone: Optional[str] = None
    contact_person: Optional[str] = None
    region: Optional[str] = None


class SupplierListResponse(BaseModel):
    """供应商列表响应模型"""
    success: bool
    data: List[Dict[str, Any]]
    total: int
    message: Optional[str] = None


class SupplierStatusUpdateResponse(BaseModel):
    """供应商状态更新响应模型"""
    success: bool
    message: str
    supplier_name: str
    new_status: str


class SupplierContactResponse(BaseModel):
    """供应商联系信息响应模型"""
    success: bool
    data: Optional[SupplierContactInfo] = None
    message: str


@router.get("/list", response_model=SupplierListResponse, summary="获取供应商列表")
async def get_suppliers_by_status(
    chat_status: str = "ready",
    limit: int = 100,
    offset: int = 0,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    获取指定聊天状态的供应商列表
    
    - **chat_status**: 聊天状态 (ready, chatting, completed, failed)
    - **limit**: 返回记录数量限制
    - **offset**: 偏移量
    """
    try:
        service = SearchSupplierService()
        
        # 验证chat_status是否有效
        from app.models.search_supplier import SearchSupplier
        valid_statuses = SearchSupplier.get_valid_chat_statuses()
        if chat_status not in valid_statuses:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的聊天状态。有效状态: {', '.join(valid_statuses)}"
            )
        
        suppliers = service.get_suppliers_by_chat_status(
            chat_status=chat_status,
            limit=limit,
            offset=offset
        )
        
        # 提取联系信息（主要是手机号和姓名）
        contact_list = []
        for supplier in suppliers:
            contact_info = {
                "id": supplier.get("id"),
                "supplier_name": supplier.get("supplier_name"),
                "phone": supplier.get("phone"),
                "contact_person": supplier.get("contact_person"),
                "region": supplier.get("region"),
                "chat_status": supplier.get("chat_status"),
                "created_at": supplier.get("created_at"),
                "updated_at": supplier.get("updated_at")
            }
            contact_list.append(contact_info)
        
        return SupplierListResponse(
            success=True,
            data=contact_list,
            total=len(contact_list),
            message=f"成功获取{len(contact_list)}个{chat_status}状态的供应商"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取供应商列表失败: {str(e)}"
        )


@router.get("/contact/{supplier_name}", response_model=SupplierContactResponse, summary="获取供应商联系信息")
async def get_supplier_contact(
    supplier_name: str,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    根据供应商名称获取联系信息
    
    - **supplier_name**: 供应商名称
    """
    try:
        service = SearchSupplierService()
        contact_info = service.get_supplier_contact_info(supplier_name)
        
        if not contact_info:
            return SupplierContactResponse(
                success=False,
                data=None,
                message=f"未找到供应商: {supplier_name}"
            )
        
        contact_data = SupplierContactInfo(
            supplier_name=contact_info.get("supplier_name"),
            phone=contact_info.get("phone"),
            contact_person=contact_info.get("contact_person"),
            region=contact_info.get("region")
        )
        
        return SupplierContactResponse(
            success=True,
            data=contact_data,
            message=f"成功获取供应商 {supplier_name} 的联系信息"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取供应商联系信息失败: {str(e)}"
        )


@router.put("/status/update", response_model=SupplierStatusUpdateResponse, summary="更新供应商聊天状态")
async def update_supplier_chat_status(
    request: SupplierStatusUpdateRequest,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    根据供应商名称更新聊天状态
    
    - **supplier_name**: 供应商名称
    - **chat_status**: 新的聊天状态 (ready, chatting, completed, failed)
    """
    try:
        service = SearchSupplierService()
        
        # 验证chat_status是否有效
        from app.models.search_supplier import SearchSupplier
        valid_statuses = SearchSupplier.get_valid_chat_statuses()
        if request.chat_status not in valid_statuses:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的聊天状态。有效状态: {', '.join(valid_statuses)}"
            )
        
        success = service.update_chat_status_by_supplier_name(
            supplier_name=request.supplier_name,
            chat_status=request.chat_status
        )
        
        if success:
            return SupplierStatusUpdateResponse(
                success=True,
                message=f"成功将供应商 {request.supplier_name} 的状态更新为 {request.chat_status}",
                supplier_name=request.supplier_name,
                new_status=request.chat_status
            )
        else:
            return SupplierStatusUpdateResponse(
                success=False,
                message=f"未找到供应商 {request.supplier_name} 或更新失败",
                supplier_name=request.supplier_name,
                new_status=request.chat_status
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新供应商状态失败: {str(e)}"
        )


@router.get("/statistics", summary="获取供应商聊天状态统计")
async def get_chat_status_statistics(
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    获取各个聊天状态的供应商数量统计
    """
    try:
        service = SearchSupplierService()
        statistics = service.get_chat_status_statistics()
        
        return {
            "success": True,
            "data": statistics,
            "message": "成功获取聊天状态统计信息"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.get("/search", summary="搜索供应商")
async def search_suppliers(
    supplier_name: str,
    exact_match: bool = False,
    current_user: Optional[UserResponse] = Depends(get_current_user_info_optional)
):
    """
    根据供应商名称搜索供应商
    
    - **supplier_name**: 供应商名称
    - **exact_match**: 是否精确匹配
    """
    try:
        service = SearchSupplierService()
        suppliers = service.search_by_supplier_name(
            supplier_name=supplier_name,
            exact_match=exact_match
        )
        
        return {
            "success": True,
            "data": suppliers,
            "total": len(suppliers),
            "message": f"找到 {len(suppliers)} 个匹配的供应商"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索供应商失败: {str(e)}"
        ) 