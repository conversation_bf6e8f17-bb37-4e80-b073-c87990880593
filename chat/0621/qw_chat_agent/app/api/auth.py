"""
认证相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.models.user import Token, LoginRequest, RefreshTokenRequest, UserResponse
from app.services.user_service import UserService
from app.core.auth import auth_manager
from app.api.dependencies import get_current_user_info, security, get_user_service
from app.database import get_material_db as get_db
from config.auth_config import JWT_ACCESS_TOKEN_EXPIRE_MINUTES

router = APIRouter(prefix="/auth", tags=["认证"])


@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    login_request: LoginRequest,
    user_service: UserService = Depends(get_user_service)
):
    """
    用户登录
    
    - **user_name**: 用户名
    - **password_**: 密码
    
    返回访问令牌和刷新令牌
    """
    user = user_service.authenticate_user(login_request.user_name, login_request.password_)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if user.status_ != 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    # 更新最后登录时间
    user_service.update_last_login(user.id)
    
    # 创建访问令牌和刷新令牌
    access_token = auth_manager.create_access_token(
        data={"sub": user.user_name, "user_id": user.id}
    )
    refresh_token = auth_manager.create_refresh_token(
        data={"sub": user.user_name, "user_id": user.id}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    user_service: UserService = Depends(get_user_service)
):
    """
    使用刷新令牌获取新的访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    try:
        # 验证刷新令牌
        token_data = auth_manager.verify_token(refresh_request.refresh_token, "refresh")
        
        # 获取用户信息
        user = user_service.get_user_by_username(token_data.user_name)
        if not user or user.status_ != 1:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 创建新的访问令牌
        access_token = auth_manager.create_access_token(
            data={"sub": user.user_name, "user_id": user.id}
        )
        
        # 创建新的刷新令牌
        new_refresh_token = auth_manager.create_refresh_token(
            data={"sub": user.user_name, "user_id": user.id}
        )
        
        return Token(
            access_token=access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
            expires_in=JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌无效"
        )


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_profile(current_user: UserResponse = Depends(get_current_user_info)):
    """
    获取当前登录用户的信息
    
    需要在请求头中包含有效的访问令牌：
    Authorization: Bearer <access_token>
    """
    return current_user


@router.post("/logout", summary="用户登出")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    用户登出
    
    注意：由于JWT是无状态的，这里只是一个形式上的登出接口
    实际的令牌失效需要客户端删除本地存储的令牌
    """
    # 在实际应用中，可以将令牌加入黑名单
    # 这里只是返回成功消息
    return {"message": "登出成功"} 