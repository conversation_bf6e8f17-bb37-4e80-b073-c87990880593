"""报价请求Agent

负责在供应商确认能力后，发送报价请求，包括：
- 询问产品价格
- 交期要求
- 付款条件
- 包装运输要求
- 质量认证要求
"""

import json
from typing import Dict, Any, List, Union
from datetime import datetime

from app.core.base_agent import (
    BaseAgent, AgentResult, AgentMessage, MessageRole, AgentConfig,
    create_success_result, create_error_result
)
from app.core.llm_client import LLMClientFactory
from app.utils.get_material_info import get_supplier_material_info
from app.utils.tool_function import extract_largest_json
from config.llm_config import DEFAULT_MODEL


class QuoteRequestAgent(BaseAgent):
    """报价请求Agent"""
    
    def __init__(self, config: AgentConfig = None, llm_client=None):
        if config is None:
            config = AgentConfig(
                name="quote_request_agent",
                description="在供应商确认能力后发送报价请求的Agent",
                system_prompt=self._get_system_prompt(),
                # model="deepseek/deepseek-chat-v3-0324",
                model=DEFAULT_MODEL,
            )
        super().__init__(config)

        # 注入LLM客户端
        self.llm_client = llm_client
        
        # 初始化LLM客户端
        if not self.llm_client:
            try:
                from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY
                self.llm_client = LLMClientFactory.create_openai_client(
                    base_url=OPENAI_BASE_URL,
                    api_key=OPENAI_API_KEY,
                    default_model=self.config.model
                )
            except ImportError:
                # 如果没有配置文件，使用环境变量
                self.llm_client = LLMClientFactory.create_from_env()
        
        self.user_prompt = self.get_user_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一名专业的采购人员，负责与供应商进行报价相关的商务沟通。

工作场景：
- 此阶段已经向供应商发送了物料需求信息
- 现在主要进行报价相关的对话交流
- 包括询问报价、回应报价单、确认交期等

工作要求：
1. 专注于报价相关的对话，保持商务沟通的专业性
2. 根据供应商的回复给出合适的响应
3. 如果信息不足以回答供应商的提问，需要转人工处理
4. 常见场景包括：询问报价进展、感谢报价单、确认需要时间等

输出格式：
必须返回JSON格式：
{
    "message": "回复内容",
    "reason": "选择这种回复方式的理由",
    "need_human": false
}

示例输出：
{
    "message": "好的，请问大概什么时候能提供报价单？",
    "reason": "询问报价时间安排",
    "need_human": false
}

{
    "message": "感谢您提供的报价单，我们会仔细评估，有问题会及时联系您。",
    "reason": "收到报价单后的礼貌回复",
    "need_human": false
}

{
    "message": "好的，我们等您的报价单。",
    "reason": "供应商表示需要时间准备报价",
    "need_human": false
}
"""

    def get_user_prompt(self) -> str:
        """获取用户提示词"""
        return """
供应商的消息：
{user_message}

对话历史上下文：
{message_history_context}

工作要求：
1. 这是报价阶段的对话，物料需求信息之前已经发送给供应商
2. 根据供应商的回复，给出合适的商务回应
3. 如果信息不足以回答供应商的提问，设置need_human为true
4. 保持专业的商务沟通语调
5. **重要：避免陷入循环对话，及时识别需要人工介入的场景**

需要转人工的场景：
- 供应商多次回复简单确认词（"嗯"、"好的"、"需要时间"、"需要一些时间"等）而未提供具体报价信息
- 对话历史显示已多次询问报价进展，但供应商仍未给出明确时间或实质性回复
- 供应商回复过于简短或模糊，无法推进报价流程
- 涉及复杂的商务谈判细节
- 技术问题超出采购人员能力范围

常见场景处理：
- 询问报价进展："请问报价准备得怎么样了？"
- 收到报价单："感谢您的报价单，我们会仔细评估"
- 供应商首次表示需要时间："好的，我们等您的消息"
- 供应商重复表示需要时间：**转人工处理**
- 价格谈判：根据具体情况回应
- 交期确认：确认具体的交货时间

输出格式：
必须返回JSON格式：
{{
    "message": "回复内容",
    "reason": "选择这种回复方式的理由",
    "need_human": false
}}

示例输出：
{{
    "message": "好的，请问大概什么时候能提供报价单？",
    "reason": "询问报价时间安排",
    "need_human": false
}}

{{
    "message": "感谢您提供的报价单，我们会仔细评估，有问题会及时联系您。",
    "reason": "收到报价单后的礼貌回复",
    "need_human": false
}}

{{
    "message": "好的，我们理解您需要时间准备，期待您的报价单。",
    "reason": "供应商多次表示需要时间或回复过于简单，可能形成循环对话，转人工跟进",
    "need_human": true
}}

请根据以上要求生成专业的回复。
"""
    
    def get_supported_types(self) -> List[str]:
        """返回支持的请求类型"""
        return [
            "quote_discussion",
            "price_inquiry",
            "quote_follow_up",
            "quote_response",
            "delivery_confirmation",
            "payment_terms_discussion"
        ]
    
    async def process(self, session_id: str, data: Dict[str, Any]) -> AgentResult:
        """处理报价请求"""
        start_time = datetime.now()
        
        try:
            # 验证输入
            if not await self.validate_input(data):
                return create_error_result("输入数据验证失败")
            
            # 预处理
            processed_data = await self.pre_process(session_id, data)
            
            # 获取用户消息和历史上下文
            user_message = processed_data.get("message", "")
            message_history = processed_data.get("message_history", [])
            supplier_info = processed_data.get("supplier_info", {})
            
            supplier_name = supplier_info.get("company_name", "")
            component_code = processed_data.get("component_code", [])
            
            material_info = get_supplier_material_info(supplier_name, component_code)
            
            # 构建对话上下文
            messages = await self._build_conversation_context(
                user_message, message_history, supplier_info, material_info
            )
            
            # 调用LLM生成报价请求
            response_message = await self.call_llm(messages)

            response_json = extract_largest_json(response_message.content)
            
            # 构建结果
            result = create_success_result(
                data={
                    "response": response_json.get("message", "提取大模型回复失败"),
                    "agent_type": "quote_request_agent",
                    "session_id": session_id,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "need_human": response_json.get("need_human", False),
                    "reason": response_json.get("reason", ""),
                },
                message="报价对话处理完成"
            )
            
            # 添加响应消息到结果
            result.add_message(
                role=MessageRole.ASSISTANT,
                content=response_json.get("message", "提取大模型回复失败"),
                metadata={
                    "agent": self.name,
                    "processing_time": (datetime.now() - start_time).total_seconds(),
                    "need_human": response_json.get("need_human", False)
                }
            )
            
            # 后处理
            result = await self.post_process(session_id, result)
            
            return result
            
        except Exception as e:
            self.log_error(f"处理报价请求失败: {str(e)}")
            return create_error_result(
                error=f"处理失败: {str(e)}",
                data={"session_id": session_id}
            )
    
    async def _build_conversation_context(
        self, 
        user_message: str, 
        message_history: Union[List[Dict[str, Any]], List[AgentMessage]] = None,
        supplier_info: Dict[str, Any] = None,
        material_info: Dict[str, Any] = None
    ) -> List[AgentMessage]:
        """构建对话上下文"""
        messages = []
        
        # 系统提示
        system_prompt = self.config.system_prompt
        
        # 如果有供应商信息，添加到系统提示中
        if supplier_info:
            system_prompt += f"\n\n当前供应商信息：\n{json.dumps(supplier_info, ensure_ascii=False, indent=2)}"
        
        # 如果有物料信息，添加到系统提示中
        if material_info:
            system_prompt += f"\n\n相关物料信息：\n{json.dumps(material_info, ensure_ascii=False, indent=2)}"
        
        messages.append(AgentMessage(
            role=MessageRole.SYSTEM,
            content=system_prompt
        ))
        
        # 添加当前用户消息
        if user_message:
            # 分析对话历史，检测循环模式
            history_context = self._analyze_message_history(message_history or [])
            
            user_question = self.user_prompt.format(
                user_message=user_message,
                message_history_context=history_context
            )
            messages.append(AgentMessage(
                role=MessageRole.USER,
                content=user_question
            ))
        
        return messages
    
    def _analyze_message_history(self, message_history: List) -> str:
        """分析对话历史，检测循环模式和关键信息"""
        if not message_history:
            return "无对话历史"
        
        # 获取最近的消息（最多10条）
        recent_messages = message_history[-10:] if len(message_history) > 10 else message_history
        
        # 统计相关模式
        quote_inquiries = 0  # 询问报价次数
        simple_responses = 0  # 简单回复次数
        time_requests = 0  # 表示需要时间的次数
        
        user_messages = []
        supplier_messages = []
        
        for msg in recent_messages:
            if isinstance(msg, dict):
                role = msg.get("role", "")
                content = msg.get("content", "")
            else:
                role = getattr(msg, "role", "")
                content = getattr(msg, "content", "")
            
            if role == "user":
                supplier_messages.append(content)
                # 检测简单回复
                if any(word in content for word in ["嗯", "好的", "需要时间", "需要一些时间", "时间吧", "等一下", "稍等"]):
                    simple_responses += 1
                if any(word in content for word in ["时间", "准备", "等"]):
                    time_requests += 1
            elif role == "assistant":
                user_messages.append(content)
                # 检测报价询问
                if any(word in content for word in ["报价", "价格", "什么时候", "时间"]):
                    quote_inquiries += 1
        
        # 构建上下文描述
        context_parts = []
        context_parts.append(f"近期消息数: {len(recent_messages)}")
        
        if quote_inquiries > 1:
            context_parts.append(f"已询问报价进展 {quote_inquiries} 次")
        
        if simple_responses > 1:
            context_parts.append(f"供应商简单回复 {simple_responses} 次")
        
        if time_requests > 1:
            context_parts.append(f"供应商表示需要时间 {time_requests} 次")
        
        # 风险评估
        if quote_inquiries >= 2 and simple_responses >= 2:
            context_parts.append("⚠️ 检测到循环对话风险：多次询问但供应商回复过于简单")
        
        if time_requests >= 2:
            context_parts.append("⚠️ 供应商多次表示需要时间，可能需要人工跟进")
        
        return "; ".join(context_parts)
    

    
    async def validate_input(self, data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        if not isinstance(data, dict):
            return False
        
        # 检查必要字段
        if "message" not in data and "message_history" not in data:
            return False
        
        return True
    
    async def pre_process(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理数据"""
        # 获取会话记忆上下文
        memory_context = await self.get_memory_context(session_id)
        
        # 合并上下文信息
        processed_data = data.copy()
        if memory_context:
            processed_data["memory_context"] = memory_context
        
        return processed_data
    
    async def post_process(self, session_id: str, result: AgentResult) -> AgentResult:
        """后处理结果"""
        # 保存会话上下文到记忆
        if result.success and result.messages:
            context_to_save = {
                "last_interaction": datetime.now().isoformat(),
                "agent_type": "quote_request_agent",
                "last_response": result.messages[-1].content if result.messages else None,
                "need_human": result.data.get("need_human") if result.data else False
            }
            await self.save_memory_context(session_id, context_to_save)
        
        return result
    
    def get_common_scenarios(self) -> Dict[str, str]:
        """获取常见报价对话场景"""
        return {
            "quote_inquiry": "询问报价进展",
            "quote_received": "收到报价单回复",
            "need_time": "供应商需要时间准备",
            "price_negotiation": "价格谈判",
            "delivery_confirmation": "交期确认",
            "payment_terms": "付款条件讨论"
        }
    
    def get_response_templates(self) -> Dict[str, str]:
        """获取回复模板"""
        return {
            "follow_up": "请问报价准备得怎么样了？",
            "thank_quote": "感谢您的报价单，我们会仔细评估，有问题会及时联系您。",
            "wait_response": "好的，我们等您的消息。",
            "need_clarification": "关于{topic}，能否提供更详细的信息？",
            "confirm_delivery": "请确认一下交货时间，我们这边需要安排生产计划。"
        }
    
    def __str__(self) -> str:
        return f"QuoteRequestAgent({self.name})"
    
    def __repr__(self) -> str:
        return f"QuoteRequestAgent(name='{self.name}', supported_types={self.get_supported_types()})"


# 使用示例
if __name__ == "__main__":
    import asyncio
    from app.core.llm_client import LLMClientFactory
    
    async def test_agent():
        # 创建Agent（使用自带的初始化配置）
        agent = QuoteRequestAgent()
        
        # 模拟处理请求
        test_data = {
            "message": "报价单我们正在准备，大概明天能发给您。"
        }
        
        result = await agent.process("test_session_001", test_data)
        print(f"处理结果: {result.success}")
        print(f"响应消息: {result.data.get('response') if result.data else 'None'}")
        print(f"需要人工: {result.data.get('need_human') if result.data else 'None'}")
        print(json.dumps(result.to_dict(), ensure_ascii=False, indent=2))
        print(f"错误信息: {result.error}")
    
    # 运行测试
    asyncio.run(test_agent())