"""
延迟清理的文件响应类
解决文件在传输过程中被清理的问题
"""

import os
import asyncio
import tempfile
import shutil
from typing import Optional, Dict, Any
from fastapi.responses import FileResponse
from app.utils.background_tasks import background_task_manager


class DelayedCleanupFileResponse(FileResponse):
    """
    延迟清理的文件响应类
    确保文件在传输完成后才被清理
    """
    
    def __init__(
        self, 
        source_path: str,
        filename: str = None,
        media_type: str = None,
        headers: Dict[str, str] = None,
        cleanup_delay: int = 300,  # 5分钟后清理
        **kwargs
    ):
        """
        初始化延迟清理文件响应
        
        Args:
            source_path: 源文件路径
            filename: 下载文件名
            media_type: 媒体类型
            headers: 响应头
            cleanup_delay: 清理延迟时间（秒）
        """
        # 创建一个持久的临时目录来存放文件
        self.persistent_temp_dir = tempfile.mkdtemp(prefix="download_persistent_")
        
        # 复制文件到持久临时目录
        if filename:
            safe_filename = self._sanitize_filename(filename)
        else:
            safe_filename = os.path.basename(source_path)
        
        self.persistent_file_path = os.path.join(self.persistent_temp_dir, safe_filename)
        
        # 复制文件
        shutil.copy2(source_path, self.persistent_file_path)
        
        # 调用父类构造函数
        super().__init__(
            path=self.persistent_file_path,
            filename=filename,
            media_type=media_type,
            headers=headers or {},
            **kwargs
        )
        
        # 调度清理任务
        background_task_manager.schedule_cleanup(
            self.persistent_temp_dir, 
            delay_seconds=cleanup_delay
        )
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不安全字符"""
        import re
        # 移除或替换不安全的字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        return safe_name


def create_download_response(
    source_path: str,
    filename: str,
    media_type: str = None,
    headers: Dict[str, str] = None,
    cleanup_delay: int = 300
) -> DelayedCleanupFileResponse:
    """
    创建下载响应的便捷函数
    
    Args:
        source_path: 源文件路径
        filename: 下载文件名
        media_type: 媒体类型
        headers: 响应头
        cleanup_delay: 清理延迟时间（秒）
        
    Returns:
        DelayedCleanupFileResponse: 延迟清理的文件响应
    """
    return DelayedCleanupFileResponse(
        source_path=source_path,
        filename=filename,
        media_type=media_type,
        headers=headers,
        cleanup_delay=cleanup_delay
    ) 