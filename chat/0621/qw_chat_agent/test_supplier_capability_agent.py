"""测试供应商能力询问Agent

用于验证SupplierCapabilityAgent的基本功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.agents.supplier_capability_agent import SupplierCapabilityAgent
from app.core.llm_client import LLMClientFactory
from app.core.base_agent import AgentConfig
from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY


async def test_supplier_capability_agent():
    """测试供应商能力询问Agent"""
    print("=== 测试供应商能力询问Agent ===")
    
    try:
        # 创建LLM客户端
        llm_client = LLMClientFactory.create_openai_client(
            base_url=OPENAI_BASE_URL,
            api_key=OPENAI_API_KEY,
            default_model="deepseek/deepseek-chat-v3-0324:free"
        )
        
        # 创建Agent配置
        agent_config = AgentConfig(
            name="supplier_capability_agent",
            description="供应商能力询问测试",
            model="deepseek/deepseek-chat-v3-0324:free",
            temperature=0.7,
            max_tokens=1000
        )
        
        # 创建Agent
        agent = SupplierCapabilityAgent(agent_config)
        
        # 注入LLM客户端
        agent.llm_client = llm_client
        
        print(f"Agent创建成功: {agent.name}")
        print(f"支持的请求类型: {agent.get_supported_types()}")
        
        # 测试场景1：初次接触供应商
        print("\n--- 测试场景1：初次接触供应商 ---")
        test_data_1 = {
            "message": "你好，我是某某公司的采购经理，想了解一下贵公司的基本情况",
            "message_history": [],
            "supplier_info": {
                "company_name": "测试供应商有限公司",
                "contact_person": "张经理"
            }
        }
        
        result_1 = await agent.process("test_session_001", test_data_1)
        print(f"处理结果: {result_1.success}")
        if result_1.success:
            print(f"AI回复: {result_1.data.get('response', 'None')}")
        else:
            print(f"错误信息: {result_1.error}")
        
        # 测试场景2：供应商回复后的追问
        print("\n--- 测试场景2：供应商回复后的追问 ---")
        test_data_2 = {
            "message": "我们公司主要生产电子元器件，有10年的生产经验，工厂在深圳",
            "message_history": [
                {"role": "user", "content": "你好，我是某某公司的采购经理，想了解一下贵公司的基本情况"},
                {"role": "assistant", "content": "您好！很高兴认识您。我想了解一下贵公司的基本情况，请问贵公司主要生产什么类型的产品？"}
            ],
            "supplier_info": {
                "company_name": "测试供应商有限公司",
                "contact_person": "张经理",
                "main_products": "电子元器件",
                "experience": "10年",
                "location": "深圳"
            }
        }
        
        result_2 = await agent.process("test_session_001", test_data_2)
        print(f"处理结果: {result_2.success}")
        if result_2.success:
            print(f"AI回复: {result_2.data.get('response', 'None')}")
        else:
            print(f"错误信息: {result_2.error}")
        
        # 测试场景3：询问具体能力
        print("\n--- 测试场景3：询问具体能力 ---")
        test_data_3 = {
            "message": "我们主要做电阻、电容、二极管这些基础元器件，月产能大概500万个",
            "message_history": [
                {"role": "user", "content": "你好，我是某某公司的采购经理，想了解一下贵公司的基本情况"},
                {"role": "assistant", "content": "您好！很高兴认识您。我想了解一下贵公司的基本情况，请问贵公司主要生产什么类型的产品？"},
                {"role": "user", "content": "我们公司主要生产电子元器件，有10年的生产经验，工厂在深圳"},
                {"role": "assistant", "content": "很好！电子元器件是我们关注的领域。请问具体是哪些类型的电子元器件呢？比如电阻、电容、芯片等？另外，贵公司的月产能大概是多少？"}
            ],
            "supplier_info": {
                "company_name": "测试供应商有限公司",
                "contact_person": "张经理",
                "main_products": "电阻、电容、二极管",
                "monthly_capacity": "500万个",
                "experience": "10年",
                "location": "深圳"
            }
        }
        
        result_3 = await agent.process("test_session_001", test_data_3)
        print(f"处理结果: {result_3.success}")
        if result_3.success:
            print(f"AI回复: {result_3.data.get('response', 'None')}")
        else:
            print(f"错误信息: {result_3.error}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_supplier_capability_agent())