#!/usr/bin/env python3
"""
供应商匹配API测试脚本
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional


class SupplierMatchTester:
    """供应商匹配API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8650"):
        self.base_url = base_url
        self.session = None
        self.use_auth = False  # 供应商匹配接口通常不需要认证
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {"Content-Type": "application/json"}
    
    async def test_health(self) -> Dict[str, Any]:
        """测试健康检查"""
        url = f"{self.base_url}/health"
        
        try:
            async with self.session.get(url) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 健康检查成功: {result['status']}")
                else:
                    print(f"❌ 健康检查失败: {result}")
                return result
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return {"error": str(e)}
    
    async def get_all_suppliers(self) -> Dict[str, Any]:
        """获取所有推荐供应商列表"""
        url = f"{self.base_url}/api/v1/supplier-match/suppliers"
        
        try:
            async with self.session.get(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    suppliers = result.get('data', [])
                    print(f"✅ 获取供应商列表成功: 共 {len(suppliers)} 个供应商")
                    for i, supplier in enumerate(suppliers[:10], 1):  # 只显示前10个
                        print(f"   {i}. {supplier}")
                    if len(suppliers) > 10:
                        print(f"   ... 还有 {len(suppliers) - 10} 个供应商")
                else:
                    print(f"❌ 获取供应商列表失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取供应商列表异常: {str(e)}")
            return {"error": str(e)}
    
    async def match_supplier(self, company_info: str) -> Dict[str, Any]:
        """匹配供应商"""
        url = f"{self.base_url}/api/v1/supplier-match/match"
        data = {
            "company_info": company_info
        }
        
        try:
            async with self.session.post(url, json=data, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 供应商匹配成功")
                    print(f"   输入: {company_info}")
                    print(f"   匹配结果: {result['matched_supplier']}")
                    print(f"   置信度: {result.get('confidence', 'N/A')}")
                    print(f"   匹配原因: {result.get('reason', 'N/A')}")
                    print(f"   成功状态: {result['success']}")
                else:
                    print(f"❌ 供应商匹配失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 供应商匹配异常: {str(e)}")
            return {"error": str(e)}


async def test_supplier_match_api():
    """测试供应商匹配API"""
    async with SupplierMatchTester(base_url="http://localhost:8650") as tester:
        print("=== 供应商匹配API测试 ===\n")
        
        # 0. 测试健康检查
        print("0. 测试健康检查...")
        await tester.test_health()
        print()
        
        # 1. 获取所有推荐供应商列表
        print("1. 获取所有推荐供应商列表...")
        suppliers_result = await tester.get_all_suppliers()
        print()
        
        if not suppliers_result.get('success', False):
            print("❌ 无法获取供应商列表，跳过匹配测试")
            return
        
        # 2. 测试各种不同的公司信息匹配
        test_cases = [
            "老板您好，我是苏州金澄精密铸造有限公司的营销人员",
            "您好，我是东莞市华强电子有限公司的采购经理",
            "老板您好，我是深圳市科技创新公司的业务代表",
            "您好，我是上海精密制造有限公司的销售总监",
            "老板您好，我是广州汽车零部件公司的项目经理",
            "您好，我是北京新材料科技有限公司的技术总监",
            "老板您好，我是宁波机械制造有限公司的商务经理",
            "您好，我是杭州智能设备公司的市场总监",
            "老板您好，我是无锡电子科技有限公司的供应链经理",
            "您好，我是佛山金属加工厂的负责人"
        ]
        
        print("2. 测试不同公司信息的供应商匹配...")
        for i, company_info in enumerate(test_cases, 1):
            print(f"\n--- 测试用例 {i} ---")
            result = await tester.match_supplier(company_info)
            
            # 添加一些延迟，避免API调用过快
            await asyncio.sleep(0.5)
        
        # 3. 测试边界情况
        print("\n3. 测试边界情况...")
        
        edge_cases = [
            "",  # 空字符串
            "hello",  # 英文内容
            "123456",  # 纯数字
            "老板您好",  # 只有问候语，没有公司信息
            "我是某某公司的",  # 公司信息不明确
            "您好，我是一家专业从事高精度机械加工、模具制造、电子元器件生产的大型综合性企业的业务经理，我们公司成立于1995年，拥有先进的生产设备和丰富的行业经验"  # 很长的信息
        ]
        
        for i, edge_case in enumerate(edge_cases, 1):
            print(f"\n--- 边界测试 {i} ---")
            if edge_case == "":
                print("   输入: [空字符串]")
            else:
                print(f"   输入: {edge_case}")
            result = await tester.match_supplier(edge_case)
            await asyncio.sleep(0.5)
        
        print("\n🎉 所有供应商匹配API测试完成！")
        print("\n💡 测试总结：")
        print("   - 测试了正常的公司信息匹配")
        print("   - 测试了各种边界情况")
        print("   - 验证了API的错误处理能力")
        print("   - 检查了返回数据的完整性")


async def test_specific_cases():
    """测试特定案例"""
    async with SupplierMatchTester(base_url="http://localhost:8650") as tester:
        print("=== 特定案例测试 ===\n")
        
        # 可以根据实际业务需求添加特定的测试案例
        specific_cases = [
            "老板您好，我是浙江红家电机有限公司的营销人员，我们专业从事精密铸造",
            "您好，我是上海鸣志的采购经理",
            "老板您好，我是小鹏汽车的供应商管理部门"
        ]
        
        for i, case in enumerate(specific_cases, 1):
            print(f"{i}. 测试特定案例...")
            result = await tester.match_supplier(case)
            print()
            await asyncio.sleep(1)


if __name__ == "__main__":
    # 运行完整测试
    # asyncio.run(test_supplier_match_api())
    
    # 或者只运行特定案例测试
    asyncio.run(test_specific_cases())