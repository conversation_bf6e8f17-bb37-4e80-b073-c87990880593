#!/usr/bin/env python3
"""
将 supplier_capability_agent 注册到 orchestrator 的示例

这个文件展示了如何：
1. 创建 orchestrator 实例
2. 创建 supplier_capability_agent 实例
3. 将 agent 注册到 orchestrator
4. 测试整个流程
"""

import asyncio
import json
from app.core.orchestrator import create_orchestrator
from app.agents.supplier_capability_agent import SupplierCapabilityAgent
from config.llm_config import OPENAI_BASE_URL, OPENAI_API_KEY


async def main():
    """主函数：演示如何注册和使用 supplier_capability_agent"""
    
    # 1. 创建 orchestrator 配置
    config = {
        "use_redis": False,  # 可以设置为 True 启用 Redis
        "redis": {
            "url": "redis://localhost:6379",
            "timeout": 5
        },
        "llm": {
            "base_url": OPENAI_BASE_URL,
            "api_key": OPENAI_API_KEY,
            "default_model": "deepseek/deepseek-chat-v3-0324:free"
        }
    }
    
    # 2. 创建 orchestrator 实例
    print("创建 orchestrator...")
    orchestrator = create_orchestrator(config)
    
    # 3. 创建 supplier_capability_agent 实例
    print("创建 supplier_capability_agent...")
    supplier_agent = SupplierCapabilityAgent()
    
    # 4. 将 agent 注册到 orchestrator
    print("注册 agent 到 orchestrator...")
    orchestrator.register_agent(supplier_agent)
    
    # 5. 验证注册成功
    print(f"已注册的 agents: {orchestrator.list_agents()}")
    
    # 6. 创建测试会话
    print("\n创建测试会话...")
    session_id = await orchestrator.create_session()
    print(f"会话ID: {session_id}")
    
    # 7. 测试消息处理
    test_messages = [
        "明天天气怎么样",
        "你好",
        "你们要什么尺寸的，有什么图纸之类的吗？"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n=== 测试消息 {i} ===")
        print(f"用户消息: {message}")

        if i == 2:
            update_result = await orchestrator.update_session_status(
                session_id
            )
            print(f"更新会话上下文结果: {update_result}")
        
        # 处理消息
        result = await orchestrator.handle_message(
            session_id=session_id,
            user_message=message,
            request_context={
                "supplier_id": "test_supplier_001",
                "component_code": ["TEST_COMPONENT_001"],
                "supplier_info": {
                    "company_name": "测试供应商公司",
                    "contact_person": "张经理"
                }
            }
        )
        
        print(f"处理成功: {result.success}")
        if result.success:
            print(f"响应: {result.data.get('response', 'N/A')}")
            print(f"使用的Agent: {result.data.get('agent_type', 'N/A')}")
        else:
            print(f"错误: {result.error}")
    
    # 8. 显示系统状态
    print("\n=== 系统状态 ===")
    status = orchestrator.get_system_status()
    print(json.dumps(status, indent=2, ensure_ascii=False))
    
    print("\n测试完成！")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())