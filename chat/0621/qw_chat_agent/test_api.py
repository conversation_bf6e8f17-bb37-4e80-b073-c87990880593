#!/usr/bin/env python3
"""
API测试脚本
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8650"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
        self.use_auth = True  # 新增：控制是否使用认证
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def set_auth_mode(self, use_auth: bool):
        """设置认证模式"""
        self.use_auth = use_auth
        print(f"🔧 认证模式设置为: {'启用' if use_auth else '禁用'}")
    
    async def test_health(self) -> Dict[str, Any]:
        """测试健康检查"""
        url = f"{self.base_url}/health"
        
        try:
            async with self.session.get(url) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 健康检查成功: {result['status']}")
                else:
                    print(f"❌ 健康检查失败: {result}")
                return result
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return {"error": str(e)}
    
    async def debug_routes(self) -> Dict[str, Any]:
        """调试：查看所有路由"""
        url = f"{self.base_url}/debug/routes"
        
        try:
            async with self.session.get(url) as response:
                result = await response.json()
                if response.status == 200:
                    print("✅ 路由调试信息:")
                    for route in result.get("routes", []):
                        print(f"   {route['methods']} {route['path']}")
                else:
                    print(f"❌ 获取路由信息失败: {result}")
                return result
        except Exception as e:
            print(f"❌ 路由调试异常: {str(e)}")
            return {"error": str(e)}
    
    async def login(self, user_name: str, password_: str) -> Dict[str, Any]:
        """登录获取token"""
        url = f"{self.base_url}/api/v1/auth/login"
        data = {
            "user_name": user_name,
            "password_": password_
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                result = await response.json()
                if response.status == 200:
                    self.access_token = result["access_token"]
                    print(f"✅ 登录成功，获取到token: {self.access_token[:20]}...")
                else:
                    print(f"❌ 登录失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return {"error": str(e)}
    
    def get_headers(self, force_auth: bool = None) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        
        # 如果force_auth指定了，使用指定值；否则使用self.use_auth
        should_use_auth = force_auth if force_auth is not None else self.use_auth
        
        if should_use_auth:
            if not self.access_token:
                raise ValueError("请先登录获取token")
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        return headers
    
    async def get_user_info(self) -> Dict[str, Any]:
        """获取当前用户信息"""
        url = f"{self.base_url}/api/v1/auth/me"
        
        try:
            async with self.session.get(url, headers=self.get_headers(force_auth=True)) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取用户信息成功: {result['user_name']}")
                else:
                    print(f"❌ 获取用户信息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取用户信息异常: {str(e)}")
            return {"error": str(e)}
    
    async def send_inquiry(self, message: str, session_id: str = None) -> Dict[str, Any]:
        """发送询价消息"""
        url = f"{self.base_url}/api/v1/inquiry/chat"
        data = {
            "message": message,
            # "session_id": session_id,
            "sid": "AOPU_lhh",
            "uid": "123467",
            "supplier_info": {
                "company_name": "苏州金澄精密铸造有限公司"
            }
        }
        
        try:
            async with self.session.post(url, json=data, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 发送询价消息成功")
                    print(f"   消息: {message}")
                    print(f"   会话ID: {result['session_id']}")
                    print(f"   响应: {result['response']}")
                    print(f"   Agent类型: {result.get('agent_type')}")
                    print(f"   需要人工: {result.get('need_human')}")
                    print(f"   响应类型: {result.get('response_type')}")
                    print(f"   物料规格: {result.get('material_spec')}")
                else:
                    print(f"❌ 发送询价消息失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 发送询价消息异常: {str(e)}")
            return {"error": str(e)}
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        url = f"{self.base_url}/api/v1/inquiry/session/{session_id}/status"
        
        try:
            async with self.session.get(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 获取会话状态成功")
                    print(f"   会话ID: {result['session_id']}")
                    print(f"   状态: {result['status']}")
                    print(f"   创建时间: {result['created_at']}")
                    print(f"   更新时间: {result['updated_at']}")
                    print(f"   消息数量: {result['message_count']}")
                    print(f"   当前Agent: {result.get('current_agent')}")
                else:
                    print(f"❌ 获取会话状态失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 获取会话状态异常: {str(e)}")
            return {"error": str(e)}
    
    async def update_session_status(self, session_id: str) -> Dict[str, Any]:
        """更新会话状态"""
        url = f"{self.base_url}/api/v1/inquiry/session/{session_id}/update-status"
        
        try:
            async with self.session.post(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 更新会话状态成功")
                    print(f"   会话ID: {session_id}")
                    print(f"   结果: {result}")
                else:
                    print(f"❌ 更新会话状态失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 更新会话状态异常: {str(e)}")
            return {"error": str(e)}
    
    async def delete_session(self, session_id: str) -> Dict[str, Any]:
        """删除会话"""
        url = f"{self.base_url}/api/v1/inquiry/session/{session_id}"
        
        try:
            async with self.session.delete(url, headers=self.get_headers()) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 删除会话成功")
                    print(f"   会话ID: {session_id}")
                    print(f"   消息: {result.get('message', '会话已删除')}")
                else:
                    print(f"❌ 删除会话失败 (状态码: {response.status}): {result}")
                return result
        except Exception as e:
            print(f"❌ 删除会话异常: {str(e)}")
            return {"error": str(e)}


async def test_with_auth():
    """测试带认证的API"""
    async with APITester(base_url="http://localhost:8650") as tester:
        print("=== 带JWT认证的API测试 ===\n")
        
        # 设置认证模式
        tester.set_auth_mode(True)
        
        # 测试登录
        print("1. 测试登录...")
        result = await tester.login("lhh-aopu", "bluemen.123")
        if "access_token" not in result:
            print("❌ 登录失败，跳过认证测试")
            return
        
        # 测试获取用户信息
        print("\n2. 测试获取用户信息...")
        await tester.get_user_info()
        
        # 测试发送询价消息
        print("\n3. 测试发送询价消息（带认证）...")
        result = await tester.send_inquiry("你好，我是认证用户")
        if "session_id" in result:
            session_id = result["session_id"]
            print(f"   获取到会话ID: {session_id}")
            
            # 测试获取会话状态
            print("\n4. 测试获取会话状态（带认证）...")
            await tester.get_session_status(session_id)


async def test_without_auth():
    """测试无认证的API"""
    async with APITester(base_url="http://localhost:8650") as tester:
    # async with APITester(base_url="http://*************:8650") as tester:
        print("\n=== 无JWT认证的API测试 ===\n")
        
        # 设置无认证模式
        tester.set_auth_mode(False)
        
        # 测试发送询价消息
        print("1. 测试发送询价消息（无认证）...")
        result = await tester.send_inquiry("你好")
        if "session_id" in result:
            session_id = result["session_id"]
            print(f"   获取到会话ID: {session_id}")
            
            # 测试会话连续性
            print("\n2. 测试会话连续性（无认证）...")
            result2 = await tester.send_inquiry("请问贵公司在哪里？", session_id)

            # 测试会话连续性
            print("\n3. 测试会话连续性（无认证）...")
            result3 = await tester.send_inquiry("你们有图纸吗？", session_id)

            # 测试会话连续性
            print("\n4. 测试会话连续性（无认证）...")
            result4 = await tester.send_inquiry("好的，这个我这边先评估一下，晚些时候回复您", session_id)
            
            # 测试获取会话状态
            print("\n3. 测试获取会话状态（无认证）...")
            await tester.get_session_status(session_id)


async def test_api():
    """测试API功能"""
    async with APITester(base_url="http://localhost:8650") as tester:
        print("=== 供应商询价系统API测试 ===\n")
        
        # 0. 测试健康检查
        print("0. 测试健康检查...")
        await tester.test_health()
        print()
        
        # 0.5. 调试路由信息
        print("0.5. 调试路由信息...")
        await tester.debug_routes()
        print()
    
    # 测试带认证的场景
    # await test_with_auth()
    
    # 测试无认证的场景
    await test_without_auth()
    
    print("\n🎉 所有API测试完成！")
    print("\n💡 提示：")
    print("   - 带认证的API会使用JWT token")
    print("   - 无认证的API会以匿名用户身份运行")
    print("   - 两种模式下的会话都会正常保存到Redis")


async def test_session_status_update():
    """专门测试会话状态更新接口"""
    # async with APITester(base_url="http://localhost:8650") as tester:
    async with APITester(base_url="http://*************:8650") as tester:
        print("=== 会话状态更新接口测试 ===\n")
        
        # 设置无认证模式（根据你的系统配置调整）
        tester.set_auth_mode(False)
        
        # 1. 先创建一个会话
        # print("1. 创建测试会话...")
        # result = await tester.send_inquiry("你好，这是状态更新测试")
        # if "session_id" not in result:
        #     print("❌ 创建会话失败，无法进行状态更新测试")
        #     return
        
        # session_id = result["session_id"]
        # print(f"   获取到会话ID: {session_id}")
        
        # 2. 获取当前会话状态
        # print(f"\n2. 获取会话当前状态...")
        # status_result = await tester.get_session_status(session_id)
        # if "status" in status_result:
        #     print(f"   当前状态: {status_result['status']}")
        
        # 3. 发送一个可能触发人工接管的消息（根据你的业务逻辑调整）
        # print(f"\n3. 发送可能触发人工接管的消息...")
        # human_trigger_result = await tester.send_inquiry(
        #     "我需要人工客服协助，这个问题比较复杂", 
        #     session_id
        # )
        # if human_trigger_result.get("need_human"):
        #     print("   ✅ 成功触发人工接管")
        # else:
        #     print("   ℹ️  未触发人工接管，继续测试状态更新功能")
        
        # 4. 再次获取会话状态
        # print(f"\n4. 获取更新后的会话状态...")
        # status_result2 = await tester.get_session_status(session_id)
        # if "status" in status_result2:
        #     print(f"   更新后状态: {status_result2['status']}")
        
        # 5. 测试状态更新接口
        print(f"\n5. 测试状态更新接口...")
        session_id = "AOPU_lhh_1688856647498767_7881300370084610"
        update_result = await tester.update_session_status(session_id)
        
        # 6. 验证状态更新结果
        print(f"\n6. 验证状态更新结果...")
        final_status = await tester.get_session_status(session_id)
        if "status" in final_status:
            print(f"   最终状态: {final_status['status']}")
        
        print(f"\n🎉 会话状态更新测试完成！")
        print(f"   会话ID: {session_id}")
        print(f"   状态变化过程已显示在上述输出中")


async def run_status_update_test_only():
    """仅运行状态更新测试"""
    await test_session_status_update()


async def test_delete_session():
    """专门测试删除会话接口"""
    # async with APITester(base_url="http://localhost:8650") as tester:
    async with APITester(base_url="http://*************:8650") as tester:
        print("=== 删除会话接口测试 ===\n")
        
        # 设置无认证模式
        tester.set_auth_mode(False)
        
        # 1. 创建一个测试会话
        # print("1. 创建测试会话...")
        # result = await tester.send_inquiry("这是一个用于测试删除功能的会话")
        # if "session_id" not in result:
        #     print("❌ 创建会话失败，无法进行删除测试")
        #     return
        
        # session_id = result["session_id"]
        # print(f"   创建的会话ID: {session_id}")
        
        # 2. 发送几条消息让会话有一些内容
        # print(f"\n2. 向会话发送一些消息...")
        # await tester.send_inquiry("第一条测试消息", session_id)
        # await tester.send_inquiry("第二条测试消息", session_id)
        
        # 3. 获取会话状态确认存在
        # print(f"\n3. 确认会话存在并查看状态...")
        # status_result = await tester.get_session_status(session_id)
        # if "session_id" not in status_result:
        #     print("❌ 会话状态获取失败，无法继续测试")
        #     return
        
        # print(f"   会话状态: {status_result.get('status')}")
        # print(f"   消息数量: {status_result.get('message_count')}")
        
        # 4. 删除会话
        print(f"\n4. 删除会话...")
        session_id = "AOPU_lhh_1688856647498767_7881300370084610"
        # session_id = "AOPU_lhh_1688857255678607_7881301254134314"
        delete_result = await tester.delete_session(session_id)
        
        # 5. 尝试再次获取会话状态，应该返回404
        print(f"\n5. 验证会话已删除（尝试获取状态，应该失败）...")
        verify_result = await tester.get_session_status(session_id)
        
        # 检查是否真的删除了
        if "error" in verify_result:
            print("✅ 会话删除验证成功 - 会话已不存在")
        elif verify_result.get("detail") and "不存在" in str(verify_result.get("detail")):
            print("✅ 会话删除验证成功 - 服务器确认会话不存在")
        else:
            print("⚠️  会话可能仍然存在，请检查删除逻辑")
            print(f"   验证结果: {verify_result}")
        
        # 6. 尝试向已删除的会话发送消息，应该失败或创建新会话
        # print(f"\n6. 尝试向已删除的会话发送消息...")
        # try_send_result = await tester.send_inquiry("这条消息发送给已删除的会话", session_id)
        # if "session_id" in try_send_result:
        #     new_session_id = try_send_result["session_id"]
        #     if new_session_id != session_id:
        #         print(f"✅ 系统正确处理 - 创建了新会话: {new_session_id}")
        #     else:
        #         print("⚠️  系统可能重用了已删除的会话ID")
        # else:
        #     print("✅ 系统正确拒绝了向已删除会话发送消息"
        
        print(f"\n🎉 删除会话测试完成！")
        print(f"   原会话ID: {session_id}")
        print(f"   删除结果: {'成功' if delete_result.get('message') else '失败'}")


async def run_delete_session_test():
    """仅运行删除会话测试"""
    await test_delete_session()


if __name__ == "__main__":
    # asyncio.run(test_api())

    # asyncio.run(test_session_status_update())

    asyncio.run(test_delete_session())