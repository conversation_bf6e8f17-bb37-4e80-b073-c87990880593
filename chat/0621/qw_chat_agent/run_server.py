#!/usr/bin/env python3
"""
启动服务器脚本
"""
import uvicorn
import sys
from pathlib import Path
from config.sys_config import SERVICE_PORT, SERVICE_HOST

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=SERVICE_HOST,
        port=SERVICE_PORT,
        reload=False,
        log_level="info"
    ) 