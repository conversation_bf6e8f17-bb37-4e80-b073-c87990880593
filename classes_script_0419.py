import pandas as pd
import json
import os
import argparse
from typing import List, Dict, Any
import requests
from openai import OpenAI

def read_excel_materials(excel_paths: List[str]) -> Dict[str, List[str]]:
    """
    读取多个Excel文件中的物料名称列信息
    
    Args:
        excel_paths: Excel文件路径列表
        
    Returns:
        Dict[str, List[str]]: 以文件名为键，物料名称列表为值的字典
    """
    materials_by_file = {}
    
    for path in excel_paths:
        try:
            # 读取Excel文件
            df = pd.read_excel(path)
            
            # 检查是否存在'物料名称'列
            if '物料名称' not in df.columns:
                print(f"警告: 文件 {path} 中没有'物料名称'列")
                continue
            
            # 获取所有不重复的物料名称
            materials = df['物料名称'].dropna().unique().tolist()
            
            # 以文件名为键存储物料名称列表
            file_name = os.path.basename(path)
            materials_by_file[file_name] = materials
            
            print(f"从 {file_name} 中读取了 {len(materials)} 个物料名称")
            
        except Exception as e:
            print(f"处理文件 {path} 时出错: {e}")
    
    return materials_by_file

def post_openai(prompt: str, model: str="gpt-4o", api_key: str=None, base_url: str=None) -> str:
    """
    使用OpenAI API调用大模型
    
    Args:
        prompt: 提示词
        model: 模型名称，默认为gpt-4o
        api_key: OpenAI API密钥
        base_url: OpenAI API基础URL
        
    Returns:
        str: 模型生成的回答
    """
    try:
        client = OpenAI(api_key=api_key, base_url=base_url)

        messages = [{"role": "system", "content": "你是一个专业的商务助手，擅长撰写简洁有力的商务洽谈语句。请全程用中文回答问题。"}]
        message = {"role": "user", "content": prompt}
        messages.append(message)

        response = client.chat.completions.create(
            model=model,
            messages=messages
        )  

        answer = response.choices[0].message.content
        return answer
    except Exception as e:
        print(f"调用OpenAI API时出错: {e}")
        return None

def generate_conversation_text(materials: List[str], model_name: str = "gpt-4o", api_key: str = None, base_url: str = None) -> str:
    """
    使用大模型生成针对物料名称的洽谈语句
    
    Args:
        materials: 物料名称列表
        model_name: 模型名称，默认为gpt-4o
        api_key: OpenAI API密钥（可选）
        base_url: OpenAI API基础URL（可选）
        
    Returns:
        str: 生成的洽谈语句
    """
    # 如果没有物料名称，返回空字符串
    if not materials or len(materials) == 0:
        return ""
    
    # 构建提示词
    # 计算物料种类数量
    material_count = len(materials)
    
    prompt = f"""
我需要一段简洁、专业的语句，用于与潜在的加工厂进行合作洽谈。我们有一个长期合作项目，需要加工以下{material_count}种不同类型的材料：
{', '.join(materials)}

这些材料将用于一台小型的医疗检测仪器。请生成一段简短的话术（1-2句话），目标是吸引这些潜在的加工厂，强调这是一个长期合作项目，需要稳定供货，如果经过验证可以，将达成长期合作关系。语言要简洁、无歧义、专业。

注意：在最终话术中不要列出具体的物料名称，只需要提及物料的种类数量，并提及这些材料用于小型医疗检测仪器。
"""
    
    # 如果提供了API密钥，则调用OpenAI API
    if api_key:
        try:
            result = post_openai(prompt, model_name, api_key, base_url)
            if result:
                return result
        except Exception as e:
            print(f"调用API时出错: {e}")
            # 如果API调用失败，使用默认生成方法
    
    # 默认生成方法（当没有提供API或API调用失败时）
    # 这里提供一个简单的模板化生成方法
    if material_count == 1:
        return f"诚挂寻找医疗检测仪器细密零件加工合作伙伴，我们提供长期稳定订单，经验证合格后将建立长期供应关系，期待与贵司共创双赢。"
    else:
        return f"诚挂寻找能够加工{material_count}种医疗检测仪器所需材料的优质合作伙伴，我们提供长期稳定订单，经验证合格后将建立长期供应关系，期待与贵司共创双赢。"

def process_excel_files(excel_paths: List[str], output_json: str, model_name: str = "gpt-4o", api_key: str = None, base_url: str = None) -> Dict[str, str]:
    """
    处理多个Excel文件，生成洽谈语句并保存为JSON
    
    Args:
        excel_paths: Excel文件路径列表
        output_json: 输出JSON文件路径
        model_name: 大模型的名称（可选）
        api_key: API密钥（可选）
        base_url: API基础URL（可选）
        
    Returns:
        Dict[str, str]: 以文件名为键，生成的洽谈语句为值的字典
    """
    # 读取所有Excel文件中的物料名称信息
    materials_by_file = read_excel_materials(excel_paths)
    
    # 为每个文件生成洽谈语句
    conversation_texts = {}
    for file_name, materials in materials_by_file.items():
        text = generate_conversation_text(materials, model_name, api_key, base_url)
        conversation_texts[file_name] = text
        print(f"为 {file_name} 生成的洽谈语句: {text}")
    
    # 保存为JSON文件
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(conversation_texts, f, ensure_ascii=False, indent=4)
    
    print(f"已将结果保存到 {output_json}")
    
    return conversation_texts

def test():
    """
    测试函数，直接在函数内指定参数值
    """
    # 直接指定参数值
    excel_paths = [
        "cy3000-坤德-材质-0418_不锈钢.xlsx",  
        "cy3000-坤德-材质-0418_镀锌钢板.xlsx" ,
        "cy3000-坤德-材质-0418_工程塑料.xlsx",
        "cy3000-坤德-材质-0418_铝合金.xlsx",
        "cy3000-坤德-材质-0418_碳素结构钢.xlsx",
    ]
    output_json = "cy3000/0419/供应商洽谈语句.json"  # 输出JSON文件路径
    
    # 可选：大模型API信息（如果有）
    model_name = "gpt-4.1-2025-04-14"  # 模型名称
    api_key = "***************************************************"  # 你的OpenAI API密钥
    base_url = "http://43.130.31.174:8003/v1"  # OpenAI API基础URL，如果使用代理或自定义端点
    
    print("开始测试处理...")
    print(f"Excel文件路径: {excel_paths}")
    print(f"输出JSON文件: {output_json}")
    
    # 处理Excel文件
    conversation_texts = process_excel_files(
        excel_paths, 
        output_json,
        model_name,
        api_key,
        base_url
    )
    
    print("测试处理完成!")
    return conversation_texts

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='读取多个Excel文件中的供应商信息，生成洽谈语句并保存为JSON')
    parser.add_argument('--excel-paths', required=True, nargs='+', help='Excel文件路径列表')
    parser.add_argument('--output-json', required=True, help='输出JSON文件路径')
    parser.add_argument('--model-name', default="gpt-4o", help='OpenAI模型名称（可选）')
    parser.add_argument('--api-key', help='OpenAI API密钥（可选）')
    parser.add_argument('--base-url', help='OpenAI API基础URL（可选）')
    parser.add_argument('--test', action='store_true', help='运行测试模式，忽略其他参数')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 如果是测试模式，直接运行test函数
    if args.test:
        test()
        return
    
    # 处理Excel文件
    process_excel_files(
        args.excel_paths,
        args.output_json,
        args.model_name,
        args.api_key,
        args.base_url
    )
    
    print("处理完成!")

if __name__ == "__main__":
    # main()
    test()
