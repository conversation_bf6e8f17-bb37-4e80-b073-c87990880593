import pandas as pd
import numpy as np
from collections import defaultdict

def analyze_material_cost():
    try:
        # 读取Excel文件的"详细组件清单"工作表
        excel_file = 'data/material_reslation_0618.xlsx'
        excel_df = pd.read_excel(excel_file, sheet_name='详细组件清单')
        
        # 读取CSV文件
        csv_file = 'data/cy3000_05_15_0618.csv'
        csv_df = pd.read_csv(csv_file)
        
        print("=" * 80)
        print("数据源分析")
        print("=" * 80)
        print(f"Excel文件行数: {len(excel_df)}")
        print(f"CSV文件行数: {len(csv_df)}")
        
        # 检查关键列
        print("\nExcel文件关键列:")
        excel_key_cols = ['组件编码', '一级分类', '二级分类']
        for col in excel_key_cols:
            if col in excel_df.columns:
                print(f"✓ {col}")
            else:
                print(f"✗ {col} - 缺失")
        
        print("\nCSV文件关键列:")
        csv_key_cols = ['物料编码', '物料名称', '商家名称', '原单价', '新单价', '单台用量']
        for col in csv_key_cols:
            if col in csv_df.columns:
                print(f"✓ {col}")
            else:
                print(f"✗ {col} - 缺失")
        
        # 清理数据：移除空行和无效数据
        csv_df = csv_df.dropna(subset=['物料编码', '原单价', '新单价', '单台用量'])
        csv_df = csv_df[csv_df['物料编码'].notna()]
        
        # 转换数值类型
        csv_df['原单价'] = pd.to_numeric(csv_df['原单价'], errors='coerce')
        csv_df['新单价'] = pd.to_numeric(csv_df['新单价'], errors='coerce')
        csv_df['单台用量'] = pd.to_numeric(csv_df['单台用量'], errors='coerce')
        
        # 删除转换失败的行
        csv_df = csv_df.dropna(subset=['原单价', '新单价', '单台用量'])
        
        print(f"\n清理后CSV有效数据行数: {len(csv_df)}")
        
        # 创建物料编码到分类的映射
        if '组件编码' in excel_df.columns and '一级分类' in excel_df.columns and '二级分类' in excel_df.columns:
            material_mapping = {}
            for _, row in excel_df.iterrows():
                material_code = row['组件编码']
                if pd.notna(material_code):
                    material_mapping[material_code] = {
                        '一级分类': row.get('一级分类', '未分类'),
                        '二级分类': row.get('二级分类', '未分类')
                    }
            
            print(f"建立的物料映射关系数量: {len(material_mapping)}")
        else:
            print("Excel文件缺少必要的分类列，使用CSV中的物料类别")
            material_mapping = {}
        
        # 为CSV数据添加分类信息
        def get_category(material_code):
            if material_code in material_mapping:
                return material_mapping[material_code]['一级分类'], material_mapping[material_code]['二级分类']
            else:
                # 如果Excel中没有找到，尝试从CSV的物料类别推断
                matching_rows = csv_df[csv_df['物料编码'] == material_code]
                if len(matching_rows) > 0:
                    csv_category = matching_rows['物料类别'].iloc[0] if pd.notna(matching_rows['物料类别'].iloc[0]) else '未分类'
                    return csv_category, '未细分'
                return '未分类', '未细分'
        
        csv_df[['一级分类', '二级分类']] = csv_df['物料编码'].apply(
            lambda x: pd.Series(get_category(x))
        )
        
        # 计算每行的成本差异
        csv_df['单位差价'] = csv_df['原单价'] - csv_df['新单价']
        csv_df['总差价'] = csv_df['单位差价'] * csv_df['单台用量']
        csv_df['原总价'] = csv_df['原单价'] * csv_df['单台用量']
        csv_df['新总价'] = csv_df['新单价'] * csv_df['单台用量']
        
        # 按一级分类->二级分类->物料->供应商组织数据
        result_data = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        
        for _, row in csv_df.iterrows():
            一级 = row['一级分类']
            二级 = row['二级分类']
            物料 = f"{row['物料编码']} - {row['物料名称']}"
            供应商 = row['商家名称'] if pd.notna(row['商家名称']) else '未知供应商'
            
            result_data[一级][二级][物料].append({
                '供应商': 供应商,
                '原单价': row['原单价'],
                '新单价': row['新单价'],
                '单台用量': row['单台用量'],
                '单位差价': row['单位差价'],
                '总差价': row['总差价'],
                '原总价': row['原总价'],
                '新总价': row['新总价']
            })
        
        # 输出结果
        print("\n" + "=" * 80)
        print("按分类组织的物料成本分析")
        print("=" * 80)
        
        total_original_cost = 0
        total_new_cost = 0
        total_savings = 0
        material_savings_detail = []
        
        for 一级分类 in sorted(result_data.keys()):
            print(f"\n【{一级分类}】")
            
            for 二级分类 in sorted(result_data[一级分类].keys()):
                print(f"  └─ {二级分类}")
                
                for 物料 in sorted(result_data[一级分类][二级分类].keys()):
                    print(f"    └─ {物料}")
                    
                    suppliers_data = result_data[一级分类][二级分类][物料]
                    
                    # 为每个物料找到最大差价的供应商
                    max_saving_supplier = max(suppliers_data, key=lambda x: x['总差价'])
                    
                    # 记录物料的最大节省
                    material_savings_detail.append({
                        '一级分类': 一级分类,
                        '二级分类': 二级分类,
                        '物料': 物料,
                        '最优供应商': max_saving_supplier['供应商'],
                        '原总价': max_saving_supplier['原总价'],
                        '新总价': max_saving_supplier['新总价'],
                        '总节省': max_saving_supplier['总差价'],
                        '单台用量': max_saving_supplier['单台用量']
                    })
                    
                    total_original_cost += max_saving_supplier['原总价']
                    total_new_cost += max_saving_supplier['新总价']
                    total_savings += max_saving_supplier['总差价']
                    
                    # 显示所有供应商信息
                    for supplier_info in suppliers_data:
                        is_best = "★" if supplier_info == max_saving_supplier else "  "
                        print(f"      {is_best} {supplier_info['供应商']}: "
                              f"原价{supplier_info['原单价']:.2f} → 新价{supplier_info['新单价']:.2f} "
                              f"(用量{supplier_info['单台用量']}) "
                              f"节省: {supplier_info['总差价']:.2f}元")
        
        # 总结信息
        print("\n" + "=" * 80)
        print("成本节省总结")
        print("=" * 80)
        print(f"原总成本: {total_original_cost:.2f} 元")
        print(f"新总成本: {total_new_cost:.2f} 元")
        print(f"总节省金额: {total_savings:.2f} 元")
        
        if total_original_cost > 0:
            savings_rate = (total_savings / total_original_cost) * 100
            print(f"总节省比例: {savings_rate:.2f}%")
        
        # 生成详细的节省清单
        print(f"\n物料总数: {len(material_savings_detail)}")
        print(f"有节省的物料数: {len([x for x in material_savings_detail if x['总节省'] > 0])}")
        print(f"成本上升的物料数: {len([x for x in material_savings_detail if x['总节省'] < 0])}")
        
        # 按节省金额排序显示前10大节省项目
        top_savings = sorted(material_savings_detail, key=lambda x: x['总节省'], reverse=True)[:10]
        print(f"\n前10大节省项目:")
        for i, item in enumerate(top_savings, 1):
            print(f"{i:2d}. {item['物料']}")
            print(f"     最优供应商: {item['最优供应商']}")
            print(f"     节省金额: {item['总节省']:.2f}元")
        
        # 返回处理后的数据用于进一步分析
        return {
            'organized_data': result_data,
            'summary': {
                'total_original_cost': total_original_cost,
                'total_new_cost': total_new_cost,
                'total_savings': total_savings,
                'savings_rate': (total_savings / total_original_cost * 100) if total_original_cost > 0 else 0
            },
            'material_details': material_savings_detail
        }
        
    except Exception as e:
        print(f"处理数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = analyze_material_cost() 