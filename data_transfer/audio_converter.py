#!/usr/bin/env python3
"""
音频文件转换脚本
支持将 MP3、M4A 等格式转换为 WAV 格式
可指定输出的声道数和采样率
"""

import argparse
import os
import sys
from pathlib import Path

try:
    from pydub import AudioSegment
    from pydub.utils import which
except ImportError:
    print("错误: 需要安装 pydub 库")
    print("请运行: pip install pydub")
    sys.exit(1)

def convert_audio(input_file, output_file=None, channels=1, sample_rate=16000, compress=False, bitrate='64k'):
    """
    转换音频文件格式
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径，如果为None则自动生成
        channels (int): 声道数，1为单声道，2为立体声
        sample_rate (int): 采样率 (Hz)
        compress (bool): 是否启用压缩模式输出MP3
        bitrate (str): 音频比特率
    
    Returns:
        str: 输出文件路径
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 获取文件扩展名
        input_path = Path(input_file)
        file_extension = input_path.suffix.lower()
        
        # 加载音频文件
        print(f"正在加载音频文件: {input_file}")
        if file_extension == '.mp3':
            audio = AudioSegment.from_mp3(input_file)
        elif file_extension in ['.m4a', '.mp4', '.aac']:
            audio = AudioSegment.from_file(input_file, format="m4a")
        elif file_extension == '.wav':
            audio = AudioSegment.from_wav(input_file)
        else:
            # 尝试自动检测格式
            audio = AudioSegment.from_file(input_file)
        
        # 设置声道数
        if channels == 1:
            audio = audio.set_channels(1)  # 转为单声道
        elif channels == 2:
            audio = audio.set_channels(2)  # 转为立体声
        else:
            raise ValueError("声道数只支持 1（单声道）或 2（立体声）")
        
        # 设置采样率
        audio = audio.set_frame_rate(sample_rate)
        
        # 确定输出文件路径和格式
        if output_file is None:
            if compress:
                output_file = str(input_path.with_suffix('.mp3'))
            else:
                output_file = str(input_path.with_suffix('.wav'))
        
        # 导出音频文件
        if compress:
            print(f"正在转换为MP3格式（压缩模式）...")
            print(f"输出设置: {channels}声道, {sample_rate}Hz, {bitrate}")
            audio.export(output_file, format="mp3", bitrate=bitrate)
        else:
            print(f"正在转换为WAV格式...")
            print(f"输出设置: {channels}声道, {sample_rate}Hz")
            audio.export(output_file, format="wav")
        
        print(f"转换完成: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return None

def main():
    parser = argparse.ArgumentParser(
        description="将音频文件转换为WAV格式",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python audio_converter.py input.mp3
  python audio_converter.py input.m4a -o output.wav
  python audio_converter.py input.mp3 -c 2 -r 44100
  python audio_converter.py input.m4a -c 1 -r 8000 -o output.wav
        """
    )
    
    parser.add_argument('input', help='输入音频文件路径')
    parser.add_argument('-o', '--output', help='输出WAV文件路径（可选，默认与输入文件同名）')
    parser.add_argument('-c', '--channels', type=int, default=1, choices=[1, 2],
                       help='声道数: 1=单声道, 2=立体声 (默认: 1)')
    parser.add_argument('-r', '--rate', type=int, default=16000,
                       help='采样率 (Hz) (默认: 16000)')
    parser.add_argument('--compress', action='store_true',
                       help='启用压缩模式，输出MP3格式以减小文件大小')
    parser.add_argument('-b', '--bitrate', type=str, default='64k',
                       help='音频比特率 (默认: 64k，仅在压缩模式下有效)')
    
    args = parser.parse_args()
    
    # 执行转换
    result = convert_audio(
        input_file=args.input,
        output_file=args.output,
        channels=args.channels,
        sample_rate=args.rate,
        compress=args.compress,
        bitrate=args.bitrate
    )
    
    if result:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()