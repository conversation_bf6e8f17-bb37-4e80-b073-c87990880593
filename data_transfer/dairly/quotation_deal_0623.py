import pandas as pd

def main():
    csv_file_path = "data/cy3000_06_23_hide.csv"
    
    try:
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        print(f"成功读取CSV文件，共{len(df)}行数据")
        
        df = df.dropna(subset=['材质', '物料编码'])
        print(f"去除材质和物料编码为空的行后，剩余{len(df)}行")
        
        df_dedup = df.drop_duplicates(subset=['材质', '物料编码'])
        print(f"根据材质和物料编码去重后，剩余{len(df_dedup)}行")
        
        df_dedup['原单价'] = pd.to_numeric(df_dedup['原单价'], errors='coerce')
        df_dedup['新单价'] = pd.to_numeric(df_dedup['新单价'], errors='coerce')  
        df_dedup['单台用量'] = pd.to_numeric(df_dedup['单台用量'], errors='coerce')
        
        df_dedup = df_dedup.dropna(subset=['原单价', '新单价', '单台用量'])
        print(f"去除价格和用量数据无效的行后，剩余{len(df_dedup)}行")
        
        df_dedup['原总价'] = df_dedup['原单价'] * df_dedup['单台用量']
        df_dedup['新总价'] = df_dedup['新单价'] * df_dedup['单台用量']
        
        original_total = df_dedup['原总价'].sum()
        new_total = df_dedup['新总价'].sum()
        difference = new_total - original_total
        
        print("\n========== 计算结果 ==========")
        print(f"原单价*单台用量 总和: {original_total:.2f}")
        print(f"新单价*单台用量 总和: {new_total:.2f}")
        print(f"差值 (新-原): {difference:.2f}")
        
        if difference > 0:
            print(f"新价格比原价格高 {difference:.2f}")
        elif difference < 0:
            print(f"新价格比原价格低 {abs(difference):.2f}")
        else:
            print("新价格与原价格相同")
            
    except FileNotFoundError:
        print(f"错误：找不到文件 {csv_file_path}")
    except Exception as e:
        print(f"处理过程中出现错误：{e}")

if __name__ == "__main__":
    main()
