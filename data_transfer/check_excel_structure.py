import pandas as pd
import sys

def check_excel_structure():
    try:
        # 读取Excel文件的"详细组件清单"工作表
        excel_file = '/Users/<USER>/Desktop/llm/yj/aopu/data_transfer/data/material_reslation_0618.xlsx'
        
        # 读取指定的工作表
        df = pd.read_excel(excel_file, sheet_name='详细组件清单')
        
        print("Excel文件结构分析:")
        print("=" * 50)
        print(f"工作表名称: 详细组件清单")
        print(f"数据行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print("\n列名列表:")
        for i, col in enumerate(df.columns):
            print(f"{i+1}. {col}")
        
        print("\n前5行数据预览:")
        print(df.head())
        
        # 检查关键列是否存在
        required_columns = ['组件编码', '物料编码', '一级分类', '二级分类']
        print("\n关键列检查:")
        for col in required_columns:
            if col in df.columns:
                print(f"✓ {col} - 存在")
                # 显示该列的唯一值数量
                unique_count = df[col].nunique()
                print(f"  唯一值数量: {unique_count}")
                if unique_count <= 10:
                    print(f"  唯一值: {df[col].unique().tolist()}")
            else:
                print(f"✗ {col} - 不存在")
        
        # 显示组件编码和物料编码的对应关系示例
        if '组件编码' in df.columns and '物料编码' in df.columns:
            print("\n组件编码与物料编码对应关系示例:")
            mapping_sample = df[['组件编码', '物料编码', '一级分类', '二级分类']].head(10)
            print(mapping_sample)
        
        return df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

if __name__ == "__main__":
    df = check_excel_structure()