# 奥普数据处理工具集

这是一个用于物料成本分析和音频处理的Python工具集，主要服务于供应链成本优化和音频内容处理需求。

## 📋 目录

- [功能概述](#功能概述)
- [环境要求](#环境要求)
- [安装指南](#安装指南)
- [使用说明](#使用说明)
- [文件结构](#文件结构)
- [数据格式](#数据格式)

## 🎯 功能概述

### 物料成本分析系统
- **成本对比分析**：对比原价格与新价格，计算节省金额和比例
- **分类组织**：按一级/二级分类组织物料数据
- **供应商优化**：自动识别最优供应商方案
- **Excel报告生成**：生成包含多个工作表的详细分析报告

### 音频处理工具
- **格式转换**：支持MP3、M4A、AAC等格式转换为WAV
- **参数调节**：可自定义声道数、采样率等参数
- **语音识别**：基于Whisper模型的语音转文字功能

## 🔧 环境要求

- Python 3.7+
- pandas
- numpy
- openpyxl
- pydub
- modelscope（用于语音识别）

## 📦 安装指南

1. **克隆项目**
```bash
git clone <repository-url>
cd data_transfer
```

2. **安装依赖**
```bash
pip install pandas numpy openpyxl pydub modelscope
```

3. **音频处理额外依赖**
```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# Windows
# 下载FFmpeg并添加到PATH环境变量
```

## 📖 使用说明

### 物料成本分析

#### 1. 控制台输出分析
```bash
python analyze_material_cost.py
```
- 在控制台输出详细的成本分析结果
- 显示按分类组织的物料信息
- 标记最优供应商方案

#### 2. Excel报告生成
```bash
python analyze_material_cost_to_excel.py
```
生成包含以下工作表的Excel文件：
- **详细数据**：所有物料的完整信息
- **最优供应商汇总**：每个物料的最佳供应商选择
- **分类汇总**：按分类统计的成本信息
- **总体汇总**：整体成本分析统计
- **前10大节省项目**：节省金额最多的物料
- **供应商分析**：按供应商统计的分析结果

#### 3. 数据结构检查
```bash
python check_excel_structure.py
```
- 检查Excel文件的结构和完整性
- 验证关键列是否存在
- 显示数据预览和统计信息

### 音频处理

#### 1. 音频格式转换
```bash
# 基础转换（转为单声道16kHz WAV）
python audio_converter.py input.mp3

# 自定义参数
python audio_converter.py input.m4a -o output.wav -c 2 -r 44100

# 压缩模式（输出MP3）
python audio_converter.py input.wav --compress -b 128k
```

**参数说明：**
- `-o, --output`：指定输出文件路径
- `-c, --channels`：声道数（1=单声道，2=立体声）
- `-r, --rate`：采样率（Hz）
- `--compress`：启用压缩模式，输出MP3格式
- `-b, --bitrate`：音频比特率（仅压缩模式）

#### 2. 语音识别
```bash
python asr_1.py
```
- 对`output.wav`文件进行语音识别
- 使用Whisper-large-v3-turbo模型
- 识别结果输出到控制台

## 📁 文件结构

```
data_transfer/
├── analyze_material_cost.py          # 物料成本分析（控制台输出）
├── analyze_material_cost_to_excel.py # 物料成本分析（Excel报告）
├── check_excel_structure.py          # Excel文件结构检查
├── audio_converter.py                # 音频格式转换工具
├── asr_1.py                          # 语音识别脚本
├── data/                             # 数据文件目录
│   ├── material_reslation_0618.xlsx  # 物料关系数据
│   ├── cy3000_05_15_0618.csv        # 成本数据
│   ├── aopu-0619.m4a                # 原始音频文件
│   └── aopu-0619.mp3                # 转换后音频文件
├── output.txt                        # 文本输出文件
├── output.wav                        # 音频输出文件
└── README.md                         # 项目说明文档
```

## 📊 数据格式

### Excel文件（物料关系数据）
**工作表：详细组件清单**
- `组件编码`：组件的唯一标识
- `一级分类`：物料的主要分类
- `二级分类`：物料的细分类别

### CSV文件（成本数据）
**必需列：**
- `物料编码`：物料的唯一标识
- `物料名称`：物料的描述性名称
- `商家名称`：供应商名称
- `原单价`：原始单价
- `新单价`：新的单价
- `单台用量`：单台设备的用量
- `物料类别`：物料分类信息

## 📈 输出结果

### 成本分析输出
- **节省金额计算**：原价 - 新价
- **节省比例**：(节省金额 / 原价) × 100%
- **最优供应商识别**：选择节省金额最大的供应商
- **分类汇总统计**：按分类统计的成本信息

### Excel报告内容
1. **详细数据**：完整的物料信息和计算结果
2. **最优供应商汇总**：推荐的供应商选择方案
3. **分类汇总**：按分类的成本统计
4. **总体汇总**：项目整体的成本分析
5. **前10大节省项目**：最具成本优化价值的物料
6. **供应商分析**：供应商维度的统计分析

## ⚠️ 注意事项

1. **数据文件路径**：确保`data/`目录下的Excel和CSV文件存在
2. **数据格式**：CSV文件中的价格和用量字段必须为数值格式
3. **音频文件**：确保FFmpeg已正确安装用于音频处理
4. **模型下载**：首次运行语音识别时会自动下载Whisper模型

## 🔄 更新日志

- **v1.0.0**：初始版本，包含基础的成本分析和音频处理功能
- 支持Excel报告生成
- 集成语音识别功能
- 添加数据结构检查工具

---

**开发团队**：奥普数据处理团队  
**最后更新**：2024年6月