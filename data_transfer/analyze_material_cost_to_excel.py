import pandas as pd
import numpy as np
from collections import defaultdict
from datetime import datetime

def analyze_material_cost_to_excel():
    try:
        # 读取Excel文件的"详细组件清单"工作表
        excel_file = 'data/material_reslation_0618.xlsx'
        excel_df = pd.read_excel(excel_file, sheet_name='详细组件清单')
        
        # 读取CSV文件
        csv_file = 'data/cy3000_05_15_0618.csv'
        csv_df = pd.read_csv(csv_file)
        
        print("=" * 80)
        print("数据源分析")
        print("=" * 80)
        print(f"Excel文件行数: {len(excel_df)}")
        print(f"CSV文件行数: {len(csv_df)}")
        
        # 清理数据：移除空行和无效数据
        csv_df = csv_df.dropna(subset=['物料编码'])
        csv_df = csv_df[csv_df['物料编码'].notna()]
        
        # 转换数值类型
        csv_df['原单价'] = pd.to_numeric(csv_df['原单价'], errors='coerce')
        csv_df['新单价'] = pd.to_numeric(csv_df['新单价'], errors='coerce')
        csv_df['单台用量'] = pd.to_numeric(csv_df['单台用量'], errors='coerce')
        
        # 删除关键数值字段缺失的行
        csv_df = csv_df.dropna(subset=['原单价', '新单价', '单台用量'])
        
        print(f"清理后CSV有效数据行数: {len(csv_df)}")
        
        # 创建物料编码到分类的映射
        material_mapping = {}
        if '组件编码' in excel_df.columns and '一级分类' in excel_df.columns and '二级分类' in excel_df.columns:
            for _, row in excel_df.iterrows():
                material_code = row['组件编码']  # 注意这里是'组件编码'不是'物料编码'
                if pd.notna(material_code):
                    material_mapping[material_code] = {
                        '一级分类': row.get('一级分类', '未分类'),
                        '二级分类': row.get('二级分类', '未分类')
                    }
            print(f"建立的物料映射关系数量: {len(material_mapping)}")
        else:
            print("Excel文件缺少必要的分类列")
        
        # 改进的分类获取函数
        def get_category(material_code):
            if material_code in material_mapping:
                return material_mapping[material_code]['一级分类'], material_mapping[material_code]['二级分类']
            else:
                # 使用CSV中的物料类别作为一级分类
                matching_rows = csv_df[csv_df['物料编码'] == material_code]
                if len(matching_rows) > 0:
                    物料类别 = matching_rows['物料类别'].iloc[0]
                    if pd.notna(物料类别) and 物料类别.strip() != '':
                        return 物料类别, '未细分'
                    else:
                        # 根据物料编码前缀推断分类
                        code_str = str(material_code)
                        if code_str.startswith('1206'):
                            return '精密金属机械加工组件', '未细分'
                        elif code_str.startswith('1207'):
                            return '塑料注塑与加工组件', '未细分'
                        elif code_str.startswith('1204'):
                            return '液体传输与控制组件', '未细分'
                        elif code_str.startswith('1209'):
                            return '机架组件', '未细分'
                        else:
                            return '其他组件', '未细分'
                return '未分类', '未细分'
        
        # 添加分类列
        category_info = csv_df['物料编码'].apply(lambda x: pd.Series(get_category(x)))
        csv_df['一级分类'] = category_info[0]
        csv_df['二级分类'] = category_info[1]
        
        # 计算新增列
        csv_df['单位差价'] = csv_df['原单价'] - csv_df['新单价']
        csv_df['总差价'] = csv_df['单位差价'] * csv_df['单台用量']
        csv_df['原总价'] = csv_df['原单价'] * csv_df['单台用量']
        csv_df['新总价'] = csv_df['新单价'] * csv_df['单台用量']
        csv_df['节省比例'] = np.where(csv_df['原单价'] != 0, 
                                  (csv_df['单位差价'] / csv_df['原单价'] * 100), 0)
        
        # 重新排列列的顺序，按新的展示维度
        column_order = [
            '一级分类', '二级分类', '物料编码', '物料名称', '型号规格', 
            '商家名称', '注册资金', '联系电话', '人员规模', '成立日期',
            '公司网址', '联系邮箱', '经营范围',
            '物料类别', '材质', '单台用量',
            '原单价', '新单价', '单位差价', '节省比例',
            '原总价', '新总价', '总差价',
            '原供应商', '交货期'
        ]
        
        # 只保留存在的列
        available_columns = [col for col in column_order if col in csv_df.columns]
        remaining_columns = [col for col in csv_df.columns if col not in available_columns]
        final_columns = available_columns + remaining_columns
        
        # 按新顺序排列数据
        result_df = csv_df[final_columns].copy()
        
        # 按一级分类、二级分类、物料编码、总差价（降序）排序
        result_df = result_df.sort_values([
            '一级分类', '二级分类', '物料编码', '总差价'
        ], ascending=[True, True, True, False])
        
        # 为每个物料标记最优供应商
        def mark_best_supplier(group):
            if len(group) > 1:
                max_saving_idx = group['总差价'].idxmax()
                group['是否最优供应商'] = '否'
                group.loc[max_saving_idx, '是否最优供应商'] = '是'
            else:
                group['是否最优供应商'] = '是'
            return group
        
        result_df = result_df.groupby('物料编码', group_keys=False).apply(mark_best_supplier).reset_index(drop=True)
        
        # 创建汇总分析数据
        summary_data = []
        
        # 按物料分组，选择每个物料的最优供应商进行汇总
        for material_code, group in result_df.groupby('物料编码'):
            best_supplier = group.loc[group['总差价'].idxmax()]
            summary_data.append({
                '一级分类': best_supplier['一级分类'],
                '二级分类': best_supplier['二级分类'],
                '物料编码': material_code,
                '物料名称': best_supplier['物料名称'],
                '最优供应商': best_supplier['商家名称'],  # 修正：使用'商家名称'
                '单台用量': best_supplier['单台用量'],
                '原单价': best_supplier['原单价'],
                '新单价': best_supplier['新单价'],
                '单位差价': best_supplier['单位差价'],
                '原总价': best_supplier['原总价'],
                '新总价': best_supplier['新总价'],
                '总节省': best_supplier['总差价'],
                '节省比例': best_supplier['节省比例'],
                '交货期': best_supplier.get('交货期', ''),
                '备注': best_supplier.get('备注', '')
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values(['一级分类', '二级分类', '总节省'], 
                                          ascending=[True, True, False])
        
        # 计算总体统计
        total_original_cost = summary_df['原总价'].sum()
        total_new_cost = summary_df['新总价'].sum()
        total_savings = summary_df['总节省'].sum()
        savings_rate = (total_savings / total_original_cost * 100) if total_original_cost > 0 else 0
        
        # 修改后的分类汇总 - 简化版本，只添加供应商信息
        def create_category_summary_with_suppliers(summary_df):
            category_groups = summary_df.groupby(['一级分类', '二级分类'])
            category_summary_list = []
            
            for (一级分类, 二级分类), group in category_groups:
                # 基本统计信息
                物料数量 = len(group)
                原总价 = group['原总价'].sum()
                新总价 = group['新总价'].sum()
                总节省 = group['总节省'].sum()
                节省比例 = (总节省 / 原总价 * 100) if 原总价 != 0 else 0
                
                # 收集该分类下的所有供应商
                供应商列表 = group['最优供应商'].dropna().unique()
                供应商数量 = len(供应商列表)
                
                # 格式化供应商列表
                if 供应商数量 <= 5:
                    对应供应商 = '; '.join(供应商列表)
                else:
                    # 如果供应商太多，显示前5个加上总数
                    对应供应商 = '; '.join(供应商列表[:5]) + f'; 等{供应商数量}家供应商'
                
                category_summary_list.append({
                    '一级分类': 一级分类,
                    '二级分类': 二级分类,
                    '物料数量': 物料数量,
                    '原总价': 原总价,
                    '新总价': 新总价,
                    '总节省': 总节省,
                    '节省比例': 节省比例,
                    '供应商数量': 供应商数量,
                    '对应供应商': 对应供应商
                })
            
            return pd.DataFrame(category_summary_list)
        
        category_summary = create_category_summary_with_suppliers(summary_df)
        category_summary = category_summary.sort_values(['总节省'], ascending=False)
        
        # 保存到Excel文件
        output_file = f'物料成本分析结果_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 保存详细数据
            result_df.to_excel(writer, sheet_name='详细数据', index=False)
            
            # 保存最优供应商汇总
            summary_df.to_excel(writer, sheet_name='最优供应商汇总', index=False)
            
            # 保存分类汇总（包含供应商信息）
            category_summary.to_excel(writer, sheet_name='分类汇总', index=False)
            
            # 创建总体汇总表
            overall_summary = pd.DataFrame({
                '项目': ['物料总数', '原总成本', '新总成本', '总节省金额', '总节省比例', 
                        '有节省的物料数', '成本上升的物料数', '涉及供应商总数'],
                '值': [
                    len(summary_df),
                    f'{total_original_cost:.2f}',
                    f'{total_new_cost:.2f}',
                    f'{total_savings:.2f}',
                    f'{savings_rate:.2f}%',
                    len(summary_df[summary_df['总节省'] > 0]),
                    len(summary_df[summary_df['总节省'] < 0]),
                    len(summary_df['最优供应商'].unique())
                ]
            })
            overall_summary.to_excel(writer, sheet_name='总体汇总', index=False)
            
            # 前10大节省项目
            top_savings = summary_df.nlargest(10, '总节省')[
                ['物料编码', '物料名称', '最优供应商', '总节省', '节省比例']
            ].copy()
            top_savings.reset_index(drop=True, inplace=True)
            top_savings.index += 1
            top_savings.to_excel(writer, sheet_name='前10大节省项目')
            
            # 新增：供应商详细分析
            supplier_analysis = summary_df.groupby('最优供应商').agg({
                '物料编码': 'count',
                '原总价': 'sum',
                '新总价': 'sum',
                '总节省': 'sum'
            }).reset_index()
            supplier_analysis.columns = ['供应商名称', '物料数量', '原总价', '新总价', '总节省']
            supplier_analysis['节省比例'] = np.where(supplier_analysis['原总价'] != 0,
                                               supplier_analysis['总节省'] / supplier_analysis['原总价'] * 100, 0)
            supplier_analysis = supplier_analysis.sort_values('总节省', ascending=False)
            supplier_analysis.to_excel(writer, sheet_name='供应商分析', index=False)
        
        print(f"\n" + "=" * 80)
        print("Excel文件生成完成")
        print("=" * 80)
        print(f"文件名: {output_file}")
        print(f"包含工作表:")
        print(f"  - 详细数据: {len(result_df)} 行")
        print(f"  - 最优供应商汇总: {len(summary_df)} 行") 
        print(f"  - 分类汇总: {len(category_summary)} 行 (包含供应商信息)")
        print(f"  - 总体汇总: 汇总统计信息")
        print(f"  - 前10大节省项目: 节省最多的10个物料")
        print(f"  - 供应商分析: 按供应商汇总的分析")
        
        print(f"\n成本分析结果:")
        print(f"原总成本: {total_original_cost:.2f} 元")
        print(f"新总成本: {total_new_cost:.2f} 元")
        print(f"总节省金额: {total_savings:.2f} 元")
        print(f"总节省比例: {savings_rate:.2f}%")
        
        return output_file, {
            'total_original_cost': total_original_cost,
            'total_new_cost': total_new_cost,
            'total_savings': total_savings,
            'savings_rate': savings_rate,
            'material_count': len(summary_df)
        }
        
    except Exception as e:
        print(f"处理数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    output_file, summary = analyze_material_cost_to_excel()
    if output_file:
        print(f"\n✅ 分析完成！结果已保存到: {output_file}") 