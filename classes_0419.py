import pandas as pd
import os
import shutil
import argparse

def filter_excel_by_material_type(excel_path, material_type, output_excel=None):
    """
    读取Excel文件，筛选出材质大类等于指定值的行，并保存为新的Excel文件
    
    Args:
        excel_path (str): Excel文件路径
        material_type (str): 要筛选的材质大类值
        output_excel (str, optional): 输出Excel文件路径，默认为None时自动生成
        
    Returns:
        pd.DataFrame: 筛选后的DataFrame
        str: 保存的Excel文件路径
    """
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    
    # 筛选材质大类等于指定值的行
    filtered_df = df[df['材质大类'] == material_type]
    
    # 如果没有指定输出文件路径，则自动生成
    if output_excel is None:
        base_name = os.path.splitext(os.path.basename(excel_path))[0]
        output_excel = f"{base_name}_{material_type}.xlsx"
    
    # 保存为新的Excel文件（不包含索引）
    filtered_df.to_excel(output_excel, index=False)
    
    print(f"已筛选出材质大类为 '{material_type}' 的 {len(filtered_df)} 条记录")
    print(f"已保存到文件: {output_excel}")
    
    return filtered_df, output_excel

def move_folders_by_material_code(df, source_folder, target_folder):
    """
    根据DataFrame中的物料编码列，在源文件夹中查找包含该编码的子文件夹，并移动到目标文件夹
    
    Args:
        df (pd.DataFrame): 包含物料编码列的DataFrame
        source_folder (str): 源文件夹路径
        target_folder (str): 目标文件夹路径
        
    Returns:
        list: 成功移动的文件夹列表
    """
    # 确保目标文件夹存在
    os.makedirs(target_folder, exist_ok=True)
    
    # 获取物料编码列表
    material_codes = df['物料编码'].unique().tolist()
    print(f"需要查找的物料编码数量: {len(material_codes)}")
    
    # 获取源文件夹下的所有子文件夹
    subfolders = [f for f in os.listdir(source_folder) if os.path.isdir(os.path.join(source_folder, f))]
    print(f"源文件夹中的子文件夹数量: {len(subfolders)}")
    
    # 记录成功移动的文件夹
    moved_folders = []
    
    # 遍历物料编码
    for code in material_codes:
        # 查找包含该编码的子文件夹
        for subfolder in subfolders:
            if str(code) in subfolder:
                source_path = os.path.join(source_folder, subfolder)
                target_path = os.path.join(target_folder, subfolder)
                
                # 如果目标路径已存在，则添加后缀
                if os.path.exists(target_path):
                    base_name = subfolder
                    counter = 1
                    while os.path.exists(target_path):
                        new_name = f"{base_name}_{counter}"
                        target_path = os.path.join(target_folder, new_name)
                        counter += 1
                
                # 移动文件夹
                try:
                    shutil.move(source_path, target_path)
                    moved_folders.append(subfolder)
                    print(f"已移动文件夹: {subfolder} -> {target_path}")
                except Exception as e:
                    print(f"移动文件夹 {subfolder} 时出错: {e}")
    
    print(f"成功移动 {len(moved_folders)} 个文件夹")
    return moved_folders

def test():
    """
    测试函数，直接在函数内指定参数值
    """
    # 直接指定参数值
    excel_path = "cy3000/0418-2/cy3000-坤德-材质-0418.xlsx"  # 替换为你的Excel文件路径
    material_type = "碳素结构钢"  # 替换为你要筛选的材质大类值
    source_folder = "董/cy3000产品资料/资料"  # 替换为你的源文件夹路径
    target_folder = "cy3000/0419/碳素结构钢"  # 替换为你的目标文件夹路径
    output_excel = None  # 可以指定输出Excel的路径，或保持None使用默认路径
    
    print("开始测试处理...")
    print(f"Excel文件路径: {excel_path}")
    print(f"材质大类: {material_type}")
    print(f"源文件夹: {source_folder}")
    print(f"目标文件夹: {target_folder}")
    
    # 筛选Excel
    filtered_df, output_file = filter_excel_by_material_type(
        excel_path, 
        material_type, 
        output_excel
    )
    
    # 移动文件夹
    moved_folders = move_folders_by_material_code(
        filtered_df, 
        source_folder, 
        target_folder
    )
    
    print("测试处理完成!")
    return filtered_df, moved_folders

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='根据Excel中的材质大类筛选并移动相关文件夹')
    parser.add_argument('--excel', required=True, help='Excel文件路径')
    parser.add_argument('--material-type', required=True, help='要筛选的材质大类值')
    parser.add_argument('--source-folder', required=True, help='源文件夹路径')
    parser.add_argument('--target-folder', required=True, help='目标文件夹路径')
    parser.add_argument('--output-excel', help='输出Excel文件路径（可选）')
    parser.add_argument('--test', action='store_true', help='运行测试模式，忽略其他参数')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 如果是测试模式，直接运行test函数
    if args.test:
        test()
        return
    
    # 筛选Excel
    filtered_df, _ = filter_excel_by_material_type(
        args.excel, 
        args.material_type, 
        args.output_excel
    )
    
    # 移动文件夹
    moved_folders = move_folders_by_material_code(
        filtered_df, 
        args.source_folder, 
        args.target_folder
    )
    
    print("处理完成!")

if __name__ == "__main__":
    # main()
    test()
