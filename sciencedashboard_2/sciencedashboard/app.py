from flask import Flask, jsonify, request, send_from_directory, send_file
from flask_cors import CORS
import redis
import os
import markdown
import logging
import base64
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 启用CORS支持

# 设置日志
logging.basicConfig(level=logging.DEBUG)

# Redis配置
REDIS_CONFIG = {
    "host": "r-uf6805sowwjiimkgospd.redis.rds.aliyuncs.com",
    "port": 6379,
    "db": 17,
    "password": "video_app_prod:video_app@2023"
}

# 创建Redis连接
redis_client = redis.Redis(**REDIS_CONFIG)

@app.route('/api/get_research_data', methods=['GET'])
def get_research_data():
    try:
        # 从查询参数获取用户ID
        user_id = request.args.get('uid')
        if not user_id:
            return jsonify({"error": "Missing user ID"}), 400

        # 构建Redis键名
        topic_key = f"research:{user_id}:topic"
        rounds_key = f"research:{user_id}:rounds"
        history_map_key = f"research:{user_id}:history_map"
        thinking_history_key = f"research:{user_id}:thinking_history"
        learnings_key = f"research:{user_id}:learnings"
        next_search_key = f"research:{user_id}:next_search"
        search_keywords_key = f"research:{user_id}:search_keywords"

        # 获取数据
        topic = redis_client.get(topic_key)
        rounds = redis_client.get(rounds_key)
        history_map = redis_client.get(history_map_key)
        thinking_history = redis_client.lrange(thinking_history_key, 0, -1)  # 获取列表所有元素
        learnings = redis_client.lrange(learnings_key, 0, -1)
        next_search = redis_client.get(next_search_key)
        search_keywords = redis_client.get(search_keywords_key)

        # 处理数据，将bytes转换为字符串
        response_data = {
            "topic": topic.decode('utf-8') if topic else None,
            "rounds": rounds.decode('utf-8') if rounds else None,
            "history_map": history_map.decode('utf-8') if history_map else None,
            "thinking_history": [item.decode('utf-8') for item in thinking_history] if thinking_history else [],
            "learnings": [item.decode('utf-8') for item in learnings] if learnings else [],
            "next_search": next_search.decode('utf-8') if next_search else None,
            "search_keywords": search_keywords.decode('utf-8') if search_keywords else None
        }

        return jsonify(response_data)

    except Exception as e:
        print(f"Error fetching research data: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/research_reports', methods=['GET'])
def get_research_reports():
    """获取所有研究报告的列表"""
    try:
        research_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'research')
        if not os.path.exists(research_dir):
            return jsonify({"error": "研究报告目录不存在"}), 404
            
        reports = []
        for filename in os.listdir(research_dir):
            if filename.endswith('.md'):
                report = {
                    "id": os.path.splitext(filename)[0],
                    "filename": filename,
                    "title": os.path.splitext(filename)[0],
                    "date": "2023-06-18",  # 可以从文件属性获取实际日期
                    "category": "市场分析",  # 可以从文件内容或其他元数据获取
                    "author": "AI研究助理"
                }
                reports.append(report)
                
        return jsonify({"reports": reports})
    except Exception as e:
        logging.error(f"获取研究报告列表出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/research_report/<report_id>', methods=['GET'])
def get_research_report(report_id):
    """获取指定研究报告的内容"""
    try:
        research_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'research')
        
        # 查找匹配的文件
        report_file = None
        for filename in os.listdir(research_dir):
            if filename.endswith('.md') and os.path.splitext(filename)[0] == report_id:
                report_file = filename
                break
                
        if not report_file:
            return jsonify({"error": "找不到指定的研究报告"}), 404
            
        # 读取文件内容
        file_path = os.path.join(research_dir, report_file)
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        return jsonify({
            "id": report_id,
            "title": report_id,
            "content": content,
            "date": "2023-06-18",
            "author": "AI研究助理",
            "category": "市场分析"
        })
    except Exception as e:
        logging.error(f"获取研究报告内容出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/articles', methods=['GET'])
def get_articles():
    """获取所有文献资料的列表"""
    try:
        articles_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'articles')
        if not os.path.exists(articles_dir):
            # 如果目录不存在，尝试创建
            os.makedirs(articles_dir)
            return jsonify({"articles": []})
            
        articles = []
        for filename in os.listdir(articles_dir):
            if filename.endswith('.pdf'):
                # 获取文件的创建时间作为日期
                file_path = os.path.join(articles_dir, filename)
                file_stats = os.stat(file_path)
                create_date = datetime.fromtimestamp(file_stats.st_ctime).strftime('%Y-%m-%d')
                
                # 生成文献信息
                article = {
                    "id": os.path.splitext(filename)[0],
                    "filename": filename,
                    "title": os.path.splitext(filename)[0].replace('_', ' ').title(),
                    "date": create_date,
                    "category": get_article_category(filename),  # 根据文件名判断分类
                    "author": "研究团队",
                    "size": f"{file_stats.st_size / 1024 / 1024:.2f} MB"  # 文件大小（MB）
                }
                articles.append(article)
                
        # 按日期降序排序
        articles.sort(key=lambda x: x['date'], reverse=True)
        return jsonify({"articles": articles})
    except Exception as e:
        logging.error(f"获取文献列表出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/article/<article_id>', methods=['GET'])
def get_article(article_id):
    """获取指定文献的内容（PDF文件）"""
    try:
        articles_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'articles')
        
        # 查找匹配的文件
        article_file = None
        for filename in os.listdir(articles_dir):
            if filename.endswith('.pdf') and os.path.splitext(filename)[0] == article_id:
                article_file = filename
                break
                
        if not article_file:
            return jsonify({"error": "找不到指定的文献资料"}), 404
            
        # 返回PDF文件
        file_path = os.path.join(articles_dir, article_file)
        return send_file(
            file_path,
            mimetype='application/pdf',
            as_attachment=False,
            download_name=article_file
        )
    except Exception as e:
        logging.error(f"获取文献内容出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/article_thumbnail/<article_id>', methods=['GET'])
def get_article_thumbnail(article_id):
    """获取指定文献的缩略图（简单处理：返回一个预置图标）"""
    try:
        # 这里可以实现PDF首页预览的生成，但需要额外的PDF处理库
        # 为简化处理，返回一个静态占位图标（base64编码）
        placeholder_icon = "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"
        
        return jsonify({
            "id": article_id,
            "thumbnail": f"data:image/png;base64,{placeholder_icon}"
        })
    except Exception as e:
        logging.error(f"获取文献缩略图出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

def get_article_category(filename):
    """根据文件名判断分类（可以扩展为更复杂的方式）"""
    filename = filename.lower()
    if 'poly' in filename or 'material' in filename:
        return "材料科学"
    elif 'market' in filename or 'industry' in filename:
        return "市场研究"
    elif 'tech' in filename or 'technology' in filename:
        return "技术报告"
    else:
        return "其他文献"

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8888) 