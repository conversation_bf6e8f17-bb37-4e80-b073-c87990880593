// 产品数据结构
const productDataStructure = {
    // 减震元件数据
    damperComponents: {
        marketSize: 42.5, // 单位：亿元
        marketGrowth: 8.2, // 单位：%
        marketShare: 32.8, // 单位：%
        competitivePosition: '行业第一',
        mainCustomers: ['一汽大众', '上汽集团', '吉利汽车', '比亚迪'],
        monthlyProduction: [
            { month: '1月', production: 150000, target: 145000 },
            { month: '2月', production: 142000, target: 145000 },
            { month: '3月', production: 155000, target: 150000 },
            { month: '4月', production: 158000, target: 150000 },
            { month: '5月', production: 162000, target: 155000 },
            { month: '6月', production: 165000, target: 155000 }
        ],
        technicalAdvantages: ['高性能复合材料', '专利减震结构', '智能化制造'],
        challenges: ['原材料成本上升', '新能源汽车轻量化要求']
    },

    // 踏板总成数据
    pedalAssemblies: {
        marketSize: 24.8, // 单位：亿元
        marketGrowth: 6.5, // 单位：%
        marketShare: 28, // 单位：%
        competitivePosition: '行业第二',
        mainCustomers: ['上汽通用', '广汽集团', '长安汽车', '北汽集团'],
        monthlyProduction: [
            { month: '1月', production: 95000, target: 90000 },
            { month: '2月', production: 87000, target: 90000 },
            { month: '3月', production: 98000, target: 100000 },
            { month: '4月', production: 102000, target: 100000 },
            { month: '5月', production: 106000, target: 105000 },
            { month: '6月', production: 110000, target: 105000 }
        ],
        technicalAdvantages: ['轻量化设计', '高精度制造工艺', '耐久性强'],
        challenges: ['新能源车型需求变化', '设计迭代加速']
    },

    // 胶轮数据
    rubberWheels: {
        marketSize: 35.8, // 单位：亿元
        marketGrowth: 7.8, // 单位：%
        marketShare: 28.5, // 单位：%
        competitivePosition: '行业第二',
        mainCustomers: ['东风日产', '奇瑞汽车', '长城汽车', '小鹏汽车'],
        monthlyProduction: [
            { month: '1月', production: 120000, target: 115000 },
            { month: '2月', production: 112000, target: 115000 },
            { month: '3月', production: 125000, target: 120000 },
            { month: '4月', production: 128000, target: 120000 },
            { month: '5月', production: 132000, target: 125000 },
            { month: '6月', production: 135000, target: 125000 }
        ],
        technicalAdvantages: ['低噪音设计', '高耐磨性能', '环保材料'],
        challenges: ['橡胶原料价格波动', '新材料技术革新']
    }
};

// 竞争对手分析数据
const competitorAnalysis = {
    damperCompetitors: [
        {
            name: '德国ZF集团',
            product: '减震元件',
            advantage: '全球化布局、技术领先',
            weakness: '本地化服务响应较慢、成本较高'
        },
        {
            name: '天纳克',
            product: '减震元件',
            advantage: '品牌知名度高、研发实力强',
            weakness: '产品成本控制、本土化程度'
        }
    ],
    pedalCompetitors: [
        {
            name: '博世集团',
            product: '踏板总成',
            advantage: '技术积累深厚、品牌影响力大',
            weakness: '产品价格较高、交付周期长'
        },
        {
            name: '国内竞争对手A',
            product: '踏板总成',
            advantage: '价格优势、反应速度快',
            weakness: '技术创新能力较弱、品质稳定性'
        }
    ],
    wheelCompetitors: [
        {
            name: '住友橡胶',
            product: '胶轮',
            advantage: '技术领先、产品线丰富',
            weakness: '成本较高、市场覆盖有限'
        },
        {
            name: '国内竞争对手B',
            product: '胶轮',
            advantage: '本地化服务、价格优势',
            weakness: '研发投入不足、品牌影响力'
        }
    ]
};

// 市场趋势分析
const marketTrends = {
    industryTrends: [
        {
            trend: '新能源汽车快速发展',
            impact: '对产品轻量化、节能要求提高',
            opportunity: '新材料、新工艺的应用空间',
            threat: '传统产品市场份额下降'
        },
        {
            trend: '智能网联化趋势',
            impact: '产品电子化、智能化需求增加',
            opportunity: '新型智能产品开发',
            threat: '研发投入加大、技术门槛提高'
        },
        {
            trend: '环保法规趋严',
            impact: '环保材料需求增加',
            opportunity: '绿色产品市场扩大',
            threat: '生产成本上升'
        }
    ],
    materialTrends: [
        {
            material: '新型复合材料',
            advantage: '轻量化、高强度',
            application: '减震元件、踏板总成',
            developmentStatus: '产业化阶段'
        },
        {
            material: '环保型橡胶材料',
            advantage: '环保、高性能',
            application: '胶轮',
            developmentStatus: '技术突破阶段'
        }
    ]
};

// 聚酯氨其他产品趋势研究数据
const otherProductTrends = {
    // 弹性体产品趋势
    elastomers: {
        marketSize: 68.5, // 单位：亿元
        marketGrowth: 9.8, // 单位：%
        keyApplications: ['汽车内饰', '工业密封', '电子设备', '医疗器械'],
        developmentStage: '快速成长期',
        researchFocus: [
            {
                direction: '高性能聚氨酯弹性体',
                description: '研发具有更高强度、更好耐候性的新型弹性体材料',
                potentialMarket: '高端汽车、航空航天、医疗设备',
                technicalChallenges: ['分子结构设计', '加工工艺优化', '性能测试标准建立']
            },
            {
                direction: '生物基弹性体',
                description: '利用生物质资源替代石油基原料，开发环保型弹性体',
                potentialMarket: '绿色建筑、可持续消费品',
                technicalChallenges: ['原料稳定性', '成本控制', '性能一致性']
            },
            {
                direction: '智能响应弹性体',
                description: '开发对温度、湿度、压力等外部刺激有响应的智能材料',
                potentialMarket: '可穿戴设备、智能传感器、软体机器人',
                technicalChallenges: ['响应机理研究', '批量生产工艺', '长期稳定性']
            }
        ],
        competitors: [
            { name: '巴斯夫', marketShare: 18.5, advantage: '技术全面、全球布局' },
            { name: '科思创', marketShare: 15.2, advantage: '创新能力强、产品线丰富' },
            { name: '亨斯迈', marketShare: 12.8, advantage: '应用研发深入、客户基础广泛' }
        ],
        industryTrends: [
            '轻量化需求推动高性能弹性体发展',
            '环保法规促进生物基和可回收弹性体研究',
            '多功能化成为产品开发主流',
            '定制化解决方案需求增加'
        ]
    },

    // 涂料产品趋势
    coatings: {
        marketSize: 52.3, // 单位：亿元
        marketGrowth: 7.5, // 单位：%
        keyApplications: ['建筑外墙', '汽车涂装', '工业防护', '木器涂装'],
        developmentStage: '成熟期创新',
        researchFocus: [
            {
                direction: '水性聚氨酯涂料',
                description: '开发VOC含量低、环保型水性聚氨酯涂料体系',
                potentialMarket: '建筑装饰、家具制造、汽车修补',
                technicalChallenges: ['耐水性提升', '干燥速度优化', '储存稳定性']
            },
            {
                direction: '自修复涂料',
                description: '研发具有自修复功能的聚氨酯涂层，延长使用寿命',
                potentialMarket: '高端汽车、高价值设备表面保护',
                technicalChallenges: ['修复机理设计', '修复效率提高', '成本控制']
            },
            {
                direction: '功能性涂料',
                description: '开发具有抗菌、隔热、导电等特殊功能的涂料',
                potentialMarket: '医疗设施、节能建筑、电子设备',
                technicalChallenges: ['功能添加剂分散性', '功能持久性', '多功能协同']
            }
        ],
        competitors: [
            { name: '阿克苏诺贝尔', marketShare: 16.8, advantage: '品牌影响力大、技术领先' },
            { name: '立邦涂料', marketShare: 14.5, advantage: '市场渠道广、本土化程度高' },
            { name: '关西涂料', marketShare: 10.2, advantage: '汽车涂料专长、技术精细' }
        ],
        industryTrends: [
            '环保法规推动水性、粉末、高固体分涂料发展',
            '功能性涂料市场需求快速增长',
            '数字化技术在涂料配方开发中应用增加',
            '涂料与基材协同设计理念兴起'
        ]
    },

    // 胶粘剂产品趋势
    adhesives: {
        marketSize: 45.8, // 单位：亿元
        marketGrowth: 8.6, // 单位：%
        keyApplications: ['建筑施工', '汽车装配', '电子封装', '包装材料'],
        developmentStage: '差异化竞争期',
        researchFocus: [
            {
                direction: '结构性胶粘剂',
                description: '开发高强度、高耐久性的结构胶，替代机械连接',
                potentialMarket: '汽车轻量化、航空航天、高端电子',
                technicalChallenges: ['界面结合机理', '耐老化性能', '快速固化技术']
            },
            {
                direction: '可拆卸胶粘剂',
                description: '研发在特定条件下可控拆卸的胶粘剂，便于产品维修和回收',
                potentialMarket: '电子产品、可回收包装、模块化设备',
                technicalChallenges: ['触发机制设计', '拆卸后残留控制', '反复使用性能']
            },
            {
                direction: '导热导电胶',
                description: '开发具有良好导热导电性能的特种胶粘剂',
                potentialMarket: '电子散热、LED封装、新能源电池',
                technicalChallenges: ['填料分散技术', '界面热阻控制', '长期可靠性']
            }
        ],
        competitors: [
            { name: '汉高', marketShare: 19.2, advantage: '技术全面、品牌认可度高' },
            { name: '3M', marketShare: 16.5, advantage: '创新能力强、应用解决方案丰富' },
            { name: '硅宝科技', marketShare: 8.3, advantage: '本土化服务、细分市场专注' }
        ],
        industryTrends: [
            '多材料连接需求推动特种胶粘剂发展',
            '电子微型化趋势带动高性能胶粘剂需求',
            '绿色环保型胶粘剂成为研发重点',
            '智能制造对胶粘剂精确施工提出新要求'
        ]
    }
};

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 初始化各个组件
    initDateFilter();
    initSidebar();
    initNotifications();
    initProductSelector();
    initProductsGrid();
    initProductDetailModal();
    simulateChartData();
    initCompanyStockData();
    initMaterialResearch();
    // AI Research板块在单独的JS文件中初始化

    // 初始化报告列表和内容
    initReportsPage();

    // 文献库页面功能
    initArticlesPage();
});

// 日期筛选器功能
function initDateFilter() {
    const dateFilters = document.querySelectorAll('[id^="date-range"]');

    dateFilters.forEach(dateFilter => {
        if (dateFilter) {
            dateFilter.addEventListener('change', function() {
                // 这里可以根据选择的日期范围更新数据
                console.log('日期范围已更改为:', this.value);

                // 模拟数据更新
                const randomChange = Math.floor(Math.random() * 5) + 1;
                updateStatCards(randomChange);
            });
        }
    });
}

// 更新统计卡片数据
function updateStatCards(change) {
    const statValues = document.querySelectorAll('.stat-value');
    const statChanges = document.querySelectorAll('.stat-change');

    statValues.forEach((statValue, index) => {
        // 获取当前值
        let currentValue = statValue.textContent;
        let numericValue;

        // 处理不同格式的值（纯数字、百分比、带单位等）
        if (currentValue.includes('%')) {
            numericValue = parseFloat(currentValue);
            const isIncrease = Math.random() > 0.3;

            if (isIncrease) {
                numericValue += (Math.random() * 2).toFixed(1) * 1;
                if (numericValue > 100) numericValue = 100;
                statChanges[index].textContent = `+${(Math.random() * 2).toFixed(1)}% 较上季`;
                statChanges[index].className = 'stat-change positive';
            } else {
                numericValue -= (Math.random() * 2).toFixed(1) * 1;
                if (numericValue < 0) numericValue = 0;
                statChanges[index].textContent = `-${(Math.random() * 2).toFixed(1)}% 较上季`;
                statChanges[index].className = 'stat-change negative';
            }

            statValue.textContent = numericValue.toFixed(1) + '%';
        }
        else if (currentValue.includes('亿')) {
            numericValue = parseFloat(currentValue);
            const isIncrease = Math.random() > 0.3;

            if (isIncrease) {
                numericValue += (Math.random() * 1.5).toFixed(1) * 1;
                statChanges[index].textContent = `+${(Math.random() * 3).toFixed(1)}% 同比`;
                statChanges[index].className = 'stat-change positive';
            } else {
                numericValue -= (Math.random() * 1).toFixed(1) * 1;
                if (numericValue < 0) numericValue = 0;
                statChanges[index].textContent = `-${(Math.random() * 2).toFixed(1)}% 同比`;
                statChanges[index].className = 'stat-change negative';
            }

            statValue.textContent = numericValue.toFixed(1) + '亿';
        }
        else if (currentValue.includes('家')) {
            numericValue = parseInt(currentValue);
            const isIncrease = Math.random() > 0.2;

            if (isIncrease) {
                numericValue += Math.floor(Math.random() * 2) + 1;
                statChanges[index].textContent = `+${Math.floor(Math.random() * 2) + 1}家 较上年`;
                statChanges[index].className = 'stat-change positive';
            } else {
                numericValue -= 1;
                if (numericValue < 0) numericValue = 0;
                statChanges[index].textContent = `-1家 较上年`;
                statChanges[index].className = 'stat-change negative';
            }

            statValue.textContent = numericValue + '家';
        }
        else {
            numericValue = parseInt(currentValue);
            const isIncrease = Math.random() > 0.3;

            if (isIncrease) {
                numericValue += change;
                statChanges[index].textContent = `+${change} 较上月`;
                statChanges[index].className = 'stat-change positive';
            } else {
                numericValue -= change;
                if (numericValue < 0) numericValue = 0;
                statChanges[index].textContent = `-${change} 较上月`;
                statChanges[index].className = 'stat-change negative';
            }

            statValue.textContent = numericValue;
        }
    });
}

// 侧边栏导航功能 - 板块切换
function initSidebar() {
    const sidebarItems = document.querySelectorAll('.sidebar li a[data-section]');
    const contentSections = document.querySelectorAll('.content');

    sidebarItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            // 获取目标板块ID
            const targetSection = this.getAttribute('data-section');

            // 移除所有活动状态
            document.querySelectorAll('.sidebar li').forEach(i => i.classList.remove('active'));

            // 添加当前项的活动状态
            this.parentElement.classList.add('active');

            // 隐藏所有内容板块
            contentSections.forEach(section => {
                section.classList.add('hidden');
            });

            // 显示目标板块
            const targetElement = document.getElementById(targetSection);
            if (targetElement) {
                targetElement.classList.remove('hidden');
            } else {
                // 处理新增板块
                switch(targetSection) {
                    case 'experiment':
                        showExperimentSystem();
                        break;
                    case 'quality':
                        showQualityManagement();
                        break;
                    case 'project':
                        showProjectManagement();
                        break;
                    case 'team':
                        showTeamCollaboration();
                        break;
                    case 'notification':
                        showNotificationCenter();
                        break;
                    default:
                        console.log('正在开发中的功能:', targetSection);
                        alert('该功能正在开发中...');
                }
            }
        });
    });
}

// 实验工艺验证初始化
function showExperimentSystem() {
    // 页面已经在HTML中定义，只需要初始化交互功能
    initProcessParameters();
    simulateExperimentData();
}

// 初始化工艺参数控件
function initProcessParameters() {
    const experimentPage = document.getElementById('experiment');
    if (!experimentPage) return;

    // 为滑块添加事件监听
    const sliders = experimentPage.querySelectorAll('input[type="range"]');
    sliders.forEach(slider => {
        const valueDisplay = slider.nextElementSibling;
        if (valueDisplay) {
            // 初始化显示
            valueDisplay.textContent = `${slider.value} ${valueDisplay.textContent.split(' ')[1]}`;
            
            // 添加事件监听
            slider.addEventListener('input', () => {
                valueDisplay.textContent = `${slider.value} ${valueDisplay.textContent.split(' ')[1]}`;
            });
        }
    });

    // 为按钮添加事件监听
    const buttons = experimentPage.querySelectorAll('.btn-action, .btn-mini, .btn-tool');
    buttons.forEach(button => {
        button.addEventListener('click', () => {
            const action = button.textContent.trim();
            console.log(`执行操作: ${action}`);
            // 这里可以添加具体的操作处理逻辑
        });
    });

    // 为新建工艺实验按钮添加特殊处理
    const newExperimentBtn = document.getElementById('newProcessExperiment');
    if (newExperimentBtn) {
        newExperimentBtn.addEventListener('click', () => {
            alert('创建新的工艺实验...');
            // 这里可以添加新建实验的逻辑
        });
    }
}

// 模拟实验数据更新
function simulateExperimentData() {
    const experimentPage = document.getElementById('experiment');
    if (!experimentPage) return;

    // 模拟温度曲线动画
    const trendLine = experimentPage.querySelector('.trend-line');
    if (trendLine) {
        animateProcessTrendLine(trendLine);
    }

    // 模拟参数变化
    simulateParameterChanges();
}

// 模拟工艺参数变化
function simulateParameterChanges() {
    const experimentPage = document.getElementById('experiment');
    if (!experimentPage) return;

    // 模拟每隔5秒更新一次参数
    setInterval(() => {
        // 更新反应温度
        const tempCell = experimentPage.querySelector('.parameters-table tbody tr:first-child td:nth-child(2)');
        if (tempCell) {
            const currentTemp = parseFloat(tempCell.textContent);
            const newTemp = currentTemp + (Math.random() * 0.6 - 0.3).toFixed(1);
            tempCell.textContent = `${newTemp.toFixed(1)} °C`;
        }

        // 更新粘度
        const viscosityCell = experimentPage.querySelector('.parameters-table tbody tr:nth-child(2) td:nth-child(2)');
        if (viscosityCell) {
            const currentViscosity = parseInt(viscosityCell.textContent);
            const newViscosity = currentViscosity + Math.floor(Math.random() * 100 - 50);
            viscosityCell.textContent = `${newViscosity} mPa·s`;
        }

        // 更新时间戳
        const timeStamp = experimentPage.querySelector('.alert-time');
        if (timeStamp) {
            const now = new Date();
            const timeString = now.toTimeString().split(' ')[0];
            timeStamp.textContent = `更新时间: ${timeString}`;
        }
    }, 5000);
}

// 模拟温度曲线动画
function animateProcessTrendLine(element) {
    if (!element) return;

    // 创建SVG元素
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("width", "100%");
    svg.setAttribute("height", "100%");
    svg.style.display = "block";
    element.innerHTML = "";
    element.appendChild(svg);

    // 创建折线图路径
    const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
    path.setAttribute("fill", "none");
    path.setAttribute("stroke", "#3498db");
    path.setAttribute("stroke-width", "2");
    svg.appendChild(path);

    // 生成初始数据点
    const width = element.clientWidth;
    const height = element.clientHeight;
    let points = [];
    const numPoints = 100;
    const baseY = height / 2;
    const amplitude = height / 4;

    for (let i = 0; i < numPoints; i++) {
        const x = (i / (numPoints - 1)) * width;
        const y = baseY + Math.sin(i * 0.2) * amplitude * Math.random() * 0.5;
        points.push({x, y});
    }

    // 绘制路径
    function updatePath() {
        let pathData = "M" + points[0].x + "," + points[0].y;
        for (let i = 1; i < points.length; i++) {
            pathData += " L" + points[i].x + "," + points[i].y;
        }
        path.setAttribute("d", pathData);
    }

    // 动画函数
    function animate() {
        // 更新点位置
        for (let i = 0; i < points.length - 1; i++) {
            points[i].y = points[i+1].y;
        }
        // 生成新点
        const lastPoint = points[points.length - 1];
        const newY = baseY + (Math.random() * 2 - 1) * amplitude;
        points[points.length - 1] = {x: lastPoint.x, y: newY};
        
        updatePath();
        requestAnimationFrame(animate);
    }

    // 开始动画
    updatePath();
    animate();
}

// 性能质量评测系统
function showQualityManagement() {
    // 页面已经在HTML中定义，只需要初始化交互功能
    initQualityManagement();
}

// 初始化性能质量评测功能
function initQualityManagement() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;

    // 初始化标准参数输入
    initQualityStandards();
    
    // 初始化雷达图
    initRadarChart();
    
    // 初始化趋势图
    initTrendChart();
    
    // 初始化结果分布图
    initDistributionChart();
    
    // 添加按钮事件监听
    initQualityButtons();
}

// 初始化质量标准参数
function initQualityStandards() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    // 监听标准值范围变化
    const standardInputs = qualityPage.querySelectorAll('.standard-min, .standard-max');
    standardInputs.forEach(input => {
        input.addEventListener('change', () => {
            validateStandardRange(input);
            updateTestResults();
        });
    });
    
    // 监听标准切换按钮
    const standardToggle = qualityPage.querySelector('.standard-toggle input[type="checkbox"]');
    if (standardToggle) {
        standardToggle.addEventListener('change', () => {
            const mode = standardToggle.checked ? '自动检测' : '手动检测';
            console.log(`切换到${mode}模式`);
            // 这里可以添加切换模式的逻辑
        });
    }
    
    // 添加保存和恢复标准按钮的监听
    const saveStandardButton = qualityPage.querySelector('.standards-card .btn-action:first-child');
    if (saveStandardButton) {
        saveStandardButton.addEventListener('click', () => {
            console.log('保存质量标准');
            alert('质量标准参数已保存！');
        });
    }
    
    const resetStandardButton = qualityPage.querySelector('.standards-card .btn-action:last-child');
    if (resetStandardButton) {
        resetStandardButton.addEventListener('click', () => {
            console.log('恢复默认标准');
            resetStandardValues();
        });
    }
}

// 验证标准范围的逻辑性
function validateStandardRange(input) {
    const row = input.closest('.standard-item');
    const minInput = row.querySelector('.standard-min');
    const maxInput = row.querySelector('.standard-max');
    
    const min = parseFloat(minInput.value);
    const max = parseFloat(maxInput.value);
    
    if (min >= max) {
        input.style.borderColor = '#ef4444';
        setTimeout(() => {
            input.style.borderColor = '#cbd5e0';
        }, 2000);
    }
}

// 重置标准值为默认值
function resetStandardValues() {
    const standardItems = document.querySelectorAll('.standard-item');
    const defaultValues = {
        '拉伸强度': [25, 35],
        '断裂伸长率': [350, 450],
        '邵氏硬度': [85, 95],
        '压缩永久变形': [5, 15],
        '密度': [1.05, 1.25],
        '耐候性': [800, 1200],
        '耐热性': [80, 120]
    };
    
    standardItems.forEach(item => {
        const label = item.querySelector('label').textContent.split(' ')[0];
        const minInput = item.querySelector('.standard-min');
        const maxInput = item.querySelector('.standard-max');
        
        for (const key in defaultValues) {
            if (label.includes(key)) {
                minInput.value = defaultValues[key][0];
                maxInput.value = defaultValues[key][1];
                break;
            }
        }
    });
    
    alert('已恢复默认标准值！');
    updateTestResults();
}

// 根据标准更新测试结果
function updateTestResults() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    const resultRows = qualityPage.querySelectorAll('.test-results-table tbody tr');
    
    resultRows.forEach(row => {
        const paramName = row.cells[0].textContent.split(' ')[0];
        const testValue = parseFloat(row.cells[1].textContent);
        const standardRange = row.cells[2].textContent.split('-');
        const min = parseFloat(standardRange[0]);
        const max = parseFloat(standardRange[1]);
        
        const statusCell = row.cells[3].querySelector('span');
        
        // 更新范围值
        row.cells[2].textContent = `${min}-${max}`;
        
        // 判断测试值是否在范围内
        if (testValue < min) {
            statusCell.textContent = '不合格';
            statusCell.className = 'status-danger';
        } else if (testValue > max) {
            statusCell.textContent = '不合格';
            statusCell.className = 'status-danger';
        } else if (testValue >= min && testValue <= min + (max - min) * 0.2 || 
                   testValue <= max && testValue >= max - (max - min) * 0.2) {
            statusCell.textContent = '临界';
            statusCell.className = 'status-warning';
        } else {
            statusCell.textContent = '合格';
            statusCell.className = 'status-normal';
        }
    });
    
    // 更新雷达图
    updateRadarChart();
}

// 初始化雷达图
function initRadarChart() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    const radarSvg = qualityPage.querySelector('.radar-placeholder svg');
    if (!radarSvg) return;
    
    // 雷达图已经在HTML中以SVG方式预设
    // 这里可以添加交互性，例如悬停显示详情等
    const radarPoints = radarSvg.querySelectorAll('circle');
    
    radarPoints.forEach(point => {
        point.addEventListener('mouseover', () => {
            point.setAttribute('r', '5');
            point.style.fill = '#e74c3c';
        });
        
        point.addEventListener('mouseout', () => {
            point.setAttribute('r', '3');
            point.style.fill = '#3498db';
        });
    });
}

// 更新雷达图数据
function updateRadarChart() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    // 获取当前的测试值
    const testResults = {};
    const resultRows = qualityPage.querySelectorAll('.test-results-table tbody tr');
    
    resultRows.forEach(row => {
        const paramName = row.cells[0].textContent.split(' ')[0];
        const testValue = parseFloat(row.cells[1].textContent);
        const standardRange = row.cells[2].textContent.split('-');
        const min = parseFloat(standardRange[0]);
        const max = parseFloat(standardRange[1]);
        
        testResults[paramName] = {
            value: testValue,
            min: min,
            max: max,
            normalized: (testValue - min) / (max - min) // 0-1之间的归一化值
        };
    });
    
    // 更新雷达图顶点位置
    const polygon = qualityPage.querySelector('.radar-polygon');
    if (polygon && Object.keys(testResults).length >= 4) {
        const center = { x: 100, y: 100 };
        const radius = 85; // 增加半径，使图形更大
        
        // 定义雷达图的四个指标对应的测试参数
        const radarMapping = {
            '强度': '拉伸强度',
            '硬度': '邵氏硬度',
            '韧性': '断裂伸长率',
            '耐久性': '耐候性'
        };
        
        // 计算四个顶点的位置
        const angles = [270, 0, 90, 180]; // 对应上、右、下、左四个方向（角度）
        let points = [];
        
        let i = 0;
        for (const key in radarMapping) {
            const param = radarMapping[key];
            if (testResults[param]) {
                // 根据归一化值调整半径
                let normalizedValue = testResults[param].normalized;
                normalizedValue = Math.max(0.2, Math.min(normalizedValue, 1)); // 限制在0.2-1之间，使图形不会太小
                
                const angle = angles[i] * Math.PI / 180;
                const x = center.x + radius * normalizedValue * Math.cos(angle);
                const y = center.y + radius * normalizedValue * Math.sin(angle);
                
                points.push(`${x},${y}`);
                
                // 更新对应顶点的圆点位置
                const circles = qualityPage.querySelectorAll('.radar-placeholder circle');
                if (circles[i]) {
                    circles[i].setAttribute('cx', x);
                    circles[i].setAttribute('cy', y);
                }
            }
            i++;
        }
        
        // 更新多边形
        polygon.setAttribute('points', points.join(' '));
    }
}

// 初始化趋势图
function initTrendChart() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    // 监听属性选择变化
    const propertySelect = qualityPage.querySelector('.property-select');
    if (propertySelect) {
        propertySelect.addEventListener('change', () => {
            updateTrendChart(propertySelect.value);
        });
    }
    
    // 监听时间范围变化
    const dateRangeSelect = qualityPage.querySelector('.date-range select');
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', () => {
            const property = propertySelect ? propertySelect.value : '拉伸强度';
            updateTrendChart(property, dateRangeSelect.value);
        });
    }
    
    // 初始化图表
    updateTrendChart('拉伸强度', '最近6个月');
}

// 更新趋势图数据
function updateTrendChart(property, timeRange) {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    const trendLine = qualityPage.querySelector('.trend-line');
    if (!trendLine) return;
    
    // 模拟数据点
    let dataPoints = [];
    const numPoints = 8;
    
    // 根据属性生成不同的数据曲线
    switch(property) {
        case '拉伸强度':
            dataPoints = [150, 120, 140, 100, 80, 90, 70, 60]; // 越小越高（Y轴向下）
            trendLine.nextElementSibling.nextElementSibling.nextElementSibling.nextElementSibling.textContent = '拉伸强度(MPa)';
            break;
        case '断裂伸长率':
            dataPoints = [130, 110, 90, 120, 100, 110, 80, 90];
            trendLine.nextElementSibling.nextElementSibling.nextElementSibling.nextElementSibling.textContent = '断裂伸长率(%)';
            break;
        case '邵氏硬度':
            dataPoints = [100, 110, 120, 110, 90, 80, 100, 110];
            trendLine.nextElementSibling.nextElementSibling.nextElementSibling.nextElementSibling.textContent = '邵氏硬度(A)';
            break;
        case '耐热性':
            dataPoints = [140, 130, 120, 110, 120, 100, 90, 100];
            trendLine.nextElementSibling.nextElementSibling.nextElementSibling.nextElementSibling.textContent = '耐热性(°C)';
            break;
        default:
            dataPoints = [150, 120, 140, 100, 80, 90, 70, 60];
    }
    
    // 生成趋势线的点坐标
    const width = 400;
    const height = 200;
    const margin = 20;
    const xStep = (width - 2 * margin) / (numPoints - 1);
    
    let points = [];
    for (let i = 0; i < numPoints; i++) {
        const x = margin + i * xStep;
        const y = dataPoints[i];
        points.push(`${x},${y}`);
    }
    
    // 更新折线
    trendLine.setAttribute('points', points.join(' '));
}

// 初始化结果分布图
function initDistributionChart() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    // 监听供应商和原料类型选择变化
    const supplierSelect = qualityPage.querySelector('.filter-group:first-child select');
    const materialSelect = qualityPage.querySelector('.filter-group:last-child select');
    
    if (supplierSelect) {
        supplierSelect.addEventListener('change', () => {
            updateDistributionChart();
        });
    }
    
    if (materialSelect) {
        materialSelect.addEventListener('change', () => {
            updateDistributionChart();
        });
    }
    
    // 初始化分布图
    updateDistributionChart();
}

// 更新结果分布图
function updateDistributionChart() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    const supplierSelect = qualityPage.querySelector('.filter-group:first-child select');
    const materialSelect = qualityPage.querySelector('.filter-group:last-child select');
    
    // 根据选择的供应商和原料类型，模拟不同的分布数据
    const supplier = supplierSelect ? supplierSelect.value : '全部供应商';
    const material = materialSelect ? materialSelect.value : '全部类型';
    
    // 更新供应商统计数据
    const qualityRate = qualityPage.querySelector('.supplier-stats .stat-row:nth-child(1) .stat-bar');
    const consistencyRate = qualityPage.querySelector('.supplier-stats .stat-row:nth-child(2) .stat-bar');
    const stabilityRate = qualityPage.querySelector('.supplier-stats .stat-row:nth-child(3) .stat-bar');
    
    const qualityValue = qualityPage.querySelector('.supplier-stats .stat-row:nth-child(1) .stat-value');
    const consistencyValue = qualityPage.querySelector('.supplier-stats .stat-row:nth-child(2) .stat-value');
    const stabilityValue = qualityPage.querySelector('.supplier-stats .stat-row:nth-child(3) .stat-value');
    
    let qualityPercent, consistencyPercent, stabilityPercent;
    
    // 根据不同的供应商和材料类型，更新统计数据
    if (supplier === '全部供应商') {
        qualityPercent = 95;
        consistencyPercent = 88;
        stabilityPercent = 92;
    } else if (supplier === '万华化学') {
        qualityPercent = 98;
        consistencyPercent = 93;
        stabilityPercent = 95;
    } else if (supplier === '巴斯夫') {
        qualityPercent = 96;
        consistencyPercent = 90;
        stabilityPercent = 94;
    } else if (supplier === '科思创') {
        qualityPercent = 94;
        consistencyPercent = 87;
        stabilityPercent = 91;
    } else {
        qualityPercent = 92;
        consistencyPercent = 85;
        stabilityPercent = 89;
    }
    
    // 根据材料类型进行微调
    if (material !== '全部类型') {
        // 材料类型对统计产生的微小影响
        const materialImpact = {
            '聚醚多元醇': [2, 1, 1],
            '聚酯多元醇': [0, 3, 1],
            '异氰酸酯': [-1, -2, 2],
            '扩链剂': [1, 0, -1]
        };
        
        if (materialImpact[material]) {
            qualityPercent += materialImpact[material][0];
            consistencyPercent += materialImpact[material][1];
            stabilityPercent += materialImpact[material][2];
        }
    }
    
    // 确保百分比在合理范围内
    qualityPercent = Math.max(0, Math.min(qualityPercent, 100));
    consistencyPercent = Math.max(0, Math.min(consistencyPercent, 100));
    stabilityPercent = Math.max(0, Math.min(stabilityPercent, 100));
    
    // 更新统计条和数值
    if (qualityRate && qualityValue) {
        qualityRate.style.width = `${qualityPercent}%`;
        qualityValue.textContent = `${qualityPercent}%`;
    }
    
    if (consistencyRate && consistencyValue) {
        consistencyRate.style.width = `${consistencyPercent}%`;
        consistencyValue.textContent = `${consistencyPercent}%`;
    }
    
    if (stabilityRate && stabilityValue) {
        stabilityRate.style.width = `${stabilityPercent}%`;
        stabilityValue.textContent = `${stabilityPercent}%`;
    }
    
    // 更新分布图的数据
    const distributionBars = qualityPage.querySelectorAll('.distribution-bar');
    
    // 为三种不同性能指标设置不同的分布
    if (distributionBars.length >= 3) {
        // 拉伸强度分布
        const tensileBar = distributionBars[0];
        const tensileSegments = tensileBar.querySelectorAll('.bar-segment');
        if (tensileSegments.length >= 3) {
            const failRate = Math.max(5, Math.min(25, 15 - qualityPercent / 10));
            const borderRate = Math.max(10, Math.min(35, 25 - consistencyPercent / 10));
            const passRate = 100 - failRate - borderRate;
            
            tensileSegments[0].style.height = `${failRate}%`;
            tensileSegments[1].style.height = `${borderRate}%`;
            tensileSegments[2].style.height = `${passRate}%`;
        }
        
        // 断裂伸长率分布
        const elongationBar = distributionBars[1];
        const elongationSegments = elongationBar.querySelectorAll('.bar-segment');
        if (elongationSegments.length >= 3) {
            const failRate = Math.max(3, Math.min(20, 10 - qualityPercent / 10));
            const borderRate = Math.max(10, Math.min(30, 20 - consistencyPercent / 10));
            const passRate = 100 - failRate - borderRate;
            
            elongationSegments[0].style.height = `${failRate}%`;
            elongationSegments[1].style.height = `${borderRate}%`;
            elongationSegments[2].style.height = `${passRate}%`;
        }
        
        // 邵氏硬度分布
        const hardnessBar = distributionBars[2];
        const hardnessSegments = hardnessBar.querySelectorAll('.bar-segment');
        if (hardnessSegments.length >= 3) {
            const failRate = Math.max(2, Math.min(15, 5 - qualityPercent / 20));
            const borderRate = Math.max(5, Math.min(25, 15 - consistencyPercent / 10));
            const passRate = 100 - failRate - borderRate;
            
            hardnessSegments[0].style.height = `${failRate}%`;
            hardnessSegments[1].style.height = `${borderRate}%`;
            hardnessSegments[2].style.height = `${passRate}%`;
        }
    }
}

// 初始化质量评测按钮事件
function initQualityButtons() {
    const qualityPage = document.getElementById('quality');
    if (!qualityPage) return;
    
    // 工具栏按钮
    const saveButton = qualityPage.querySelector('.quality-toolbar .btn-tool:nth-child(1)');
    const reportButton = qualityPage.querySelector('.quality-toolbar .btn-tool:nth-child(2)');
    const printButton = qualityPage.querySelector('.quality-toolbar .btn-tool:nth-child(3)');
    const shareButton = qualityPage.querySelector('.quality-toolbar .tool-group:last-child .btn-tool:nth-child(1)');
    const historyButton = qualityPage.querySelector('.quality-toolbar .tool-group:last-child .btn-tool:nth-child(2)');
    const settingsButton = qualityPage.querySelector('.quality-toolbar .tool-group:last-child .btn-tool:nth-child(3)');
    
    // 页面顶部按钮
    const newTestButton = document.getElementById('newPerformanceTest');
    const importDataButton = document.getElementById('importQualityData');
    const analysisButton = document.getElementById('qualityDataAnalysis');
    const reportsButton = document.getElementById('qualityReports');
    
    // 添加事件监听
    if (saveButton) {
        saveButton.addEventListener('click', () => {
            console.log('保存质量评测数据');
            alert('质量评测数据已保存！');
        });
    }
    
    if (reportButton) {
        reportButton.addEventListener('click', () => {
            generateQualityReport();
        });
    }
    
    if (printButton) {
        printButton.addEventListener('click', () => {
            printQualityResults();
        });
    }
    
    if (shareButton) {
        shareButton.addEventListener('click', () => {
            shareQualityResults();
        });
    }
    
    if (historyButton) {
        historyButton.addEventListener('click', () => {
            showHistoryData();
        });
    }
    
    if (settingsButton) {
        settingsButton.addEventListener('click', () => {
            showQualitySettings();
        });
    }
    
    if (newTestButton) {
        newTestButton.addEventListener('click', () => {
            createNewQualityTest();
        });
    }
    
    if (importDataButton) {
        importDataButton.addEventListener('click', () => {
            importQualityData();
        });
    }
    
    if (analysisButton) {
        analysisButton.addEventListener('click', () => {
            analyzeQualityData();
        });
    }
    
    if (reportsButton) {
        reportsButton.addEventListener('click', () => {
            showQualityReports();
        });
    }
    
    // 备注文本框自动保存
    const noteTextarea = qualityPage.querySelector('.test-note textarea');
    if (noteTextarea) {
        noteTextarea.addEventListener('blur', () => {
            console.log('自动保存备注: ' + noteTextarea.value);
        });
    }
}

// 生成质量报告
function generateQualityReport() {
    console.log('生成质量报告');
    alert('正在生成质量报告，请稍候...');
    
    // 模拟报告生成延迟
    setTimeout(() => {
        alert('质量评测报告已生成！');
    }, 1500);
}

// 打印质量结果
function printQualityResults() {
    console.log('打印质量结果');
    alert('正在准备打印质量评测结果...');
}

// 分享质量结果
function shareQualityResults() {
    console.log('分享质量结果');
    
    const shareOptions = [
        '发送邮件给团队',
        '导出为PDF',
        '添加到研发报告',
        '同步到质量数据库'
    ];
    
    const optionList = shareOptions.map((option, index) => 
        `${index + 1}. ${option}`
    ).join('\n');
    
    const selection = prompt(`请选择分享方式:\n${optionList}`);
    
    if (selection) {
        alert(`已选择: ${shareOptions[parseInt(selection) - 1] || '取消分享'}`);
    }
}

// 显示历史数据
function showHistoryData() {
    console.log('查看历史数据');
    alert('正在加载历史数据...');
}

// 显示质量设置
function showQualitySettings() {
    console.log('打开质量设置');
    
    const settingsOptions = [
        '标准管理',
        '测试方法配置',
        '报告模板',
        '数据同步设置',
        '用户权限'
    ];
    
    const optionList = settingsOptions.map((option, index) => 
        `${index + 1}. ${option}`
    ).join('\n');
    
    const selection = prompt(`质量评测设置:\n${optionList}`);
    
    if (selection) {
        alert(`已选择: ${settingsOptions[parseInt(selection) - 1] || '取消设置'}`);
    }
}

// 创建新的质量测试
function createNewQualityTest() {
    console.log('创建新的性能测试');
    
    const testType = prompt(
        '请选择测试类型:\n' +
        '1. 常规性能测试\n' +
        '2. 加速老化测试\n' +
        '3. 环境适应性测试\n' +
        '4. 机械性能测试\n' +
        '5. 自定义测试'
    );
    
    if (testType) {
        alert('新测试创建中，请准备样品和测试设备...');
    }
}

// 导入质量数据
function importQualityData() {
    console.log('导入检测数据');
    alert('请选择要导入的质量检测数据文件...');
}

// 分析质量数据
function analyzeQualityData() {
    console.log('分析质量数据');
    alert('正在启动数据分析工具...');
}

// 显示质量报告列表
function showQualityReports() {
    console.log('查看检测报告列表');
    alert('正在获取检测报告列表...');
}

// 零部件开发
function showProjectManagement() {
    // 隐藏所有内容区域，然后显示项目管理区域
    document.querySelectorAll('.content').forEach(content => {
        content.classList.add('hidden');
    });
    document.getElementById('project').classList.remove('hidden');
    
    // 初始化零部件开发页面的交互功能
    initComponentDevelopment();
}

function initComponentDevelopment() {
    // 初始化工艺流程图交互
    const processSteps = document.querySelectorAll('.process-step');
    processSteps.forEach(step => {
        step.addEventListener('click', function() {
            // 点击时显示更多详细信息
            showProcessStepDetails(this);
        });
    });
    
    // 初始化开发阶段交互
    const workflowSteps = document.querySelectorAll('.workflow-step');
    workflowSteps.forEach(step => {
        step.addEventListener('click', function() {
            // 点击时高亮显示当前阶段并显示详情
            workflowSteps.forEach(s => s.classList.remove('active'));
            this.classList.add('active');
            showWorkflowStepDetails(this);
        });
    });
    
    // 初始化按钮交互
    document.getElementById('newComponent').addEventListener('click', createNewComponent);
    document.getElementById('designValidation').addEventListener('click', showDesignValidation);
    document.getElementById('processFlow').addEventListener('click', showProcessFlow);
    document.getElementById('componentRecords').addEventListener('click', showComponentRecords);
    
    // 初始化输入交互
    initRangeInputs();
}

function showProcessStepDetails(step) {
    // 这里可以显示工艺步骤的详细信息，例如弹出模态框或展开详细视图
    console.log("显示工艺步骤详情:", step.querySelector('h5').textContent);
    // 实际项目中可以根据步骤ID或名称获取并显示更多数据
}

function showWorkflowStepDetails(step) {
    // 显示开发阶段的详细信息
    const stepName = step.querySelector('h4').textContent;
    const stepStatus = step.getAttribute('data-status');
    console.log(`显示${stepName}阶段详情, 当前状态: ${stepStatus}`);
    
    // 这里可以根据阶段ID或名称获取更多相关数据
}

function createNewComponent() {
    console.log("创建新零部件开发项目");
    // 实现新建零部件的逻辑，可以显示表单或弹出模态框
    alert("新建零部件功能正在开发中...");
}

function showDesignValidation() {
    console.log("显示设计验证工具");
    // 实现打开设计验证界面的逻辑
    alert("设计验证工具正在开发中...");
}

function showProcessFlow() {
    console.log("显示工艺流程设计工具");
    // 实现打开工艺流程设计界面的逻辑
    alert("工艺流程设计工具正在开发中...");
}

function showComponentRecords() {
    console.log("显示零部件开发记录");
    // 实现显示历史开发记录的逻辑
    alert("零部件开发记录功能正在开发中...");
}

function initRangeInputs() {
    // 初始化所有范围输入控件的更新逻辑
    const rangeInputs = document.querySelectorAll('input[type="range"]');
    rangeInputs.forEach(input => {
        // 为每个range input添加事件监听，更新显示的值
        const valueDisplay = input.nextElementSibling;
        if (valueDisplay && valueDisplay.classList.contains('value')) {
            input.addEventListener('input', function() {
                const unit = valueDisplay.textContent.split(' ')[1] || '';
                valueDisplay.textContent = this.value + ' ' + unit;
            });
        }
    });
}

// 团队协作
function showTeamCollaboration() {
    const mainContent = document.querySelector('.content:not(.hidden)');
    mainContent.innerHTML = `
        <div class="dashboard-header">
            <h2>团队协作</h2>
            <div class="research-tools">
                <button class="btn-tool"><i class="fas fa-users"></i> 团队管理</button>
                <button class="btn-tool"><i class="fas fa-comments"></i> 讨论区</button>
                <button class="btn-tool"><i class="fas fa-calendar-alt"></i> 日程安排</button>
            </div>
        </div>
        <div class="placeholder-content">
            <i class="fas fa-users fa-3x"></i>
            <p>团队协作系统正在开发中...</p>
        </div>
    `;
}

// 通知中心
function showNotificationCenter() {
    const mainContent = document.querySelector('.content:not(.hidden)');
    mainContent.innerHTML = `
        <div class="dashboard-header">
            <h2>通知中心</h2>
            <div class="research-tools">
                <button class="btn-tool"><i class="fas fa-bell"></i> 消息设置</button>
                <button class="btn-tool"><i class="fas fa-filter"></i> 筛选器</button>
                <button class="btn-tool"><i class="fas fa-check-double"></i> 全部已读</button>
            </div>
        </div>
        <div class="placeholder-content">
            <i class="fas fa-bell fa-3x"></i>
            <p>通知中心正在开发中...</p>
        </div>
    `;
}

// 产品选择器功能
function initProductSelector() {
    const productButtons = document.querySelectorAll('.product-btn');

    productButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的活动状态
            productButtons.forEach(btn => btn.classList.remove('active'));

            // 添加当前按钮的活动状态
            this.classList.add('active');

            // 获取选中的产品
            const selectedProduct = this.getAttribute('data-product');
            console.log('选中产品:', selectedProduct);

            // 更新产品相关数据
            updateProductData(selectedProduct);
        });
    });
}

// 更新产品相关数据
function updateProductData(product) {
    let productData;
    let competitors;

    // 根据选择的产品获取相应数据
    switch(product) {
        case 'damper':
            productData = productDataStructure.damperComponents;
            competitors = competitorAnalysis.damperCompetitors;
            break;
        case 'pedal':
            productData = productDataStructure.pedalAssemblies;
            competitors = competitorAnalysis.pedalCompetitors;
            break;
        case 'wheel':
            productData = productDataStructure.rubberWheels;
            competitors = competitorAnalysis.wheelCompetitors;
            break;
        case 'all':
            // 汇总所有产品数据
            productData = {
                marketSize: (productDataStructure.damperComponents.marketSize +
                           productDataStructure.pedalAssemblies.marketSize +
                           productDataStructure.rubberWheels.marketSize).toFixed(1),
                marketGrowth: ((productDataStructure.damperComponents.marketGrowth +
                              productDataStructure.pedalAssemblies.marketGrowth +
                              productDataStructure.rubberWheels.marketGrowth) / 3).toFixed(1),
                marketShare: ((productDataStructure.damperComponents.marketShare +
                             productDataStructure.pedalAssemblies.marketShare +
                             productDataStructure.rubberWheels.marketShare) / 3).toFixed(1),
                competitivePosition: '行业领先',
                mainCustomers: Array.from(new Set([
                    ...productDataStructure.damperComponents.mainCustomers,
                    ...productDataStructure.pedalAssemblies.mainCustomers,
                    ...productDataStructure.rubberWheels.mainCustomers
                ])),
                technicalAdvantages: ['综合技术优势', '全产品线布局', '规模化生产'],
                challenges: ['全球市场竞争', '技术创新要求', '成本控制压力']
            };
            competitors = [
                ...competitorAnalysis.damperCompetitors,
                ...competitorAnalysis.pedalCompetitors,
                ...competitorAnalysis.wheelCompetitors
            ];
            break;
    }

    if (productData) {
        // 更新市场规模
        document.querySelector('.stat-card:nth-child(1) .stat-value').textContent = productData.marketSize + '亿';
        document.querySelector('.stat-card:nth-child(1) .stat-change').textContent = `+${productData.marketGrowth}% 同比`;

        // 更新市场份额
        document.querySelector('.stat-card:nth-child(2) .stat-value').textContent = productData.marketShare + '%';

        // 更新主机厂覆盖
        document.querySelector('.stat-card:nth-child(3) .stat-value').textContent = productData.mainCustomers.length + '家';

        // 更新产能利用率（假设根据月度产量和目标计算）
        const latestMonth = productData.monthlyProduction ?
            productData.monthlyProduction[productData.monthlyProduction.length - 1] : null;
        if (latestMonth) {
            const utilizationRate = ((latestMonth.production / latestMonth.target) * 100).toFixed(1);
            document.querySelector('.stat-card:nth-child(4) .stat-value').textContent = utilizationRate + '%';
        }

        // 更新性能对比表格
        const tableBody = document.querySelector('.comparison-table tbody');
        if (tableBody) {
            tableBody.innerHTML = '';

            // 添加技术优势
            productData.technicalAdvantages.forEach(advantage => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>技术优势</td>
                    <td>${advantage}</td>
                    <td>行业标准</td>
                    <td class="positive">领先</td>
                `;
                tableBody.appendChild(row);
            });

            // 添加挑战
            productData.challenges.forEach(challenge => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>面临挑战</td>
                    <td>${challenge}</td>
                    <td>行业共性</td>
                    <td class="negative">需应对</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 更新竞争对手分析
        updateCompetitorAnalysis(competitors);

        // 更新月度产量图表
        if (productData.monthlyProduction) {
            updateProductionChart(productData.monthlyProduction);
        }
    }
}

// 更新竞争对手分析
function updateCompetitorAnalysis(competitors) {
    const insightsGrid = document.querySelector('.insights-grid');
    if (insightsGrid) {
        insightsGrid.innerHTML = '';

        competitors.forEach(competitor => {
            const insightCard = document.createElement('div');
            insightCard.className = 'insight-card';
            insightCard.innerHTML = `
                <div class="insight-header">
                    <i class="fas fa-building"></i>
                    <h4>${competitor.name}</h4>
                </div>
                <div class="insight-content">
                    <p><strong>产品:</strong> ${competitor.product}</p>
                    <p><strong>优势:</strong> ${competitor.advantage}</p>
                    <p><strong>劣势:</strong> ${competitor.weakness}</p>
                </div>
            `;
            insightsGrid.appendChild(insightCard);
        });
    }
}

// 更新月度产量图表
function updateProductionChart(productionData) {
    const trendLine = document.querySelector('.trend-line');
    if (!trendLine) return;

    // 计算生产达成率的波动路径
    const points = productionData.map((data, index) => {
        const x = (index / (productionData.length - 1)) * 100;
        const achievementRate = (data.production / data.target) * 100;
        const y = Math.min(100 - (achievementRate - 80) * 2, 100); // 将80-100%的达成率映射到0-100%的显示范围
        return `${x}% ${y}%`;
    });

    const clipPath = `polygon(0 100%, ${points.join(', ')}, 100% 100%)`;
    trendLine.style.clipPath = clipPath;
}

// 通知功能
function initNotifications() {
    const notificationItems = document.querySelectorAll('.notification-item');

    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除未读状态
            this.classList.remove('unread');

            // 更新通知计数
            updateNotificationCount();
        });
    });

    // 查看全部通知链接
    const viewAllLink = document.querySelector('.view-all');
    if (viewAllLink) {
        viewAllLink.addEventListener('click', function(e) {
            e.preventDefault();

            // 将所有通知标记为已读
            notificationItems.forEach(item => {
                item.classList.remove('unread');
            });

            // 更新通知计数
            updateNotificationCount();
        });
    }
}

// 更新通知计数
function updateNotificationCount() {
    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
    const badge = document.querySelector('.badge');

    if (badge) {
        badge.textContent = unreadCount;

        // 如果没有未读通知，可以隐藏徽章
        if (unreadCount === 0) {
            badge.style.display = 'none';
        } else {
            badge.style.display = 'inline-block';
        }
    }
}

// 模拟图表数据
function simulateChartData() {
    // 在实际项目中，这里会使用图表库如Chart.js或ECharts
    // 这里只是简单模拟一些动画效果

    // 趋势线动画
    animateTrendLine();

    // 饼图动画
    animatePieChart();
}

// 趋势线动画
function animateTrendLine() {
    const trendLine = document.querySelector('.trend-line');
    if (!trendLine) return;

    // 创建随机的波浪路径
    const points = [];
    const segments = 10;

    for (let i = 0; i <= segments; i++) {
        const x = (i / segments) * 100;
        const y = Math.random() * 60 + 20; // 20-80之间的随机值
        points.push(`${x}% ${y}%`);
    }

    const clipPath = `polygon(0 100%, ${points.join(', ')}, 100% 100%)`;

    // 应用新的路径
    trendLine.style.clipPath = clipPath;

    // 添加渐变背景
    trendLine.style.background = 'linear-gradient(180deg, rgba(52, 152, 219, 0.3) 0%, rgba(255, 255, 255, 0) 100%)';
}

// 饼图动画
function animatePieChart() {
    const pieChart = document.querySelector('.pie-chart');
    if (!pieChart) return;

    // 随机生成市场份额数据
    const kaizongShare = Math.floor(Math.random() * 10) + 30; // 30-40之间
    const competitorAShare = Math.floor(Math.random() * 10) + 20; // 20-30之间
    const competitorBShare = Math.floor(Math.random() * 10) + 15; // 15-25之间
    const othersShare = 100 - kaizongShare - competitorAShare - competitorBShare;

    // 更新饼图样式
    pieChart.style.background = `conic-gradient(
        #3498db 0% ${kaizongShare}%,
        #e74c3c ${kaizongShare}% ${kaizongShare + competitorAShare}%,
        #2ecc71 ${kaizongShare + competitorAShare}% ${kaizongShare + competitorAShare + competitorBShare}%,
        #f39c12 ${kaizongShare + competitorAShare + competitorBShare}% 100%
    )`;

    // 更新文本
    const segments = pieChart.querySelectorAll('.pie-segment');
    if (segments.length >= 4) {
        segments[0].textContent = `凯众 ${kaizongShare}%`;
        segments[1].textContent = `竞争对手A ${competitorAShare}%`;
        segments[2].textContent = `竞争对手B ${competitorBShare}%`;
        segments[3].textContent = `其他 ${othersShare}%`;
    }
}

// 模拟公司股票数据
function initCompanyStockData() {
    // 获取股价元素
    const stockPrice = document.querySelector('.company-stat:nth-child(2) .stat-value');
    const stockChange = document.querySelector('.company-stat:nth-child(2) .stat-change');

    if (stockPrice && stockChange) {
        // 每30秒更新一次股价
        setInterval(() => {
            // 当前股价
            const currentPrice = parseFloat(stockPrice.textContent.replace('¥', ''));

            // 随机波动 (-2% 到 +2%)
            const changePercent = (Math.random() * 4 - 2).toFixed(2);
            const changeAmount = (currentPrice * changePercent / 100).toFixed(2);
            const newPrice = (currentPrice * 1 + changeAmount * 1).toFixed(2);

            // 更新显示
            stockPrice.textContent = `¥${newPrice}`;

            if (changePercent > 0) {
                stockChange.textContent = `+${changePercent}%`;
                stockChange.className = 'stat-change positive';
            } else {
                stockChange.textContent = `${changePercent}%`;
                stockChange.className = 'stat-change negative';
            }
        }, 30000); // 30秒
    }
}

// 添加一些随机事件，使界面看起来更加动态
setInterval(function() {
    // 随机更新一个统计卡片
    const randomChange = Math.floor(Math.random() * 3) + 1;
    const statCards = document.querySelectorAll('.stat-card');
    const randomIndex = Math.floor(Math.random() * statCards.length);

    const statValue = statCards[randomIndex].querySelector('.stat-value');
    const statChange = statCards[randomIndex].querySelector('.stat-change');

    if (statValue && statChange) {
        let currentValue = statValue.textContent;

        // 处理不同格式的值
        if (currentValue.includes('%')) {
            let numericValue = parseFloat(currentValue);
            const isIncrease = Math.random() > 0.3;

            if (isIncrease) {
                numericValue += 0.5;
                if (numericValue > 100) numericValue = 100;
                statChange.textContent = `+0.5% 较上季`;
                statChange.className = 'stat-change positive';
            } else {
                numericValue -= 0.5;
                if (numericValue < 0) numericValue = 0;
                statChange.textContent = `-0.5% 较上季`;
                statChange.className = 'stat-change negative';
            }

            statValue.textContent = numericValue.toFixed(1) + '%';
        }
        else if (currentValue.includes('亿')) {
            let numericValue = parseFloat(currentValue);
            const isIncrease = Math.random() > 0.3;

            if (isIncrease) {
                numericValue += 0.2;
                statChange.textContent = `+0.5% 同比`;
                statChange.className = 'stat-change positive';
            } else {
                numericValue -= 0.1;
                if (numericValue < 0) numericValue = 0;
                statChange.textContent = `-0.3% 同比`;
                statChange.className = 'stat-change negative';
            }

            statValue.textContent = numericValue.toFixed(1) + '亿';
        }
        else if (currentValue.includes('家')) {
            let numericValue = parseInt(currentValue);
            const isIncrease = Math.random() > 0.2;

            if (isIncrease && numericValue < 40) {
                numericValue += 1;
                statChange.textContent = `+1家 较上年`;
                statChange.className = 'stat-change positive';
            } else if (numericValue > 20) {
                numericValue -= 1;
                statChange.textContent = `-1家 较上年`;
                statChange.className = 'stat-change negative';
            }

            statValue.textContent = numericValue + '家';
        }
        else {
            let numericValue = parseInt(currentValue);
            const isIncrease = Math.random() > 0.3;

            if (isIncrease) {
                numericValue += randomChange;
                statChange.textContent = `+${randomChange} 较上月`;
                statChange.className = 'stat-change positive';
            } else {
                numericValue -= randomChange;
                if (numericValue < 0) numericValue = 0;
                statChange.textContent = `-${randomChange} 较上月`;
                statChange.className = 'stat-change negative';
            }

            statValue.textContent = numericValue;
        }
    }
}, 15000); // 每15秒更新一次

// 初始化其他产品选择器
function initOtherProductSelector() {
    const otherProductBtns = document.querySelectorAll('[data-other-product]');

    if (otherProductBtns.length > 0) {
        otherProductBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有按钮的active类
                otherProductBtns.forEach(b => b.classList.remove('active'));

                // 为当前点击的按钮添加active类
                this.classList.add('active');

                // 获取选中的产品类型
                const otherProduct = this.getAttribute('data-other-product');

                // 更新产品趋势数据
                updateOtherProductData(otherProduct);
            });
        });

        // 默认显示弹性体数据
        updateOtherProductData('elastomers');
    }
}

// 更新其他产品趋势数据
function updateOtherProductData(productType) {
    let productData;

    // 根据选择的产品类型获取相应数据
    switch(productType) {
        case 'elastomers':
            productData = otherProductTrends.elastomers;
            break;
        case 'coatings':
            productData = otherProductTrends.coatings;
            break;
        case 'adhesives':
            productData = otherProductTrends.adhesives;
            break;
        case 'all':
            // 汇总所有产品数据
            productData = {
                marketSize: (otherProductTrends.elastomers.marketSize +
                           otherProductTrends.coatings.marketSize +
                           otherProductTrends.adhesives.marketSize).toFixed(1),
                marketGrowth: ((otherProductTrends.elastomers.marketGrowth +
                              otherProductTrends.coatings.marketGrowth +
                              otherProductTrends.adhesives.marketGrowth) / 3).toFixed(1),
                developmentStage: '多元发展期',
                keyApplications: Array.from(new Set([
                    ...otherProductTrends.elastomers.keyApplications,
                    ...otherProductTrends.coatings.keyApplications,
                    ...otherProductTrends.adhesives.keyApplications
                ])),
                researchFocus: [
                    ...otherProductTrends.elastomers.researchFocus,
                    ...otherProductTrends.coatings.researchFocus,
                    ...otherProductTrends.adhesives.researchFocus
                ],
                competitors: [
                    ...otherProductTrends.elastomers.competitors,
                    ...otherProductTrends.coatings.competitors,
                    ...otherProductTrends.adhesives.competitors
                ],
                industryTrends: [
                    ...otherProductTrends.elastomers.industryTrends,
                    ...otherProductTrends.coatings.industryTrends,
                    ...otherProductTrends.adhesives.industryTrends
                ]
            };
            break;
    }

    if (productData) {
        // 更新市场规模
        const marketSizeElement = document.querySelector('#section2 .stat-card:nth-child(1) .stat-value');
        if (marketSizeElement) {
            marketSizeElement.textContent = productData.marketSize + '亿';
        }

        const marketGrowthElement = document.querySelector('#section2 .stat-card:nth-child(1) .stat-change');
        if (marketGrowthElement) {
            marketGrowthElement.textContent = `+${productData.marketGrowth}% 同比`;
        }

        // 更新发展阶段
        const stageElement = document.querySelector('#section2 .stat-card:nth-child(2) .stat-value');
        if (stageElement) {
            stageElement.textContent = productData.developmentStage;
        }

        // 更新应用领域
        const applicationsElement = document.querySelector('#section2 .stat-card:nth-child(3) .stat-value');
        if (applicationsElement) {
            applicationsElement.textContent = productData.keyApplications.length + '个主要领域';
        }

        // 更新研究方向
        const researchDirectionsElement = document.querySelector('#section2 .stat-card:nth-child(4) .stat-value');
        if (researchDirectionsElement) {
            researchDirectionsElement.textContent = productData.researchFocus.length + '个重点方向';
        }

        // 更新研究方向分析
        updateResearchDirections(productData.researchFocus);

        // 更新竞争格局
        updateCompetitorPieChart(productData.competitors);

        // 更新应用领域分布
        updateApplicationBarChart(productData.keyApplications);

        // 更新行业趋势
        updateIndustryTrends(productData.industryTrends);

        // 更新技术挑战
        if (productType !== 'all') {
            const challenges = productData.researchFocus.flatMap(item =>
                item.technicalChallenges.map(challenge => ({
                    challenge: challenge,
                    direction: item.direction
                }))
            );
            updateTechnicalChallenges(challenges);
        }
    }
}

// 更新研究方向分析
function updateResearchDirections(researchFocus) {
    const researchGrid = document.querySelector('.research-grid');
    if (!researchGrid) return;

    researchGrid.innerHTML = '';

    researchFocus.forEach(focus => {
        const card = document.createElement('div');
        card.className = 'research-card';

        let challengesHTML = '';
        focus.technicalChallenges.forEach(challenge => {
            challengesHTML += `<span class="challenge-tag">${challenge}</span>`;
        });

        card.innerHTML = `
            <h4>${focus.direction}</h4>
            <p>${focus.description}</p>
            <p class="market"><i class="fas fa-chart-line"></i> 潜在市场: ${focus.potentialMarket}</p>
            <div class="challenges">
                ${challengesHTML}
            </div>
        `;

        researchGrid.appendChild(card);
    });
}

// 更新竞争格局饼图
function updateCompetitorPieChart(competitors) {
    const pieChart = document.querySelector('#other-market-share-chart .pie-chart');
    if (!pieChart) return;

    pieChart.innerHTML = '';

    // 计算总市场份额
    const totalShare = competitors.reduce((sum, comp) => sum + comp.marketShare, 0);
    const otherShare = 100 - totalShare;

    // 添加前三名竞争对手
    const topCompetitors = competitors.sort((a, b) => b.marketShare - a.marketShare).slice(0, 3);

    const colors = ['#3498db', '#e74c3c', '#2ecc71'];

    topCompetitors.forEach((competitor, index) => {
        const segment = document.createElement('div');
        segment.className = 'pie-segment';
        segment.style.setProperty('--percentage', competitor.marketShare);
        segment.style.setProperty('--color', colors[index]);
        segment.textContent = competitor.name;
        pieChart.appendChild(segment);
    });

    // 添加其他竞争对手
    const otherSegment = document.createElement('div');
    otherSegment.className = 'pie-segment';
    otherSegment.style.setProperty('--percentage', otherShare);
    otherSegment.style.setProperty('--color', '#f39c12');
    otherSegment.textContent = '其他';
    pieChart.appendChild(otherSegment);
}

// 更新应用领域柱状图
function updateApplicationBarChart(applications) {
    const barChart = document.querySelector('.bar-chart');
    if (!barChart) return;

    barChart.innerHTML = '';

    // 为每个应用领域创建一个柱状图
    const heights = [65, 45, 30, 25]; // 模拟的百分比高度

    applications.forEach((app, index) => {
        if (index < 4) { // 最多显示4个应用领域
            const barContainer = document.createElement('div');
            barContainer.className = 'bar-container';

            const bar = document.createElement('div');
            bar.className = 'bar';
            bar.style.height = `${heights[index]}%`;
            bar.textContent = app;

            const barValue = document.createElement('span');
            barValue.className = 'bar-value';
            barValue.textContent = `${heights[index]}%`;

            barContainer.appendChild(bar);
            barContainer.appendChild(barValue);
            barChart.appendChild(barContainer);
        }
    });
}

// 更新行业趋势
function updateIndustryTrends(trends) {
    const trendsList = document.querySelector('.trends-list');
    if (!trendsList) return;

    trendsList.innerHTML = '';

    // 最多显示8个趋势
    const displayTrends = trends.slice(0, 8);

    displayTrends.forEach(trend => {
        const trendItem = document.createElement('div');
        trendItem.className = 'trend-item';
        trendItem.innerHTML = `<p>${trend}</p>`;
        trendsList.appendChild(trendItem);
    });
}

// 更新技术挑战
function updateTechnicalChallenges(challenges) {
    const challengesGrid = document.querySelector('.challenges-grid');
    if (!challengesGrid) return;

    challengesGrid.innerHTML = '';

    // 最多显示6个挑战
    const displayChallenges = challenges.slice(0, 6);

    const icons = [
        'fa-microscope', 'fa-flask', 'fa-atom',
        'fa-cogs', 'fa-vial', 'fa-brain'
    ];

    displayChallenges.forEach((item, index) => {
        const card = document.createElement('div');
        card.className = 'challenge-card';

        card.innerHTML = `
            <h4><i class="fas ${icons[index % icons.length]}"></i> ${item.challenge}</h4>
            <p>这是${item.direction}研究方向中的关键技术挑战，需要突破该领域的技术瓶颈。</p>
            <div class="solution">
                <p class="solution-title">可能的解决方案:</p>
                <p>通过跨学科合作，结合材料科学、化学工程和计算模拟等方法，系统性解决该挑战。</p>
            </div>
        `;

        challengesGrid.appendChild(card);
    });
}

// 初始化产品应用列表
function initProductsGrid() {
    // 筛选标签功能
    const filterTags = document.querySelectorAll('.filter-tag');
    const productCards = document.querySelectorAll('.product-card');
    const productSearch = document.getElementById('product-search');

    if (filterTags.length > 0 && productCards.length > 0) {
        // 筛选标签点击事件
        filterTags.forEach(tag => {
            tag.addEventListener('click', function() {
                // 移除所有标签的active类
                filterTags.forEach(t => t.classList.remove('active'));

                // 为当前点击的标签添加active类
                this.classList.add('active');

                // 获取筛选类别
                const filter = this.getAttribute('data-filter');

                // 筛选产品卡片
                filterProducts(filter);
            });
        });

        // 搜索框输入事件
        if (productSearch) {
            productSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();

                // 如果搜索框为空，恢复当前选中的筛选标签
                if (searchTerm === '') {
                    const activeTag = document.querySelector('.filter-tag.active');
                    if (activeTag) {
                        const filter = activeTag.getAttribute('data-filter');
                        filterProducts(filter);
                    }
                    return;
                }

                // 根据搜索词筛选产品卡片
                productCards.forEach(card => {
                    const title = card.querySelector('h4').textContent.toLowerCase();
                    const desc = card.querySelector('.product-desc').textContent.toLowerCase();

                    if (title.includes(searchTerm) || desc.includes(searchTerm)) {
                        card.style.display = 'flex';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // 分页按钮点击事件
        const pageBtns = document.querySelectorAll('.page-btn');
        if (pageBtns.length > 0) {
            pageBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (!this.classList.contains('active') && !this.classList.contains('next')) {
                        pageBtns.forEach(b => b.classList.remove('active'));
                        this.classList.add('active');

                        // 这里可以添加分页逻辑
                        console.log('切换到页面:', this.textContent);
                    }
                });
            });
        }
    }
}

// 筛选产品卡片
function filterProducts(filter) {
    const productCards = document.querySelectorAll('.product-card');

    productCards.forEach(card => {
        if (filter === 'all' || card.getAttribute('data-category') === filter) {
            card.style.display = 'flex';
        } else {
            card.style.display = 'none';
        }
    });
}

// 初始化产品详情模态框
function initProductDetailModal() {
    const detailBtns = document.querySelectorAll('.btn-detail');
    const modal = document.getElementById('productDetailModal');
    const closeBtns = document.querySelectorAll('.close-modal');
    const generateReportBtn = document.getElementById('generate-report');

    if (detailBtns.length > 0 && modal) {
        // 详情按钮点击事件
        detailBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const productType = this.getAttribute('data-product');

                // 更新模态框内容
                updateProductDetailModal(productType);

                // 显示模态框
                modal.classList.add('show');

                // 阻止页面滚动
                document.body.style.overflow = 'hidden';
            });
        });

        // 关闭按钮点击事件
        if (closeBtns.length > 0) {
            closeBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 隐藏模态框
                    modal.classList.remove('show');

                    // 恢复页面滚动
                    document.body.style.overflow = 'auto';
                });
            });
        }

        // 点击模态框外部关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.remove('show');
                document.body.style.overflow = 'auto';
            }
        });

        // 生成研究报告按钮点击事件
        if (generateReportBtn) {
            generateReportBtn.addEventListener('click', function() {
                alert('研究报告生成功能将在后续版本中推出');
            });
        }
    }
}

// 更新产品详情模态框内容
function updateProductDetailModal(productType) {
    // 获取产品数据
    let productData;

    // 根据产品类型获取相应数据
    switch(productType) {
        case 'elastomers':
            productData = otherProductTrends.elastomers;
            break;
        case 'coatings':
            productData = otherProductTrends.coatings;
            break;
        case 'adhesives':
            productData = otherProductTrends.adhesives;
            break;
        case 'footwear':
            productData = {
                marketSize: 38.2,
                marketGrowth: 5.3,
                developmentStage: '成熟期',
                keyApplications: ['运动鞋', '休闲鞋', '专业鞋', '户外鞋'],
                researchFocus: [
                    {
                        direction: '高性能鞋底材料',
                        description: '开发具有更好减震性、耐磨性和抓地力的鞋底材料',
                        potentialMarket: '高端运动鞋、专业运动鞋',
                        technicalChallenges: ['材料配方优化', '加工工艺改进', '性能测试标准']
                    },
                    {
                        direction: '环保型鞋材',
                        description: '研发可生物降解或可回收的环保型聚氨酯鞋材',
                        potentialMarket: '可持续时尚、环保品牌',
                        technicalChallenges: ['生物降解性', '成本控制', '性能平衡']
                    }
                ],
                competitors: [
                    { name: '拜耳材料', marketShare: 22.5, advantage: '技术领先、全球布局' },
                    { name: '巴斯夫', marketShare: 18.3, advantage: '产品线丰富、研发实力强' }
                ],
                industryTrends: [
                    '个性化定制鞋材需求增加',
                    '环保材料成为市场焦点',
                    '3D打印技术与聚氨酯材料结合应用',
                    '运动休闲鞋市场持续扩大'
                ]
            };
            break;
        case 'medical':
            productData = {
                marketSize: 29.7,
                marketGrowth: 12.4,
                developmentStage: '快速增长',
                keyApplications: ['医疗导管', '植入物', '伤口敷料', '医疗设备外壳'],
                researchFocus: [
                    {
                        direction: '生物相容性材料',
                        description: '开发具有更好生物相容性和生物稳定性的医用聚氨酯材料',
                        potentialMarket: '植入医疗器械、人工器官',
                        technicalChallenges: ['生物相容性测试', '长期稳定性', '灭菌工艺兼容']
                    },
                    {
                        direction: '抗菌材料',
                        description: '研发具有抗菌性能的医用聚氨酯材料，减少感染风险',
                        potentialMarket: '伤口敷料、导管、医疗设备表面',
                        technicalChallenges: ['抗菌机理研究', '长效抗菌', '安全性评估']
                    }
                ],
                competitors: [
                    { name: '科思创', marketShare: 16.8, advantage: '医疗级材料专长、全球认证' },
                    { name: '亨斯迈', marketShare: 14.2, advantage: '产品线完整、应用开发能力强' }
                ],
                industryTrends: [
                    '医疗器械微型化趋势推动材料创新',
                    '可降解医用材料需求增加',
                    '智能医疗设备对材料提出新要求',
                    '全球医疗器械法规趋严'
                ]
            };
            break;
        // 可以添加更多产品类型的数据
        default:
            productData = {
                marketSize: 0,
                marketGrowth: 0,
                developmentStage: '未知',
                keyApplications: [],
                researchFocus: [],
                competitors: [],
                industryTrends: []
            };
    }

    if (productData) {
        // 更新模态框标题
        const modalTitle = document.getElementById('modal-title');
        if (modalTitle) {
            let title;
            switch(productType) {
                case 'elastomers': title = '弹性体'; break;
                case 'coatings': title = '涂料'; break;
                case 'adhesives': title = '胶粘剂'; break;
                case 'footwear': title = '鞋材'; break;
                case 'medical': title = '医疗器械'; break;
                case 'textile': title = '纺织涂层'; break;
                case 'composite': title = '轻量化复合材料'; break;
                case 'electronic': title = '电子封装材料'; break;
                default: title = '产品详情';
            }
            modalTitle.textContent = title + ' 详细分析';
        }

        // 更新市场概览
        document.getElementById('detail-market-size').textContent = productData.marketSize + '亿';
        document.getElementById('detail-growth').textContent = '+' + productData.marketGrowth + '%';
        document.getElementById('detail-stage').textContent = productData.developmentStage;
        document.getElementById('detail-applications').textContent = productData.keyApplications.length + '个主要领域';

        // 更新研究方向
        const researchFocus = document.getElementById('detail-research');
        if (researchFocus) {
            researchFocus.innerHTML = '';

            productData.researchFocus.forEach(focus => {
                const researchItem = document.createElement('div');
                researchItem.className = 'research-item';

                let challengesHTML = '';
                focus.technicalChallenges.forEach(challenge => {
                    challengesHTML += `<span class="challenge-tag">${challenge}</span>`;
                });

                researchItem.innerHTML = `
                    <h5>${focus.direction}</h5>
                    <p>${focus.description}</p>
                    <p class="market"><i class="fas fa-chart-line"></i> 潜在市场: ${focus.potentialMarket}</p>
                    <div class="challenges">
                        ${challengesHTML}
                    </div>
                `;

                researchFocus.appendChild(researchItem);
            });
        }

        // 更新竞争格局
        const competitors = document.getElementById('detail-competitors');
        if (competitors) {
            competitors.innerHTML = '';

            productData.competitors.forEach(competitor => {
                const competitorItem = document.createElement('div');
                competitorItem.className = 'competitor-item';

                competitorItem.innerHTML = `
                    <h5>${competitor.name} <span class="share">${competitor.marketShare}%</span></h5>
                    <p><strong>优势:</strong> ${competitor.advantage}</p>
                `;

                competitors.appendChild(competitorItem);
            });
        }

        // 更新行业趋势
        const trends = document.getElementById('detail-trends');
        if (trends) {
            trends.innerHTML = '';

            productData.industryTrends.forEach(trend => {
                const trendItem = document.createElement('div');
                trendItem.className = 'trend-item';
                trendItem.innerHTML = `<p>${trend}</p>`;
                trends.appendChild(trendItem);
            });
        }

        // 更新技术挑战
        const challenges = document.getElementById('detail-challenges');
        if (challenges) {
            challenges.innerHTML = '';

            const icons = [
                'fa-microscope', 'fa-flask', 'fa-atom',
                'fa-cogs', 'fa-vial', 'fa-brain'
            ];

            // 从研究方向中提取技术挑战
            const allChallenges = productData.researchFocus.flatMap(item =>
                item.technicalChallenges.map(challenge => ({
                    challenge: challenge,
                    direction: item.direction
                }))
            );

            // 最多显示6个挑战
            const displayChallenges = allChallenges.slice(0, 6);

            displayChallenges.forEach((item, index) => {
                const challengeItem = document.createElement('div');
                challengeItem.className = 'challenge-item';

                challengeItem.innerHTML = `
                    <h5><i class="fas ${icons[index % icons.length]}"></i> ${item.challenge}</h5>
                    <p>这是${item.direction}研究方向中的关键技术挑战，需要突破该领域的技术瓶颈。</p>
                    <div class="solution">
                        <p class="solution-title">可能的解决方案:</p>
                        <p>通过跨学科合作，结合材料科学、化学工程和计算模拟等方法，系统性解决该挑战。</p>
                    </div>
                `;

                challenges.appendChild(challengeItem);
            });
        }
    }
}

// 材料科学研究相关功能
function initMaterialResearch() {
    initMolecularDesigner();
    initExperimentComparison();
    initExperimentTracking();
    initKnowledgeBase();
    initResearchToolbar();
}

function initMolecularDesigner() {
    const canvas = document.querySelector('.molecular-canvas');
    const params = document.querySelectorAll('.structure-params input[type="range"]');

    canvas.addEventListener('click', () => {
        // 这里可以集成专业的分子结构设计工具
        alert('分子结构设计器即将推出');
    });

    params.forEach(param => {
        param.addEventListener('input', (e) => {
            e.target.nextElementSibling.textContent = `${e.target.value}%`;
            updatePerformancePrediction();
        });
    });
}

function updatePerformancePrediction() {
    const predictions = {
        tensileStrength: {min: 25.8, max: 32.4},
        elongation: {min: 380, max: 420},
        hardness: {min: 85, max: 95}
    };

    // 根据参数值更新预测结果
    document.querySelectorAll('.prediction-value').forEach((bar, index) => {
        const randomValue = Math.random() * 0.2 + 0.7; // 模拟预测值
        bar.style.width = `${randomValue * 100}%`;
    });
}

function initExperimentComparison() {
    const select = document.querySelector('.experiment-select');
    const filters = document.querySelectorAll('.comparison-filters .filter-btn');

    select.addEventListener('change', updateComparisonChart);
    filters.forEach(btn => {
        btn.addEventListener('click', (e) => {
            filters.forEach(b => b.classList.remove('active'));
            e.target.classList.add('active');
            updateComparisonChart();
        });
    });
}

function updateComparisonChart() {
    // 模拟更新实验数据对比图表
    const bars = document.querySelectorAll('.comparison-bars .bar');
    bars.forEach(bar => {
        const height = Math.random() * 40 + 50;
        bar.style.height = `${height}%`;
    });
}

function initExperimentTracking() {
    const searchInput = document.querySelector('.tracking-filters input');
    const select = document.querySelector('.tracking-filters select');

    searchInput.addEventListener('input', filterExperiments);
    select.addEventListener('change', filterExperiments);
}

function filterExperiments() {
    // 实现实验记录过滤逻辑
    const items = document.querySelectorAll('.tracking-item');
    items.forEach(item => {
        const random = Math.random();
        item.style.display = random > 0.5 ? 'flex' : 'none';
    });
}

function initKnowledgeBase() {
    const searchInput = document.querySelector('.literature-analysis .search-box input');
    const filterTags = document.querySelectorAll('.literature-analysis .filter-tag');

    searchInput.addEventListener('input', filterLiterature);
    filterTags.forEach(tag => {
        tag.addEventListener('click', (e) => {
            filterTags.forEach(t => t.classList.remove('active'));
            e.target.classList.add('active');
            filterLiterature();
        });
    });
}

function filterLiterature() {
    // 实现文献过滤逻辑
    const items = document.querySelectorAll('.timeline-item');
    items.forEach(item => {
        const random = Math.random();
        item.style.display = random > 0.3 ? 'flex' : 'none';
    });
}

function initResearchToolbar() {
    const toolbarBtns = document.querySelectorAll('.research-toolbar .btn-tool');

    toolbarBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const action = e.target.textContent.trim();
            switch(action) {
                case '保存工作区':
                    saveWorkspace();
                    break;
                case '分享结果':
                    shareResults();
                    break;
                case '导出报告':
                    exportReport();
                    break;
                case '团队讨论':
                    openDiscussion();
                    break;
                case '历史记录':
                    showHistory();
                    break;
                case '设置':
                    openSettings();
                    break;
            }
        });
    });
}

function saveWorkspace() {
    alert('工作区保存成功');
}

function shareResults() {
    alert('分享功能即将推出');
}

function exportReport() {
    alert('报告导出功能即将推出');
}

function openDiscussion() {
    alert('团队讨论功能即将推出');
}

function showHistory() {
    alert('历史记录功能即将推出');
}

function openSettings() {
    alert('设置功能即将推出');
}

// 研究报告页面功能
document.addEventListener('DOMContentLoaded', function() {
    // 侧边栏菜单切换逻辑中已经包含了section5的处理，无需额外添加

    // 初始化报告列表和内容
    initReportsPage();
});

function initReportsPage() {
    // 获取报告列表元素
    const reportItems = document.querySelectorAll('.report-item');
    const reportContent = document.getElementById('reportContent');
    const searchInput = document.getElementById('report-search');
    const filterSelect = document.getElementById('report-filter');
    
    // 加载报告列表
    loadReportsList();
    
    // 绑定报告项点击事件 - 由于列表是动态加载的，使用事件委托
    document.querySelector('.report-items').addEventListener('click', function(e) {
        // 找到点击的报告项
        const reportItem = e.target.closest('.report-item');
        if (!reportItem) return;
        
        // 移除其他项的active类
        document.querySelectorAll('.report-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // 当前项添加active类
        reportItem.classList.add('active');
        
        // 显示加载状态
        reportContent.innerHTML = `
            <div class="report-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载报告内容...</p>
            </div>
        `;
        
        // 加载报告内容
        loadReportContent(reportItem.getAttribute('data-report'));
    });
    
    // 绑定搜索和过滤功能
    if (searchInput) {
        searchInput.addEventListener('input', filterReports);
    }
    
    if (filterSelect) {
        filterSelect.addEventListener('change', filterReports);
    }
    
    // 绑定按钮事件
    bindReportActions();
}

// 加载报告列表
function loadReportsList() {
    const reportItemsContainer = document.querySelector('.report-items');
    const reportCountElem = document.querySelector('.report-count');
    
    // 显示加载状态
    reportItemsContainer.innerHTML = `
        <div class="loading-placeholder">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在加载报告列表...</p>
        </div>
    `;
    
    // 从后端API获取报告列表
    fetch('http://localhost:8888/api/research_reports')
        .then(response => {
            if (!response.ok) {
                throw new Error('无法加载报告列表');
            }
            return response.json();
        })
        .then(data => {
            if (!data.reports || data.reports.length === 0) {
                reportItemsContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <p>暂无研究报告</p>
                    </div>
                `;
                reportCountElem.textContent = '(0)';
                return;
            }
            
            // 更新报告计数
            reportCountElem.textContent = `(${data.reports.length})`;
            
            // 生成报告列表HTML
            const reportsHTML = data.reports.map((report, index) => `
                <div class="report-item ${index === 0 ? 'active' : ''}" data-report="${report.id}">
                    <div class="report-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="report-info">
                        <h4>${report.title}</h4>
                        <div class="report-meta">
                            <span class="report-date"><i class="fas fa-calendar"></i> ${report.date}</span>
                            <span class="report-author"><i class="fas fa-user"></i> ${report.author}</span>
                            <span class="report-category"><i class="fas fa-tag"></i> ${report.category}</span>
                        </div>
                    </div>
                </div>
            `).join('');
            
            reportItemsContainer.innerHTML = reportsHTML;
            
            // 初始加载第一个报告的内容
            if (data.reports.length > 0) {
                loadReportContent(data.reports[0].id);
            }
        })
        .catch(error => {
            console.error('加载报告列表失败:', error);
            reportItemsContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>加载报告列表失败，请稍后重试</p>
                    <small>${error.message}</small>
                </div>
            `;
        });
}

function loadReportContent(reportId) {
    const reportContent = document.getElementById('reportContent');
    
    // 从后端API获取报告内容
    fetch(`http://localhost:8888/api/research_report/${reportId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('无法加载报告内容');
            }
            return response.json();
        })
        .then(data => {
            // 使用marked库将markdown转换为HTML
            const htmlContent = marked.parse(data.content);
            reportContent.innerHTML = htmlContent;
        })
        .catch(error => {
            console.error('加载报告失败:', error);
            reportContent.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>加载报告内容失败，请稍后重试</p>
                    <small>${error.message}</small>
                </div>
            `;
        });
}

function filterReports() {
    const searchInput = document.getElementById('report-search');
    const filterSelect = document.getElementById('report-filter');
    const reportItems = document.querySelectorAll('.report-item');
    
    const searchTerm = searchInput.value.toLowerCase();
    const filterValue = filterSelect.value;
    
    let visibleCount = 0;
    
    reportItems.forEach(item => {
        const title = item.querySelector('h4').textContent.toLowerCase();
        const category = item.querySelector('.report-category').textContent.toLowerCase();
        
        const matchesSearch = title.includes(searchTerm);
        const matchesFilter = filterValue === 'all' || 
                             (filterValue === 'market' && category.includes('市场')) ||
                             (filterValue === 'technical' && category.includes('技术')) ||
                             (filterValue === 'innovation' && category.includes('创新'));
        
        if (matchesSearch && matchesFilter) {
            item.style.display = 'flex';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    // 更新报告计数
    document.querySelector('.report-count').textContent = `(${visibleCount})`;
}

function bindReportActions() {
    // 导出报告按钮
    const exportBtn = document.getElementById('exportReport');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            alert('报告已导出为PDF文件');
        });
    }
    
    // 打印报告按钮
    const printBtn = document.getElementById('printReport');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            window.print();
        });
    }
    
    // 分享报告按钮
    const shareBtn = document.getElementById('shareReport');
    if (shareBtn) {
        shareBtn.addEventListener('click', function() {
            const shareModal = document.getElementById('reportDetailModal');
            shareModal.style.display = 'flex';
            
            // 设置模态框标题和内容
            document.getElementById('modal-title').textContent = '分享研究报告';
            document.getElementById('modalReportContent').innerHTML = `
                <div class="share-options">
                    <p>选择分享方式：</p>
                    <div class="share-buttons">
                        <button class="share-btn"><i class="fas fa-envelope"></i> 邮件</button>
                        <button class="share-btn"><i class="fas fa-comment"></i> 微信</button>
                        <button class="share-btn"><i class="fas fa-link"></i> 链接</button>
                        <button class="share-btn"><i class="fas fa-qrcode"></i> 二维码</button>
                    </div>
                    <div class="share-link-container">
                        <input type="text" value="https://kaizhong.com/research/report/123456" readonly>
                        <button class="copy-link">复制</button>
                    </div>
                </div>
            `;
        });
    }
    
    // 编辑报告按钮
    const editBtn = document.getElementById('editReport');
    if (editBtn) {
        editBtn.addEventListener('click', function() {
            alert('进入报告编辑模式');
        });
    }
    
    // 关闭模态框按钮
    const closeModalBtns = document.querySelectorAll('.close-modal');
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = document.getElementById('reportDetailModal');
            modal.style.display = 'none';
        });
    });
    
    // 下载报告按钮
    const downloadBtn = document.getElementById('downloadReport');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            alert('报告下载已开始');
            const modal = document.getElementById('reportDetailModal');
            modal.style.display = 'none';
        });
    }
}

// 文献库页面功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化文献库页面
    initArticlesPage();
});

function initArticlesPage() {
    // 获取页面元素
    const articleContent = document.getElementById('articleContent');
    const searchInput = document.getElementById('article-search');
    const filterSelect = document.getElementById('article-filter');
    const uploadBtn = document.getElementById('uploadArticle');
    
    // 加载文献列表
    loadArticlesList();
    
    // 绑定文献项点击事件 - 使用事件委托
    document.querySelector('.article-items').addEventListener('click', function(e) {
        // 找到点击的文献项
        const articleItem = e.target.closest('.article-item');
        if (!articleItem) return;
        
        // 移除其他项的active类
        document.querySelectorAll('.article-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // 当前项添加active类
        articleItem.classList.add('active');
        
        // 显示加载状态
        articleContent.innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载文献内容...</p>
            </div>
        `;
        
        // 加载文献内容
        loadArticleContent(articleItem.getAttribute('data-article'));
    });
    
    // 绑定搜索和过滤功能
    if (searchInput) {
        searchInput.addEventListener('input', filterArticles);
    }
    
    if (filterSelect) {
        filterSelect.addEventListener('change', filterArticles);
    }
    
    // 绑定上传按钮点击事件
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function() {
            const uploadModal = document.getElementById('uploadArticleModal');
            uploadModal.style.display = 'flex';
        });
    }
    
    // 绑定模态框关闭按钮
    document.querySelectorAll('.close-modal').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('uploadArticleModal').style.display = 'none';
            document.getElementById('articleDetailModal').style.display = 'none';
        });
    });
    
    // 绑定上传提交按钮
    document.getElementById('submitUpload').addEventListener('click', function() {
        // 这里需要实现文件上传功能，但目前我们简单模拟一下
        alert('上传功能需要与服务器交互，这里暂未实现');
        document.getElementById('uploadArticleModal').style.display = 'none';
    });
    
    // 绑定文献操作按钮
    bindArticleActions();
}

// 加载文献列表
function loadArticlesList() {
    const articleItemsContainer = document.querySelector('.article-items');
    const articleCountElem = document.querySelector('.article-count');
    
    // 显示加载状态
    articleItemsContainer.innerHTML = `
        <div class="loading-placeholder">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在加载文献列表...</p>
        </div>
    `;
    
    // 从后端API获取文献列表
    fetch('http://localhost:8888/api/articles')
        .then(response => {
            if (!response.ok) {
                throw new Error('无法加载文献列表');
            }
            return response.json();
        })
        .then(data => {
            if (!data.articles || data.articles.length === 0) {
                articleItemsContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-file-pdf"></i>
                        <p>暂无文献资料</p>
                        <small>点击上方"上传文献"按钮添加文献</small>
                    </div>
                `;
                articleCountElem.textContent = '(0)';
                return;
            }
            
            // 更新文献计数
            articleCountElem.textContent = `(${data.articles.length})`;
            
            // 生成文献列表HTML
            const articlesHTML = data.articles.map((article, index) => `
                <div class="article-item ${index === 0 ? 'active' : ''}" data-article="${article.id}">
                    <div class="article-icon"><i class="fas fa-file-pdf"></i></div>
                    <div class="article-info">
                        <h4>${article.title}</h4>
                        <div class="article-meta">
                            <span class="article-date"><i class="fas fa-calendar"></i> ${article.date}</span>
                            <span class="article-author"><i class="fas fa-user"></i> ${article.author}</span>
                            <span class="article-category"><i class="fas fa-tag"></i> ${article.category}</span>
                            <span class="article-size"><i class="fas fa-weight"></i> ${article.size}</span>
                        </div>
                    </div>
                </div>
            `).join('');
            
            articleItemsContainer.innerHTML = articlesHTML;
            
            // 初始加载第一个文献的内容
            if (data.articles.length > 0) {
                loadArticleContent(data.articles[0].id);
            }
        })
        .catch(error => {
            console.error('加载文献列表失败:', error);
            articleItemsContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>加载文献列表失败，请稍后重试</p>
                    <small>${error.message}</small>
                </div>
            `;
        });
}

// 加载文献内容 (PDF)
function loadArticleContent(articleId) {
    const articleContent = document.getElementById('articleContent');
    
    // 创建iframe来显示PDF
    const pdfUrl = `http://localhost:8888/api/article/${articleId}`;
    articleContent.innerHTML = `
        <iframe src="${pdfUrl}" title="PDF文献" frameborder="0"></iframe>
    `;
}

// 过滤文献列表
function filterArticles() {
    const searchInput = document.getElementById('article-search');
    const filterSelect = document.getElementById('article-filter');
    const articleItems = document.querySelectorAll('.article-item');
    
    const searchTerm = searchInput.value.toLowerCase();
    const filterValue = filterSelect.value;
    
    let visibleCount = 0;
    
    articleItems.forEach(item => {
        const title = item.querySelector('h4').textContent.toLowerCase();
        const category = item.querySelector('.article-category').textContent.toLowerCase();
        
        const matchesSearch = title.includes(searchTerm);
        const matchesFilter = filterValue === 'all' || 
                             (filterValue === 'material' && category.includes('材料')) ||
                             (filterValue === 'market' && category.includes('市场')) ||
                             (filterValue === 'tech' && category.includes('技术')) ||
                             (filterValue === 'other' && category.includes('其他'));
        
        if (matchesSearch && matchesFilter) {
            item.style.display = 'flex';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    // 更新文献计数
    document.querySelector('.article-count').textContent = `(${visibleCount})`;
}

// 绑定文献操作按钮事件
function bindArticleActions() {
    // 下载按钮
    const downloadBtn = document.getElementById('downloadArticle');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            const activeItem = document.querySelector('.article-item.active');
            if (!activeItem) {
                alert('请先选择要下载的文献');
                return;
            }
            
            const articleId = activeItem.getAttribute('data-article');
            window.open(`http://localhost:8888/api/article/${articleId}`, '_blank');
        });
    }
    
    // 打印按钮
    const printBtn = document.getElementById('printArticle');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            const iframe = document.querySelector('.article-content iframe');
            if (iframe) {
                iframe.contentWindow.print();
            } else {
                alert('请先选择要打印的文献');
            }
        });
    }
    
    // 分享按钮
    const shareBtn = document.getElementById('shareArticle');
    if (shareBtn) {
        shareBtn.addEventListener('click', function() {
            const activeItem = document.querySelector('.article-item.active');
            if (!activeItem) {
                alert('请先选择要分享的文献');
                return;
            }
            
            const articleId = activeItem.getAttribute('data-article');
            const articleTitle = activeItem.querySelector('h4').textContent;
            
            const shareModal = document.getElementById('articleDetailModal');
            shareModal.style.display = 'flex';
            
            // 设置模态框标题和内容
            document.getElementById('article-modal-title').textContent = `分享文献: ${articleTitle}`;
            document.getElementById('modalArticleContent').innerHTML = `
                <div class="share-options">
                    <p>选择分享方式：</p>
                    <div class="share-buttons">
                        <button class="share-btn"><i class="fas fa-envelope"></i> 邮件</button>
                        <button class="share-btn"><i class="fas fa-comment"></i> 微信</button>
                        <button class="share-btn"><i class="fas fa-link"></i> 链接</button>
                        <button class="share-btn"><i class="fas fa-qrcode"></i> 二维码</button>
                    </div>
                    <div class="share-link-container">
                        <input type="text" value="https://kaizhong.com/library/article/${articleId}" readonly>
                        <button class="copy-link">复制</button>
                    </div>
                </div>
            `;
        });
    }
    
    // 删除按钮
    const deleteBtn = document.getElementById('deleteArticle');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            const activeItem = document.querySelector('.article-item.active');
            if (!activeItem) {
                alert('请先选择要删除的文献');
                return;
            }
            
            if (confirm('确定要删除此文献吗？此操作不可恢复。')) {
                // 这里需要实现删除文件的API，但目前简单模拟
                alert('删除功能需要与服务器交互，这里暂未实现');
            }
        });
    }
    
    // 模态框下载按钮
    const downloadModalBtn = document.getElementById('downloadArticleModal');
    if (downloadModalBtn) {
        downloadModalBtn.addEventListener('click', function() {
            const activeItem = document.querySelector('.article-item.active');
            if (activeItem) {
                const articleId = activeItem.getAttribute('data-article');
                window.open(`http://localhost:8888/api/article/${articleId}`, '_blank');
                document.getElementById('articleDetailModal').style.display = 'none';
            }
        });
    }
}
