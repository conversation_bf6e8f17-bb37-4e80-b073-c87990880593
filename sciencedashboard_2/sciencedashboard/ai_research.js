/**
 * AI Research板块功能
 * 包含AI聊天机器人和研究面板
 */

// 会话UID
let sessionUID = null;
let pollingInterval = null;
let hasStartedConversation = false; // 标记是否已开始对话

// Redis配置
const REDIS_CONFIG = {
    host: 'r-uf6805sowwjiimkgospd.redis.rds.aliyuncs.com',
    port: 6379,
    db: 17,
    password: 'video_app_prod:video_app@2023'
};

// 生成随机uid
const generateUID = () => {
    return 'user_' + Math.random().toString(36).substr(2, 9);
};

// 初始化AI Research板块
function initAIResearch() {
    // 为新会话生成UID
    sessionUID = generateUID();

    initChatbot();
    initResearchPanel();
    setupEventListeners();

    // 不再自动开始轮询，而是等待用户发送第一条消息后
}

// 开始轮询
function startPolling() {
    if (pollingInterval) return; // 如果已经在轮询，则不重复启动

    console.log('开始轮询Redis数据...');

    // 立即执行一次
    pollRedisData();

    // 每5秒轮询一次
    pollingInterval = setInterval(pollRedisData, 5000);
}

// 停止轮询
function stopPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
        console.log('停止轮询Redis数据');
    }
}

// 轮询Redis数据
async function pollRedisData() {
    try {
        // 使用新的Flask后端API
        const response = await fetch(`http://localhost:8888/api/get_research_data?uid=${sessionUID}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data && Object.keys(data).length > 0) {
            updateResearchPanelWithRedisData(data);
        }
    } catch (error) {
        console.error('轮询Redis数据失败:', error);
    }
}

// 使用Redis数据更新研究面板
function updateResearchPanelWithRedisData(data) {
    const researchPanel = document.querySelector('.ai-research-panel');
    if (!researchPanel) return;

    let content = '<div class="research-progress">';

    // 显示研究主题
    if (data.topic) {
        content += `
            <div class="research-topic">
                <h3><i class="fas fa-microscope"></i> 研究主题</h3>
                <p>${data.topic}</p>
            </div>
            <hr class="section-divider">
        `;
    }

    // 显示当前轮数
    if (data.rounds) {
        content += `
            <div class="research-rounds">
                <h3><i class="fas fa-sync"></i> 研究进度</h3>
                <p>当前轮数：${data.rounds}</p>
            </div>
            <hr class="section-divider">
        `;
    }

    if (data.next_search) {
        content += `
            <div class="research-topic">
                <h3><i class="fas fa-search"></i> 当前研究</h3>
                <p>${data.next_search}</p>
            </div>
            <hr class="section-divider">
        `;
    }

    if (data.search_keywords) {
        content += `
            <div class="research-topic">
                <h3><i class="fas fa-tags"></i> 关键词</h3>
                <p>${data.search_keywords}</p>
            </div>
            <hr class="section-divider">
        `;
    }

    // 显示研究规划
    if (data.history_map) {
        content += `
            <div class="research-map">
                <h3><i class="fas fa-sitemap"></i> 研究规划</h3>
                <pre class="map-content">${data.history_map}</pre>
            </div>
            <hr class="section-divider">
        `;
    }

    if (data.learnings && data.learnings.length > 0) {
        content += `
            <div class="thinking-history">
                <h3><i class="fas fa-lightbulb"></i> 研究要点</h3>
                <ul>
                    ${data.learnings.map(thought => `
                        <li class="thought-item">${marked.parse(thought)}</li>
                    `).join('')}
                </ul>
            </div>
            <hr class="section-divider">
        `;
    }

    // 显示思考历史
    if (data.thinking_history && data.thinking_history.length > 0) {
        content += `
            <div class="thinking-history">
                <h3><i class="fas fa-brain"></i> 研究思考历史</h3>
                <ul>
                    ${data.thinking_history.map(thought => `
                        <li class="thought-item">${marked.parse(thought)}</li>
                    `).join('')}
                </ul>
            </div>
        `;
    }

    content += '</div>';
    researchPanel.innerHTML = content;
}

// 初始化聊天机器人
function initChatbot() {
    const chatContainer = document.querySelector('.ai-chatbot-container');
    if (!chatContainer) return;

    // 初始化聊天界面
    renderWelcomeMessage();
}

// 渲染欢迎消息
function renderWelcomeMessage() {
    const chatMessages = document.querySelector('.chat-messages');
    if (!chatMessages) return;

    const welcomeMessage = `
        <div class="chat-message ai">
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <p>您好，我是您的AI研究助手。我可以帮助您：</p>
                <ul>
                    <li>分析聚氨酯材料性能数据</li>
                    <li>提供相关研究文献</li>
                    <li>协助实验设计</li>
                    <li>解答技术问题</li>
                </ul>
                <p>请问有什么我可以帮您的？</p>
            </div>
        </div>
    `;

    chatMessages.innerHTML = welcomeMessage;
}

// 初始化研究面板
function initResearchPanel() {
    const researchPanel = document.querySelector('.ai-research-panel');
    if (!researchPanel) return;

    // 研究面板初始为空，将来会根据聊天内容动态更新
    researchPanel.innerHTML = `
        <div class="panel-placeholder">
            <i class="fas fa-microscope"></i>
            <p>AI分析结果将显示在此处</p>
            <p class="hint">与AI助手交流以获取研究分析</p>
        </div>
    `;
}

// 设置事件监听器
function setupEventListeners() {
    const chatForm = document.querySelector('.chat-input-form');
    const chatInput = document.querySelector('.chat-input');

    if (!chatForm || !chatInput) return;

    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const message = chatInput.value.trim();

        if (message) {
            sendMessage(message);
            chatInput.value = '';
        }
    });
}

// 发送消息
function sendMessage(message) {
    if (!message.trim()) return;

    const chatMessages = document.querySelector('.chat-messages');
    const chatInput = document.querySelector('.chat-input');

    if (!chatMessages || !chatInput) return;

    // 添加用户消息到聊天区域
    const userMessageHTML = `
        <div class="chat-message user">
            <div class="message-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="message-content">
                <p>${message}</p>
            </div>
        </div>
    `;

    chatMessages.insertAdjacentHTML('beforeend', userMessageHTML);

    // 清空输入框
    chatInput.value = '';

    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // 接收AI响应
    receiveAIResponse(message);
}

// 接收AI响应
async function receiveAIResponse(userMessage) {
    const chatMessages = document.querySelector('.chat-messages');
    if (!chatMessages) return;

    try {
        // 使用会话UID
        const requestData = {
            sid: "deep_research",
            uid: sessionUID,
            question: userMessage
        };

        // 发送API请求
        const response = await fetch('http://39.103.228.66:28103/faq4', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const data = await response.json();
        const aiResponse = marked.parse(data.answer);

        const aiMessageHTML = `
            <div class="chat-message ai">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>${aiResponse}</p>
                </div>
            </div>
        `;

        chatMessages.insertAdjacentHTML('beforeend', aiMessageHTML);

        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 如果是第一次对话，开始轮询Redis数据
        if (!hasStartedConversation) {
            hasStartedConversation = true;
            // 延迟2秒后开始轮询，给服务器一些时间处理数据
            setTimeout(() => {
                startPolling();
            }, 2000);
        }

        // 根据回答内容更新研究面板
        if (userMessage.includes("聚氨酯") || userMessage.includes("材料")) {
            updateResearchPanel("materials");
        } else if (userMessage.includes("实验") || userMessage.includes("测试")) {
            updateResearchPanel("experiments");
        } else if (userMessage.includes("文献") || userMessage.includes("论文")) {
            updateResearchPanel("literature");
        }
    } catch (error) {
        console.error('API请求错误:', error);
        // 发生错误时显示友好的错误消息
        const errorMessageHTML = `
            <div class="chat-message ai">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>抱歉，我暂时无法回答您的问题。请稍后再试。</p>
                </div>
            </div>
        `;
        chatMessages.insertAdjacentHTML('beforeend', errorMessageHTML);
    }
}

// 更新研究面板
function updateResearchPanel(type) {
    // 这里可以添加额外的面板更新逻辑，但不要覆盖Redis数据显示
    console.log('Panel type updated:', type);
}

// 在页面卸载时停止轮询
window.addEventListener('beforeunload', () => {
    stopPolling();
});

// 导出模块
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在AI Research页面
    const aiResearchSection = document.getElementById('section4');
    if (aiResearchSection) {
        initAIResearch();
    }
});
