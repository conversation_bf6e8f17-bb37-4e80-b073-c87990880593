/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
}

:root {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --accent-color: #e74c3c;
    --text-color: #333;
    --text-light: #666;
    --bg-color: #f5f7fa;
    --card-bg: #fff;
    --border-color: #e1e4e8;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --sidebar-width: 220px;
    --right-sidebar-width: 280px;
    --header-height: 60px;
    --footer-height: 40px;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

.container {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr var(--right-sidebar-width);
    grid-template-rows: var(--header-height) 1fr var(--footer-height);
    grid-template-areas:
        "header header header"
        "sidebar content right-sidebar"
        "footer footer footer";
    min-height: 100vh;
}

/* 头部样式 */
header {
    grid-area: header;
    background-color: var(--primary-color);
    color: white;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-name {
    font-weight: 500;
}

.btn {
    padding: 6px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1a6ea8;
}

/* 侧边栏样式 */
.sidebar {
    grid-area: sidebar;
    background-color: #2c3e50;
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar ul {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.sidebar li {
    padding: 0 20px;
}

.sidebar li.divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 10px 20px;
    padding: 0;
}

.sidebar a {
    color: #ecf0f1;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.sidebar a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar li.active a {
    background-color: var(--primary-color);
    font-weight: 500;
}

/* 主内容区域样式 */
.content {
    grid-area: content;
    padding: 20px;
    overflow-y: auto;
}

.content.hidden {
    display: none;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.date-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-filter select {
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: white;
}

/* 产品选择器 */
.product-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.product-btn {
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    background-color: white;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.product-btn:hover {
    background-color: #f5f5f5;
}

.product-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 统计卡片样式 */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(52, 152, 219, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-info {
    flex: 1;
}

.stat-info h3 {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-change {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.positive {
    color: var(--success-color);
}

.negative {
    color: var(--danger-color);
}

/* 图表卡片样式 */
.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.chart-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.chart-card h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.chart {
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 占位图表样式 - 实际项目中会被JavaScript图表库替代 */
.placeholder-chart {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.progress-bar {
    height: 30px;
    background-color: #f1f1f1;
    border-radius: 15px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    line-height: 30px;
    transition: width 0.5s;
}

.progress-labels {
    display: flex;
    justify-content: space-between;
}

.trend-line {
    width: 100%;
    height: 80%;
    background: linear-gradient(180deg, rgba(52, 152, 219, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
    position: relative;
    border-radius: 4px;
}

.trend-line::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
    clip-path: polygon(0 0, 10% 50%, 20% 20%, 30% 60%, 40% 40%, 50% 10%, 60% 30%, 70% 50%, 80% 20%, 90% 40%, 100% 0);
}

/* 饼图样式 */
.pie-chart {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    position: relative;
    margin: 0 auto;
    background: conic-gradient(
        #3498db 0% 32.8%,
        #e74c3c 32.8% 57.3%,
        #2ecc71 57.3% 75.5%,
        #f39c12 75.5% 100%
    );
}

.pie-segment {
    position: absolute;
    font-size: 0.8rem;
    color: white;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.pie-segment:nth-child(1) {
    top: 40%;
    left: 30%;
    transform: translate(-50%, -50%);
}

.pie-segment:nth-child(2) {
    top: 30%;
    right: 30%;
    transform: translate(50%, -50%);
}

.pie-segment:nth-child(3) {
    bottom: 30%;
    right: 35%;
    transform: translate(50%, 50%);
}

.pie-segment:nth-child(4) {
    bottom: 30%;
    left: 35%;
    transform: translate(-50%, 50%);
}

/* 产品性能对比表格 */
.performance-comparison {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.performance-comparison h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.comparison-table-container {
    overflow-x: auto;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
    padding: 10px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.comparison-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table td.positive {
    color: var(--success-color);
    font-weight: 500;
}

.comparison-table td.negative {
    color: var(--danger-color);
    font-weight: 500;
}

/* 市场洞察卡片 */
.insights-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.insights-container h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.insight-card {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 15px;
    background-color: #f8f9fa;
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.insight-header i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.insight-header h4 {
    font-size: 1rem;
    font-weight: 600;
}

.insight-content p {
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.insight-content p:last-child {
    margin-bottom: 0;
    font-weight: 500;
}

/* 最近活动样式 */
.recent-activities {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.recent-activities h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(52, 152, 219, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 3px;
}

.activity-desc {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.activity-time {
    color: var(--text-light);
    font-size: 0.8rem;
}

/* 右侧边栏样式 */
.right-sidebar {
    grid-area: right-sidebar;
    padding: 20px;
    background-color: var(--bg-color);
    border-left: 1px solid var(--border-color);
}

/* 公司信息组件 */
.company-info-widget {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.company-info-widget h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.company-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.company-stat {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.stat-label {
    color: var(--text-light);
    font-size: 0.9rem;
    min-width: 70px;
}

.company-stat .stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0;
}

.company-stat .stat-change {
    font-size: 0.8rem;
    margin-left: 5px;
}

.calendar-widget, .notifications-widget {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.calendar-widget h3, .notifications-widget h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.badge {
    background-color: var(--accent-color);
    color: white;
    border-radius: 10px;
    padding: 2px 8px;
    font-size: 0.7rem;
}

/* 日历样式 */
.mini-calendar {
    margin-bottom: 15px;
}

.calendar-header {
    text-align: center;
    margin-bottom: 10px;
    font-weight: 500;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day {
    text-align: center;
    padding: 5px;
    font-size: 0.8rem;
}

.calendar-day.header {
    font-weight: 500;
    color: var(--text-light);
}

.calendar-day.weekend {
    color: var(--accent-color);
}

.calendar-day.disabled {
    color: #ccc;
}

.calendar-day.today {
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
}

.calendar-day.event {
    position: relative;
}

.calendar-day.event::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

/* 即将到来的事件样式 */
.upcoming-events h4 {
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.upcoming-events ul {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.upcoming-events li {
    display: flex;
    gap: 10px;
}

.event-date {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    height: fit-content;
}

.event-details {
    flex: 1;
}

.event-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.event-time {
    color: var(--text-light);
    font-size: 0.8rem;
}

/* 通知样式 */
.notification-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.notification-item {
    display: flex;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.notification-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.notification-item.unread .notification-title {
    font-weight: 600;
}

.notification-item.unread::before {
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--accent-color);
    position: absolute;
    left: -4px;
    top: 50%;
    transform: translateY(-50%);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(52, 152, 219, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.notification-content {
    flex: 1;
    position: relative;
}

.notification-title {
    margin-bottom: 3px;
}

.notification-desc {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.notification-time {
    color: var(--text-light);
    font-size: 0.8rem;
}

.view-all {
    display: block;
    text-align: center;
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--primary-color);
}

.view-all:hover {
    text-decoration: underline;
}

/* 占位内容样式 */
.placeholder-content {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    color: var(--text-light);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.placeholder-content p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

/* 页脚样式 */
footer {
    grid-area: footer;
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 10px;
    font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        grid-template-columns: var(--sidebar-width) 1fr;
        grid-template-areas:
            "header header"
            "sidebar content"
            "footer footer";
    }

    .right-sidebar {
        display: none;
    }

    .charts-container,
    .insights-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-areas:
            "header"
            "content"
            "footer";
    }

    .sidebar {
        display: none;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .charts-container {
        grid-template-columns: 1fr;
    }

    .product-selector {
        flex-direction: column;
    }
}

/* 研究方向分析 */
.research-directions {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.research-directions h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.research-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.research-card {
    background-color: var(--bg-color);
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--accent-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.research-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.research-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.research-card p {
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.research-card .market {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 10px;
}

.research-card .challenges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.research-card .challenge-tag {
    background-color: var(--light-bg);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* 行业趋势分析 */
.industry-trends {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.industry-trends h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.trends-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.trend-item {
    background-color: var(--bg-color);
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--secondary-color);
    transition: transform 0.3s ease;
}

.trend-item:hover {
    transform: translateY(-3px);
}

.trend-item p {
    color: var(--text-color);
    font-size: 0.95rem;
    line-height: 1.5;
}

/* 技术挑战与解决方案 */
.technical-challenges {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.technical-challenges h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.challenges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.challenge-card {
    background-color: var(--bg-color);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.challenge-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.challenge-card h4 i {
    margin-right: 8px;
    color: var(--accent-color);
}

.challenge-card p {
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.challenge-card .solution {
    margin-top: auto;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
}

.challenge-card .solution-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

/* 柱状图样式 */
.bar-chart {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    height: 200px;
    padding: 20px 10px;
}

.bar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 60px;
}

.bar {
    width: 40px;
    background: linear-gradient(to top, var(--primary-color), var(--accent-color));
    border-radius: 4px 4px 0 0;
    position: relative;
    color: transparent;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: height 1s ease;
}

.bar-value {
    margin-top: 5px;
    font-size: 0.8rem;
    color: var(--text-color);
}

/* 产品类型选择器样式调整 */
.product-selector [data-other-product] {
    background-color: var(--light-bg);
    color: var(--text-color);
    border: none;
    padding: 10px 15px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.product-selector [data-other-product]:hover {
    background-color: var(--hover-color);
}

.product-selector [data-other-product].active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 产业应用概览 */
.industry-overview {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.industry-overview h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.overview-stat {
    display: flex;
    align-items: center;
    background-color: var(--bg-color);
    border-radius: 8px;
    padding: 15px;
    transition: transform 0.3s ease;
}

.overview-stat:hover {
    transform: translateY(-3px);
}

.overview-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.overview-stat .stat-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.overview-stat .stat-info h4 {
    color: var(--text-color);
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.overview-stat .stat-info .stat-value {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.search-box {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.search-box input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 20px 0 0 20px;
    font-size: 0.9rem;
    outline: none;
}

.search-box button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 0 20px 20px 0;
    cursor: pointer;
}

/* 产品应用列表 */
.products-grid-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.products-grid-container h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.filter-tag {
    background-color: var(--light-bg);
    color: var(--text-color);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tag:hover {
    background-color: var(--hover-color);
}

.filter-tag.active {
    background-color: var(--primary-color);
    color: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.product-card {
    background-color: var(--bg-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.product-card.new {
    border: 2px solid var(--accent-color);
}

.product-header {
    padding: 15px;
    background-color: var(--light-bg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.product-header h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    margin: 0;
}

.market-size {
    color: var(--accent-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.new-tag {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--accent-color);
    color: white;
    padding: 3px 8px;
    font-size: 0.7rem;
    border-radius: 0 0 0 8px;
}

.product-body {
    padding: 15px;
    flex: 1;
}

.product-desc {
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.product-stats {
    display: flex;
    justify-content: space-between;
}

.product-stat {
    display: flex;
    flex-direction: column;
}

.product-stat .stat-label {
    color: var(--text-color);
    font-size: 0.8rem;
    margin-bottom: 3px;
}

.product-stat .stat-value {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.product-footer {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.btn-detail {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 0.9rem;
}

.btn-detail:hover {
    background-color: var(--secondary-color);
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.page-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-btn:hover:not(.active) {
    background-color: var(--light-bg);
}

/* 产品详情模态框 */
.product-detail-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.product-detail-modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--bg-color);
    border-radius: 8px;
    width: 80%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background-color: var(--bg-color);
    z-index: 10;
}

.modal-header h3 {
    color: var(--primary-color);
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-color);
    cursor: pointer;
}

.modal-body {
    padding: 20px;
}

.detail-section {
    margin-bottom: 25px;
}

.detail-section h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-stat {
    background-color: var(--light-bg);
    padding: 12px;
    border-radius: 8px;
}

.detail-stat .stat-label {
    color: var(--text-color);
    font-size: 0.8rem;
    margin-bottom: 5px;
    display: block;
}

.detail-stat .stat-value {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.research-focus {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.research-item {
    background-color: var(--light-bg);
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--accent-color);
}

.research-item h5 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1rem;
}

.research-item p {
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 10px;
}

.research-item .market {
    color: var(--accent-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.research-item .challenges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.research-item .challenge-tag {
    background-color: var(--bg-color);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.competitors-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.competitor-item {
    background-color: var(--light-bg);
    padding: 15px;
    border-radius: 8px;
}

.competitor-item h5 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.competitor-item h5 .share {
    margin-left: auto;
    background-color: var(--primary-color);
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.competitor-item p {
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.5;
}

.trends-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.trend-item {
    background-color: var(--light-bg);
    padding: 12px;
    border-radius: 8px;
    border-left: 3px solid var(--secondary-color);
}

.trend-item p {
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.5;
}

.challenges-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.challenge-item {
    background-color: var(--light-bg);
    padding: 15px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
}

.challenge-item h5 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.challenge-item h5 i {
    margin-right: 8px;
    color: var(--accent-color);
}

.challenge-item p {
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 10px;
}

.challenge-item .solution {
    margin-top: auto;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
}

.challenge-item .solution-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-secondary {
    background-color: var(--light-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--hover-color);
}

/* 行业趋势总览 */
.industry-trends-overview {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.industry-trends-overview h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.trends-chart-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .overview-stats,
    .products-grid,
    .detail-stats,
    .research-focus,
    .competitors-list,
    .trends-list,
    .challenges-list,
    .trends-chart-container {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
    }
}

/* 材料科学研究看板样式 */
.research-tools {
    display: flex;
    gap: 1rem;
    margin-left: auto;
}

.btn-tool {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-tool:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
}

.research-workspace {
    display: grid;
    grid-template-columns: 1fr 1.2fr 1fr;
    gap: 1.5rem;
    padding: 1rem;
    height: calc(100vh - 200px);
    overflow: hidden;
}

.workspace-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
}

.workspace-section h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.tool-card {
    background: var(--bg-light);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.tool-card h4 {
    color: var(--text-dark);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

/* 分子结构设计器 */
.molecular-canvas {
    background: #f8f9fa;
    border-radius: 6px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.placeholder-3d {
    text-align: center;
    color: var(--text-light);
}

.placeholder-3d i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.structure-params {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.param-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.range-inputs input {
    width: 80px;
    padding: 0.3rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* 性能预测模型 */
.prediction-params {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.param-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.prediction-range {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.prediction-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.prediction-value {
    height: 100%;
    background: var(--primary-color);
    border-radius: 4px;
}

/* 实验数据分析 */
.comparison-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.comparison-filters {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.3rem 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    cursor: pointer;
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.comparison-bars {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    height: 200px;
    padding: 1rem;
}

.bar-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    width: 60px;
}

.bar {
    width: 30px;
    background: var(--primary-color);
    border-radius: 4px 4px 0 0;
}

.bar.reference {
    width: 30px;
    background: #e9ecef;
    margin-left: -30px;
}

/* 实验记录追踪 */
.tracking-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tracking-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.tracking-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.tracking-status.success {
    background: #28a745;
}

.tracking-status.in-progress {
    background: #ffc107;
}

/* 知识库与灵感 */
.inspiration-feed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.inspiration-item {
    background: white;
    border-radius: 6px;
    padding: 1rem;
}

.inspiration-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.inspiration-header i {
    color: var(--primary-color);
}

.inspiration-refs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.btn-mini {
    padding: 0.2rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
}

/* 文献分析 */
.literature-timeline {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timeline-item {
    display: flex;
    gap: 1rem;
    padding-left: 1rem;
    border-left: 2px solid var(--primary-color);
}

.timeline-date {
    color: var(--text-light);
    font-size: 0.9rem;
    white-space: nowrap;
}

.timeline-content h5 {
    margin-bottom: 0.3rem;
}

.timeline-content p {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* 底部工具栏 */
.research-toolbar {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: white;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

.tool-group {
    display: flex;
    gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .research-workspace {
        grid-template-columns: 1fr 1fr;
    }

    .knowledge-base {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .research-workspace {
        grid-template-columns: 1fr;
    }

    .knowledge-base {
        grid-column: auto;
    }

    .research-tools {
        flex-wrap: wrap;
    }
}

/* AI Research板块样式 */
.ai-research-workspace {
    display: flex;
    gap: 20px;
    height: calc(100vh - 180px);
}

/* 聊天机器人容器 */
.ai-chatbot-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chatbot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f0f8ff;
    border-bottom: 1px solid #e0e0e0;
}

.chatbot-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.chatbot-header h3 i {
    margin-right: 8px;
    color: #3498db;
}

.chatbot-controls {
    display: flex;
    gap: 10px;
}

.btn-icon {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.btn-icon:hover {
    color: #3498db;
    background-color: rgba(52, 152, 219, 0.1);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f9f9f9;
}

.chat-message {
    display: flex;
    margin-bottom: 20px;
    max-width: 85%;
}

.chat-message.ai {
    align-self: flex-start;
}

.chat-message.user {
    align-self: flex-end;
    margin-left: auto;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    flex-shrink: 0;
}

.message-avatar i {
    font-size: 1rem;
}

.chat-message.ai .message-avatar {
    background-color: #3498db;
    color: white;
}

.chat-message.user .message-avatar {
    background-color: #2ecc71;
    color: white;
}

.message-content {
    background-color: white;
    padding: 12px 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.chat-message.ai .message-content {
    background-color: #f0f8ff;
    border-top-left-radius: 0;
}

.chat-message.user .message-content {
    background-color: #e8f8f5;
    border-top-right-radius: 0;
    text-align: right;
}

.message-content p {
    margin: 0 0 8px 0;
    line-height: 1.5;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content ul li {
    margin-bottom: 5px;
}

/* 聊天输入区域 */
.chat-input-form {
    display: flex;
    padding: 15px;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}

.chat-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 0.95rem;
    outline: none;
    transition: border-color 0.2s;
}

.chat-input:focus {
    border-color: #3498db;
}

.btn-send {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0 15px;
    margin-left: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-send:hover {
    background-color: #2980b9;
}

/* 研究分析面板 */
.ai-research-panel {
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
}

.panel-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #95a5a6;
    text-align: center;
}

.panel-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.panel-placeholder p {
    margin: 5px 0;
    font-size: 1.1rem;
}

.panel-placeholder .hint {
    font-size: 0.9rem;
    opacity: 0.7;
    max-width: 80%;
}

/* 分析卡片样式 */
.analysis-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.analysis-card h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 1rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 8px;
}

.property-chart, .metrics-chart {
    height: 200px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.chart-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #95a5a6;
}

.chart-placeholder i {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

.chart-placeholder p {
    margin: 0;
    font-size: 0.9rem;
}

/* 表格样式 */
.comparison-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.comparison-table th, .comparison-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.comparison-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #2c3e50;
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

/* 实验工作流程 */
.experiment-workflow {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.workflow-step {
    display: flex;
    align-items: flex-start;
    background-color: #fff;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e0e0e0;
}

.step-number {
    width: 30px;
    height: 30px;
    background-color: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    color: #2c3e50;
}

.step-content p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* 参数表格 */
.parameters-table table {
    width: 100%;
    border-collapse: collapse;
}

.parameters-table th, .parameters-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    font-size: 0.9rem;
}

.parameters-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #2c3e50;
}

/* 文献列表 */
.literature-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.literature-item {
    background-color: #fff;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #e0e0e0;
}

.literature-item h4 {
    margin: 0 0 8px 0;
    color: #2980b9;
    font-size: 1rem;
}

.literature-item .authors {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0 0 5px 0;
}

.literature-item .journal {
    color: #2c3e50;
    font-size: 0.85rem;
    font-style: italic;
    margin: 0 0 8px 0;
}

.literature-item .abstract {
    color: #555;
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .ai-research-workspace {
        flex-direction: column;
        height: auto;
    }

    .ai-chatbot-container, .ai-research-panel {
        height: 500px;
    }
}

@media (max-width: 768px) {
    .ai-chatbot-container, .ai-research-panel {
        height: 400px;
    }

    .chat-message {
        max-width: 95%;
    }
}

.thought-item {
    border-bottom: 1px solid #ccc;
    padding: 10px;
    background-color: #f9f9f9;
    margin-bottom: 5px;
}

.section-divider {
    border: 0;
    height: 1px;
    background: #f0f0f0;  /* 更浅的颜色 */
    margin: 3px 0;      /* 增加上下间距 */
    width: 100%;         /* 确保宽度充满容器 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* 添加微妙的阴影效果 */
}

/* 为了更好的视觉效果，可以给内容块添加一些间距 */
.research-topic,
.research-rounds,
.research-map,
.thinking-history {
    padding: 3px 0;
}

/* 研究报告页面样式 */
.reports-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.reports-list {
    flex: 0 0 30%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    max-height: calc(100vh - 220px);
    overflow-y: auto;
}

.report-items {
    margin-top: 15px;
}

.report-item {
    display: flex;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #eaeaea;
}

.report-item:hover {
    background-color: #f5f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.report-item.active {
    background-color: #f0f7ff;
    border-left: 4px solid #3498db;
}

.report-icon {
    flex: 0 0 40px;
    height: 40px;
    background-color: #f1f8fe;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #3498db;
    font-size: 18px;
}

.report-info {
    flex: 1;
}

.report-info h4 {
    margin: 0 0 8px 0;
    font-size: 15px;
    color: #333;
    line-height: 1.4;
}

.report-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 12px;
    color: #777;
}

.report-meta span {
    display: flex;
    align-items: center;
}

.report-meta i {
    margin-right: 4px;
    font-size: 11px;
}

.report-count {
    color: #777;
    font-weight: normal;
    font-size: 14px;
}

.report-content-container {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    max-height: calc(100vh - 220px);
    overflow-y: auto;
}

.report-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.btn-action {
    padding: 8px 15px;
    border: 1px solid #ddd;
    background-color: #fff;
    border-radius: 4px;
    color: #555;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s;
}

.btn-action:hover {
    background-color: #f9f9f9;
    border-color: #ccc;
}

.btn-action i {
    font-size: 14px;
}

.report-content {
    min-height: 400px;
    line-height: 1.6;
}

.report-content h1 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}

.report-content h2 {
    font-size: 20px;
    margin: 30px 0 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #444;
}

.report-content h3 {
    font-size: 18px;
    margin: 25px 0 12px;
    color: #555;
}

.report-content p {
    margin-bottom: 15px;
    color: #666;
}

.report-content ul, .report-content ol {
    margin: 15px 0;
    padding-left: 25px;
}

.report-content li {
    margin-bottom: 8px;
}

.report-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #777;
}

.report-loading i {
    font-size: 30px;
    margin-bottom: 15px;
    color: #3498db;
}

/* 报告详情模态框样式 */
.report-detail-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.report-detail-modal .modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 80%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.report-detail-modal .modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-detail-modal .modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.report-detail-modal .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.close-modal {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #777;
}

.close-modal:hover {
    color: #333;
}

/* 报告分享选项样式 */
.share-options {
    padding: 15px 0;
}

.share-buttons {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.share-btn {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
}

.share-btn:hover {
    background-color: #e9e9e9;
}

.share-btn i {
    font-size: 16px;
    color: #3498db;
}

.share-link-container {
    display: flex;
    margin-top: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.share-link-container input {
    flex: 1;
    padding: 10px;
    border: none;
    outline: none;
    background-color: #f9f9f9;
}

.copy-link {
    padding: 10px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    cursor: pointer;
}

.copy-link:hover {
    background-color: #2980b9;
}

.error-message {
    text-align: center;
    padding: 30px;
    color: #e74c3c;
}

.error-message i {
    font-size: 40px;
    margin-bottom: 15px;
}

.error-message p {
    margin-bottom: 10px;
    font-size: 18px;
}

.error-message small {
    color: #888;
}

/* 加载状态和空状态样式 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #777;
}

.loading-placeholder i {
    font-size: 30px;
    margin-bottom: 15px;
    color: #3498db;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #aaa;
}

.empty-state i {
    font-size: 40px;
    margin-bottom: 15px;
}

.empty-state p {
    font-size: 16px;
}

/* 文献库页面样式 */
.articles-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    height: calc(100vh - 250px); /* 增加总高度控制 */
}

.articles-list {
    flex: 0 0 30%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    height: 100%; /* 改为100%高度 */
    max-height: 100%; /* 调整最大高度 */
    overflow-y: auto;
}

.article-items {
    margin-top: 15px;
}

.article-item {
    display: flex;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #eaeaea;
}

.article-item:hover {
    background-color: #f5f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.article-item.active {
    background-color: #f0f7ff;
    border-left: 4px solid #3498db;
}

.article-icon {
    flex: 0 0 40px;
    height: 40px;
    background-color: #f1f8fe;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #e74c3c;
    font-size: 18px;
}

.article-info {
    flex: 1;
}

.article-info h4 {
    margin: 0 0 8px 0;
    font-size: 15px;
    color: #333;
    line-height: 1.4;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 12px;
    color: #777;
}

.article-meta span {
    display: flex;
    align-items: center;
}

.article-meta i {
    margin-right: 4px;
    font-size: 11px;
}

.article-count {
    color: #777;
    font-weight: normal;
    font-size: 14px;
}

.article-content-container {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    height: 100%; /* 改为100%高度 */
    max-height: 100%; /* 调整最大高度 */
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.article-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.article-content {
    flex: 1;
    min-height: 0; /* 修改为0，让它能够正确伸缩 */
    overflow: hidden;
    position: relative;
    height: 100%; /* 确保内容区域也能充满父容器 */
}

.article-content iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.article-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #aaa;
}

.article-placeholder i {
    font-size: 64px;
    margin-bottom: 20px;
    color: #e74c3c;
}

.article-placeholder p {
    font-size: 18px;
}

/* 文献详情模态框样式 */
.article-detail-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.article-detail-modal .modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 80%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.article-detail-modal .modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.article-detail-modal .modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.article-detail-modal .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 上传文献模态框样式 */
.upload-article-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.upload-article-modal .modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input[type="file"] {
    display: block;
    width: 100%;
    padding: 10px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    cursor: pointer;
}

/* 实验工艺验证系统样式 */
.experiment-workspace {
    display: grid;
    grid-template-columns: 1fr 1.2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    height: calc(100vh - 180px);
    overflow: hidden;
}

.workspace-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
}

.workspace-section h3 {
    color: var(--primary-color);
    font-size: 1.1rem;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

/* 工艺参数卡片样式 */
.parameter-card, .monitoring-card, .record-card {
    background-color: var(--bg-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.parameter-card h4, .monitoring-card h4, .record-card h4 {
    color: var(--text-color);
    font-size: 1rem;
    margin-bottom: 12px;
}

.parameter-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.parameter-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.parameter-item label {
    width: 120px;
    flex-shrink: 0;
    font-size: 0.9rem;
    color: var(--text-color);
}

.parameter-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.parameter-inputs input[type="number"] {
    width: 60px;
    padding: 6px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
}

.parameter-item input[type="range"] {
    flex: 1;
}

.parameter-item .value {
    width: 70px;
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: flex-end;
}

.btn-action {
    padding: 8px 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.btn-action:hover {
    background-color: var(--secondary-color);
}

/* 监控面板样式 */
.monitoring-chart {
    height: 180px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    margin-bottom: 10px;
}

.parameters-table table {
    width: 100%;
    border-collapse: collapse;
}

.parameters-table th, .parameters-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.parameters-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: var(--text-color);
}

.parameters-table tr:last-child td {
    border-bottom: none;
}

.status-normal {
    color: var(--success-color);
    font-weight: 500;
}

.status-warning {
    color: var(--warning-color);
    font-weight: 500;
}

.status-danger {
    color: var(--danger-color);
    font-weight: 500;
}

.alert-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    border-radius: 4px;
}

.alert-item.normal {
    background-color: rgba(46, 204, 113, 0.1);
}

.alert-item.warning {
    background-color: rgba(243, 156, 18, 0.1);
}

.alert-item.danger {
    background-color: rgba(231, 76, 60, 0.1);
}

.alert-item i {
    font-size: 1.2rem;
}

.alert-item.normal i {
    color: var(--success-color);
}

.alert-item.warning i {
    color: var(--warning-color);
}

.alert-item.danger i {
    color: var(--danger-color);
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.alert-time {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* 实验记录样式 */
.select-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.batch-select {
    flex: 1;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
}

.btn-mini {
    padding: 4px 8px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.comparison-chart {
    height: 180px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.record-item {
    display: flex;
    gap: 10px;
    background-color: white;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid var(--border-color);
}

.record-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-top: 5px;
}

.record-status.success {
    background-color: var(--success-color);
}

.record-status.in-progress {
    background-color: var(--warning-color);
}

.record-status.failed {
    background-color: var(--danger-color);
}

.record-content {
    flex: 1;
}

.record-content h5 {
    font-size: 0.95rem;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.record-content p {
    font-size: 0.85rem;
    margin: 3px 0;
    color: var(--text-light);
}

.record-actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
    justify-content: center;
}

/* 底部工具栏 */
.process-toolbar {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    border-top: 1px solid var(--border-color);
    margin-top: 20px;
}

.tool-group {
    display: flex;
    gap: 10px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .experiment-workspace {
        grid-template-columns: 1fr 1fr;
    }

    .experiment-records {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .experiment-workspace {
        grid-template-columns: 1fr;
    }

    .experiment-records {
        grid-column: auto;
    }
}

/* 性能质量评测系统样式 */
.quality-workspace {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr;
    grid-gap: 15px;
    height: calc(100vh - 200px);
    overflow: hidden;
}

.quality-workspace .workspace-section {
    background-color: #f7f9fc;
    border-radius: 6px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    overflow-y: auto;
    height: 100%;
}

.quality-workspace h3 {
    font-size: 1.1rem;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e1e5eb;
    color: #334155;
}

/* 质量标准卡片样式 */
.standards-card {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.standards-card h4 {
    font-size: 1rem;
    margin-top: 0;
    margin-bottom: 12px;
    color: #2c3e50;
    display: flex;
    align-items: center;
}

.standard-group {
    margin-bottom: 15px;
}

.standard-item {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px dashed #e0e0e0;
}

.standard-item label {
    width: 35%;
    font-size: 0.9rem;
    color: #4a5568;
}

.standard-range {
    display: flex;
    align-items: center;
    width: 35%;
}

.standard-range input {
    width: 45%;
    padding: 4px;
    text-align: center;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
}

.standard-range span {
    margin: 0 5px;
    color: #718096;
}

.standard-ref {
    width: 30%;
    padding: 4px;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #718096;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.standard-toggle {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed #e0e0e0;
}

.toggle-label {
    margin-right: 10px;
    font-size: 0.9rem;
    color: #4a5568;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 46px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #3498db;
}

input:focus + .slider {
    box-shadow: 0 0 1px #3498db;
}

input:checked + .slider:before {
    transform: translateX(22px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 结果分析卡片样式 */
.results-card {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.results-card h4 {
    font-size: 1rem;
    margin-top: 0;
    margin-bottom: 12px;
    color: #2c3e50;
}

.sample-id {
    font-weight: bold;
    color: #2c3e50;
    background-color: #f0f7ff;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #cfdff0;
}

.sample-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    background: #f9f9f9;
    border-radius: 4px;
    padding: 10px;
}

.sample-detail {
    flex: 1 0 50%;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.sample-detail label {
    font-weight: bold;
    margin-right: 5px;
    color: #4a5568;
    font-size: 0.9rem;
}

.results-chart {
    margin-bottom: 15px;
}

.results-chart h5 {
    font-size: 0.95rem;
    margin-top: 0;
    margin-bottom: 10px;
    color: #2c3e50;
}

.radar-chart-container {
    height: 240px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.radar-chart {
    height: 100%;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.radar-chart svg {
    width: 100%;
    height: 100%;
    max-height: 230px;
}

.radar-chart text {
    font-size: 10px;
}

/* 调整雷达图标签位置使其不被截断 */
.radar-chart text[y="10"] {
    y: 15;
}

.radar-chart text[y="195"] {
    y: 190;
}

.radar-chart text[x="10"] {
    x: 15;
}

.radar-chart text[x="190"] {
    x: 185;
}

.test-results-table {
    margin-bottom: 15px;
}

.test-results-table table {
    width: 100%;
    border-collapse: collapse;
}

.test-results-table th {
    background-color: #f0f7ff;
    padding: 8px;
    text-align: left;
    font-size: 0.9rem;
    color: #334155;
    border-bottom: 1px solid #e1e5eb;
}

.test-results-table td {
    padding: 8px;
    font-size: 0.9rem;
    border-bottom: 1px solid #e9ecef;
}

.status-normal {
    color: #10b981;
    background-color: rgba(16, 185, 129, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.status-warning {
    color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.status-danger {
    color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.test-note {
    margin-top: 15px;
}

.test-note h5 {
    font-size: 0.95rem;
    margin-top: 0;
    margin-bottom: 10px;
    color: #2c3e50;
}

.test-note textarea {
    width: 100%;
    height: 80px;
    padding: 10px;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
    resize: none;
    font-size: 0.9rem;
    font-family: inherit;
}

/* 历史数据与统计样式 */
.stats-card {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.stats-card h4 {
    font-size: 1rem;
    margin-top: 0;
    margin-bottom: 12px;
    color: #2c3e50;
}

.batch-select-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.property-select {
    padding: 6px;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
    min-width: 120px;
}

.date-range {
    display: flex;
    align-items: center;
}

.date-range label {
    margin-right: 8px;
    font-size: 0.9rem;
    color: #4a5568;
}

.date-range select {
    padding: 6px;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
}

.trend-chart-container {
    height: 200px;
    width: 100%;
}

.trend-chart {
    height: 100%;
    width: 100%;
}

.stats-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.filter-group label {
    margin-right: 8px;
    font-size: 0.9rem;
    color: #4a5568;
}

.filter-group select {
    padding: 6px;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
}

.supplier-stats {
    margin-bottom: 15px;
}

.stat-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.stat-label {
    width: 25%;
    font-size: 0.9rem;
    color: #4a5568;
}

.stat-bar-container {
    width: 75%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    display: flex;
    align-items: center;
    position: relative;
}

.stat-bar {
    height: 100%;
    background-color: #3498db;
    border-radius: 10px;
}

.stat-value {
    position: absolute;
    right: 10px;
    color: #4a5568;
    font-size: 0.8rem;
    font-weight: bold;
}

.batch-distribution h5 {
    font-size: 0.95rem;
    margin-top: 15px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.distribution-chart {
    height: 200px;
}

.distribution-placeholder {
    display: flex;
    height: 100%;
    align-items: flex-end;
}

.distribution-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 85%;
    margin: 0 5px;
}

.bar-segment {
    width: 80%;
    margin-bottom: 1px;
}

.bar-label {
    margin-top: 5px;
    font-size: 0.8rem;
    color: #4a5568;
    text-align: center;
}

.legend {
    display: flex;
    justify-content: center;
    padding-top: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.legend-color {
    width: 12px;
    height: 12px;
    margin-right: 5px;
    border-radius: 2px;
}

.legend-label {
    font-size: 0.8rem;
    color: #4a5568;
}

/* 底部工具栏 */
.quality-toolbar {
    display: flex;
    justify-content: space-between;
    background-color: #f0f7ff;
    padding: 10px 15px;
    border-radius: 6px;
    margin-top: 15px;
}

.tool-group {
    display: flex;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .quality-workspace {
        grid-template-columns: 1fr 1fr;
    }
    
    .workspace-section.quality-stats {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .quality-workspace {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .workspace-section {
        max-height: none;
    }
    
    .workspace-section.quality-stats {
        grid-column: span 1;
    }
    
    .quality-toolbar {
        flex-direction: column;
    }
    
    .tool-group {
        margin-bottom: 10px;
    }
}

/* 零部件开发页面样式 */
.process-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    margin-top: 10px;
}

.process-step {
    display: flex;
    align-items: center;
    width: 100%;
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 5px;
}

.process-icon {
    width: 40px;
    height: 40px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.process-icon i {
    color: white;
    font-size: 18px;
}

.process-content {
    flex-grow: 1;
}

.process-content h5 {
    margin: 0 0 5px 0;
    font-size: 15px;
    color: #333;
}

.process-content p {
    margin: 0;
    font-size: 13px;
    color: #666;
}

.process-arrow {
    margin: 5px 0;
    color: #aaa;
    font-size: 16px;
}

.resource-planning {
    padding: 10px;
}

.resource-item {
    margin-bottom: 15px;
}

.resource-item label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #444;
}

.resource-list {
    list-style-type: none;
    padding-left: 5px;
    margin: 0;
}

.resource-list li {
    padding: 6px 0;
    border-bottom: 1px dashed #ddd;
    font-size: 13px;
}

.resource-list li:last-child {
    border-bottom: none;
}

.workflow-step {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 10px;
    background-color: #f5f7fa;
    border-left: 4px solid #ddd;
}

.workflow-step[data-status="completed"] {
    border-left-color: #2ecc71;
}

.workflow-step[data-status="in-progress"] {
    border-left-color: #3498db;
    background-color: #ecf0f1;
}

.workflow-step[data-status="pending"] {
    border-left-color: #95a5a6;
    opacity: 0.8;
}

.workflow-step.active {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #7f8c8d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.workflow-step[data-status="completed"] .step-number {
    background-color: #2ecc71;
}

.workflow-step[data-status="in-progress"] .step-number {
    background-color: #3498db;
}

/* 媒体查询适配 */
@media (max-width: 1200px) {
    .component-design,
    .development-progress,
    .production-process {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .experiment-workspace {
        grid-template-columns: 1fr;
    }
    
    .process-chart {
        padding: 10px;
    }
    
    .workflow-step {
        padding: 10px;
    }
}

/* 平台效率数据看板样式 */
.efficiency-stats {
    display: flex;
    background-color: #f8f9fa;
    border-radius: 12px;
    margin: 15px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.efficiency-stat {
    flex: 1;
    padding: 15px;
    text-align: center;
    border-right: 1px solid #e6e6e6;
    transition: all 0.3s ease;
}

.efficiency-stat:last-child {
    border-right: none;
}

.efficiency-stat:hover {
    background-color: #edf2f7;
}

.efficiency-value {
    font-size: 28px;
    font-weight: bold;
    color: #2ecc71;
    margin: 10px 0 5px;
    display: block;
}

.efficiency-label {
    font-size: 13px;
    color: #555;
    margin: 0;
}

.efficiency-desc {
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

.improved-process {
    display: flex;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    border-radius: 8px;
    background-color: #f0f7ff;
    transition: all 0.3s ease;
}

.improved-process:hover {
    background-color: #e3f0ff;
    transform: translateY(-2px);
}

.process-before-after {
    flex: 1;
    display: flex;
    align-items: center;
}

.process-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 15px;
    color: #3498db;
}

.process-arrow i {
    margin: 5px 0;
}

.process-time {
    background-color: #fff;
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.process-time.before {
    border-left: 3px solid #e74c3c;
}

.process-time.after {
    border-left: 3px solid #2ecc71;
}

.process-time span {
    font-weight: bold;
}

.process-description {
    flex: 1;
    padding-left: 15px;
}

.process-title {
    font-weight: bold;
    margin: 0 0 5px 0;
    color: #333;
}

.process-detail {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.efficiency-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.efficiency-header h4 {
    margin: 0;
    color: #333;
}

.efficiency-header .btn-mini {
    font-size: 12px;
    padding: 5px 10px;
    background-color: #3498db;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.efficiency-header .btn-mini:hover {
    background-color: #2980b9;
}

/* 媒体查询适配 */
@media (max-width: 1024px) {
    .efficiency-stats {
        flex-wrap: wrap;
    }
    
    .efficiency-stat {
        flex: 0 0 50%;
        border-bottom: 1px solid #e6e6e6;
    }
    
    .efficiency-stat:nth-child(2n) {
        border-right: none;
    }
    
    .efficiency-stat:nth-last-child(-n+2) {
        border-bottom: none;
    }
    
    .improved-process {
        flex-direction: column;
    }
    
    .process-description {
        padding-left: 0;
        padding-top: 10px;
    }
}

@media (max-width: 768px) {
    .efficiency-stat {
        flex: 0 0 100%;
        border-right: none;
    }
    
    .efficiency-stat:nth-last-child(1) {
        border-bottom: none;
    }
    
    .process-before-after {
        flex-direction: column;
    }
    
    .process-arrow {
        flex-direction: row;
        margin: 10px 0;
    }
    
    .process-arrow i {
        margin: 0 5px;
    }
}
