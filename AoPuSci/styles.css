:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --background-color: #f5f6fa;
    --text-color: #2c3e50;
    --border-color: #dcdde1;
    --sidebar-width: 280px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 5rem;
}

h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.main-container {
    display: flex;
    padding-top: 5rem;
    min-height: 100vh;
    position: relative;
}

.side-nav {
    width: var(--sidebar-width);
    background-color: white;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    gap: 2rem;
    position: fixed;
    top: 5rem;
    left: 0;
    bottom: 0;
    overflow-y: auto;
    z-index: 100;
    border-right: 1px solid var(--border-color);
}

.tab-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tab-btn {
    padding: 1rem;
    border: none;
    background: none;
    color: var(--text-color);
    font-size: 1rem;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    text-align: left;
    border-radius: 4px;
}

.tab-btn:hover {
    color: var(--secondary-color);
    background-color: var(--background-color);
}

.tab-btn.active {
    color: var(--secondary-color);
    background-color: var(--background-color);
}

.tab-btn.active::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: var(--secondary-color);
    border-radius: 2px;
}

.product-selector {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.product-selector label {
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 500;
}

.product-selector select {
    padding: 0.8rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: white;
    color: var(--primary-color);
    font-size: 1rem;
    cursor: pointer;
    width: 100%;
}

.product-selector select:hover {
    border-color: var(--secondary-color);
}

.content {
    flex: 1;
    background-color: white;
    margin: 1rem;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    min-height: calc(100vh - 7rem);
    overflow-y: auto;
    margin-left: calc(var(--sidebar-width) + 1rem);
    margin-right: calc(var(--sidebar-width) + 1rem);
}

footer {
    text-align: center;
    padding: 2rem;
    background-color: var(--primary-color);
    color: white;
    margin-top: 2rem;
}

/* Markdown 内容样式 */
.markdown-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.markdown-content h1 {
    color: var(--primary-color);
    margin: 2rem 0 1rem;
    font-size: 2rem;
}

.markdown-content h2 {
    color: var(--primary-color);
    margin: 1.5rem 0 1rem;
    font-size: 1.5rem;
}

.markdown-content p {
    margin-bottom: 1rem;
}

.markdown-content ul, 
.markdown-content ol {
    margin: 1rem 0;
    padding-left: 2rem;
}

.markdown-content li {
    margin-bottom: 0.5rem;
}

.submenu-container {
    position: relative;
}

.submenu {
    display: none;
    margin-left: 1rem;
    border-left: 2px solid var(--border-color);
}

.submenu-container.active .submenu {
    display: block;
}

.submenu-btn {
    padding: 0.8rem 1rem;
    border: none;
    background: none;
    color: var(--text-color);
    font-size: 0.95rem;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    text-align: left;
    border-radius: 4px;
    width: 100%;
}

.submenu-btn:hover {
    color: var(--secondary-color);
    background-color: var(--background-color);
}

.submenu-btn.active {
    color: var(--secondary-color);
    background-color: var(--background-color);
}

.submenu-section {
    margin-top: 0.5rem;
}

.submenu-title {
    color: var(--text-color);
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    opacity: 0.8;
}

.submenu-trigger {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.submenu-trigger::after {
    content: '▼';
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.submenu-container.active .submenu-trigger::after {
    transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    header {
        padding: 1rem;
    }

    .main-container {
        flex-direction: column;
        padding: 0;
    }

    .side-nav {
        position: relative;
        top: 0;
        left: 0;
        width: 100%;
        height: auto;
        border-radius: 0;
        padding: 1rem;
    }

    .tab-group {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .tab-btn {
        text-align: center;
    }

    .tab-btn.active::before {
        display: none;
    }

    .content {
        margin-left: 0;
        margin-right: 0;
        border-radius: 0;
        padding: 1rem;
    }

    .submenu {
        margin-left: 0;
        border-left: none;
        border-top: 1px solid var(--border-color);
    }

    .submenu-btn {
        padding: 0.6rem 1rem;
    }
}

/* 优化方案图片样式 */
.optimization-image {
    margin: 2rem 0;
    padding: 1.5rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.optimization-image h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    text-align: center;
}

.optimization-image svg {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .optimization-image {
        margin: 1rem 0;
        padding: 1rem;
    }
    
    .optimization-image svg {
        max-width: 100%;
        height: auto;
    }
}

/* 右侧产品型号列表样式 */
.product-list {
    width: var(--sidebar-width);
    background-color: white;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: fixed;
    top: 5rem;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    z-index: 100;
    border-left: 1px solid var(--border-color);
}

.product-list h2 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.product-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-item {
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    background-color: white;
}

.product-item:hover {
    background-color: var(--background-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.product-item.active {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.product-item h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.product-item.active h3 {
    color: white;
}

.product-info {
    font-size: 0.9rem;
    line-height: 1.5;
}

.product-info p {
    margin: 0;
    opacity: 0.9;
}

.product-item.active .product-info p {
    opacity: 1;
}

.reg-number {
    font-weight: 500;
    color: var(--secondary-color);
}

.product-item.active .reg-number {
    color: white;
}

/* 响应式设计更新 */
@media (max-width: 1200px) {
    .product-list {
        display: none;
    }
    
    .content {
        margin-right: 1rem;
    }
}

@media (max-width: 768px) {
    body {
        overflow-x: visible;
    }

    header {
        position: relative;
        height: auto;
    }

    .main-container {
        padding-top: 0;
        flex-direction: column;
    }
    
    .side-nav {
        position: relative;
        top: 0;
        left: 0;
        bottom: auto;
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding: 1rem;
    }
    
    .content {
        margin: 0;
        border-radius: 0;
        padding: 1rem;
    }
}

/* Markdown 表格样式 */
.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.markdown-content th,
.markdown-content td {
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    text-align: left;
}

.markdown-content th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
}

.markdown-content tr:nth-child(even) {
    background-color: var(--background-color);
}

.markdown-content tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.markdown-content td {
    vertical-align: top;
}

/* 响应式表格 */
@media (max-width: 768px) {
    .markdown-content table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}

/* Mermaid 图表样式 */
.mermaid-diagram {
    margin: 2rem 0;
    padding: 1.5rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.mermaid-diagram h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    text-align: center;
}

.mermaid {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: auto;
}

.mermaid svg {
    max-width: 100%;
    height: auto;
}

@media (max-width: 768px) {
    .mermaid-diagram {
        margin: 1rem 0;
        padding: 1rem;
    }
    
    .mermaid svg {
        min-width: 100%;
    }
} 