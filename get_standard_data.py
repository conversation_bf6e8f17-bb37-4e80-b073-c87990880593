#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import argparse
import os
import openai
import json
from collections import defaultdict

def process_excel(excel_path, product_id, output_path=None):
    """
    读取指定的Excel文件，根据"物料编码"列去重，
    只保留"物料编码"，"物料名称"，"规格/型号"，"制造商"和"当前供货商"列，
    并添加一个"产品id"列。
    
    Args:
        excel_path (str): Excel文件路径
        product_id (str): 要设置的产品ID值
        output_path (str, optional): 输出Excel文件路径，默认为None（使用默认路径）
    
    Returns:
        str: 输出文件路径
    """
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        
        # 检查必要的列是否存在
        required_columns = ["物料编码", "物料名称", "规格/型号", "制造商", "当前供货商"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"Excel文件缺少以下列: {', '.join(missing_columns)}")
        
        # 根据"物料编码"列去重
        print("根据'物料编码'列去重...")
        df_unique = df.drop_duplicates(subset=["物料编码"])
        
        # 只保留指定的列
        print("只保留指定的列...")
        df_filtered = df_unique[["物料编码", "物料名称", "规格/型号", "制造商", "当前供货商"]]
        
        # 添加"产品id"列
        print(f"添加'产品id'列，值为: {product_id}")
        df_filtered["产品id"] = product_id
        
        # 确定输出路径
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(excel_path))[0]
            output_path = os.path.join(os.path.dirname(excel_path), f"{base_name}_标准化.xlsx")
        
        # 保存到新的Excel文件
        print(f"保存结果到: {output_path}")
        df_filtered.to_excel(output_path, index=False)
        
        print(f"处理完成。原始数据行数: {len(df)}, 去重后行数: {len(df_unique)}")
        return output_path
    
    except Exception as e:
        print(f"处理Excel文件时出错: {str(e)}")
        raise

def classify_materials_by_vendor(excel_path, api_key=None, model_name="gpt-4o", base_url=None, output_path=None):
    """
    读取指定的Excel文件，按照"当前供货商"进行分类汇总，
    使用大模型对每个供货商的物料名称进行分类，
    并将分类结果添加到原始数据中的"物料类别-new"列。
    
    Args:
        excel_path (str): Excel文件路径
        api_key (str, optional): OpenAI API密钥
        model_name (str, optional): 使用的模型名称，默认为"gpt-4o"
        base_url (str, optional): OpenAI API基础URL
        output_path (str, optional): 输出Excel文件路径，默认为None（使用默认路径）
    
    Returns:
        str: 输出文件路径
    """
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        
        # 检查必要的列是否存在
        required_columns = ["物料编码", "物料名称", "当前供货商"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"Excel文件缺少以下列: {', '.join(missing_columns)}")
        
        # 按照"当前供货商"进行分组
        print("按照'当前供货商'进行分组...")
        vendor_groups = df.groupby("当前供货商")
        
        # 初始化OpenAI客户端
        if api_key:
            if base_url:
                client = openai.OpenAI(api_key=api_key, base_url=base_url)
            else:
                client = openai.OpenAI(api_key=api_key)
        else:
            client = openai.OpenAI()  # 使用环境变量中的API密钥
        
        # 存储分类结果
        material_categories = {}
        
        # 对每个供货商的物料进行分类
        for vendor, group in vendor_groups:
            if pd.isna(vendor) or vendor == "":
                continue
                
            print(f"正在处理供货商: {vendor}")
            material_names = group["物料名称"].tolist()
            
            # 准备提示词
            prompt = f"""
            请将以下医疗设备相关的物料名称进行分类，并返回JSON格式的结果。
            每个物料名称应该被分配到一个合适的类别中。
            请根据物料的功能、用途和特性进行分类，尽量使用通用的物料类别术语。
            
            物料名称列表：
            {json.dumps(material_names, ensure_ascii=False)}
            
            请返回以下格式的JSON：
            {{
                "物料名称1": "类别1",
                "物料名称2": "类别1",
                "物料名称3": "类别2",
                ...
            }}
            
            只返回JSON格式的结果，不要包含任何其他解释或说明。
            """
            
            try:
                # 调用OpenAI API
                response = client.chat.completions.create(
                    model=model_name,
                    messages=[
                        {"role": "system", "content": "你是一个专业的医疗设备物料分类专家，擅长将物料按照功能和用途进行分类。"},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.2,
                    response_format={"type": "json_object"}
                )
                
                # 解析响应
                result = json.loads(response.choices[0].message.content)
                
                # 更新分类结果
                material_categories.update(result)
                
                print(f"供货商 {vendor} 的物料已分类完成，共 {len(result)} 项")
                
            except Exception as e:
                print(f"处理供货商 {vendor} 的物料时出错: {str(e)}")
                # 如果API调用失败，为该供货商的所有物料设置默认分类
                for name in material_names:
                    material_categories[name] = "未分类"
        
        # 将分类结果添加到原始数据中
        print("将分类结果添加到原始数据中...")
        df["物料类别-new"] = df["物料名称"].map(material_categories)
        
        # 对于未被分类的物料，设置为"未分类"
        df["物料类别-new"].fillna("未分类", inplace=True)
        
        # 确定输出路径
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(excel_path))[0]
            output_path = os.path.join(os.path.dirname(excel_path), f"{base_name}_分类.xlsx")
        
        # 保存到新的Excel文件
        print(f"保存结果到: {output_path}")
        df.to_excel(output_path, index=False)
        
        print(f"处理完成。共处理 {len(df)} 行数据，分类了 {len(material_categories)} 个物料名称")
        return output_path
    
    except Exception as e:
        print(f"处理Excel文件时出错: {str(e)}")
        raise

def main():
    parser = argparse.ArgumentParser(description="处理Excel文件，根据物料编码去重并添加产品ID或进行物料分类")
    parser.add_argument("--excel", required=True, help="Excel文件路径")
    parser.add_argument("--product-id", help="要设置的产品ID值")
    parser.add_argument("--output", help="输出Excel文件路径（可选）")
    parser.add_argument("--classify", action="store_true", help="执行物料分类")
    parser.add_argument("--api-key", help="OpenAI API密钥")
    parser.add_argument("--model-name", default="gpt-4o", help="使用的模型名称，默认为gpt-4o")
    parser.add_argument("--base-url", help="OpenAI API基础URL")
    parser.add_argument("--test", action="store_true", help="运行测试模式")
    
    args = parser.parse_args()
    
    if args.test:
        test()
    elif args.classify:
        classify_materials_by_vendor(args.excel, args.api_key, args.model_name, args.base_url, args.output)
    else:
        if not args.product_id:
            raise ValueError("当不使用--classify选项时，必须提供--product-id参数")
        process_excel(args.excel, args.product_id, args.output)

def test():
    """
    测试函数，用于快速测试脚本功能
    """
    # 在这里设置测试参数
    excel_path = "o1000/标件/cy3000-标件-结果.xlsx"  # 替换为实际的测试Excel文件路径
    product_id = "id_3cg6"  # 替换为测试用的产品ID
    output_path = "o1000/标件/cy3000-标件-结果_标准化.xlsx"  # 替换为测试输出文件路径
    
    # 分类测试参数
    classify_excel_path = "o1000/标件/cy3000-标件-结果_标准化.xlsx"  # 替换为实际的测试Excel文件路径
    classify_output_path = "o1000/标件/cy3000-标件-标准化_分类.xlsx"  # 替换为测试输出文件路径
    api_key = "***************************************************"  # 替换为你的OpenAI API密钥，或者使用环境变量
    model_name = "gpt-4.1-2025-04-14"  # 替换为你要使用的模型名称
    base_url = "http://43.130.31.174:8003/v1" # 如果使用自定义API端点，请在这里指定
    
    # 选择要测试的功能
    test_standard = True  # 设置为True测试标准化功能
    test_classify = True  # 设置为True测试分类功能
    
    print("===== 运行测试模式 =====")
    try:
        if test_standard:
            print("\n----- 测试标准化功能 -----")
            result_path = process_excel(excel_path, product_id, output_path)
            print(f"标准化测试成功！结果保存在: {result_path}")
        
        if test_classify:
            print("\n----- 测试分类功能 -----")
            result_path = classify_materials_by_vendor(classify_excel_path, api_key, model_name, base_url, classify_output_path)
            print(f"分类测试成功！结果保存在: {result_path}")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    # main()
    test()
